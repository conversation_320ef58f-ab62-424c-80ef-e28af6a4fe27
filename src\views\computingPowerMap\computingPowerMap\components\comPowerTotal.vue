<template>
  <section class="panel power-panel fancy-corner-panel">
    <div class="card-content">
      <Title title="智算资源统计" />
      <div class="comPowerInfo">
        <div>
          <img src="/images/computingPower/comPowerAiIcon.png" alt="" />
        </div>
        <div style="cursor: pointer" @click="skipPageFunc">
          <p>
            <span>{{ zhisuanTotal }}</span>
            <b>TFLOPS</b>
          </p>
          <!-- <div class="for-910B" v-if="data910B && requestParams.cloudName == ''">
            <div>智算中心</div>
            <div>
              <div>
                910B总量：<span>{{ data910B?.totalCount || 0 }} 张</span>
              </div>
              <div>
                已分配：<span>{{ data910B?.allocatedCount || 0 }} 张</span>
              </div>
              <div>
                剩余量：<span>{{ data910B?.availableCount || 0 }}张</span>
              </div>
            </div>
          </div> -->
        </div>
      </div>

      <div class="comPowerMain">
        <div class="comPowerMainTop">
          <div class="comPowerMainTopLeft">
            <div class="comPowerMainTopLeftIcon">
              <img
                src="/images/computingPower/comPowerGpuIcon.png"
                alt="Icon Description"
                class=""
                width="33"
                height="33"
              />
            </div>
            <div class="comPowerMainTopLeftInfo m-l-14 title">
              <p class="fz-21">
                <span class="comPowerMainTopLeftNumber fz-w m-r-6">{{ latest.gpuTotal }}</span><span class="fz-15 m-l-4">张</span>
              </p>
              <p class="fz-13 m-t-6 c-6">总数</p>
            </div>
          </div>

          <div class="comPowerMainTopRight">
            <p class="">
              <span class="f-1">已分配数</span><span class="m-r-4 fz-w m-t-2">{{ latest.gpuUsed }}</span>张
            </p>
            <p class="">
              <span class="f-1">剩余量</span><span class="m-r-4 fz-w m-t-2">{{ latest.gpuAvi }}</span>张
            </p>
          </div>
        </div>
        <div v-if="gpuListsData.length > 0" class="comPowerMainContent f-d-c f-2">
          <div
            v-for="(item, index) in gpuListsData"
            :key="item.modelName"
            class="m-t-30 br-6 p-r-10"
          >
            <div v-if="item.modelName === '910B'" class="for-910b-zhisuan">智算中心</div>
            <div class="f-r-c-b w100">
              <div class="f-r f-a-c">
                <img
                  src="/images/computingPower/comPowerGpuIconSm.png"
                  alt="Icon Description"
                  class=""
                  width="19"
                  height="19"
                />
                <span class="fz-15 m-l-8">{{ item.modelName + '总量' }}</span>
              </div>
              <p class="c-comPower-theme fz-w fz-20">
                {{ item.totalCount }}<span class="fz-15"> 张</span>
              </p>
            </div>
            <el-progress
              :percentage="percentage(Number(item.allocatedCount), Number(item.totalCount))"
              class="progress-warp m-t-2"
              :stroke-width="16"
              :show-text="true"
              :color="customColors[index]"
              :format="getLabel"
              :text-inside="true"
            />
            <div class="f-r-c-b m-t-6 fz-15">
              <p class="c-6">
                <i :class="customiColors[index]"></i>已分配
                <span class="c-comPower-theme fz-20">{{ item.allocatedCount }}<span class="fz-15"> 张</span></span>
              </p>
              <p class="c-6">
                <i class="bg9DB7E4Grid"></i>剩余量
                <span class="c-comPower-theme fz-20">{{ item.availableCount }}<span class="fz-15"> 张</span></span>
              </p>
            </div>
            <div class="f-r-c-b m-t-4 c-6 fz-13">
              <span></span>
            </div>
          </div>
        </div>
        <div v-else class="comPowerMainContent comPowerNoData">暂无数据</div>
      </div>
      <!--      <div ref="barChartRef" style="width: 100%" class="power-bar-chart"></div>-->
      <slot></slot>
    </div>
  </section>
  <!--  <Card>-->
  <!--  </Card>-->
</template>

<script setup lang="ts">
import { ref, onBeforeMount, onMounted, watch } from 'vue'
import Title from './Title.vue'
import type { comPowerGpuItemType } from '@/views/computingPowerMap/operationsOverview/interface/type'
import { totalPhysicaltApi, totalComputetApi } from '@/api/modules/comPowerCenter'
// Gpu数组
let gpuListsData = ref<comPowerGpuItemType[]>([])
let data910B = ref<
  | comPowerGpuItemType
  | {
      modelName: string
      totalCount: string
      allocatedCount: string
      availableCount: string
    }
>()

const props = defineProps({
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})
const skipPageFunc = () => {
  // 新开一个tab页
  window.open('/dashboard', '_blank')
  // router.push({
  //   path: '/dashboard',
  // })
}
let percentage = (Used: number, Total: number) => {
  let num = (Used / Total) * 100
  Math.round(num)
  return num || 0
}
let getLabel = (percent: number) => {
  let str = percent.toFixed(2).replace(/\.?0+$/, '')
  return str + '%' || '0%'
}
const customColors = ref([
  'linear-gradient(to right, rgba(47, 141, 155, 1), rgba(61, 216, 255, 1))',
  'linear-gradient(to right, rgba(61, 110, 239, 1), rgba(61, 207, 255, 1))',
  'linear-gradient(to right, rgba(115, 58, 229, 1), rgba(77, 140, 255, 1))',
  'linear-gradient(to right, rgba(146, 155, 47, 1), rgba(50, 164, 214, 1))',
])
const customiColors = ref(['bg2F8E9DGrid', 'bg3D6EEFGrid', 'bg733AE5Grid', 'bg929B2FGrid'])
// const color0 = ref()
const zhisuanTotal = ref(0)
const latest = ref({
  gpuTotal: 0,
  gpuUsed: 0,
  gpuAvi: 0,
})

const cityNameMap = ref<Record<string, string>>({
  '570': '衢州',
  '571': '杭州',
  '572': '湖州',
  '573': '嘉兴',
  '574': '宁波',
  '575': '绍兴',
  '576': '台州',
  '577': '温州',
  '578': '丽水',
  '579': '金华',
  '580': '舟山',
})
const cityCodeMap = ref<Record<string, string>>({
  hangzhou: '571',
  ningbo: '574',
  wenzhou: '577',
  jiaxing: '573',
  huzhou: '572',
  shaoxing: '575',
  jinhua: '579',
  quzhou: '570',
  zhoushan: '580',
  taizhou: '576',
  lishui: '578',
  zhejiang: '',
})

const getDataInfo = () => {
  totalPhysicaltApi({
    cityName:
      cityNameMap.value[props.requestParams.cityCode] ||
      cityNameMap.value[cityCodeMap.value[props.requestParams.city]] ||
      null,
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
  }).then((res: any) => {
    if (res.code == 200) {
      const gpuLists = res.entity
      gpuListsData.value = gpuLists.filter((item: any) => item.modelName != '910B') || []
      data910B.value = gpuLists.find((item: any) => item.modelName == '910B')
      if (data910B && props.requestParams.cloudName == '') {
        gpuListsData.value.unshift(
          data910B.value as {
            modelName: string
            totalCount: string
            allocatedCount: string
            availableCount: string
          },
        )
      }
      latest.value.gpuTotal = gpuLists.reduce(
        (sum: number, item: any) => sum + Number(item.totalCount),
        0,
      )
      latest.value.gpuUsed = gpuLists.reduce(
        (sum: number, item: any) => sum + Number(item.allocatedCount),
        0,
      )
      latest.value.gpuAvi = gpuLists.reduce(
        (sum: number, item: any) => sum + Number(item.availableCount),
        0,
      )
    }
  })
  totalComputetApi({
    cityName:
      cityNameMap.value[props.requestParams.cityCode] ||
      cityNameMap.value[cityCodeMap.value[props.requestParams.city]] ||
      null,
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
  }).then((res: any) => {
    if (res.code == 200) {
      zhisuanTotal.value = res.entity
    }
  })
}
// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)
// 原始数值堆叠柱状图数据
onMounted(() => {
  getDataInfo()
})

onBeforeMount(() => {
  // barChart?.dispose()
})
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}
.panel {
  position: relative;
}
.power-panel {
  height: 693px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(42, 107, 255, 0.08);
  padding: 2px;
  position: relative;
  background-color: #fff;
  background: url('/images/computingPower/comPowerLeftBg.png') no-repeat center center;
  background-size: contain;
}
.fancy-corner-panel {
  position: relative;
  border-radius: 16px;
}
.card-content {
  padding: 0 20px;
  height: 100%;
}
.power-bar-chart {
  width: 100%;
  background: transparent;
  margin-top: 0;
}
.comPowerInfo {
  padding: 25px 0 8px 50px;
}
.comPowerInfo > div {
  display: inline-block;
  vertical-align: top;
}
.comPowerInfo > div > img {
  width: 76px;
  margin-right: 30px;
}
.comPowerInfo > div p {
  margin: 0 0 14px 0;
  line-height: 1;
}
.comPowerInfo > div p span {
  display: inline-block;
  vertical-align: middle;
  font-size: 32px;
  color: #004fb1;
}
.comPowerInfo > div p b {
  display: inline-block;
  margin-left: 10px;
  font-size: 18px;
  color: #080c13;
  font-weight: normal;
  vertical-align: middle;
}
.comPowerInfo > div > span {
  font-size: 20px;
  color: #004fb1;
  font-family: ziHuiJingDianYaHei, 'Microsoft YaHei';
}
.for-910b-zhisuan {
  font-size: 18px;
  color: #004fb1;
  font-family: ziHuiJingDianYaHei, 'Microsoft YaHei';
}
.for-910B {
  display: flex;
  > div:first-child {
    font-size: 18px;
    color: #004fb1;
    font-family: ziHuiJingDianYaHei, 'Microsoft YaHei';
  }
  > div:last-child {
    margin-left: 8px;
    span {
      color: #004fb1;
      float: right;
    }
  }
}
.comPowerMain {
  width: 100%;
  height: calc(100% - 200px);
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.6), transparent);
  border-radius: 6px;
  padding: 0 20px;
  box-sizing: border-box;

  .comPowerMainTop {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    padding-top: 20px;
    .comPowerMainTopLeft {
      width: 45%;
      display: flex;
      justify-content: center;
      align-items: center;
      //flex-direction: column;
      .comPowerMainTopLeftIcon {
        width: 60px;
        height: 60px;
        background: #5183e6;
        border: 2px solid #ffffff;
        border-radius: 50%;
        text-align: center;
        //display: inline-block;
        //vertical-align: middle;
        display: flex;
        justify-content: center;
        align-items: center;
        //line-height: 60px;
      }
      .comPowerMainTopLeftInfo {
        .comPowerMainTopLeftNumber {
          font-size: 22px;
          color: #004fb1;
        }
        p {
          margin: 0;
          font-size: 15px;
          color: #080c13;
        }
      }
      & > div {
        display: inline-block;
      }
    }
    .comPowerMainTopRight {
      width: 45%;
      //text-align: center;
      p {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        text-align: left;
        padding: 2px 8px;
        margin: 0;
        span:nth-child(1) {
          display: inline-block;
          width: 80px;
          text-align: left;
          font-size: 15px;
        }
      }
      p:nth-child(1) {
        border-radius: 15px;
        background-color: rgba(233, 248, 236, 0.9921568627);
        color: #10bc1e;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 16px;
      }
      p:nth-child(2) {
        border-radius: 15px;
        font-size: 14px;
        background-color: #edf2fd;
        color: #5e8ef1;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .comPowerMainContent {
    height: calc(100% - 90px);
    overflow: auto;
  }
  .comPowerMainContent.comPowerNoData {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.bg2F8E9DGrid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #2f8e9d;
  margin-right: 10px;
}
.bg3D6EEFGrid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #3d6eef;
  margin-right: 10px;
}
.bg733AE5Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #733ae5;
  margin-right: 10px;
}
.bg929B2FGrid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #929b2f;
  margin-right: 10px;
}

.bg9DB7E4Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #9db7e4;
  margin-right: 10px;
}
</style>
