<template>
  <div class="medium sle">
    <span v-if="fields.beforeText"> {{ fields.beforeText }} </span>
    <el-tooltip :content="String(content)" placement="top">
      <span :style="fields.valueStyle" class="medium sle">
        {{ content }}
      </span>
    </el-tooltip>
    <span v-if="fields.afterText">&nbsp; {{ fields.afterText }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { filterEnum } from '@/utils'

// Props 定义
const props = defineProps<{
  formModel: FormDataType
  fields: FormDataType
  dicCollection: FormDataType
}>()

const { formModel, fields, dicCollection } = toRefs(props)

const content = computed(() => {
  if (!formModel.value || !fields.value) return ''

  if (fields.value.keyName) {
    if (formModel.value[fields.value.keyName] == 0) return 0
    return formModel.value[fields.value.keyName] || ''
  }

  const enumData = fields.value.dicKey
    ? (dicCollection.value[fields.value.dicKey] ?? [])
    : (fields.value.options ?? [])

  const fieldNames = {
    label: fields.value.labelField ?? 'label',
    value: fields.value.valueField ?? 'value',
  }
  const text = filterEnum(formModel.value[fields.value.key], enumData, fieldNames)

  return text || ''
})
</script>
