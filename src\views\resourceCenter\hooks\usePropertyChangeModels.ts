import { useUserStore } from '@/stores/modules/user'
import type { Ref } from 'vue'

interface IChangeReqModel {
  id: number
  volumeSize: number
}
export interface IEcsModel {
  resourceDetailId: number
  resourceType: 'ecs'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IRedisModel {
  resourceDetailId: number
  resourceType: 'redis'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IMySqlModel {
  resourceDetailId: number
  resourceType: 'mysql'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IPostgreSqlModel {
  resourceDetailId: number
  resourceType: 'postgreSql'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IGcsModel {
  resourceDetailId: number
  resourceType: 'gcs'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IEvsModel {
  resourceDetailId: number
  resourceType: 'evs'
  changeType: 'storage_expand' | 'delay'
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IEipModel {
  resourceDetailId: number
  resourceType: 'eip'
  changeType: 'bandwidth_expand' | 'delay'
  changeTime: string
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IObsModel {
  resourceDetailId: number
  resourceType: 'obs'
  changeType: 'delay'
  changeTime: string
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface ISlbModel {
  resourceDetailId: number
  resourceType: 'slb'
  changeType: 'instance_spec_change' | 'delay'
  flavorType: string
  flavorName: string
  changeTime: string
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface INatModel {
  resourceDetailId: number
  resourceType: 'nat'
  changeType: 'delay'
  changeTime: string
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}

export interface IBaseModel {
  applicant: string
  department: string
  workOrdertype: string
  orderTitle: string
  // @工单id
  id: string
  // @业务系统id
  busiSystemId: string
  // 局方负责人
  applyUserName: string
  // @厂家
  manufacturer: string
  // @厂家负责人
  manufacturerContacts: string
  // @厂家电话
  manufacturerMobile: string
  // @所属业务模块
  moduleId: string
  // @二级部门领导
  busiDepartLeaderId: string
  // @三级部门领导
  levelThreeLeaderId: string
  // @资源申请说明
  orderDesc: string
  // @资源上云说明书
  files: any[]

  // @操作名称
  operationName: string
  moduleName: string
  busiDepartLeaderLabel: string
  levelThreeLeaderLabel: string
  busiSystemName: string
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}

type IChangeTypeMap = {
  ecs: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  redis: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  mysql: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  postgreSql: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  gcs: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  evs: 'storage_expand' | 'delay'
  eip: 'bandwidth_expand' | 'delay'
  slb: 'instance_spec_change' | 'bandwidth_expand' | 'delay'
  nat: 'delay'
  obs: 'delay' | 'storage_expand'
  default: 'delay'
}

// 重新定义 ChangeTypeToBefore 使其成为判别联合类型
export type ChangeTypeToBefore<C> = C extends 'instance_spec_change'
  ? string
  : C extends 'storage_expand'
    ? Array<[string, number, string?]>
    : C extends 'bandwidth_expand'
      ? string
      : C extends 'delay'
        ? string
        : string
export type ChangeTypeToAfter<C> = C extends 'instance_spec_change'
  ? [string, string] | string
  : C extends 'storage_expand'
    ? Array<[string, number, string?]>
    : C extends 'bandwidth_expand'
      ? string
      : C extends 'delay'
        ? string
        : string

type IBasePropsItem<T extends keyof IChangeTypeMap> = {
  [Resource in keyof IChangeTypeMap]: {
    [Change in IChangeTypeMap[Resource]]: {
      resourceType: Resource
      changeType: Change
      resourceDetailId: number
      changeTypeCn: string
      before: ChangeTypeToBefore<Change>
      after: ChangeTypeToAfter<Change>
      templateCode?: string
      ref?: Ref<any, any>
    }
  }[IChangeTypeMap[Resource]]
}[T]

export type IPropsItem<T extends keyof IChangeTypeMap> = IBasePropsItem<T> &
  (T extends 'ecs' | 'gcs' | 'mysql' | 'redis' | 'postgreSql'
    ? { eipId: number }
    : { eipId?: never })

export interface IPropsListItem<T extends keyof IChangeTypeMap> {
  resourceDetailId: number
  deviceName: string
  resourceType: T
  resourceTypeCn: string
  domainCode?: string
  cloudPlatform?: string
  azId?: string
  regionId?: string
  resourcePoolName?: string
  props: IPropsItem<T>[]
  deviceId?: string
}

export function useBaseModel() {
  const userStore = useUserStore()
  const model: IBaseModel = {
    applicant: userStore.userInfo.userName,
    department: userStore.userInfo.sysDeptName,
    workOrdertype: '变更申请',
    orderTitle: '',
    // @工单id
    id: '',
    // @业务系统id
    busiSystemId: '',
    // 局方负责人
    applyUserName: '',
    // @厂家
    manufacturer: '',
    // @厂家负责人
    manufacturerContacts: '',
    // @厂家电话
    manufacturerMobile: '',
    // @所属业务模块
    moduleId: '',
    // @二级部门领导
    busiDepartLeaderId: '',
    // @三级部门领导
    levelThreeLeaderId: '',
    // @资源申请说明
    orderDesc: '',
    // @资源上云说明书
    files: [],

    // @操作名称
    operationName: '开通资源申请单',
    moduleName: '',
    busiDepartLeaderLabel: '',
    levelThreeLeaderLabel: '',
    busiSystemName: '',
    ref: null,
  }
  return model
}
