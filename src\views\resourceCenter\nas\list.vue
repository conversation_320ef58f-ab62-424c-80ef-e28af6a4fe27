<template>
  <div class="table-box">
    <sl-page-header
      title="NAS"
      title-line="提供了对NAS的管理能力"
      :icon="{
        class: 'page_NAS',
        color: '#0052D9',
        size: '40px',
      }"
    >
    </sl-page-header>
    <div class="resource-tab">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleBatchRecycle" type="primary" v-permission="'BatchRecycle'">
          批量回收
        </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: collapsed }"
          ref="formRef"
          :options="formOptions"
          v-model="formModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <dataList
          ref="dataListRef"
          :query-params="queryParams"
          :hide-operations="shouldHideResourceOperations"
        ></dataList>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { Delete, Search, ArrowDown, ArrowUp, Upload } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useDichooks } from '../hooks/useDichooks'
import { normalizeExportArgs } from '@/views/resourceCenter/utils'
import { useDownload } from '@/hooks/useDownload'
import { resourceExport } from '@/api/modules/resourecenter'
import { useRolePermission } from '../hooks/useRolePermission'
import { useGlobalDicStore } from '@/stores/modules/dic'

// 获取路由信息
const route = useRoute()

const { shouldHideResourceOperations } = useRolePermission()

const { busiSystemOptions } = useBusiSystemOptions()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const formRef = ref<any>(null)

// 从query参数中获取业务系统的初始值
const getBusinessSysIdsFromQuery = () => {
  const businessSysIds = route.query.businessSysIds
  if (businessSysIds && typeof businessSysIds === 'string') {
    // 字符串格式，用逗号分割
    return businessSysIds.split(',').filter((id: string) => id.trim())
  }
  return []
}

const formModel = reactive<any>({
  businessSysIds: getBusinessSysIdsFromQuery(),
})

const queryParams = ref<any>({ type: 'nas', ...formModel })

function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}

function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

const { resourcePoolsDic } = useDichooks()

// 是否默认折叠搜索项
const collapsed = ref(true)

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: 'NAS名称',
        type: 'input',
        key: 'deviceName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSysIds',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
        props: {
          select: {
            multiple: true,
            filterable: true,
            clearable: true,
            collapseTags: true,
          },
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'nas', 'NAS存储.xlsx', {
                      ...formModel,
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '存储大小',
        type: 'input',
        key: 'spec',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '存储路径',
        type: 'input',
        key: 'deviceStatus',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '所属云',
        type: 'input',
        key: 'cloudPlatform',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请人',
        type: 'input',
        key: 'applyUserName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '工单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'applyTime',
        options: getDic('time'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '开通时间',
        type: 'date',
        key: 'effectiveTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        label: '到期时间',
        type: 'date',
        key: 'expireTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 批量回收操作
const dataListRef = ref()
const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}
</script>

<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
