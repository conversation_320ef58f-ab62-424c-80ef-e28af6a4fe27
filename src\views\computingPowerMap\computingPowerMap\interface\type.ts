// 资源使用情况数据类型定义
export interface ResourceUsageData {
  vcpuTotal: number // vCPU总量
  vcpuUsed: number // vCPU已用量
  vcpuAvi: number // vCPU剩余量
  memoryTotal: number // 内存总量，单位：GB
  memoryUsed: number // 内存已用量，单位：GB
  memoryAvi: number // 内存剩余量，单位：GB
  storageTotal: number // 存储总量，单位：GB
  storageUsed: number // 存储已用量，单位：GB
  storageAvi: number // 存储剩余量，单位：GB
  eipTotal: number // 公网IP总量
  eipUsed: number // 公网IP已用量
  eipAvi: number // 公网IP剩余量
  dcnTotal: number // dcn总量
  dcnUsed: number // dcn已用量
  dcnAvi: number // dcn剩余量
  vmNum: number // 虚拟池数量
}

// API 请求参数类型
export interface ResourceUsageParams {
  cloudName?: string // 云平台类型
  platformTypeName?: string // 云平台
  cityCode?: string // 地市编码
  areaCode?: string // 区县编码
}

// 存储组件数据类型
export interface StorageData {
  total: number
  used: number
  remaining: number
  percentage: number
  memoryunits: string
}

// vCPU组件数据类型
export interface VCpuData {
  total: number
  used: number
  remaining: number
  percentage: number
}

// 内存组件数据类型
export interface MemoryData {
  total: number
  used: number
  remaining: number
  percentage: number
  memoryunits: string
}

// 网络组件数据类型
export interface InternetData {
  dcn: {
    total: number
    used: number
    remaining: number
  }
  publicIp: {
    total: number
    used: number
    remaining: number
  }
}
