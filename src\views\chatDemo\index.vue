<template>
  <div class="chat-demo-container">
    <div class="demo-header">
      <h1 class="title">
        <el-icon class="title-icon"><ChatDotRound /></el-icon>
        命令校验
      </h1>
      <p class="subtitle">输入命令，系统将自动校验命令的合法性</p>
    </div>

    <div class="demo-content">
      <div class="input-section">
        <div class="input-header">
          <el-icon><Edit /></el-icon>
          <span>命令输入</span>
        </div>
        <el-input
          v-model="inputCommand"
          type="textarea"
          :rows="6"
          placeholder="请输入网络设备命令（一行一个），例如：&#10;ip route-static ******* 32 vlan-interface 10 ******* backup-interface vlan-interface 11 backup-nexthop *******&#10;支持多行命令输入，请先选择对应的厂家..."
          class="command-input"
          :disabled="loading"
        />

        <!-- 厂家选择 -->
        <div class="vendor-selection">
          <div class="vendor-header">
            <el-icon><Setting /></el-icon>
            <span>设备型号</span>
          </div>
          <el-radio-group v-model="selectedVendor" class="vendor-options">
            <el-radio label="华为-Eudemon1000E-G5">华为-Eudemon1000E-G5</el-radio>
            <el-radio label="锐捷-S5310-48GT4XS-E">锐捷-S5310-48GT4XS-E</el-radio>
            <el-radio label="锐捷-S6250-48XS8CQ">锐捷-S6250-48XS8CQ</el-radio>
            <el-radio label="锐捷-S6510-48VS8CQ">锐捷-S6510-48VS8CQ</el-radio>
            <el-radio label="新华三-S5130S-54S-HI-G">新华三-S5130S-54S-HI-G</el-radio>
            <el-radio label="中兴-5952G-D">中兴-5952G-D</el-radio>
            <el-radio label="烽火-S6900-48S8CQ">烽火-S6900-48S8CQ</el-radio>
            <el-radio label="浪潮-SC5630EL">浪潮-SC5630EL</el-radio>
            <el-radio label="浪潮-CN61108PC-V-H">浪潮-CN61108PC-V-H</el-radio>
            <el-radio label="迪普-FW1000-TA-A10">迪普-FW1000-TA-A10</el-radio>
            <el-radio label="迪普-FW1000-TA-A10">迪普-FW1000-TA-A10</el-radio>
          </el-radio-group>
        </div>

        <div class="input-actions">
          <el-button
            type="primary"
            :loading="loading"
            @click="validateCommand"
            :disabled="!inputCommand.trim()"
            size="large"
          >
            <el-icon v-if="!loading"><Promotion /></el-icon>
            {{ loading ? '校验中...' : '开始校验' }}
          </el-button>
          <el-button @click="clearInput" :disabled="loading" size="large">
            <el-icon><Delete /></el-icon>
            清空
          </el-button>
        </div>
      </div>

      <div class="result-section" v-if="validationResults.length > 0 || error">
        <div class="result-header">
          <el-icon><Document /></el-icon>
          <span>校验结果</span>
        </div>

        <!-- 错误提示 -->
        <el-alert v-if="error" :title="error" type="error" show-icon class="error-alert" />

        <!-- 校验结果 -->
        <div v-if="validationResults.length > 0" class="results-container">
          <div class="result-summary">
            <el-tag :type="getSummaryType()" size="large">
              共检测到 {{ validationResults.length }} 项结果
            </el-tag>
            <el-tag v-if="responseTime > 0" type="info" size="large" class="response-time-tag">
              <el-icon><Timer /></el-icon>
              响应时间: {{ responseTime }}ms
            </el-tag>
          </div>

          <!-- 表格形式展示结果 -->
          <el-table :data="validationResults" class="validation-table" stripe>
            <el-table-column prop="index" label="行号" width="80" align="center">
              <template #default="{ $index }">
                <span class="line-number">{{ $index + 1 }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="command" label="配置内容" min-width="300">
              <template #default="{ row }">
                <el-tag class="command-tag" type="info" effect="plain">
                  {{ row.command }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              prop="error_level"
              label="是否通过 ANTLR 校验"
              width="180"
              align="center"
            >
              <template #default="{ row }">
                <div class="validation-status">
                  <el-icon v-if="row.error_level === -1" class="success-icon">
                    <SuccessFilled />
                  </el-icon>
                  <el-icon v-else class="error-icon">
                    <WarningFilled />
                  </el-icon>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="error_message" label="原因" min-width="300">
              <template #default="{ row }">
                <div v-if="row.error_level === -1" class="success-reason">
                  符合
                  <el-tag class="rule-tag" type="info" effect="plain" size="small">
                    {{ getRuleName(row.command) }}
                  </el-tag>
                  规则
                </div>
                <div v-else class="error-reason">
                  {{ row.error_message }}
                  <el-tag
                    v-if="getMissingParam(row.error_message)"
                    class="param-tag"
                    type="warning"
                    effect="plain"
                    size="small"
                  >
                    {{ getMissingParam(row.error_message) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ChatDotRound,
  Edit,
  Promotion,
  Delete,
  Document,
  SuccessFilled,
  WarningFilled,
  Setting,
  Timer,
} from '@element-plus/icons-vue'
import { uuid } from '@/utils'
declare global {
  interface Window {
    demo_check_url?: string
  }
}
// 响应式数据
const inputCommand = ref('')
const loading = ref(false)
const validationResults = ref<ValidationResult[]>([])
const error = ref('')
const selectedVendor = ref('华为-Eudemon1000E-G5') // 默认选择华为
const responseTime = ref(0) // 添加响应时间变量

// 类型定义
interface ValidationResult {
  command: string
  error_message: string
  error_level: number
}

interface ApiResponse {
  code: string
  data: ValidationResult[]
  success: string
}

// 校验命令
const validateCommand = async () => {
  if (!inputCommand.value.trim()) {
    ElMessage.warning('请输入网络设备命令')
    return
  }

  loading.value = true
  error.value = ''
  validationResults.value = []

  const startTime = Date.now() // 记录开始时间
  const demo_check_url = window.demo_check_url || 'http://188.104.1.26:5000/py-sl/configCheck'
  try {
    const response = await fetch(demo_check_url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        memoryId: uuid(16), // 使用 16 位 uuid
        message: inputCommand.value.trim(),
        // sourceType: selectedVendor.value, // 添加厂家参数
        sourceType: 'h3c', // 添加厂家参数
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`)
    }

    const data: ApiResponse = await response.json()
    const endTime = Date.now() // 记录结束时间
    responseTime.value = endTime - startTime // 计算响应时间

    if (data.code === '200' && Array.isArray(data.data)) {
      validationResults.value = data.data
      ElMessage.success(
        `校验完成！检测到 ${data.data.length} 项结果，响应时间 ${responseTime.value}ms`,
      )
    } else {
      throw new Error('返回数据格式错误')
    }
  } catch (err) {
    console.error('校验请求失败:', err)
    error.value = err instanceof Error ? err.message : '校验请求失败，请检查网络连接'
    ElMessage.error('校验失败，请稍后重试')
    responseTime.value = 0 // 出错时重置响应时间
  } finally {
    loading.value = false
  }
}

// 清空输入
const clearInput = () => {
  inputCommand.value = ''
  validationResults.value = []
  error.value = ''
  responseTime.value = 0 // 清空时重置响应时间
}

// 获取总结标签类型
const getSummaryType = () => {
  const hasError = validationResults.value.some((r) => r.error_level === 1)

  if (hasError) return 'danger'
  return 'success'
}

// 获取规则名称
const getRuleName = (command: string) => {
  // 根据命令内容判断规则名称
  if (command.includes('interface')) return 'interface_name'
  if (command.includes('ip address')) return 'ip_address'
  if (command.includes('vrrp')) return 'vrrp_config'
  if (command.includes('port')) return 'port_config'
  return 'general_rule'
}

// 提取缺失参数
const getMissingParam = (errorMessage: string) => {
  const match = errorMessage.match(/缺少\s+(\w+)\s+参数/)
  return match ? match[1] : null
}
</script>

<style scoped lang="scss">
.chat-demo-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 48px);
  border-radius: 12px;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;

  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 32px;
    font-weight: bold;
    color: #2c3e50;
    margin: 0 0 16px 0;

    .title-icon {
      font-size: 36px;
      color: #409eff;
    }
  }

  .subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

.demo-content {
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr;
}

.input-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .input-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 16px;
  }

  .command-input {
    margin-bottom: 16px;

    :deep(.el-textarea__inner) {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.6;
      border-radius: 8px;
      border: 2px solid #e4e7ed;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .vendor-selection {
    margin-bottom: 16px;

    .vendor-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 12px;
    }

    .vendor-options {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }
  }

  .input-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}

.result-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 16px;
  }

  .error-alert {
    margin-bottom: 16px;
  }
}

.results-container {
  .result-summary {
    margin-bottom: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;

    .response-time-tag {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .result-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .result-item {
    border: 2px solid #e4e7ed;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    }

    &.result-success {
      border-color: #67c23a;
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    }

    &.result-error {
      border-color: #f56c6c;
      background: linear-gradient(135deg, #fff5f5 0%, #fee 100%);
    }

    .result-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .result-index {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: bold;
        font-size: 16px;
        color: #2c3e50;
      }
    }

    .command-block,
    .error-block {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .command-title,
      .error-title {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .command-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-all;
        margin: 0;
        color: #2c3e50;
      }

      .error-message {
        color: #e6a23c;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        padding: 12px;
        background: rgba(230, 162, 60, 0.1);
        border-radius: 6px;
        border-left: 4px solid #e6a23c;
      }
    }
  }
}

.validation-table {
  margin-top: 20px;

  :deep(.el-table__header) {
    background-color: #f5f7fa;
  }

  :deep(.el-table__row) {
    &:hover {
      background-color: #f0f9ff;
    }
  }
}

.line-number {
  font-weight: bold;
  color: #606266;
}

.command-tag {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  max-width: 100%;
  word-break: break-all;
}

.validation-status {
  display: flex;
  justify-content: center;
  align-items: center;

  .success-icon {
    color: #67c23a;
    font-size: 18px;
  }

  .error-icon {
    color: #f56c6c;
    font-size: 18px;
  }
}

.success-reason {
  color: #67c23a;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-reason {
  color: #f56c6c;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.rule-tag,
.param-tag {
  font-size: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .chat-demo-container {
    padding: 16px;
  }

  .demo-header .title {
    font-size: 24px;

    .title-icon {
      font-size: 28px;
    }
  }

  .input-section,
  .result-section {
    padding: 16px;
  }

  .input-actions {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }
}
</style>
