<template>
  <div class="chat-demo-container">
    <div class="demo-header">
      <h1 class="title">
        <el-icon class="title-icon"><ChatDotRound /></el-icon>
        命令校验
      </h1>
      <p class="subtitle">输入命令，系统将自动校验命令的合法性</p>
    </div>

    <div class="demo-content">
      <div class="input-section">
        <div class="input-header">
          <el-icon><Edit /></el-icon>
          <span>命令输入</span>
        </div>
        <el-input
          v-model="inputCommand"
          type="textarea"
          :rows="6"
          placeholder="请输入网络设备命令（一行一个），例如：&#10;ip route-static ******* 32 vlan-interface 10 ******* backup-interface vlan-interface 11 backup-nexthop *******&#10;支持多行命令输入，请先选择对应的厂家..."
          class="command-input"
          :disabled="loading"
        />

        <!-- 厂家与型号联选 -->
        <div class="vendor-selection">
          <div class="vendor-header">
            <el-icon><Setting /></el-icon>
            <span>厂家与型号</span>
          </div>
          <div class="vendor-cascader">
            <el-select
              v-model="selectedManufacturer"
              placeholder="选择厂家"
              :disabled="loading"
              @change="handleManufacturerChange"
              style="min-width: 180px; background: transparent"
            >
              <el-option label="华三" value="h3c" />
              <el-option label="锐捷" value="rj" />
              <el-option label="浪潮" value="lc" />
              <el-option label="中兴" value="zx" />
              <el-option label="烽火" value="fh" />
            </el-select>
            <el-select
              v-model="selectedModel"
              placeholder="选择型号"
              :disabled="!selectedManufacturer || loading"
              style="min-width: 220px; background: transparent"
            >
              <el-option
                v-for="item in availableModels"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>

        <div class="input-actions">
          <el-button
            type="primary"
            :loading="loading"
            @click="validateCommand"
            :disabled="!inputCommand.trim() || !selectedManufacturer || !selectedModel"
            size="large"
          >
            <el-icon v-if="!loading"><Promotion /></el-icon>
            {{ loading ? '校验中...' : '开始校验' }}
          </el-button>
          <el-button @click="triggerImportFile" :disabled="loading" size="large">
            <el-icon><UploadFilled /></el-icon>
            导入文件
          </el-button>
          <el-button @click="clearInput" :disabled="loading" size="large">
            <el-icon><Delete /></el-icon>
            清空
          </el-button>
          <input
            ref="fileInputRef"
            type="file"
            accept=".txt,.xlsx,.xls"
            @change="handleImportFile"
            style="display: none"
          />
        </div>
      </div>

      <div class="result-section" v-if="validationResults.length > 0 || error">
        <div class="result-header">
          <el-icon><Document /></el-icon>
          <span>校验结果</span>
        </div>

        <!-- 错误提示 -->
        <el-alert v-if="error" :title="error" type="error" show-icon class="error-alert" />

        <!-- 校验结果 -->
        <div v-if="validationResults.length > 0" class="results-container">
          <el-tabs v-model="activeTab">
            <el-tab-pane :label="`全部(${validationResults.length})`" name="all">
              <div class="result-summary">
                <el-tag :type="getSummaryType()" size="large">
                  共检测到 {{ validationResults.length }} 项结果
                </el-tag>
                <el-tag v-if="responseTime > 0" type="info" size="large" class="response-time-tag">
                  <el-icon><Timer /></el-icon>
                  响应时间: {{ responseTime }}ms
                </el-tag>
                <el-button type="success" size="small" @click="exportToExcel()">
                  <el-icon><Download /></el-icon>
                  导出全部
                </el-button>
              </div>

              <el-table
                :data="validationResults"
                class="validation-table"
                stripe
                :max-height="tableMaxHeight"
              >
                <el-table-column
                  show-overflow-tooltip
                  prop="index"
                  label="行号"
                  width="80"
                  align="center"
                >
                  <template #default="{ $index }">
                    <span class="line-number">{{ $index + 1 }}</span>
                  </template>
                </el-table-column>

                <el-table-column show-overflow-tooltip prop="command" label="命令" min-width="360">
                  <template #default="{ row }">
                    <div class="command-cell">
                      <span class="command-ellipsis">{{ row.command }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  show-overflow-tooltip
                  prop="error_level"
                  label="是否通过校验"
                  width="180"
                  align="center"
                >
                  <template #default="{ row }">
                    <div class="validation-status">
                      <el-icon v-if="row.error_level === -1" class="success-icon">
                        <SuccessFilled />
                      </el-icon>
                      <el-icon v-else class="error-icon">
                        <WarningFilled />
                      </el-icon>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  show-overflow-tooltip
                  prop="error_message"
                  label="原因"
                  min-width="300"
                >
                  <template #default="{ row }">
                    <div v-if="row.error_level !== -1" class="error-reason">
                      {{ row.error_message }}
                      <el-tag
                        v-if="getMissingParam(row.error_message)"
                        class="param-tag"
                        type="warning"
                        effect="plain"
                        size="small"
                      >
                        {{ getMissingParam(row.error_message) }}
                      </el-tag>
                    </div>
                    <div v-else>/</div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>

            <el-tab-pane :label="`错误(${errorResults.length})`" name="error">
              <div class="result-summary">
                <el-tag type="danger" size="large">
                  共检测到 {{ errorResults.length }} 项错误
                </el-tag>
                <el-tag v-if="responseTime > 0" type="info" size="large" class="response-time-tag">
                  <el-icon><Timer /></el-icon>
                  响应时间: {{ responseTime }}ms
                </el-tag>
                <el-button
                  type="warning"
                  size="small"
                  @click="exportToExcel(errorResults, '错误结果')"
                >
                  <el-icon><Download /></el-icon>
                  导出错误
                </el-button>
              </div>

              <el-table
                :data="errorResults"
                class="validation-table"
                stripe
                :max-height="tableMaxHeight"
              >
                <el-table-column
                  show-overflow-tooltip
                  prop="index"
                  label="行号"
                  width="80"
                  align="center"
                >
                  <template #default="{ $index }">
                    <span class="line-number">{{ $index + 1 }}</span>
                  </template>
                </el-table-column>

                <el-table-column show-overflow-tooltip prop="command" label="命令" min-width="360">
                  <template #default="{ row }">
                    <div class="command-cell">
                      <span class="command-ellipsis">{{ row.command }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  show-overflow-tooltip
                  prop="error_level"
                  label="是否通过校验"
                  width="180"
                  align="center"
                >
                  <template #default="{ row }">
                    <div class="validation-status">
                      <el-icon v-if="row.error_level === -1" class="success-icon">
                        <SuccessFilled />
                      </el-icon>
                      <el-icon v-else class="error-icon">
                        <WarningFilled />
                      </el-icon>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  show-overflow-tooltip
                  prop="error_message"
                  label="原因"
                  min-width="300"
                >
                  <template #default="{ row }">
                    <div v-if="row.error_level !== -1" class="error-reason">
                      {{ row.error_message }}
                      <el-tag
                        v-if="getMissingParam(row.error_message)"
                        class="param-tag"
                        type="warning"
                        effect="plain"
                        size="small"
                      >
                        {{ getMissingParam(row.error_message) }}
                      </el-tag>
                    </div>
                    <div v-else>/</div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ChatDotRound,
  Edit,
  Promotion,
  Delete,
  Document,
  SuccessFilled,
  WarningFilled,
  Setting,
  Timer,
  UploadFilled,
  Download,
} from '@element-plus/icons-vue'
import { uuid } from '@/utils'
import * as XLSX from 'xlsx'
declare global {
  interface Window {
    demo_check_url?: string
  }
}
// 响应式数据
const inputCommand = ref('')
const loading = ref(false)
const validationResults = ref<ValidationResult[]>([])
const activeTab = ref<'all' | 'error'>('all')
const error = ref('')
const tableMaxHeight = ref<number>(520)
// 厂家与型号
const selectedManufacturer = ref<'h3c' | 'zx' | 'rj' | 'lc' | 'fh' | ''>('h3c')
const selectedModel = ref<string>('S5130S-54S-HI-G')
const fileInputRef = ref<HTMLInputElement | null>(null)
const responseTime = ref(0) // 添加响应时间变量

// 类型定义
interface ValidationResult {
  command: string
  error_message: string
  error_level: number
}

interface ApiResponse {
  code: string
  data: ValidationResult[]
  success: string
}

interface VendorModelItem {
  label: string
  value: string
}

const vendorModels: Record<string, VendorModelItem[]> = {
  h3c: [{ label: 'S5130S-54S-HI-G', value: 'S5130S' }],
  zx: [{ label: '5952G-D', value: '5952G' }],
  rj: [
    { label: 'S6510-48VS8CQ', value: 'S6510' },
    { label: 'S6250-48XS8CQ', value: 'S6250' },
    { label: 'S5310-48GT4XS-E', value: 'S5310' },
  ],
  lc: [
    { label: 'CN61108PC-V-H', value: 'CN61108PC' },
    { label: 'SC5630EL', value: 'SC5630EL' },
  ],
  fh: [{ label: 'S6900-48S8CQ', value: 'S6900' }],
}

const availableModels = computed<VendorModelItem[]>(() => {
  return selectedManufacturer.value ? vendorModels[selectedManufacturer.value] || [] : []
})

const errorResults = computed<ValidationResult[]>(() =>
  validationResults.value.filter((r) => r.error_level !== -1),
)

const handleManufacturerChange = () => {
  const list = availableModels.value
  selectedModel.value = list.length > 0 ? list[0].value : ''
}
const uid = uuid(16)
// 校验命令
const validateCommand = async () => {
  if (!inputCommand.value.trim()) {
    ElMessage.warning('请输入网络设备命令')
    return
  }
  if (!selectedManufacturer.value || !selectedModel.value) {
    ElMessage.warning('请选择厂家和型号')
    return
  }
  loading.value = true
  error.value = ''
  validationResults.value = []
  // window.demo_check_url = 'https://yapi.aizhp.site/mock/30/v1/woc/portalcenter/aiConfigCheck'
  const startTime = Date.now() // 记录开始时间
  const demo_check_url = window.demo_check_url || 'http://188.104.1.26:5000/py-sl/configCheck'
  try {
    const response = await fetch(demo_check_url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        memoryId: uid, // 使用 16 位 uuid
        message: inputCommand.value.trim(),
        sourceType: selectedManufacturer.value, // 厂家参数
        model: selectedModel.value, // 型号参数
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`)
    }

    const data: ApiResponse = await response.json()
    const endTime = Date.now() // 记录结束时间
    responseTime.value = endTime - startTime // 计算响应时间

    if (data.code === '200' && Array.isArray(data.data)) {
      validationResults.value = data.data
      ElMessage.success(
        `校验完成！检测到 ${data.data.length} 项结果，响应时间 ${responseTime.value}ms`,
      )
    } else {
      throw new Error('返回数据格式错误')
    }
  } catch (err) {
    console.error('校验请求失败:', err)
    error.value = err instanceof Error ? err.message : '校验请求失败，请检查网络连接'
    ElMessage.error('校验失败，请稍后重试')
    responseTime.value = 0 // 出错时重置响应时间
  } finally {
    loading.value = false
  }
}

// 清空输入
const clearInput = () => {
  inputCommand.value = ''
  validationResults.value = []
  error.value = ''
  responseTime.value = 0 // 清空时重置响应时间
}

// 统一触发与处理导入
const triggerImportFile = () => {
  fileInputRef.value?.click()
}

const handleImportFile = async (e: Event) => {
  const target = e.target as HTMLInputElement
  const file = target.files && target.files[0]
  if (!file) return
  try {
    const ext = file.name.split('.').pop()?.toLowerCase()
    if (ext === 'txt') {
      const text = await file.text()
      inputCommand.value = text
      ElMessage.success(`已导入 ${file.name}`)
      return
    }

    if (ext === 'xlsx' || ext === 'xls') {
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const firstSheetName = workbook.SheetNames[0]
      if (!firstSheetName) {
        ElMessage.error('Excel 文件没有可用的工作表')
        return
      }
      const sheet = workbook.Sheets[firstSheetName]
      const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 }) as unknown as any[][]
      const firstColValues = (rows || [])
        .map((r) => (Array.isArray(r) ? r[0] : undefined))
        .filter((v) => v !== undefined && v !== null && String(v).trim() !== '')
        .map((v) => String(v).trim())

      if (firstColValues.length === 0) {
        ElMessage.warning('未在第一列读取到有效数据')
      }

      const content = firstColValues.length > 0 ? `#\n${firstColValues.join('\n#\n')}\n#` : '#\n#'
      inputCommand.value = content
      ElMessage.success(`已导入 ${file.name}（${firstColValues.length} 行）`)
      return
    }

    ElMessage.error('不支持的文件类型，仅支持 .txt/.xlsx/.xls')
  } catch (err) {
    console.error('读取文件失败:', err)
    ElMessage.error('读取文件失败，请重试')
  } finally {
    if (target) target.value = ''
  }
}

// 导出Excel（支持传入数据集与工作表名）
const exportToExcel = (rows?: ValidationResult[], sheetName: string = '校验结果') => {
  const dataToExport = rows ?? validationResults.value
  if (dataToExport.length === 0) {
    ElMessage.warning('暂无可导出的数据')
    return
  }
  const exportData = dataToExport.map((row, index) => ({
    行号: index + 1,
    命令: row.command,
    是否通过ANTLR校验: row.error_level === -1 ? '通过' : '未通过',
    原因: row.error_level === -1 ? '' : row.error_message,
  }))

  const worksheet = XLSX.utils.json_to_sheet(exportData)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
  const date = new Date().toISOString().slice(0, 10)
  XLSX.writeFile(workbook, `${sheetName}_${date}.xlsx`)
}

// 获取总结标签类型
const getSummaryType = () => {
  const hasError = validationResults.value.some((r) => r.error_level === 1)

  if (hasError) return 'danger'
  return 'success'
}

// 规则名称函数已移除：成功记录不再展示原因

// 提取缺失参数
const getMissingParam = (errorMessage: string) => {
  const match = errorMessage.match(/缺少\s+(\w+)\s+参数/)
  return match ? match[1] : null
}

// 根据窗口高度动态设置表格最大高度，避免切换Tab时页面滚动条跳动
const computeTableMaxHeight = () => {
  // 视窗高度 - 外层padding/头部等占位 - 结果区内头部与操作区
  const viewport = window.innerHeight
  const reserved = 280 // 估算：头部+输入区底部到表格顶部的固定占用
  const max = Math.max(300, viewport - reserved)
  tableMaxHeight.value = max
}

onMounted(() => {
  computeTableMaxHeight()
  window.addEventListener('resize', computeTableMaxHeight)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', computeTableMaxHeight)
})
</script>

<style scoped lang="scss">
.chat-demo-container {
  padding: 24px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 48px - 55px);
  border-radius: 12px;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;

  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 32px;
    font-weight: bold;
    color: #2c3e50;
    margin: 0 0 16px 0;

    .title-icon {
      font-size: 36px;
      color: #409eff;
    }
  }

  .subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

.demo-content {
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr;
}

.input-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .input-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 16px;
  }

  .command-input {
    margin-bottom: 16px;
    background: transparent;
    :deep(.el-textarea__inner) {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.6;
      border-radius: 8px;
      border: 2px solid #e4e7ed;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .vendor-selection {
    margin-bottom: 16px;

    .vendor-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 12px;
    }

    .vendor-cascader {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }

  .input-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}

.result-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 16px;
  }

  .error-alert {
    margin-bottom: 16px;
  }
}

.results-container {
  .result-summary {
    margin-bottom: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;

    .response-time-tag {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .result-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .result-item {
    border: 2px solid #e4e7ed;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    }

    &.result-success {
      border-color: #67c23a;
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    }

    &.result-error {
      border-color: #f56c6c;
      background: linear-gradient(135deg, #fff5f5 0%, #fee 100%);
    }

    .result-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .result-index {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: bold;
        font-size: 16px;
        color: #2c3e50;
      }
    }

    .command-block,
    .error-block {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .command-title,
      .error-title {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .command-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-all;
        margin: 0;
        color: #2c3e50;
      }

      .error-message {
        color: #e6a23c;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        padding: 12px;
        background: rgba(230, 162, 60, 0.1);
        border-radius: 6px;
        border-left: 4px solid #e6a23c;
      }
    }
  }
}

.validation-table {
  margin-top: 20px;

  :deep(.el-table__header) {
    background-color: #f5f7fa;
  }

  :deep(.el-table__row) {
    &:hover {
      background-color: #f0f9ff;
    }
  }
}

.line-number {
  font-weight: bold;
  color: #606266;
}

.command-tag {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  max-width: 100%;
  word-break: break-all;
}

.validation-status {
  display: flex;
  justify-content: center;
  align-items: center;

  .success-icon {
    color: #67c23a;
    font-size: 18px;
  }

  .error-icon {
    color: #f56c6c;
    font-size: 18px;
  }
}

.success-reason {
  color: #67c23a;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-reason {
  color: #f56c6c;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.rule-tag,
.param-tag {
  font-size: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .chat-demo-container {
    padding: 16px;
  }

  .demo-header .title {
    font-size: 24px;

    .title-icon {
      font-size: 28px;
    }
  }

  .input-section,
  .result-section {
    padding: 16px;
  }

  .input-actions {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }
}
</style>
