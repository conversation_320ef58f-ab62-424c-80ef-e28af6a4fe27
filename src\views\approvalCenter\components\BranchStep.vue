<template>
  <div>
    <div>
      <sl-base-steps
        :space="160"
        :process-status="processStatus"
        :active="active"
        align-center
        :gray-mode="grayMode"
      >
        <sl-base-branch-step
          v-for="item in data"
          :key="item.title"
          :branch-space="item.branchSpace"
          :branch="item.branch"
          :title="item.title"
          font-size="12px"
        />
      </sl-base-steps>
    </div>
    <div style="position: relative">
      <span
        :class="{ active: activeChain.length > 2 && greaterThanCost }"
        class="greater-than-cost"
      >
        高于成本
      </span>
      <span :class="{ active: activeChain.length > 2 && !greaterThanCost }" class="less-than-cost">
        低于成本
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import SlBaseSteps from '@/components/base/SlBaseSteps/steps.vue'
import SlBaseBranchStep from '@/components/base/SlBaseSteps/branch.vue'
import { configData } from '@/components/base/SlBaseSteps/help'
import { ref } from 'vue'

const props = defineProps({
  nodeTree: {
    type: Object,
    default: () => ({}),
  },
  activeId: {
    type: String,
    default: '',
  },
  processStatus: {
    type: String as () => 'wait' | 'process' | 'finish' | 'error' | 'success',
    default: 'finish',
  },
  grayMode: {
    type: Boolean,
    default: false,
  },
})
interface TreeNode {
  title: string
  id: string
  branch?: TreeNode[]
}

function convertEntityToResult(node: any, result: TreeNode[] = []): TreeNode[] {
  result.push({
    title: node.taskName,
    id: node.task,
  })
  let mainIdx = 0
  for (let index = 0; index < node.children.length; index++) {
    const element = node.children[index]
    if (index === 0) {
      mainIdx = result.length - 1
      convertEntityToResult(element, result)
    } else {
      const thatNode = result[mainIdx]
      if (thatNode) {
        thatNode.branch = convertEntityToResult(element)
      }
    }
  }

  return result
}
const greaterThanCost = ref(false)
const emit = defineEmits(['stepChange'])
const convertedResult = convertEntityToResult(props.nodeTree)
const { data, active, activeChain } = configData(convertedResult, {
  branchSpace: 10, // 上下间距
  activeId: props.activeId, //活跃的节点id
})
if (activeChain.some((item: any) => item.id === 'province_gov_admin')) {
  greaterThanCost.value = true
}

emit('stepChange', { active, activeChain })
</script>
<style scoped>
.greater-than-cost.active {
  color: var(--el-color-primary);
}
.less-than-cost.active {
  color: var(--el-color-primary);
}
.greater-than-cost {
  position: absolute;
  left: 300px;
  top: -70px;
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}
.less-than-cost {
  position: absolute;
  left: 300px;
  top: 58px;
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}
</style>
