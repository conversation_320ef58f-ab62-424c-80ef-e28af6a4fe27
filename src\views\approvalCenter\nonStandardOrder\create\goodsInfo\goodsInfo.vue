<template>
  <div class="form-container">
    <sl-form
      ref="slFormRef"
      :model-value="basicModel"
      size="small"
      show-block-title
      :options="formOptions"
    >
      <template #infoBar-slot>
        <SlBlockTitle :size="14" style="margin-bottom: 10px">
          <div style="display: flex; justify-content: space-between; align-items: center">
            <span>资源信息</span>
            <div>
              <el-dropdown size="small" type="primary" @command="addGoods">
                <el-button type="primary" :icon="Plus"> 添加资源 </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="ecs">云主机</el-dropdown-item>
                    <el-dropdown-item command="gcs">GPU云主机</el-dropdown-item>
                    <el-dropdown-item command="bms">裸金属</el-dropdown-item>
                    <el-dropdown-item command="gms">GPU裸金属</el-dropdown-item>
                    <el-dropdown-item divided command="redis">redis</el-dropdown-item>
                    <el-dropdown-item command="mysql">mysql</el-dropdown-item>
                    <el-dropdown-item command="postgreSql">postgreSql</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </SlBlockTitle>
      </template>
      <!-- 配置信息 -->
      <template #goodsInfoSlot>
        <div style="padding-top: 6px" v-if="goodsList.length">
          <template v-for="(goods, goodsIndex) in goodsList" :key="goods">
            <ecs
              v-if="goods.productType === 'ecs'"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
              @deleteGoods="deleteGoods(goodsIndex)"
            ></ecs>
            <gcs
              v-if="goods.productType === 'gcs'"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
              @deleteGoods="deleteGoods(goodsIndex)"
            ></gcs>
            <bms
              v-if="goods.productType === 'bms'"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
              @deleteGoods="deleteGoods(goodsIndex)"
            ></bms>
            <gms
              v-if="goods.productType === 'gms'"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
              @deleteGoods="deleteGoods(goodsIndex)"
            ></gms>
            <mysql
              v-if="goods.productType === 'mysql'"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
              @deleteGoods="deleteGoods(goodsIndex)"
            ></mysql>
            <redis
              v-if="goods.productType === 'redis'"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
              @deleteGoods="deleteGoods(goodsIndex)"
            ></redis>
            <postgreSql
              v-if="goods.productType === 'postgreSql'"
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
              @deleteGoods="deleteGoods(goodsIndex)"
            ></postgreSql>
          </template>
        </div>
        <div v-else>
          <el-empty description="暂无资源信息" :image-size="100"> </el-empty>
        </div>
      </template>
      <template #contractCost="{ form, item }">
        <div style="display: flex; align-items: center">
          <span style="color: red; margin-right: 5px">¥</span>
          <el-input-number
            v-model="form[item.key]"
            :step="0.01"
            style="max-width: 150px"
            :min="0"
          />
          <span style="margin-left: 5px">元</span>
        </div>
      </template>
    </sl-form>
  </div>
</template>
<script setup lang="tsx">
import { Plus } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { reactive } from 'vue'
import ecs from './ecs.vue'
import gcs from './gcs.vue'
import bms from './bms.vue'
import gms from './gms.vue'
import mysql from './mysql.vue'
import redis from './redis.vue'
import postgreSql from './postgreSql.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { type IModel, initModel } from '../model'
import SlForm from '@/components/form/SlForm.vue'
import { ref } from 'vue'

const props = defineProps<{
  goodsList: any[]
  basicModel: IModel['basicModel']
}>()

const slFormRef = ref<InstanceType<typeof SlForm>>()
function deleteGoods(goodsIndex: number) {
  const goodsList = props.goodsList
  goodsList.splice(goodsIndex, 1)
}

function addGoods(command: 'ecs' | 'gcs' | 'bms' | 'gms' | 'mysql' | 'redis' | 'postgreSql') {
  const goodsList = props.goodsList
  const openType = goodsList.length > 0 ? goodsList[0].openType : 'online'
  goodsList.push(initModel(command, openType))
}
const formOptions = reactive([
  {
    style: 'margin: 0;',
    hideBlockTitle: true,
    gutter: 20,
    groupItems: [
      {
        span: 24,
        noFormItem: true,
        style: 'position: sticky; top: 0; z-index: 99; background: #fff',
        type: 'slot',
        slotName: 'infoBar-slot',
      },
      {
        span: 24,
        noFormItem: true,
        type: 'slot',
        slotName: 'goodsInfoSlot',
      },
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin:20px 0">
              工单费用信息
            </SlBlockTitle>
          )
        },
      },
      {
        label: '合同费用',
        type: 'slot',
        key: 'contractCost',
        slotName: 'contractCost',
        required: true,
        rules: [
          {
            required: true,
            message: '请输入合同费用',
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule: any, value: any, callback: any) => {
              if (Number(value) === 0) {
                callback(new Error('请输入合同费用'))
              } else {
                callback()
              }
            },
          },
        ],
        span: 24,
      },
    ],
  },
])
defineExpose({
  validate: async () => {
    return await slFormRef.value?.validate()
  },
})
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 468px);
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}
.form-container {
  display: flex;
  flex-direction: column;
}
</style>
