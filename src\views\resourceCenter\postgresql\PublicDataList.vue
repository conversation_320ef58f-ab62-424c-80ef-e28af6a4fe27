<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getCorporateResourceList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="goodsOrderId"
  />
  <SlDialog
    v-model="subnetDialogVisible"
    title="子网信息"
    width="1000px"
    :show-cancel="false"
    @confirm="subnetDialogVisible = false"
    destroy-on-close
  >
    <SubnetList :subnet-list="currentSubnetList" :hide-network-plane="true" />
  </SlDialog>

  <!-- 用户列表弹窗 -->
  <SlDialog
    v-model="userListDialogVisible"
    title="用户列表"
    width="1200px"
    @close="userListDialogVisible = false"
    :show-confirm="false"
    cancel-text="关闭"
    destroy-on-close
  >
    <UserList v-if="currentRow" :resource-id="currentRow.id" :rds-id="currentRow.deviceId" />
  </SlDialog>

  <!-- 白名单列表弹窗 -->
  <SlDialog
    v-model="whiteListDialogVisible"
    title="白名单列表"
    width="1200px"
    @close="whiteListDialogVisible = false"
    :show-confirm="false"
    cancel-text="关闭"
    destroy-on-close
  >
    <WhiteList v-if="currentRow" :resource-id="currentRow.id" :rds-id="currentRow.deviceId" />
  </SlDialog>

  <!-- 资源变更弹窗 -->
  <ResourceChangeDialog
    v-model:visible="changeDialogVisible"
    resource-type="postgreSql"
    :selected-resources="selectedResources"
    :allowed-change-types="allowedChangeTypes"
    @confirm="handleConfirm"
    source-type="DG"
  />

  <corporatePropertyChange
    v-if="propertyChangeDrawer"
    :goods-list="goodsList"
    type="postgreSql"
    v-model="propertyChangeDrawer"
    @confirm="handlePropertyChangeConfirm"
  />
</template>

<script setup lang="tsx" name="PublicDataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getCorporateResourceList, dgRecoveryWorkOrderCreate } from '@/api/modules/resourecenter'
import SubnetList from './components/subnetList.vue'
import UserList from './components/UserList.vue'
import WhiteList from './components/WhiteList.vue'
import SlMessage from '@/components/base/SlMessage'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import { useVerifyThePromptBox } from '@/hooks/useVerifyThePromptBox'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import type { ResourceChangeType } from '../types'
import { ElMessage } from 'element-plus'
import { useResourceChange } from '../hooks/useResourceChange'
import corporatePropertyChange from '@/views/corporatePropertyChange/index.vue'
import { transformData } from '@/views/corporatePropertyChange/propertyChange'

const { queryParams } = defineProps<{
  queryParams: Record<string, any>
  hideOperations: boolean
}>()

const { validateResources } = useResourceChange()
const { validateUnsubscribe, validateChangeDg, getDgFormData } = useRecycleValidation()

interface ResourceItem {
  id: string
  deviceName: string
  deviceId: string
  billType: string
  osVersion: string
  mountOrNot: string
  spec: string
  sysDisk: string
  ip: string
  tenantName: string
  cloudPlatform: string
  resourcePoolName: string
  azName: string
  orderCode: string
  billId: string
  deviceStatus: string
  deviceStatusCn: string
  createTime: string
  applyUserName: string
}

const proTable = ref<ProTableInstance>()
const currentRow = ref<ResourceItem>()
const selectedResources = ref<ResourceItem[]>([])
const currentSubnetList = ref<any[]>([])

// 弹窗状态
const subnetDialogVisible = ref(false)
const userListDialogVisible = ref(false)
const whiteListDialogVisible = ref(false)
const changeDialogVisible = ref(false)
const propertyChangeDrawer = ref(false)
const goodsList = ref<any[]>([])

const currentChange = (val: ResourceItem) => {
  currentRow.value = val
}

const handleSelectionChange = (selection: ResourceItem[]) => {
  selectedResources.value = selection
}

const handleUserList = (row: ResourceItem) => {
  currentRow.value = row
  userListDialogVisible.value = true
}

const handleWhiteList = (row: ResourceItem) => {
  currentRow.value = row
  whiteListDialogVisible.value = true
}

const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', fixed: 'left', width: 50 },
  { type: 'index', label: '序号', width: 60, fixed: 'left' },
  {
    prop: 'deviceName',
    label: '云数据库名称',
    width: 150,
    fixed: 'left',
    render: ({ row }: any): VNode => {
      return (
        <router-link
          to={{
            path: '/postgreSqlDetail',
            query: {
              id: row.id,
              deviceId: row.deviceId,
              sourceType: 'DG',
            },
          }}
          style="color: #0052D9; text-decoration: none;"
        >
          {row.deviceName}
        </router-link>
      )
    },
  },
  { prop: 'deviceId', label: '资源ID', width: 180 },
  {
    prop: 'billType',
    label: '计费类型',
    width: 100,
    render: ({ row }) => formatBillType(row.billType),
  },
  { prop: 'osVersion', label: '数据库版本', width: 120 },
  {
    prop: 'mountOrNot',
    label: '系列',
    width: 100,
    render: ({ row }) => {
      const seriesMap: Record<string, string> = {
        ALONE: '单机版',
        COLONY: '主备版本',
      }
      return seriesMap[row.mountOrNot] || row.mountOrNot
    },
  },
  { prop: 'spec', label: '实例规格', width: 120 },
  { prop: 'sysDisk', label: '存储大小', width: 100 },
  { prop: 'ip', label: 'IP', width: 120 },
  { prop: 'tenantName', label: '租户', width: 120 },
  { prop: 'cloudPlatform', label: '所属云', width: 100 },
  { prop: 'resourcePoolName', label: '所属资源池', width: 120 },
  { prop: 'azName', label: '可用区', width: 100 },
  { prop: 'orderCode', label: '订单编号', width: 150 },
  { prop: 'billId', label: '计费号', width: 120 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 },
  { prop: 'createTime', label: '订购时间', width: 160 },
  { prop: 'applyUserName', label: '订购人', width: 100 },
  {
    prop: 'operation',
    label: '操作',
    width: 200,
    fixed: 'right',
    render: ({ row }) => (
      <div>
        <el-button type="primary" link onClick={() => handleUserList(row)}>
          用户列表
        </el-button>
        <el-button type="primary" link onClick={() => handleWhiteList(row)}>
          白名单列表
        </el-button>
      </div>
    ),
  },
])
// 格式化计费类型
const formatBillType = (type: string): string => {
  const billTypeMap: Record<string, string> = {
    day: '按天计费',
    month: '按月计费',
    year: '按年计费',
  }
  return billTypeMap[type] || type || '--'
}

// PostgreSQL允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = ['instance_spec_change', 'storage_expand']

// 处理资源变更
const handleResourceChange = async (dg = false) => {
  if (selectedResources.value.length === 0) {
    ElMessage.warning('请选择要变更的资源')
    return
  }

  // 使用新的校验逻辑
  if (validateChangeDg(selectedResources.value, 'postgreSql')) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value, dg)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

function handleOpenPropertyChangeDrawer(list: any) {
  console.log(list)
  if (!list) return ElMessage.warning('请选择要变更的资源')
  propertyChangeDrawer.value = true
  goodsList.value = transformData(list)
}

// 处理企业属性变更
const handlePropertyChangeConfirm = () => {
  propertyChangeDrawer.value = false
  proTable.value?.getTableList()
}

// 处理确认
const handleConfirm = (data: any) => {
  changeDialogVisible.value = false
  handleOpenPropertyChangeDrawer(data)
}

const handleCreateUnsubscribe = async (goodsItems: any[]) => {
  const res = await dgRecoveryWorkOrderCreate(getDgFormData('postgreSql', goodsItems))
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  SlMessage.success('已提交退订')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}

// 批量回收功能
const handleBatchUnsubscribe = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateUnsubscribe(selectedList, 'postgreSql')) {
    const arr = await useVerifyThePromptBox(selectedList, [], true)
    if (!arr || !arr.length) return
    handleCreateUnsubscribe(arr)
  }
}

defineExpose({
  handleBatchUnsubscribe,
  handleResourceChange,
})
</script>
