<template>
  <div style="margin-top: 10px">
    <!-- 分页组件 -->
    <el-pagination
      class="comPowerDialogPage"
      :background="true"
      :current-page="pageable.pageNum"
      :page-size="pageable.pageSize"
      :page-sizes="[10, 20, 30, 40, 50, 100]"
      :total="pageable.total"
      layout="->,total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script setup lang="ts">
/**
 * @description 每页条数改变
 * @param {Number} val 当前条数
 * @return void
 * */
interface Pageable {
  pageNum: number
  pageSize: number
  total: number
}

interface PaginationProps {
  pageable: Pageable
  handleSizeChange: (size: number) => void
  handleCurrentChange: (currentPage: number) => void
}
defineProps<PaginationProps>()
</script>
<style scoped lang="scss"></style>
<style lang="scss">
.comPowerDialogPage.el-pagination {
  .el-select__wrapper.el-tooltip__trigger {
    background: #3f6eb8;
    color: #fff;
    box-shadow: none;
    border-radius: 1px;
    border: 1px solid #42659e;
    .el-select__input.is-default,
    .el-select__selected-item.el-select__placeholder {
      color: #fff;
    }
  }
  .el-pagination__total.is-first {
    color: #ffffff;
    font-size: 15px;
  }
  .el-pagination__goto {
    color: #ffffff;
  }
  .el-input__wrapper {
    background: #3f6eb8;
    border: none;
    box-shadow: none;
    .el-input__inner {
      color: #ffffff;
    }
  }
  .el-pagination__classifier {
    color: #ffffff;
  }
}
.comPowerDialogPage.el-pagination.is-background .btn-next:disabled,
.comPowerDialogPage.el-pagination.is-background .btn-prev:disabled {
  background: transparent;
  color: #3f5d78;
}
.comPowerDialogPage.el-pagination.is-background .btn-next,
.comPowerDialogPage.el-pagination.is-background .btn-prev {
  background: transparent;
  color: #0787de;
}
.comPowerDialogPage.el-pagination.is-background .el-pager li {
  background: #3f6eb8;
  padding: 2px 8px;
  color: #ffffff;
}
.comPowerDialogPage.el-pagination.is-background .el-pager li.is-active {
  background: #317ced;
}
.comPowerDialogPage.el-pagination .btn-next .el-icon,
.comPowerDialogPage.el-pagination .btn-prev .el-icon {
  font-size: 24px;
}
</style>
