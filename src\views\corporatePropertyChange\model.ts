import type { Ref } from 'vue'
export type IGoodsType = 'ecs' | 'gcs' | 'slb' | 'evs' | 'eip' | 'rdsMysql' | 'postgreSql'

interface IChangeReqModel {
  id: number
  volumeSize: number
}
export interface IEcsModel {
  resourceDetailId: number
  resourceType: 'ecs'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IRedisModel {
  resourceDetailId: number
  resourceType: 'redis'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IMySqlModel {
  resourceDetailId: number
  resourceType: 'rdsMysql'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IPostgresqlModel {
  resourceDetailId: number
  resourceType: 'postgreSql'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
}
export interface IGcsModel {
  resourceDetailId: number
  resourceType: 'gcs'
  changeType: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand' | 'delay'
  flavorType: string
  flavorName: string
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  eipId: number
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IEvsModel {
  resourceDetailId: number
  resourceType: 'evs'
  changeType: 'storage_expand' | 'delay'
  volumeChangeReqModels: IChangeReqModel[]
  changeTime: string
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IEipModel {
  resourceDetailId: number
  resourceType: 'eip'
  changeType: 'bandwidth_expand' | 'delay'
  changeTime: string
  eipBandwidth: number
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface IObsModel {
  resourceDetailId: number
  resourceType: 'obs'
  changeType: 'delay'
  changeTime: string
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface ISlbModel {
  resourceDetailId: number
  resourceType: 'slb'
  changeType: 'instance_spec_change' | 'delay'
  flavorType: string
  flavorName: string
  changeTime: string
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}
export interface INatModel {
  resourceDetailId: number
  resourceType: 'nat'
  changeType: 'delay'
  changeTime: string
  // 表单实例 提交需删
  ref?: {
    clearValidate: (props?: string | string[]) => void
  } | null
}

type IChangeTypeMap = {
  ecs: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand'
  redis: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand'
  rdsMysql: 'instance_spec_change' | 'storage_expand'
  postgreSql: 'instance_spec_change' | 'storage_expand'
  gcs: 'instance_spec_change' | 'storage_expand' | 'bandwidth_expand'
  evs: 'storage_expand'
  eip: 'bandwidth_expand'
  slb: 'instance_spec_change' | 'bandwidth_expand'
}

// 重新定义 ChangeTypeToBefore 使其成为判别联合类型
export type ChangeTypeToBefore<C> = C extends 'instance_spec_change'
  ? string
  : C extends 'storage_expand'
    ? Array<[string, number, string?]>
    : C extends 'bandwidth_expand'
      ? string
      : C extends 'delay'
        ? string
        : string
export type ChangeTypeToAfter<C> = C extends 'instance_spec_change'
  ? [string, string] | string
  : C extends 'storage_expand'
    ? Array<[string, number, string?]>
    : C extends 'bandwidth_expand'
      ? string
      : C extends 'delay'
        ? string
        : string

type IBasePropsItem<T extends keyof IChangeTypeMap> = {
  [Resource in keyof IChangeTypeMap]: {
    [Change in IChangeTypeMap[Resource]]: {
      resourceType: Resource
      changeType: Change
      resourceDetailId: number
      changeTypeCn: string
      before: ChangeTypeToBefore<Change>
      after: ChangeTypeToAfter<Change>
      templateCode?: string
      ref?: Ref<any, any>
    }
  }[IChangeTypeMap[Resource]]
}[T]

export type IPropsItem<T extends keyof IChangeTypeMap> = IBasePropsItem<T> &
  (T extends 'ecs' | 'gcs' | 'rdsMysql' | 'redis' | 'postgreSql'
    ? { eipId: number }
    : { eipId?: never })

export interface IPropsListItem<T extends keyof IChangeTypeMap> {
  resourceDetailId: number
  deviceName: string
  resourceType: T
  resourceTypeCn: string
  domainCode?: string
  cloudPlatform?: string
  azId?: string
  regionId?: string
  resourcePoolName?: string
  props: IPropsItem<T>[]
  deviceId?: string
  uuid: string
  description?: string
}
