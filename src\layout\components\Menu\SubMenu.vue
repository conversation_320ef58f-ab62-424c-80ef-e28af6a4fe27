<template>
  <template v-for="subItem in menuList" :key="subItem.name">
    <el-sub-menu
      v-if="subItem.children?.length"
      :class="{ isCollapse: !isCollapse }"
      :index="subItem.name"
    >
      <template #title>
        <i v-if="subItem.meta.icon" :class="subItem.meta.icon" class="iconfont sub-icon"></i>
        <span class="sle">{{ subItem.meta.title }}</span>
      </template>
      <SubMenu :menu-list="subItem.children" :is-child="true" />
    </el-sub-menu>
    <el-menu-item
      v-else
      :index="subItem.name"
      :class="{ 'external-link-item': subItem.meta.isLink }"
      :disabled="getDisabled(subItem)"
      :style="isChild ? {} : { marginLeft: '0px' }"
      @click="handleClickMenu(subItem)"
    >
      <i v-if="subItem.meta.icon" :class="subItem.meta.icon" class="iconfont sub-icon"></i>
      <template #title>
        <span class="sle">{{ subItem.meta.title }}</span>
        <span
          class="order-count"
          v-if="
            ['workOrder', 'recycleWorkOrder', 'changeWorkOrder', 'nonStandardOrder'].includes(
              subItem.name,
            )
          "
        >
          {{
            orderData[getOrderKeys(subItem.name)]?.pendingCount > 99
              ? '99+'
              : orderData[getOrderKeys(subItem.name)]?.pendingCount
          }}
        </span>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import { useGlobalStore } from '@/stores/modules/global'
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { useWorkOrderStore } from '@/stores/modules/workOrder'

const globalStore = useGlobalStore()
const isCollapse = computed(() => globalStore.isCollapse)

defineProps<{ menuList: Menu.MenuOptions[]; isChild?: boolean }>()
const router = useRouter()
const handleClickMenu = (subItem: Menu.MenuOptions) => {
  if (subItem.meta.isLink) {
    const resolved = router.resolve(subItem.meta.isLink)
    window.open(resolved.href, '_blank')
    return
  }
  router.push(subItem.path)
}

const authStore = useAuthStore()

const getDisabled = (subItem: Menu.MenuOptions) => {
  return subItem.meta.isDisabled || !Boolean(authStore.authButtonListGet[subItem.name])
}

const workOrderStore = useWorkOrderStore()
// 使用计算属性获取工单数据
const orderData = computed<any>(() => workOrderStore.orderData)
const getOrderKeys = (name: string) => {
  return (
    {
      workOrder: 'workOrder',
      recycleWorkOrder: 'recycleOrder',
      changeWorkOrder: 'changeOrder',
      nonStandardOrder: 'nonStanderOrder',
    }[name] || ''
  )
}
</script>

<style lang="scss">
.el-menu {
  overflow-y: hidden;
  --el-menu-active-color: var(--el-color-primary);
  --el-menu-active-bg-color: #e0eefb; // 可选：覆盖悬停背景色
  --el-menu-hover-bg-color: #e0eefb; // 可选：覆盖悬停背景色
}
.el-sub-menu .el-menu-item {
  .sle {
    font-weight: 400;
  }
}
.el-sub-menu .el-sub-menu__title:hover {
  color: var(--el-menu-hover-text-color) !important;
  background-color: transparent !important;
}
.el-menu--collapse {
  .is-active {
    .el-sub-menu__title {
      color: #ffffff !important;
      background-color: var(--el-color-primary) !important;
    }
  }
  .el-sub-menu__title {
    display: flex;
    justify-content: center;
    border-radius: 0 5px 5px 0;
  }
}
.el-menu-item {
  height: 30px !important;
  border-radius: 20px;
  margin-right: 20px;
  margin-left: 20px;
  padding-left: 20px !important;
  padding-right: 0;
  margin-bottom: 5px;
  &:hover {
    color: var(--el-menu-hover-text-color);
  }
  &.is-active {
    color: var(--el-menu-active-color) !important;
    background-color: var(--el-menu-active-bg-color) !important;
  }
}
.el-menu--collapse {
  .el-menu-item {
    margin-left: 0;
    &.is-active,
    &:hover {
      color: #ffffff !important;
      background-color: var(--el-color-primary) !important;
      display: flex;
      justify-content: center;
      border-radius: 0 5px 5px 0;
    }
  }
  .el-menu-item .el-menu-tooltip__trigger {
    padding: 0 15px;
  }
}
.el-sub-menu__title {
  height: 40px !important;
}
.vertical,
.classic,
.transverse {
  .el-menu-item {
    &.is-active {
      &::before {
        left: 0;
      }
    }
  }
}
.columns {
  .el-menu-item {
    &.is-active {
      &::before {
        right: 0;
      }
    }
  }
}
.sub-icon {
  font-size: 18px;
  text-align: center;
  vertical-align: middle;
}
// 下面是根据产品图修改的样式
.sle {
  margin-left: 5px;
}

.order-count {
  margin-left: 5px;
  height: 16px;
  background-color: #f56c6c;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  padding: 0 5px;
  font-size: 10px;
  color: #fff;
}

// 外链菜单项样式 - 防止显示激活状态
.external-link-item.is-active {
  color: inherit !important;
  background-color: transparent !important;
}
.el-menu--collapse .external-link-item.is-active {
  color: inherit !important;
  background-color: transparent !important;
}
</style>
