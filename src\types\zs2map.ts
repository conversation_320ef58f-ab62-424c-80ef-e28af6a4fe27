// Zs2Map相关的类型定义

// 卡类型利用率数据
export interface CardUtilizationItem {
  timePoint: string
  cardType: string
  memoryUtilization: number
  computeUtilization: number
  aggregationType: string
}

// 业务系统排行项
export interface BusinessSystemRankingItem {
  businessSystemName: string
  memoryUtilization: number
  computeUtilization: number
  ranking: number
  taskNum: number
  deptName: string
  deviceCount: number
}

// 业务系统排行响应
export interface BusinessSystemRankingResponse {
  memoryUtilizationRanking: BusinessSystemRankingItem[]
  computeUtilizationRanking: BusinessSystemRankingItem[]
}

// 卡类型利用率请求参数
export interface CardUtilizationRequest {
  startTime: string
  endTime: string
  aggregationType: 'day' | 'month'
  modelName: string
  areaCode?: string
}

// 业务系统排行请求参数
export interface BusinessSystemRankingRequest {
  areaCode?: string
  modelName?: string
}
