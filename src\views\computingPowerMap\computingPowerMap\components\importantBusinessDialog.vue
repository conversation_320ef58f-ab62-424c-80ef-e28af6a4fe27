<template>
  <div class="bottom-dialog-container">
    <!-- <img @click="closeBottomDialog" src="/images/computingPower/comPowerIconClose.png" alt="" /> -->
    <div class="bottom-dialog-content">
      <div class="bottom-dialog-main">
        <div class="bottom-dialog-title">
          <div>重要业务TOP3</div>
          <div>
            <img src="/images/computingPower/comPowerDialogTitleBigBg.png" alt="" />
          </div>
        </div>
        <div class="bottom-dialog-tooltip">
          <div class="bottom-dialog-alarm">
            <div class="bottom-dialog-alarm-item">
              <img src="/images/computingPower/comPowerIconAlarm1.png" alt="" />
              <span>严重 {{ yanzhongAlarmNum }}</span>
            </div>
            <div class="bottom-dialog-alarm-item">
              <img src="/images/computingPower/comPowerIconAlarm2.png" alt="" />
              <span>主要 {{ zhuyaoAlarmNum }}</span>
            </div>
            <div class="bottom-dialog-alarm-item">
              <img src="/images/computingPower/comPowerIconAlarm2.png" alt="" />
              <span>次要 {{ ciyaoAlarmNum }}</span>
            </div>
            <div class="bottom-dialog-alarm-item">
              <img src="/images/computingPower/comPowerIconAlarm2.png" alt="" />
              <span>警告 {{ jinggaoAlarmNum }}</span>
            </div>
            <div class="bottom-dialog-alarm-item">
              <img src="/images/computingPower/comPowerIconAlarm2.png" alt="" />
              <span>提示 {{ tishiAlarmNum }}</span>
            </div>
            <div class="bottom-dialog-alarm-item">
              <img src="/images/computingPower/comPowerIconAlarm2.png" alt="" />
              <span>未知 {{ weizhiAlarmNum }}</span>
            </div>
          </div>
          <div class="bottom-dialog-search">
            <el-select
              v-model="cloudValue"
              @change="changeCloudTypeFunc"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 100px"
            >
              <el-option
                v-for="item in cloudOption"
                :key="item.cloudName"
                :label="item.cloudName"
                :value="item.cloudName"
              />
            </el-select>
            <el-select
              v-model="platformTypeName"
              @change="changeCloudPlatformTypeNameFunc"
              placeholder="请选择云平台"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 140px"
            >
              <el-option
                v-for="item in platformTypeNamePoolList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
            <el-select
              v-model="resourcePoolValue"
              @change="changeResourcePooolFunc"
              placeholder="请选择资源池"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 160px"
            >
              <el-option
                v-for="item in resourcePoolList"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>

            <el-input
              v-model="searchForm.name"
              placeholder="请输入虚拟机名称"
              clearable
              style="width: 160px"
            ></el-input>
            <el-input
              v-model="searchForm.osVersion"
              placeholder="请输入操作系统"
              clearable
              style="width: 160px"
            ></el-input>
            <el-input
              v-model="searchForm.ip"
              placeholder="请输入IPV4或IPV6"
              clearable
              style="width: 160px"
            ></el-input>
            <el-select
              v-model="searchForm.bsName"
              clearable
              placeholder="请选择业务系统"
              :suffix-icon="CaretBottom"
              style="width: 160px"
            >
              <el-option v-for="item in bsNameList" :key="item" :label="item" :value="item" />
            </el-select>
            <el-button class="searchBtn" :icon="Search" @click="searchFunc"> 搜索 </el-button>
            <el-button class="exportBtn" @click="exportFile">
              <img src="/images/computingPower/comPowerIconExport.png" alt="" />
              导出
            </el-button>
          </div>
        </div>
        <div class="bottom-dialog-table">
          <el-auto-resizer>
            <template #default="{ height }">
              <el-table
                ref="tableRef"
                class="comPowerTable"
                :data="tableData"
                :height="height"
                style="width: 100%"
              >
                <el-table-column type="index" label="序号" width="60"></el-table-column>
                <el-table-column
                  :key="item.prop"
                  v-for="item in columns"
                  show-overflow-tooltip
                  :prop="item.prop"
                  :label="item.label"
                >
                  <template #default="scope">
                    <span
                      v-if="item.prop == 'relatedPool' || item.prop == 'bsName'"
                      class="c-comPower-table-cell-blue-theme"
                      >{{ scope.row[item.prop] || '--' }}</span>
                    <span v-else>{{ scope.row[item.prop] || '--' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-auto-resizer>
        </div>
        <div style="margin-top: 10px">
          <Pagination
            :pageable="pageable"
            :handle-size-change="handleSizeChange"
            :handle-current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CaretBottom, Search } from '@element-plus/icons-vue'
import { onMounted, reactive, type Ref, ref, watch } from 'vue'
import { ElTable } from 'element-plus'
import { useDownload } from '@/hooks/useDownload' // 定义需要发出的事件类型
import Pagination from '@/views/computingPowerMap/computingPowerMap/components/pagination.vue'
import {
  getComPowerMapPageCriticalBusiness,
  getComPowerMapAlarmStatsCriticalBusiness,
  getComPowerMapPageBaseDeviceHandResourcePool,
  downloadComPowerMapAlarmStatsCriticalBusiness,
} from '@/api/modules/comPowerCenter'
import { getComputerPowerMapListBusinessApi } from '@/api/modules/computingPowerMap'
// const emit = defineEmits(['close'])
// 定义类型/接口
interface OptionItem {
  name: string
  instanceId: string
}
// 定义类型/接口
interface columnsItem {
  prop: string
  label: string
}
const columns: Ref<columnsItem[]> = ref([])
// 定义props接收父组件传递的参数
const props = defineProps({
  cloudListArr: {
    type: Array,
    required: false,
    default: () => [],
  },
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  bsNameType: {
    type: String,
    required: true,
    default: '',
  },
})

const searchForm = reactive({
  name: '',
  ip: '',
  osVersion: '',
  bsName: '',
})
const cloudValue = ref('')
const cloudOption: any = ref([])
//业务系统列表
const bsNameList: any = ref([])

//云平台
const platformTypeName = ref('')
const platformTypeNamePoolList = ref([])

const resourcePoolValue = ref('')
const resourcePoolList: Ref<OptionItem[]> = ref([])

const yanzhongAlarmNum = ref(0)
const zhuyaoAlarmNum = ref(0)
const ciyaoAlarmNum = ref(0)
const jinggaoAlarmNum = ref(0)
const tishiAlarmNum = ref(0)
const weizhiAlarmNum = ref(0)
const tableData = ref([])
const pageable = reactive({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: 20,
  // 总条数
  total: 0,
})

const changeCloudTypeFunc = () => {
  pageable.pageNum = 1
  platformTypeName.value = ''
  cloudOption.value.forEach((item: any) => {
    if (item.cloudName == cloudValue.value) {
      platformTypeNamePoolList.value =
        (item.platformTypes && item.platformTypes.map((item: any) => item.platformTypeName)) || []
    }
  })
  //清空掉资源池信息
  resourcePoolValue.value = ''
  resourcePoolList.value = []
  getBaseDeviceHandResourcePool()
  getDataInfo()
}
//切换云平台
const changeCloudPlatformTypeNameFunc = () => {
  pageable.pageNum = 1
  resourcePoolValue.value = ''
  resourcePoolList.value = []
  getBaseDeviceHandResourcePool()
  getDataInfo()
}
//查询物理资源池
const getBaseDeviceHandResourcePool = () => {
  let params = {
    cloudName: cloudValue.value,
    platformTypeName: platformTypeName.value,
    cityCode: props.requestParams.cityCode,
    pageNum: 1,
    pageSize: 9999,
  }
  getComPowerMapPageBaseDeviceHandResourcePool(params).then((res: any) => {
    if (res.code == 200) {
      resourcePoolList.value = res.entity.records || []
    }
  })
}
//切换资源池
const changeResourcePooolFunc = () => {
  pageable.pageNum = 1
  getDataInfo()
}
const handleSizeChange = (val: number) => {
  pageable.pageNum = 1
  pageable.pageSize = val
  getDataInfo()
}
/**
 * @description 当前页改变
 * @param {Number} val 当前页
 * @return void
 * */
const handleCurrentChange = (val: number) => {
  pageable.pageNum = val
  getDataInfo()
}
// 导出文件
const exportFile = () => {
  let params: any = {
    cloudName: cloudValue.value,
    platformTypeName: platformTypeName.value,
    relatedPool: resourcePoolValue.value,
    cityCode: props.requestParams.cityCode,
    areaCode: props.requestParams.areaCode,
    bsNameType: props.bsNameType,
    name: queryParams.name,
    osVersion: queryParams.osVersion,
    ip: queryParams.ip,
    bsNamebsName: queryParams.bsName,
  }
  let temName = props.bsNameType + '.xlsx'
  useDownload(downloadComPowerMapAlarmStatsCriticalBusiness, temName, params)
}

const queryParams = reactive({
  name: '',
  osVersion: '',
  ip: '',
  bsName: '',
})
const searchFunc = () => {
  queryParams.name = searchForm.name
  queryParams.osVersion = searchForm.osVersion
  queryParams.ip = searchForm.ip
  queryParams.bsName = searchForm.bsName
  getDataInfo()
}
// 获取数据信息
const getDataInfo = () => {
  let parms: any = {
    cloudName: cloudValue.value,
    platformTypeName: platformTypeName.value,
    relatedPool: resourcePoolValue.value,
    cityCode: props.requestParams.cityCode,
    areaCode: props.requestParams.areaCode,
    name: queryParams.name,
    osVersion: queryParams.osVersion,
    ip: queryParams.ip,
    bsName: queryParams.bsName,
    pageNum: pageable.pageNum,
    pageSize: pageable.pageSize,
    bsNameType: props.bsNameType,
  }
  getComPowerMapPageCriticalBusiness(parms).then((res: any) => {
    if (res.code == 200) {
      tableData.value = res.entity.records || []
      pageable.total = res.entity.total || 0
    }
  })
  getComPowerMapAlarmStatsCriticalBusiness(parms).then((res: any) => {
    if (res.code == 200) {
      yanzhongAlarmNum.value = res.entity.criticalCount || 0
      zhuyaoAlarmNum.value = res.entity.majorCount || 0
      ciyaoAlarmNum.value = res.entity.minorCount || 0
      jinggaoAlarmNum.value = res.entity.warningCount || 0
      tishiAlarmNum.value = res.entity.infoCount || 0
      weizhiAlarmNum.value = res.entity.indeterminateCount || 0
    }
  })
}

// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)
// 监听参数变化自动重新请求（可选）
watch(
  () => props.bsNameType,
  () => {
    getDataInfo()
  },
  { deep: true },
)
// 关闭当前弹窗
// const closeBottomDialog = () => {
//   emit('close')
// }

const xuniColumns = ref([
  {
    prop: 'name',
    label: '虚拟机名称',
  },
  {
    prop: 'osVersion',
    label: '操作系统版本',
  },
  {
    prop: 'platformTypeName',
    label: '所属云平台',
  },
  {
    prop: 'relatedPool',
    label: '所属物理资源池',
  },
  {
    prop: 'mgmtIpv4',
    label: '管理网IPv4地址',
  },
  {
    prop: 'mgmtIpv6',
    label: '管理网IPv6地址',
  },
  {
    prop: 'memTotalCapacity',
    label: '内存大小(GB)',
  },
  {
    prop: 'vdisksInfo',
    label: '存储大小(GB)',
  },
  {
    prop: 'vcpuInfo',
    label: 'vCPU核数',
  },
  {
    prop: 'bsName',
    label: '关联业务系统',
  },
])

const wuliColumns = ref([
  {
    prop: 'name',
    label: '物理机名称',
  },
  {
    prop: 'osVersion',
    label: '操作系统版本',
  },
  {
    prop: 'platformTypeName',
    label: '所属云平台',
  },
  {
    prop: 'model',
    label: '设备型号',
  },
  {
    prop: 'idcRoom',
    label: '所属机房',
  },
  {
    prop: 'idcRack',
    label: '所属机架',
  },
  {
    prop: 'relatedPool',
    label: '所属物理资源池',
  },
  {
    prop: 'mgmtIpv4',
    label: '管理网IPv4地址',
  },
  {
    prop: 'mgmtIpv6',
    label: '管理网IPv6地址',
  },
  {
    prop: 'memTotalCapacity',
    label: '内存大小(GB)',
  },
  {
    prop: 'localStorageCapacity',
    label: '存储大小(GB)',
  },
  {
    prop: 'cpuCoresCount',
    label: 'vCPU核数',
  },
])

onMounted(() => {
  cloudValue.value = props.requestParams.cloudName
  platformTypeName.value = props.requestParams.platformTypeName
  cloudOption.value = props.cloudListArr
  cloudOption.value.forEach((item: any) => {
    if (item.cloudName == cloudValue.value) {
      platformTypeNamePoolList.value =
        (item.platformTypes && item.platformTypes.map((item: any) => item.platformTypeName)) || []
    }
  })
  getBaseDeviceHandResourcePool()
  if (props.bsNameType == '智家云电脑') {
    columns.value = wuliColumns.value
  } else {
    columns.value = xuniColumns.value
  }
  // 获取业务系统
  getComputerPowerMapListBusinessApi({
    type: 'critical_business',
    bsNameType: props.bsNameType,
  }).then((res: any) => {
    bsNameList.value = res.entity
    console.log(res)
  })
  getDataInfo()
})
</script>
<style scoped lang="scss">
.bottom-dialog-container {
  height: 100vh;
  // position: fixed;
  // left: 0;
  // bottom: 0;
  // width: 100%;
  // height: 440px;
  // z-index: 20;
  //height: 367px;
  & > img {
    position: absolute;
    top: 0;
    right: 10px;
    cursor: pointer;
  }
  .bottom-dialog-content {
    // position: absolute;
    // left: 0;
    // bottom: 0;
    // width: 100%;
    // height: 400px;
    height: 100%;
    padding: 12px 9px 11px 10px;
    box-shadow: 0px 0px 5px 0px #09155c;
    border-radius: 1px;
    box-sizing: border-box;
    background-color: rgba(170, 198, 245, 0.3);
    //background: url('/images/computingPower/comPowerBottomDialogBg.png') no-repeat 0 0;
    //background-size: 100% 100%;

    .bottom-dialog-main {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      padding: 8px 15px;
      border-radius: 2px;
      box-sizing: border-box;
      background: linear-gradient(0deg, rgba(124, 166, 238, 0.92), rgba(10, 52, 103, 0.92));
    }
    .bottom-dialog-title {
      display: flex;
      div {
        width: calc(100% - 170px);
        font-size: 18px;
        color: #ffffff;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei';
        img {
          width: 100%;
        }
      }
      div:nth-child(1) {
        width: 150px;
        margin-right: 15px;
      }
    }
    .bottom-dialog-tooltip {
      margin-bottom: 10px;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: space-between;
      align-content: stretch;
      flex-direction: column;
      flex-wrap: nowrap;
      .bottom-dialog-alarm {
        //display: inline-block;
        //margin-right: 139px;
        margin-bottom: 10px;
        .bottom-dialog-alarm-item {
          display: inline-block;
          margin-right: 50px;
          img {
            margin-right: 8px;
          }
          span {
            font-size: 16px;
          }
        }
        .bottom-dialog-alarm-item:nth-child(1) {
          color: #ff0000;
        }
        .bottom-dialog-alarm-item:nth-child(2) {
          color: #ffa800;
        }
        .bottom-dialog-alarm-item:nth-child(3) {
          color: #ffea00;
        }
        .bottom-dialog-alarm-item:nth-child(4) {
          color: #fcff00;
        }
        .bottom-dialog-alarm-item:nth-child(5) {
          color: #27d9ff;
        }
        .bottom-dialog-alarm-item:nth-child(6) {
          color: #2093ff;
        }
      }
      .bottom-dialog-search {
        width: 100%;
        text-align: right;
        vertical-align: top;
        margin: 20px 40px 20px 0;
        .el-select {
          background: transparent;
          font-size: 15px;
          color: #ffffff;
          margin-right: 13px;
        }
        .el-input {
          background: transparent;
          font-size: 14px;
          color: #ffffff;
          margin-right: 13px;
          height: 32px;
        }
        .searchBtn {
          min-width: 80px;
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          img {
            margin-right: 8px;
          }
        }
        .exportBtn {
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          img {
            margin-right: 8px;
          }
        }
      }
    }
    .bottom-dialog-table {
      flex: 1;
      overflow: hidden;
      .el-table {
        background: transparent;
        th.el-table__cell {
          background: transparent;
        }
      }
    }
  }
}
</style>
