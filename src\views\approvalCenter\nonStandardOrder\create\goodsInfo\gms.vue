<template>
  <div :style="props.style">
    <sl-form
      class="goods-info-form"
      size="small"
      ref="slFormRef"
      :options="goodsInfoOptions"
      :model-value="goods"
    >
      <!-- 操作系统插槽 -->
      <template #imageSlot="{ form, item }">
        <el-input v-model="form[item.key][0]" />
        <span style="margin: 0 6px; max-width: 2px">/</span>
        <el-input v-model="form[item.key][1]" />
      </template>
      <!-- 实例规格插槽 -->
      <template #flavorSlot="{ form, item }">
        <el-input-number :controls="false" v-model="form[item.key][0]" style="margin-right: 4px" />C
        <el-input-number :controls="false" v-model="form[item.key][1]" style="margin: 0 4px" />GB
        <span style="margin: 0 6px; max-width: 2px">/</span>
        <el-input v-model="form[item.key][2]" style="margin-right: 4px" />
      </template>
      <!-- 可用区插槽 -->
      <template #azSlot="{ form, item }">
        <el-select v-model="form[item.key]" filterable clearable>
          <el-option
            v-for="option in item.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
      <!-- 删除按钮 -->
      <template #globalFormSlot>
        <div @click="handleGoodsDelete" class="goods-del-btn">
          <el-icon><CircleCloseFilled /></el-icon>
        </div>
      </template>
      <!-- 系统盘插槽 -->
      <template #sysDiskSlot="{ form, item }">
        <div style="display: flex; flex-grow: 1">
          <el-input v-model="form[item.key][0]" />
          <el-input-number
            v-model="form[item.key][1]"
            v-bind="item.props"
            style="margin: 0 4px; min-width: 90px"
          >
          </el-input-number>
          GB
        </div>
      </template>
      <!-- 数据盘插槽 -->
      <template #evsSlot="{ form, item }">
        <div
          style="
            flex-grow: 0.12;
            display: flex;
            align-items: flex-start;
            height: 100%;
            margin-right: 6px;
          "
        >
          <el-switch v-model="form[item.swithKey]" active-value="1" inactive-value="0" />
        </div>
        <div>
          <el-form-item
            class="evs-item"
            v-for="(evs, evsIndex) in form[item.key]"
            :key="evsIndex"
            :prop="item.key + '.' + evsIndex"
            :rules="evsRules"
          >
            <div class="evs-item-content">
              <el-input :disabled="item.disabled" v-model="form[item.key][evsIndex][0]" />
              <el-input-number
                :disabled="item.disabled"
                v-bind="item.props"
                v-model="form[item.key][evsIndex][1]"
                style="margin: 0 4px; min-width: 90px"
              />
              <span>GB</span>
              <div class="evs-icons" v-if="form.isMountEvs == 1">
                <el-icon
                  @click="handleEvsRemove(form[item.key], evsIndex)"
                  v-if="form[item.key].length > 1"
                >
                  <RemoveFilled />
                </el-icon>
                <el-icon
                  @click="handleEvsAdd(form[item.key])"
                  v-if="form[item.key].length == evsIndex + 1"
                >
                  <CirclePlusFilled />
                </el-icon>
              </div>
            </div>
          </el-form-item>
        </div>
      </template>
      <!-- 公网IP插槽 -->
      <template #networkIpSlot="{ form, item }">
        <el-switch
          style="flex-grow: 0.2"
          v-model="form[item.swithKey]"
          active-value="1"
          inactive-value="0"
        />
        <el-input-number v-bind="item.props || {}" v-model="form[item.key]" clearable />
      </template>
    </sl-form>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watchEffect } from 'vue'
import type { IGmsModel } from '../model'
import { CirclePlusFilled, RemoveFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import slForm from '@/components/form/SlForm.vue'

const props = defineProps<{
  goods: IGmsModel
  style?: string | Record<string, string | number>
}>()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const emit = defineEmits(['deleteGoods'])
function handleGoodsDelete() {
  emit('deleteGoods')
}
const formModel = props.goods
// 数据盘配置
const handleEvsRemove = (item: any, index: number) => {
  item.splice(index, 1)
}

const handleEvsAdd = (item: Array<any>) => {
  item.push(['', 0])
}

const evsDisabled = ref(false)
const netSizeDisabled = ref(false)
const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGmsModel) {
  goods.ref = slFormRef
}
watchEffect(() => {
  if (formModel.isMountEvs === '0') {
    evsDisabled.value = true
    if (slFormRef.value) {
      formModel.evs = [['', 0]]
      slFormRef.value.clearValidate(['evs'])
    }
  } else {
    evsDisabled.value = false
  }
})
watchEffect(() => {
  if (formModel.isBindPublicNetworkIp === '0') {
    netSizeDisabled.value = true
    if (slFormRef.value) {
      formModel.eipValue = 0
      slFormRef.value.clearValidate(['eipValue'])
    }
  } else {
    netSizeDisabled.value = false
    formModel.eipValue = formModel.eipValue || 5
  }
})

const validateevsTypeevsSize = (rule: any, value: any, callback: any) => {
  if (evsDisabled.value) callback()
  let error = ''
  if (!value[0] && !value[1]) {
    error = '数据盘类型、大小不能为空'
  } else if (!value[0] && value[1]) {
    error = '数据盘类型不能为空'
  } else if (value[0] && !value[1]) {
    error = '数据盘大小不能为空'
  }
  error ? callback(new Error(error)) : callback()
}

const validateDiskTypeSiskSize = (rule: any, value: any, callback: any) => {
  if (!formModel.sysDisk[0]) {
    callback(new Error('系统盘类型不能为空'))
  } else if (!formModel.sysDisk[1]) {
    callback(new Error('系统盘大小不能为空'))
  } else {
    callback()
  }
}

const validateArrRequired = (rule: any, value: any, callback: any) => {
  if (value && value.length === 2 && value[0] !== '' && value[1] !== '') {
    callback()
  } else {
    callback(new Error(rule.message))
  }
}
const validateSpecRequired = (rule: any, value: any, callback: any) => {
  if (value && value.length === 3 && value[0] && value[1] && value[2] !== '') {
    callback()
  } else {
    callback(new Error(rule.message))
  }
}

const computedMin = 0
const evsRules = [
  {
    validator: validateevsTypeevsSize,
    trigger: 'change',
  },
]

const goodsInfoOptions = reactive([
  {
    style: 'margin:auto 0 0 0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '资源池',
        type: 'input',
        key: 'resourcePoolName',
        span: 8,
        rules: {
          required: true,
          message: '资源池不能为空',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: 'GPU裸金属名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '主机名称不能为空', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '实例规格',
        type: 'slot',
        slotName: 'flavorSlot',
        key: 'gms',
        span: 8,
        required: true,
        rules: [
          {
            validator: validateSpecRequired,
            message: '实例规格不能为空',
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: '操作系统',
        type: 'slot',
        slotName: 'imageSlot',
        key: 'imageOs',
        span: 8,
        rules: [
          {
            validator: validateArrRequired,
            message: '操作系统名称和版本不能为空',
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: '系统盘',
        type: 'slot',
        slotName: 'sysDiskSlot',
        key: 'sysDisk',
        options: getDic('sysDisk'),
        props: {
          step: 1,
        },
        span: 8,
        required: true,
        rules: [{ validator: validateDiskTypeSiskSize, trigger: ['blur', 'change'] }],
      },
      {
        label: '是否绑定公网IP',
        type: 'slot',
        slotName: 'networkIpSlot',
        swithKey: 'isBindPublicNetworkIp',
        key: 'eipValue',
        suffix: 'M',
        props: {
          min: 0,
          step: 1,
          disabled: netSizeDisabled,
        },
        rules: [
          {
            validator: validateEmpty,
            disabled: netSizeDisabled,
            message: '带宽大小不能为空',
            trigger: ['blur', 'change'],
          },
        ],
        span: 8,
        required: true,
      },
      {
        label: '开通数量',
        type: 'inputNumber',
        key: 'numbers',
        props: {
          min: 1,
          step: 1,
          max: 99,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '开通数量不能为空', trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '申请时长',
        type: 'inputNumber',
        key: 'time',
        span: 8,
        props: {
          min: 1,
          step: 1,
        },
        rules: [{ required: true, message: '申请时长不能为空', trigger: ['blur', 'change'] }],
        suffix: '天',
      },
      {
        label: '是否挂载数据盘',
        type: 'slot',
        slotName: 'evsSlot',
        disabled: evsDisabled,
        swithKey: 'isMountEvs',
        key: 'evs',
        options: getDic('evs'),
        span: 8,
        required: true,
        props: {
          min: computedMin,
          max: 2048,
          step: 1,
        },
      },
    ],
  },
])
</script>
<style scoped>
.disaster-recovery-tip {
  margin-left: 8px;
  color: var(--el-color-warning);
  flex-basis: auto;
  position: absolute;
  top: 18px;
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0px;
}
</style>
