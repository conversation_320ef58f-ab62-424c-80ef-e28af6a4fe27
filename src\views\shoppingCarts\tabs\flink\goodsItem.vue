<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="goods.orderJson"
  >
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { IFlinkModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import eventBus from '@/utils/eventBus'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<IFlinkModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: route.query.orderId ? true : false,
  })
}

const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IFlinkModel>) {
  goods.ref = slFormRef
}

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: 'Flink名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入Flink名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: 'vCPU',
        type: 'inputNumber',
        key: 'vCPU',
        span: 8,
        props: {
          min: 1,
        },
        rules: {
          required: true,
          message: '请输入vCPU',
          trigger: ['blur', 'change'],
        },
        suffix: '核',
      },
      {
        label: '内存',
        type: 'inputNumber',
        key: 'memory',
        span: 8,
        required: true,
        props: {
          min: 1,
        },
        rules: {
          required: true,
          message: '请输入副本数',
          trigger: ['blur', 'change'],
        },
        suffix: 'GB',
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
