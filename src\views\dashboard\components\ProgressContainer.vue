<template>
  <div class="progress-container" :class="{ 'has-label': showLabel }">
    <!-- 进度条内容区域 -->
    <div class="progress-wrapper">
      <!-- 进度条背景 -->
      <div class="progress-background">
        <!-- 各个进度条项 -->
        <div
          v-for="(item, index) in progressItems"
          :key="index"
          class="progress-item-wrapper"
          :style="{
            left: `${getItemStartPosition(index)}%`,
            width: `${(item.value / total) * 100}%`,
          }"
        >
          <div
            class="progress-item"
            :style="{
              backgroundColor: item.color || defaultColors[index % defaultColors.length],
              transition: `width ${animationDuration}ms ease-out`,
            }"
            @mouseenter="handleHover(item, true)"
            @mouseleave="handleHover(item, false)"
          ></div>

          <span
            v-if="showLabel && isItemHovered(item)"
            class="progress-tooltip"
            :style="{ backgroundColor: item.color || defaultColors[index % defaultColors.length] }"
          >
            {{ item.label }}: {{ item.value }} ({{ ((item.value / total) * 100).toFixed(1) }}%)
          </span>
        </div>
      </div>

      <!-- 进度条下方的标签 -->
      <div class="progress-labels" v-if="showLabel && !showTooltip">
        <div
          v-for="(item, index) in progressItems"
          :key="index"
          class="progress-label"
          :style="{ color: item.color || defaultColors[index % defaultColors.length] }"
        >
          {{ item.label }} ({{ ((item.value / total) * 100).toFixed(1) }}%)
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs } from '@vueuse/core'
import { ref, computed, onMounted } from 'vue'

export interface ProgressItem {
  value: number
  label: string
  color?: string
}

// 直接在组件内定义进度条数据
// const progressItems = ref<ProgressItem[]>([
//   { label: 'a', value: 10, color: '#409EFF' },
//   { label: 'b', value: 20, color: '#67C23A' },
//   { label: 'c', value: 70, color: '#E6A23C' },
// ])

const props = defineProps<{
  progressItems: ProgressItem[]
}>()
const { progressItems } = toRefs(props)
// 存储悬停状态
const hoveredItem = ref<ProgressItem | null>(null)

// 默认配置
const showLabel = ref(true)
const showTooltip = ref(true)
const animationDuration = ref(1000)

// 默认颜色列表
const defaultColors = [
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399',
  '#36CBCB',
  '#919FFF',
  '#FF919F',
  '#CC66CC',
  '#00BFFF',
]

// 计算总计值
const total = computed(() => {
  return progressItems.value.reduce((sum, item) => sum + item.value, 0)
})

// 计算项目的起始位置
const getItemStartPosition = (index: number) => {
  const previousItems = progressItems.value.slice(0, index)
  const sum = previousItems.reduce((acc, item) => acc + item.value, 0)
  return (sum / total.value) * 100
}

// 判断项目是否被悬停
const isItemHovered = (item: ProgressItem) => {
  return hoveredItem.value === item
}

// 处理悬停事件
const handleHover = (item: ProgressItem, isHovered: boolean) => {
  if (showTooltip.value) {
    hoveredItem.value = isHovered ? item : null
  }
}

// 初始化动画
onMounted(() => {
  setTimeout(() => {
    // 触发重排以启动动画
  }, 10)
})
</script>

<style scoped>
.progress-container {
  width: 100%;
  margin: 1rem 0;
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
}

.progress-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-background {
  position: relative;
  height: 12px;
  background-color: #f5f7fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) inset;
}

.progress-item-wrapper {
  position: absolute;
  top: 0;
  height: 100%;
  overflow: visible;
}

.progress-item {
  height: 100%;
  transition: width 1s ease-out;
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  box-sizing: border-box;
}

.progress-item:last-child {
  border-right: none;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #606266;
  margin-top: 0.3rem;
}

.progress-label {
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.progress-tooltip {
  position: absolute;
  bottom: calc(100% + 5px);
  left: 50%;
  transform: translateX(-50%);
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.2s,
    visibility 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.progress-item-wrapper:hover .progress-tooltip {
  opacity: 1;
  visibility: visible;
}

.control-panel {
  margin-top: 1rem;
  display: flex;
  gap: 0.5rem;
}

.control-panel button {
  padding: 0.5rem 1rem;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-panel button:hover {
  background-color: #3088ee;
}
</style>
