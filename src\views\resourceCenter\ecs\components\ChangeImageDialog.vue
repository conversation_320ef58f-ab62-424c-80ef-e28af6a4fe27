<template>
  <SlDialog
    v-model="visible"
    title="更换镜像"
    width="600px"
    destroy-on-close
    confirm-text="确定"
    cancel-text="取消"
    @close="handleClose"
    @confirm="handleConfirm"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="选择镜像" prop="imageId" required>
        <el-select
          v-model="formData.imageId"
          placeholder="请选择要更换的镜像"
          style="width: 100%"
          :loading="imageLoading"
        >
          <el-option
            v-for="image in imageList"
            :key="image.id"
            :label="image.name"
            :value="image.id"
          >
            <div>
              <div>{{ image.name }}</div>
              <div style="font-size: 12px; color: #999">
                {{ image.osType }} | {{ image.description }}
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </SlDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import SlDialog from '@/components/SlDialog/index.vue'
import { getImagesList, executeResourceOperation } from '@/api/modules/resourecenter'

interface ImageItem {
  id: string
  name: string
  osType?: string
  osVersion?: string
  description: string
}

interface Props {
  visible: boolean
  resourceData: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

const formRef = ref<FormInstance>()
const imageLoading = ref(false)
const imageList = ref<ImageItem[]>([])
const submitLoading = ref(false)

const formData = reactive({
  imageId: '',
})

const rules: FormRules = {
  imageId: [{ required: true, message: '请选择镜像', trigger: 'change' }],
}

// 监听弹窗打开，自动加载镜像列表
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.resourceData) {
      loadImages()
    }
  },
  { immediate: true },
)

/**
 * 加载镜像列表
 */
async function loadImages() {
  if (!props.resourceData) return

  try {
    imageLoading.value = true
    const params = {
      regionId: props.resourceData.resourcePoolId,
      domainCode: props.resourceData.domainCode,
      azId: props.resourceData.azId,
    }

    const { entity } = await getImagesList(params)
    if (entity) {
      imageList.value = entity
    }
  } catch (error) {
    console.error('获取镜像列表失败:', error)
  } finally {
    imageLoading.value = false
  }
}

/**
 * 关闭弹窗
 */
function handleClose() {
  resetForm()
  visible.value = false
}

/**
 * 确认更换镜像
 */
async function handleConfirm() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true
    const params = {
      id: props.resourceData.id,
      operationType: 'REBUILDECS',
      imageId: formData.imageId,
    }
    await executeResourceOperation(params)
    ElMessage.success('已提交镜像更换请求')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('更换镜像失败:', error)
  } finally {
    submitLoading.value = false
  }
}

/**
 * 重置表单
 */
function resetForm() {
  formData.imageId = ''
  imageList.value = []
  formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">
:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
  line-height: 1.4;
}
</style>
