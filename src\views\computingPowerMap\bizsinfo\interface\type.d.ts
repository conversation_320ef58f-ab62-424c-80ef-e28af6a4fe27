/**
 * @name 业务系统列表
 */
export interface BusinessSystemListType {
  busiSystemId: string
  busiSystemName: string
}

/**
 * @name 获取业务系统详情
 */
export interface GetBusinessSystemDetailsParamsType {
  busiSystemId: number
}

/**
 * @name 云主机使用率筛选条件
 */
export interface GetCloudHostUsageListParamsType {
  tenantIds: string
  startTime: string
  endTime: string
}

/**
 * @name 云主机使用率
 */
export interface CloudHostUsageListType {
  day: string
  avgCpuUtil: string // CPU
  avgMemUtil: string // MEMORY
  avgDiskReadIops: string // READ
  avgDiskWriteIops: string // WRITE
}

// /**
//  * @name 磁盘使用率条件
//  */
// export interface EvsUsageListType {
//   date: string
//   read: number
//   write: number
// }

/**
 * @name 资源数量筛选条件
 */
export interface GetResourceNumberListParamsType {
  busiSystemIds: array<string>
}

/**
 * @name 资源数量列表
 */
export interface ResourceNumberListType {
  goodType: string
  amount: number
  diffAmount: number
  lastMount: number
  ratio: string
}

/**
 * @name 业务系统模块列表
 */
interface oacBusinessSystemModuleListItemType {
  moduleName: string
}

/**
 * @name 业务系统表单
 */
export interface BusinessSystemFormDataType {
  busiSystemName?: string // 业务系统名称
  oacBusinessSystemModuleList: oacBusinessSystemModuleListItemType[] // 业务模块名称集合
  importanceLevelId?: string // 业务系统等级，1-核心 2-重要 3-一般
  firstFieldId?: string // 业务归属类别，1-基础通信类系统 2-增值业务类系统 3-网管支撑类系统 4-业务平台类系统
  lifeCycle?: string // 生命周期状态，1-工程 2-在网 3-下线
  applicant?: string // 业务负责人
  department?: string // 所属部门
  phone?: string // 联系电话
  userEmail?: string // 邮箱
  manufacturer?: string // 厂家名称
  instanceId?: string // 厂家实例 ID
  manufacturerUserId?: string // 厂家用户名称 ID，有值表示原有用户，无值表示新增用户
  manufacturerContacts?: string // 厂家负责人名称
  manufacturerShortName?: string // 厂家简称
  manufacturerUserUpdate: boolean // true-编辑用户信息 false-不编辑
  manufacturerEmail?: string // 厂家负责人邮箱
  manufacturerEmailUpdate: boolean // 是否编辑厂家负责人邮箱
  manufacturerMobile?: string // 厂家负责人联系方式
  manufacturerMobileUpdate: boolean // 是否编辑厂家负责人联系方式
}

/**
 * @name 用户配置信息筛选条件
 */
export interface GetPropertyConfigParamsType {
  type: string
}

/**
 * @name 用户配置信息返回
 */
interface GetPropertyConfigType {
  configJson: string
}

/**
 * @name 用户配置信息表单
 */
export interface PropertyConfigFormDataType {
  type: string
  configJson: string
}

/**
 * @name 公告列表信息返回
 */
export interface GetNoticeListType {
  id: number
  title: string
  content: string
}
