import http from '@/api'
import { V1CLOUD, WOC, PERFORMANCE } from '../config/servicePort'
// import { changeDateFormat } from '@/utils'
// import type { RoleType } from '@/views/managementCenter/roleManagement/interface/type'
// import type { flavorType } from '@/views/configCenter/standards/interface/type'

/**
 * @name 平台类型
 */
export const getComPowerStatsCloudGroup = (config: any) => {
  return http.get(V1CLOUD + '/console/compute_power_map/stats/cloud_group', config)
}

// /**
//  * @name 查询物理资源池列表
//  */
// export const getComPowerPageBaseDeviceHardwareResourcePool = (config: any) => {
//   return http.post(V1CLOUD + '/console/compute_power_map/page/base_device/HARDWARE_RESOURCE_POOL', config)
// }
// /**
//  * @name 查询虚拟资源池列表
//  */
// export const getComPowerPageBaseDeviceVirtualResourcePool = (config: any) => {
//   return http.post(V1CLOUD + '/console/compute_power_map/page/base_device/VIRTUAL_RESOURCE_POOL', config)
// }
//
// /**
//  * @name 资源池列表
//  */
// export const getComPowerMapRegions = (config: any) => {
//   return http.post(V1CLOUD + '/console/compute_power_map/regions', config)
// }
/**
 * @name 国产化数据
 */
export const getComPowerMapStatsDataBaseNode = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/stats/database_node', config)
}

/**
 * @name 国产化数据-弹窗
 */
export const getComPowerMapPageDataBaseService = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/page/database_service', config)
}
//
/**
 * @name 重保业务数据
 */
export const getComPowerMapStatsCriticalBusiness = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/stats/critical_business', config)
}

/**
 * @name 重保业务数据-弹窗-虚拟机列表
 */
export const getComPowerMapPageCriticalBusiness = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/page/critical_business', config)
}

/**
 * @name 重保业务数据-弹窗-告警数据
 */
export const getComPowerMapAlarmStatsCriticalBusiness = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/alarm_stats/critical_business', config)
}

/**
 * @name 绿色节能数据
 */
export const getComPowerMapStatsGreenEnergySaved = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/stats/green_energy_saved', config)
}
/**
 * @name 绿色节能-弹窗数据
 */
export const getComPowerMapPageGreenEnergySaved = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/page/green_energy_saved', config)
}
/**
 * @name 资产分析数据
 */
export const getComPowerMapStatsAssetAnalysis = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/stats/asset_analysis', config)
}
/**
 * @name 资产分析-弹窗数据
 */
export const getComPowerMapStatsAssetAnalysisByType = (config: any) => {
  return http.post(
    V1CLOUD + '/console/compute_power_map/page/asset_analysis/' + config.type,
    config,
  )
}
/**
 * @name 基础设施数据
 */
export const getComPowerMapStatsBaseDevice = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/stats/base_device', config)
}
/**
 * @name 基础设施数据-弹窗数据
 */
export const getComPowerMapPageBaseDevice = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/page/base_device/' + config.type, config)
}
/**
 * @name 获取物理资源池
 */
export const getComPowerMapPageBaseDeviceHandResourcePool = (config: any) => {
  return http.post(
    V1CLOUD + '/console/compute_power_map/page/base_device/HARDWARE_RESOURCE_POOL',
    config,
  )
}
/**
 * @name 获取 存储、vCpu、内存、网络用量-数据
 */
export const getComPowerMapStatsResUsageLatest = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/stats/res_usage/latest', config)
}
/**
 * @name 高亮地市及区县信息
 */
export const getComPowerMapStatsCloudCityAreas = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/stats/cloud_city_areas', config)
}

/**
 * @name  主要资源慨况
 */
export const getComPowerMapResourceCount = (config: any) => {
  return http.post(V1CLOUD + '/console/compute_power_map/resource/count', config)
}
/**
 * @name  左侧弹窗-折线图信息-利用率-两条线
 */
export const getComPowerMapResourceLineData = (config: any) => {
  return http.get(PERFORMANCE + '/view/pm/map', config)
}
/**
 * @name  左侧弹窗-折线图信息-三条线
 */
export const getComPowerMapCmMap = (config: any) => {
  return http.get(V1CLOUD + '/console/compute_power_map/cm/map', config)
}

/**
 * @name gpu数据 - 算力切片
 */
export const totalPhysicaltApi = (config: any) => {
  return http.post(WOC + '/zsMap/totalPhysical', config)
}
/**
 * @name gpu数据 - 算力总值
 */
export const totalComputetApi = (config: any) => {
  return http.post(WOC + '/zsMap/totalCompute', config)
}

/**
 * @name vCpu、内存-弹窗
 */

export const getResourceUsageDetailApi = (params: {
  cloudName?: string
  platformTypeName?: string
  cityCode?: string
  areaCode?: string
  startTime?: string
  endTime?: string
}) => http.get<any>(PERFORMANCE + '/view/pm/map', params)

/**
 * 物理机数据
 */
export const getvmCountApi = (params: {
  cloudName?: string
  platformTypeName?: string
  cityCode?: string
  areaCode?: string
  startTime?: string
  endTime?: string
}) => http.post<any>(V1CLOUD + '/console/compute_power_map/stats/vm_count', params)
