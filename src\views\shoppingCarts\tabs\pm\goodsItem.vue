<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="goods.orderJson"
  >
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>

    <template #gpuSlot="{ form, item }">
      <el-switch
        style="flex-grow: 0.2"
        v-model="form[item.swithKey]"
        active-value="1"
        inactive-value="0"
      />
      <el-select
        :disabled="form.isUseGpu === '0'"
        style="margin-left: 10px"
        placeholder="请选择显卡类型"
        v-model="form.gpuCardType"
        @change="() => (form.gpuType = '')"
        clearable
      >
        <el-option label="GPU" value="GPU" />
        <el-option label="NPU" value="NPU" />
      </el-select>
      <el-select
        :disabled="form.isUseGpu === '0'"
        style="margin-left: 10px"
        placeholder="请选择显卡型号"
        v-model="form.gpuType"
        clearable
      >
        <!-- GPU选项 -->
        <template v-if="form.gpuCardType === 'GPU'">
          <el-option label="A10" value="A10" />
          <el-option label="V100" value="V100" />
          <el-option label="T4" value="T4" />
          <el-option label="A40" value="A40" />
        </template>
        <!-- NPU选项 -->
        <template v-else-if="form.gpuCardType === 'NPU'">
          <el-option label="300I" value="300I" />
          <el-option label="910B2" value="910B2" />
          <el-option label="910B4" value="910B4" />
        </template>
      </el-select>
      <el-input-number
        :disabled="form.isUseGpu === '0'"
        :min="0"
        style="margin-left: 10px"
        placeholder="请输入显卡数量"
        v-model="form.gpuCount"
        clearable
      />
      <span style="margin-left: 10px">张</span>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import type { IPmModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<IPmModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: route.query.orderId ? true : false,
  })
}

const formModel = props.goods.orderJson

const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IPmModel>) {
  goods.ref = slFormRef
}

function validateGpu(rule: any, value: any, callback: any) {
  if (formModel.isUseGpu === '0') {
    callback()
  } else {
    if (!formModel.gpuCardType || !formModel.gpuType || !formModel.gpuCount) {
      callback(new Error('请选择显卡配置'))
    } else {
      callback()
    }
  }
}

watch(
  () => formModel.isUseGpu,
  (newVal) => {
    if (newVal === '0') {
      formModel.gpuCardType = ''
      formModel.gpuType = ''
      formModel.gpuCount = 0
    }
  },
)

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '裸金属名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入裸金属名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: 'CPU',
        type: 'inputNumber',
        key: 'cpu',
        props: {
          min: 1,
        },
        span: 8,
        rules: {
          required: true,
          message: '请输入CPU',
          trigger: ['blur', 'change'],
        },
        suffix: '核',
      },
      {
        label: '内存',
        type: 'inputNumber',
        key: 'memory',
        span: 8,
        props: {
          min: 1,
        },
        required: true,
        rules: {
          required: true,
          message: '请输入内存',
          trigger: ['blur', 'change'],
        },
        suffix: 'GB',
      },
      {
        label: '硬盘',
        type: 'inputNumber',
        key: 'disk',
        props: {
          min: 1,
          step: 1,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请输入保留时间', trigger: ['blur', 'change'] },
        ],
        suffix: 'GB',
      },
      {
        label: '使用GPU/NPU',
        type: 'slot',
        slotName: 'gpuSlot',
        swithKey: 'isUseGpu',
        key: 'placeholder',
        props: {
          min: 0,
          step: 1,
        },
        span: 16,
        required: true,
        rules: [{ validator: validateGpu, trigger: ['blur', 'change'] }],
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
