<template>
  <div>
    <el-row :gutter="24">
      <el-col :span="4">
        <el-select
          clearable
          v-model="model.vpc"
          placeholder="请选择VPC"
          value-key="id"
          :remote="true"
          filterable
          :remote-method="remoteMethod"
          :loading="loading"
        >
          <el-option
            v-for="item in vpcOptions"
            :key="item.value.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
      <el-col :span="4">
        <el-select clearable v-model="model.subnet" placeholder="请选择子网" value-key="id">
          <el-option
            v-for="item in subnetOptions"
            :key="item.value.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-button :icon="Plus" type="primary" link @click="createVpcAndSubnet">
      创建VPC及子网
    </el-button>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { getCorporateVpcTreeApi } from '@/api/modules/approvalCenter'

// 在window上添加updateVpc 类型
declare global {
  interface Window {
    updateVpc: () => void
  }
}

const props = defineProps<{
  item: any
  form: any
}>()
const model = props.form
const vpcOptions = ref<any[]>([])
const subnetOptions = ref<any[]>([])

const loading = ref(false)
const remoteMethod = async () => {
  loading.value = true
  if (model.resourcePool?.code && model.az?.code && model.tenant) {
    try {
      await getVpcDic()
    } finally {
      loading.value = false
    }
  }
}

const getVpcDic = async () => {
  const { entity } = await getCorporateVpcTreeApi({
    tenantId: model.tenant?.id,
    regionCode: model.resourcePool?.code,
    azCode: model.az?.code,
  })
  vpcOptions.value = entity.map((item: any) => {
    return {
      label: item.vpcName,
      value: item,
    }
  })
}

watch(
  [() => model.resourcePool?.code, () => model.az?.code, () => model.tenant],
  ([regionCode, azCode, tenant]) => {
    if (regionCode && azCode && tenant) {
      getVpcDic()
    } else {
      vpcOptions.value = []
      subnetOptions.value = []
      model.vpc = null
      model.subnet = null
    }
  },
)
watch(
  () => model.vpc,
  (vpc: any) => {
    if (vpc) {
      subnetOptions.value = (vpc.vpcSubnetOrderList || []).map((e: any) => ({
        label: e.subnetName,
        value: e,
      }))
    }
  },
)

onMounted(() => {
  window.updateVpc = () => {
    // todo 刷新vpc列表
    if (!model.resourcePool || !model.az) return
    getVpcDic()
  }
})
onUnmounted(() => {
  window.updateVpc = () => 0
})

const createVpcAndSubnet = () => {
  window.open('/corporate/vpc', '_blank')
}
</script>
