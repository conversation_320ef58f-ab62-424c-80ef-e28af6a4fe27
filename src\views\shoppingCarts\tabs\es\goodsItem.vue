<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="goods.orderJson"
  >
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { IEsModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<IEsModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: route.query.orderId ? true : false,
  })
}

const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IEsModel>) {
  goods.ref = slFormRef
}
// json 格式校验
const validateJson = (rule: any, value: any, callback: any) => {
  try {
    JSON.parse(value)
    callback()
  } catch (error) {
    console.error(error)
    callback(new Error('请输入正确的索引模版'))
  }
}
const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '索引模版名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入索引模版名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '日均增量数据',
        type: 'inputNumber',
        key: 'dailyIncrementData',
        props: {
          min: 1,
        },
        span: 8,
        rules: [
          {
            required: true,
            message: '请输入日均增量数据',
            trigger: ['blur', 'change'],
          },
        ],
        suffix: 'G/天',
      },
      {
        label: '数据保留时间',
        type: 'inputNumber',
        key: 'retentionTime',
        span: 8,
        required: true,
        props: {
          min: 1,
          step: 1,
        },
        rules: {
          required: true,
          message: '请输入数据保留时间',
          trigger: ['blur', 'change'],
        },
        suffix: '天',
      },
      {
        label: '索引副本数',
        type: 'inputNumber',
        key: 'replication',
        props: {
          min: 1,
          step: 1,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请输入索引副本数', trigger: ['blur', 'change'] },
        ],
        suffix: '个',
      },
      {
        label: '磁盘大小',
        type: 'inputNumber',
        key: 'diskSize',
        props: {
          min: 1,
          step: 1,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请输入磁盘大小', trigger: ['blur', 'change'] },
        ],
        suffix: 'GB',
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
      {
        label: '索引模版',
        type: 'editor',
        placeholder: '请输入模板的Json格式或粘贴到此处',
        key: 'template',
        span: 24,
        props: {
          language: 'json',
          options: {
            mode: 'json',
            minimap: { enabled: false },
            scrollbar: {
              verticalScrollbarSize: 2,
            },
          },
        },
        rules: [
          { required: true, message: '请输入索引模版', trigger: ['change'] },
          { validator: validateJson, trigger: ['change'] },
        ],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
