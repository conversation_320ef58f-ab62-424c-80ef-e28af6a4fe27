import type { ColumnProps } from '@/components/SlProTable/interface'
import type { NonStandardOrderColumnsType } from '../../../interface/type'

export const nonStandardOrderNameKey = {
  ecs: 'vmName',
  gcs: 'vmName',
  bms: 'bmsName',
  gms: 'gmsName',
  mysql: 'mysqlName',
  postgreSql: 'postgreSqlName',
  redis: 'redisName',
}

const whethers = [
  { label: '是', value: true },
  { label: '否', value: false },
]
export const nonStandardOrderValueEnum = [
  { code: 'ecs', desc: '云主机', goodsList: 'ecsModelList', params: ['vpcId', 'subnetId'] },
  { code: 'gcs', desc: 'GPU云主机', goodsList: 'gcsModelList', params: ['vpcId', 'subnetId'] },
  { code: 'bms', desc: '裸金属', goodsList: 'bmsModelList' },
  { code: 'gms', desc: 'GPU裸金属', goodsList: 'gmsModelList' },
  { code: 'mysql', desc: 'MySQL', goodsList: 'mysqlModelList' },
  { code: 'postgreSql', desc: 'PostgreSQL', goodsList: 'postgreSqlModelList' },
  { code: 'redis', desc: 'Redis', goodsList: 'redisModelList' },
]

// 云服务器 - GPU云主机
const ecsColumns: NonStandardOrderColumnsType['ecs'] = [
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: nonStandardOrderValueEnum,
  },
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'catalogueDomainName', label: '云类型', minWidth: 200 },
  { prop: 'domainName', label: '云平台', minWidth: 200 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'azName', label: '可用区', minWidth: 200 },
  { prop: 'flavorName', label: '实例规格', minWidth: 140 },
  {
    prop: 'imageVersion',
    label: '操作系统',
    width: 140,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.imageOs ?? '--'} / {scope.row.imageVersion ?? '--'}
        </div>
      )
    },
  },
  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },
  { prop: 'bindPublicIp', label: '是否绑定公网IP', width: 100, enum: whethers },
  {
    label: '带宽/M',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'mountDataDisk', label: '是否挂载数据盘', width: 120, enum: whethers },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'applyTime', label: '申请时长/天', width: 150 },
]

// 裸金属 - GPU裸金属
const bmsColumns: NonStandardOrderColumnsType['bms'] = [
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: nonStandardOrderValueEnum,
  },
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  {
    prop: 'flavorName',
    label: '实例规格',
    minWidth: 140,
  },
  {
    prop: 'imageVersion',
    label: '操作系统',
    width: 140,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.imageOs ?? '--'} / {scope.row.imageVersion ?? '--'}
        </div>
      )
    },
  },

  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },

  { prop: 'bindPublicIp', label: '是否绑定公网IP', width: 100, enum: whethers },
  {
    prop: 'bandWiDthNumbers',
    label: '公网带宽/M',
    minWidth: 100,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'mountDataDisk', label: '是否挂载数据盘', width: 120, enum: whethers },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'applyTime', label: '申请时长/天', width: 150 },
]

// MySQL
const mysqlColumns: NonStandardOrderColumnsType['mysql'] = [
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: nonStandardOrderValueEnum,
  },
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'mysqlVersion', label: '版本', width: 100 },
  { prop: 'mysqlType', label: '实例类型', width: 100 },
  {
    prop: 'flavorName',
    label: '实例规格',
    minWidth: 140,
  },
  {
    prop: 'imageVersion',
    label: '操作系统',
    width: 140,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.imageOs ?? '--'} / {scope.row.imageVersion ?? '--'}
        </div>
      )
    },
  },

  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },
  { prop: 'bindPublicIp', label: '是否绑定公网IP', width: 100, enum: whethers },
  {
    prop: 'bandWiDthNumbers',
    label: '公网带宽/M',
    minWidth: 100,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'mountDataDisk', label: '是否挂载数据盘', width: 120, enum: whethers },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'applyTime', label: '申请时长/天', width: 150 },
]

// postgreSql
const postgreSqlColumns: NonStandardOrderColumnsType['postgreSql'] = [
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: nonStandardOrderValueEnum,
  },
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'postgreSqlVersion', label: '版本', width: 100 },
  { prop: 'postgreSqlType', label: '实例类型', width: 100 },
  {
    prop: 'flavorName',
    label: '实例规格',
    minWidth: 140,
  },
  {
    prop: 'imageVersion',
    label: '操作系统',
    width: 140,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.imageOs ?? '--'} / {scope.row.imageVersion ?? '--'}
        </div>
      )
    },
  },
  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },
  { prop: 'bindPublicIp', label: '是否绑定公网IP', width: 100, enum: whethers },
  {
    prop: 'bandWiDthNumbers',
    label: '公网带宽/M',
    minWidth: 100,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'mountDataDisk', label: '是否挂载数据盘', width: 120, enum: whethers },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'applyTime', label: '申请时长/天', width: 150 },
]

// redis
const redisColumns: NonStandardOrderColumnsType['redis'] = [
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: nonStandardOrderValueEnum,
  },
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'redisVersion', label: '版本', width: 100 },
  { prop: 'redisType', label: '实例类型', width: 100 },
  {
    prop: 'flavorName',
    label: '实例规格',
    minWidth: 140,
  },
  {
    prop: 'imageVersion',
    label: '操作系统',
    width: 140,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.imageOs ?? '--'} / {scope.row.imageVersion ?? '--'}
        </div>
      )
    },
  },
  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },
  { prop: 'bindPublicIp', label: '是否绑定公网IP', width: 100, enum: whethers },
  {
    prop: 'bandWiDthNumbers',
    label: '公网带宽/M',
    minWidth: 100,
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'mountDataDisk', label: '是否挂载数据盘', width: 120, enum: whethers },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div class="medium sle">
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'applyTime', label: '申请时长/天', width: 150 },
]

const nonStandardOrderAllColumns: NonStandardOrderColumnsType = {
  ecs: ecsColumns,
  gcs: ecsColumns,
  bms: bmsColumns,
  gms: bmsColumns,
  mysql: mysqlColumns,
  postgreSql: postgreSqlColumns,
  redis: redisColumns,
}

export default nonStandardOrderAllColumns

export const indexColumn: ColumnProps = { type: 'index', label: '序号', width: 55 }

//  --非标状态--
export const nonStandardOrderStatusList = [
  { label: '非标中', value: 'changing', elType: 'warning' },
  { label: '非标成功', value: 'nonStandardOrder_success', elType: 'success' },
  { label: '非标失败', value: 'nonStandardOrder_fail', elType: 'danger' },
  { label: '待更变', value: 'wait_nonStandardOrder', elType: 'primary' },
]

export const nonStandardOrderStatusColumn: ColumnProps = {
  prop: 'nonStandardOrderStatus',
  label: '非标状态',
  width: 100,
  link: true,
  enum: nonStandardOrderStatusList,
}
