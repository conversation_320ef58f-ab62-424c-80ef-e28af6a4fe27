<template>
  <div class="password-encryption">
    <div class="password" v-if="show">
      <el-tooltip effect="dark" :content="value ?? '--'" placement="top">
        {{ value ?? '--' }}
      </el-tooltip>
    </div>
    <div class="password" v-else>
      {{ value.replace(/./g, '*') }}
    </div>
    <el-icon style="cursor: pointer; margin-left: 10px" @click="show = !show">
      <component :is="show ? View : Hide"></component>
    </el-icon>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { View, Hide } from '@element-plus/icons-vue'

const props = defineProps({
  value: {
    type: [String, Number],
    default: '',
  },
})
const value = ref(props.value.toString())
const show = ref(false)
</script>

<style lang="scss" scoped>
.password-encryption {
  display: flex;
  align-items: center;
  width: 100%;
  .password {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    width: calc(100% - 20px);
  }
}
</style>
