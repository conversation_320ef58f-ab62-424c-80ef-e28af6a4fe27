<template>
  <Card style="height: 100%">
    <Title title="智算运行状态" />
    <div class="gpu-container" v-loading="loading">
      <div v-if="tableData.length === 0 || loading" class="empty-state">暂无数据</div>
      <template v-else>
        <div class="gpu-item" v-for="item in tableData" :key="item.name">
          <!-- <div class="gpu-item-name">{{ item.name }}</div>
          <div class="gpu-item-businessName">{{ item.businessName }}</div> -->
          <GpuGaugeCard :item="item" />
        </div>
      </template>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { queryDeviceRunSortApi } from '@/api/modules/zsMap'
import Card from './Card.vue'
import Title from './Title.vue'
import GpuGaugeCard from './GpuGaugeCard.vue'
import { ref, watchEffect } from 'vue'

const props = defineProps<{
  areaCode?: string
}>()

// 定义表格行数据类型
interface TableRow {
  businessName?: string
  memUtilpercent?: string
  gpuUtilPercent?: string
  name: string
}

// 表格数据
const tableData = ref<TableRow[]>([
  {
    name: '910B',
  },
  {
    name: '300I',
  },
  {
    name: 'T4',
  },
  {
    name: 'A10',
  },
  {
    name: 'A40',
  },

  {
    name: 'V100',
  },
])

const loading = ref(false)

const initData = async (areaCode?: string) => {
  loading.value = true
  try {
    const { entity }: any = await queryDeviceRunSortApi({ areaCode })
    tableData.value = tableData.value.map((item: any) => {
      let obj: any = {}
      const data = entity.find((e: any) => e.modelName === item.name)
      if (data) {
        obj = {
          ...item,
          businessName: data.businessName ?? null,
          memUtilpercent:
            data.memUtilpercent || data.memUtilpercent === 0 ? data.memUtilpercent : null,
          gpuUtilPercent:
            data.gpuUtilPercent || data.gpuUtilPercent === 0 ? data.gpuUtilPercent : null,
        }
      } else {
        obj = {
          ...item,
          businessName: null,
          memUtilpercent: null,
          gpuUtilPercent: null,
        }
      }
      return obj
    })
  } finally {
    loading.value = false
  }
}

watchEffect(() => {
  initData(props.areaCode)
})
</script>

<style lang="scss" scoped>
// 子元素一行两个铺满整个盒子
.gpu-container {
  padding: 5px;
  padding-top: 15px;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  .gpu-item {
    height: 101px;
    background: linear-gradient(to right, #fff, #edf3fc);
    border-radius: 6px;
    // border: 1px solid #edf3fc;
    text-align: center;
  }
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
}
</style>
