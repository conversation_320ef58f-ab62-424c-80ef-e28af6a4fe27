<template>
  <div>
    <div>
      <sl-page-header title="云灾备"></sl-page-header>
    </div>
    <sl-form
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formData"
      :label-width="120"
      ref="formRef"
    >
      <template #region-slot="{ form, item }">
        <region-select :form="form" :item="item" ref="regionRef" />
      </template>
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
          <el-button type="primary" plain class="cart-btn" @click="handleAddToCart">
            加入清单
          </el-button>
          <el-button type="primary" class="order-btn" @click="handleDirectOrder" v-if="vifOpened">
            直接开通
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, watch, computed, markRaw } from 'vue'
import RegionSelect from './components/RegionSelect.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import CustomRadio from './components/CustomRadio.vue'
import { useTenant } from './hooks/useTenant'
import { corporateOrderApi, corporateOrderTemSaveApi } from '@/api/modules/resourecenter'

import useGetTheParameters from './hooks/useGetTheParameters'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import SlForm from '@/components/form/SlForm.vue'
import eventBus from '@/utils/eventBus'
import { useAuthStore } from '@/stores/modules/auth'

const authStore = useAuthStore()
const vifOpened = computed(() =>
  authStore.authButtonListGet?.viewOfPublicTenants?.includes('Opened'),
)

const formData = ref<any>({
  tenant: null,
  paymentType: 'day', // 默认按天付费，将根据备份类型动态设置

  region: 'placeholder',
  domain: null,
  resourcePool: null,
  catalogueDomainCode: 'cloudst_group_moc',
  catalogueDomainName: '移动云',
  az: null,
  instanceName: '',
  backupType: 'ECS', // 备份类型：ECS(云主机) 或 EVS(云硬盘)
  frequency: 'days', // 备份频率：days(每天) 或 weeks(每周)
  daysOfWeek: undefined, // 星期几备份（当frequency为weeks时使用）
})

// 备份类型选项
const backupTypeOptions = ref([
  { label: '云主机备份', value: 'ECS' },
  { label: '块存储备份', value: 'EVS' },
])

// 备份频率选项
const frequencyOptions = ref([
  { label: '每天', value: 'days' },
  { label: '每周', value: 'weeks' },
])

// 星期选项 (1-7对应周一到周日)
const weekDayOptions = ref([
  { label: '1', value: 1 },
  { label: '2', value: 2 },
  { label: '3', value: 3 },
  { label: '4', value: 4 },
  { label: '5', value: 5 },
  { label: '6', value: 6 },
  { label: '7', value: 7 },
])

// 绑定资源选项

// 根据备份类型动态设置付费类型
const updatePaymentTypeByBackupType = () => {
  if (formData.value.backupType === 'ECS') {
    formData.value.paymentType = 'day' // 云主机备份按天付费
  } else if (formData.value.backupType === 'EVS') {
    formData.value.paymentType = 'quant' // 云硬盘备份按量付费
  }
}

const regionRef = ref<InstanceType<typeof RegionSelect>>()

// 监听备份类型变化，动态设置付费类型
watch(
  () => formData.value.backupType,
  () => {
    updatePaymentTypeByBackupType()
  },
  { immediate: true }, // 立即执行一次
)

// 监听备份频率变化，重置星期选择
watch(
  () => formData.value.frequency,
  (newFrequency) => {
    if (newFrequency === 'days') {
      formData.value.daysOfWeek = undefined
      formRef.value?.clearValidate(['daysOfWeek'])
    }
  },
)

const { tenantList: tenantListOptions } = useTenant()
const router = useRouter()

const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '备份类型',
        type: 'radio',
        key: 'backupType',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请选择备份类型', trigger: ['blur', 'change'] }],
        options: backupTypeOptions,
        props: {
          select: {
            valueKey: 'value',
          },
        },
      },
      {
        label: '付费类型',
        type: 'component',
        key: 'paymentType',
        span: 24,
        required: true,
        options: computed(() => {
          if (formData.value.backupType === 'ECS') {
            return [
              {
                label: '按日付费',
                value: 'day',
              },
            ]
          } else if (formData.value.backupType === 'EVS') {
            return [
              {
                label: '按量计费',
                value: 'quant',
              },
            ]
          }
          return []
        }),
        component: markRaw(CustomRadio),
      },
      {
        label: '区域',
        type: 'slot',
        slotName: 'region-slot',
        key: 'region',
        span: 24,
        required: true,
        rules: [
          {
            validator: (_rule: any, _value: any, callback: any) => {
              if (!formData.value.domain) {
                callback(new Error('请选择云平台'))
              }
              if (!formData.value.resourcePool) {
                callback(new Error('请选择资源池'))
              }
              if (!formData.value.az) {
                callback(new Error('请选择可用区'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
      },

      {
        label: '租户',
        type: 'select',
        key: 'tenant',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请选择租户', trigger: ['blur', 'change'] }],
        options: tenantListOptions,
        props: {
          select: {
            valueKey: 'id',
          },
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '云灾备名称',
        type: 'input',
        key: 'instanceName',
        required: true,
        rules: [
          { required: true, message: '请输入云灾备名称', trigger: ['blur', 'change'] },
          {
            validator: (_rule: any, value: any, callback: any) => {
              if (!value) {
                callback()
                return
              }
              if (value.length > 50) {
                callback(new Error('云灾备名称长度不能超过50个字符'))
                return
              }
              // 只允许中文、字母、数字、下划线(_)、短横线(-)
              const pattern = /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/
              if (!pattern.test(value)) {
                callback(new Error('云灾备名称只能包含中文、字母、数字、下划线(_)、短横线(-)'))
                return
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
        props: {
          maxlength: 50,
          showWordLimit: true,
          placeholder: '填写云灾备名称',
        },
        span: 13,
      },
      {
        label: '备份频率',
        type: 'select',
        key: 'frequency',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请选择备份频率', trigger: ['blur', 'change'] }],
        options: frequencyOptions,
        props: {
          select: {
            valueKey: 'value',
          },
        },
      },
      {
        label: '星期',
        type: 'select',
        key: 'daysOfWeek',
        span: 13,
        required: false,
        rules: [
          {
            validator: (_rule: any, value: any, callback: any) => {
              if (formData.value.frequency === 'weeks' && !value) {
                callback(new Error('请选择星期'))
              } else {
                callback()
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        options: weekDayOptions,
        props: {
          select: {
            valueKey: 'value',
            disabled: computed(() => formData.value.frequency !== 'weeks'),
            placeholder: computed(() =>
              formData.value.frequency === 'days' ? '每天备份' : '选择每周几备份',
            ),
          },
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              配置概要
            </SlBlockTitle>
          )
        },
      },
      {
        label: '云灾备名称',
        type: 'text',
        getter: (form: any) => form.instanceName || '',
        span: 8,
      },
      {
        label: '区域',
        type: 'text',
        getter: (form: any) =>
          form.resourcePool?.name ? `${form.domain?.name} - ${form.resourcePool.name}` : '',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        getter: (form: any) => form.tenant?.name || '',
        span: 8,
      },
      {
        label: '付费类型',
        type: 'text',
        getter: (form: any) =>
          form.paymentType === 'day'
            ? '按天付费'
            : form.paymentType === 'quant'
              ? '按量付费'
              : '按月付费',
        span: 8,
      },
      {
        label: '备份类型',
        type: 'text',
        getter: (form: any) => (form.backupType === 'ECS' ? '云主机备份' : '块存储备份'),
        span: 8,
      },
      {
        label: '备份频率',
        type: 'text',
        getter: (form: any) => (form.frequency === 'days' ? '每天' : '每周'),
        span: 8,
      },
      {
        label: '星期',
        type: 'text',
        getter: (form: any) => {
          if (form.frequency === 'days') {
            return '每天'
          }
          if (form.frequency === 'weeks' && form.daysOfWeek !== undefined) {
            return `星期${form.daysOfWeek}`
          }
          return '--'
        },
        span: 8,
      },
    ],
  },
])

const formRef = ref<InstanceType<typeof SlForm>>()

// 处理取消操作
const handleCancel = () => {
  router.go(-1)
  eventBus.emit('corporateShoppingList:updateCount')
}

// 处理加入清单操作
const handleAddToCart = async () => {
  try {
    // 1. 校验表单数据
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'backup', false)

    // 3. 调用API
    await corporateOrderTemSaveApi(params)
    ElMessage.success('加入清单成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}

// 处理直接开通操作
const handleDirectOrder = async () => {
  try {
    // 1. 校验表单数据
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'backup')

    // 3. 调用API
    await corporateOrderApi(params)
    ElMessage.success('发起开通成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}
</script>

<style lang="scss" scoped>
/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}

/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
