<template>
  <div id="ResourceFilter">
    <el-popover :visible="popoverVisible" placement="bottom-end" :width="400" trigger="click">
      <template #reference>
        <el-icon @click="handleSelectedShow"><Filter /></el-icon>
      </template>
      <div class="list">
        <div class="list-header">
          <div class="list-title">添加筛选</div>
          <div class="list-subtitle">资源类型</div>
        </div>
        <div class="list-items">
          <el-checkbox
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            @change="handleCheckAllChange"
          >
            全部
          </el-checkbox>
          <el-checkbox-group v-model="selectKeys" @change="handleCheckedSystemChange">
            <el-checkbox
              v-for="resource in props.resourceList"
              :key="resource.key"
              :value="resource.key"
            >
              {{ resource.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="list-footer">
          <el-button @click="handleClose"> 取消 </el-button>
          <el-button type="primary" @click="handleSubmit"> 确认 </el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script setup lang="ts" name="ResourceFilter">
import { ref, onMounted } from 'vue'
import { Filter } from '@element-plus/icons-vue'
import { getPropertyConfigApi, submitPropertyConfigApi } from '@/api/modules/computingPowerMap'
import SlMessage from '@/components/base/SlMessage'
interface ResourceItem {
  key: string
  name: string
  show: boolean
  data: object
}
const props = defineProps<{
  resourceList: ResourceItem[]
}>()
const selectKeys = ref<any>([])
const initialSelectKeys = ref<any>([])
const checkAll = ref(false)
const isIndeterminate = ref(true)
const getPropertyConfig = async () => {
  const { entity } = await getPropertyConfigApi({ type: 'TENANT_VIEW' })
  if (entity && entity.configJson) {
    selectKeys.value = JSON.parse(entity.configJson)
  } else {
    selectKeys.value = props.resourceList.map((item) => item.key)
  }
  initialSelectKeys.value = JSON.parse(JSON.stringify(selectKeys.value))
  props.resourceList.forEach((item) => {
    item.show = false
    if (selectKeys.value.includes(item.key)) {
      item.show = true
    }
  })
}
const handleCheckAllChange = (val: any) => {
  selectKeys.value = val ? props.resourceList.map((item) => item.key) : []
  isIndeterminate.value = false
}
const handleCheckedSystemChange = (value: string[]) => {
  const checkedCount = value.length
  checkAll.value = checkedCount === props.resourceList.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < props.resourceList.length
}
const popoverVisible = ref(false)
const handleSelectedShow = () => {
  popoverVisible.value = true
}
const handleClose = () => {
  popoverVisible.value = false
  selectKeys.value = JSON.parse(JSON.stringify(initialSelectKeys.value))
}
const handleSubmit = async () => {
  if (selectKeys.value.length === 0) {
    return SlMessage({
      message: '请至少选择一个资源类型',
      type: 'warning',
    })
  }
  popoverVisible.value = false
  await submitPropertyConfigApi({
    type: 'TENANT_VIEW',
    configJson: JSON.stringify(selectKeys.value),
  })
  props.resourceList.forEach((item: any) => {
    item.show = selectKeys.value.includes(item.key)
  })
}

onMounted(() => {
  getPropertyConfig()
})
</script>
<style lang="scss" scoped>
#ResourceFilter {
  width: 32px;
  height: 32px;
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  background-color: #f0f2f7;
  cursor: pointer;
  &:hover {
    background-color: #e1e3e9;
  }
}
.list {
  .list-header {
    .list-title {
      font-weight: 600;
    }
    .list-subtitle {
      margin-top: 10px;
      color: #999;
    }
  }
  .list-items {
    display: flex;
    margin-top: 10px;
    flex-wrap: wrap;
    &::after {
      content: '';
      flex: auto;
    }
    .list-item {
      height: 30px;
      padding: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;
      background-color: #f5f6f8;
      margin-right: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
      cursor: pointer;
      white-space: nowrap;
      &.active {
        color: white;
        background-color: var(--el-color-primary);
      }
    }
  }
  .list-footer {
    text-align: right;
  }
}
</style>
