<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getVirtualNicList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>

  <!-- 变更IP弹窗 -->
  <el-dialog
    v-model="changeIpDialogVisible"
    title="变更IP"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form :model="changeIpForm" label-width="100px">
      <el-form-item label="请选择IP">
        <el-select v-model="changeIpForm.ip" placeholder="请选择IP" style="width: 80%" filterable>
          <el-option v-for="ip in ipOptions" :key="ip" :label="ip" :value="ip" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeChangeIpDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmitChangeIpWithRefresh" :loading="submitLoading">
          提交
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="tsx" name="DataList">
import { ref, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getVirtualNicList, deleteVirtualNic } from '@/api/modules/resourecenter'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useChangeIp } from '../hooks/useChangeIp'

const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
  isSelectMode: {
    type: Boolean,
    default: false,
  },
  hideOperations: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['currentChange', 'selected'])

const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 动态计算列配置
const columns = computed(() => {
  const baseColumns: ColumnProps<any>[] = [
    { type: 'index', label: '序号', width: 55, fixed: 'left' },
    {
      prop: 'vnicName',
      label: '虚拟网卡名称',
      fixed: 'left',
      width: 150,
      render: ({ row }) => {
        return (
          <el-button onClick={() => handleViewDetail(row)} type="primary" link>
            {row.vnicName}
          </el-button>
        )
      },
    },
    { prop: 'tenantName', label: '租户' },
    { prop: 'catalogueDomainName', label: '云类型' },
    { prop: 'domainName', label: '云平台' },
    { prop: 'regionName', label: '资源池' },
    { prop: 'azName', label: '可用区' },
    { prop: 'vpcName', label: 'VPC/网络' },
    { prop: 'subnetName', label: '子网' },
    { prop: 'ipAddress', label: 'IP地址' },
    { prop: 'vmName', label: '云主机' },
    { prop: 'createTime', label: '创建时间', width: 150 },
  ]

  if (!props.hideOperations) {
    // 如果不是选择模式，添加操作列
    if (!props.isSelectMode) {
      baseColumns.push({
        prop: 'operation',
        label: '操作',
        width: 200,
        fixed: 'right',
        render: operationRender,
      })
    } else {
      // 选择模式下添加操作列（绑定操作）
      baseColumns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: selectOperationRender,
      })
    }
  }

  return baseColumns
})

// 普通操作列渲染函数
function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleEdit(row)} type="primary" link>
        编辑
      </el-button>
      <el-button
        onClick={() => handleChangeIp(row)}
        disabled={!(row.type == 2 && row.domainCode == 'plf_prov_moc_zj_vmware' && row.subnetId)}
        type="primary"
        link
      >
        变更IP
      </el-button>
      <el-button onClick={() => handleDelete(row)} type="primary" link>
        删除
      </el-button>
    </>
  )
}

// 选择模式下的操作列渲染函数
function selectOperationRender({ row }: any): VNode {
  return (
    <>
      <el-button
        onClick={() => handleSelect(row)}
        type="primary"
        disabled={props.queryParams.operateType == 'BIND' && row.vmId}
        link
      >
        {props.queryParams.operateType == 'BIND' ? '绑定' : '解绑'}
      </el-button>
    </>
  )
}

// 选择虚拟网卡
const handleSelect = (row: any) => {
  emit('selected', row, props.queryParams.operateType)
}

const proTable = ref<ProTableInstance>()

// 使用变更IP hooks
const {
  changeIpDialogVisible,
  changeIpForm,
  ipOptions,
  submitLoading,
  handleChangeIp,
  handleSubmitChangeIp,
  closeChangeIpDialog,
} = useChangeIp()

// 包装提交函数，传入刷新回调
const handleSubmitChangeIpWithRefresh = () => {
  handleSubmitChangeIp(() => proTable.value?.getTableList())
}

const router = useRouter()
const handleEdit = (row: any) => {
  router.push({
    path: '/virtualNicCreate',
    query: { id: row.id },
  })
}

// 删除虚拟网卡
const handleDelete = async (row: any) => {
  ElMessageBox.confirm(`确定要删除虚拟网卡 "${row.vnicName}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const res = await deleteVirtualNic({
          id: row.id,
        })
        if (res.code == 200) {
          ElMessage.success('删除成功')
          proTable.value?.getTableList()
        }
      } catch (error) {
        console.error('删除虚拟网卡失败', error)
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 处理查看详情
const handleViewDetail = (row: any) => {
  // 跳转到虚拟网卡详情页面
  router.push({
    path: '/virtualNicDetail',
    query: {
      id: row.id,
      sourceType: 'DG',
    },
  })
}

defineExpose({
  proTable,
})
</script>
