<template>
  <div class="table-main">
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="changeWorkOrderPageApi"
      :init-param="queryParams"
      :current-change="currentChange"
      hidden-table-header
      row-key="goodsOrderId"
    >
    </SlProTable>
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode } from 'vue'
import { useRouter } from 'vue-router'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { changeWorkOrderPageApi } from '@/api/modules/orderCenter'

const router = useRouter()

const { queryParams } = defineProps<{
  queryParams: Record<string, any>
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 跳转到详情页面
const goToDetail = (row: any) => {
  router.push({
    path: '/changeDetail',
    query: {
      id: row.id,
    },
  })
}

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'orderCode',
    label: '订单编号',
    width: 300,
    fixed: 'left',
    render: ({ row }) => {
      return (
        <el-button onClick={() => goToDetail(row)} type="primary" link>
          {row.orderCode}
        </el-button>
      )
    },
  },
  { label: '变更人', prop: 'createdUserName', minWidth: 100 },
  { label: '变更时间', prop: 'createTime', minWidth: 100 },
  {
    prop: 'operation',
    label: '操作',
    width: 100,
    fixed: 'right',
    render: operationRender,
  },
])

function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => goToDetail(row)} type="primary" link>
        详情
      </el-button>
    </>
  )
}

const proTable = ref<ProTableInstance>()

defineExpose({})
</script>
