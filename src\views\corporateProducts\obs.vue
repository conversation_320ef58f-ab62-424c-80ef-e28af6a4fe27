<template>
  <div>
    <div>
      <sl-page-header title="对象存储"></sl-page-header>
    </div>
    <sl-form
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formData"
      :label-width="120"
      ref="formRef"
    >
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
          <el-button type="primary" plain class="cart-btn" @click="handleAddToCart">
            加入清单
          </el-button>
          <el-button type="primary" class="order-btn" @click="handleDirectOrder" v-if="vifOpened">
            直接开通
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { markRaw, ref, computed } from 'vue'
import CustomRadio from './components/CustomRadio.vue'
import RegionSelect from './components/RegionSelect.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import CustomTable from './components/CustomTable.vue'
import { useTenant } from './hooks/useTenant'
import {
  getEcsSpecList,
  corporateOrderApi,
  corporateOrderTemSaveApi,
} from '@/api/modules/resourecenter'
import useGetTheParameters from './hooks/useGetTheParameters'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import SlForm from '@/components/form/SlForm.vue'
import { watch } from 'vue'
import { validObsName } from '../resourceCenter/utils'
import eventBus from '@/utils/eventBus'
import { showTips } from '@/utils'
import { useAuthStore } from '@/stores/modules/auth'
const authStore = useAuthStore()
const vifOpened = computed(() =>
  authStore.authButtonListGet?.viewOfPublicTenants?.includes('Opened'),
)

const flavorMode = ref<any[]>([])
const getFlavorMode = async (regionId: string) => {
  const { entity } = await getEcsSpecList({ type: 'obs', pageSize: 10000, regionId })
  if (entity.records.length === 0) {
    showTips('该资源池暂无该产品')
    formData.value.resourcePool = null
  }
  flavorMode.value = entity.records.map((item: any) => ({
    label: item.name,
    value: item,
  }))
}

const { tenantList: tenantListOptions } = useTenant()
const router = useRouter()
const formData = ref<FormDataType>({
  isBindEip: false,
  tenant: null,
  paymentType: 'month',
  domain: null,
  resourcePool: null,
  az: null,
  region: 'placeholder',
  obs: [
    {
      name: '',
      type: '',
      capacity: '',
      quantity: '',
    },
  ],
  catalogueDomainCode: 'cloudst_group_moc',
  catalogueDomainName: '移动云',
})

watch(
  () => formData.value.resourcePool?.code,
  (newCode: string, oldCode: string) => {
    // 只有当code真正改变时才执行
    if (newCode !== oldCode) {
      formData.value.obs.forEach((item: any) => {
        item.type = null
      })
      // 如果选择了资源池，获取对应的可用区
      if (formData.value.resourcePool && formData.value.resourcePool.id) {
        getFlavorMode(formData.value.resourcePool.id)
      }
    }
  },
)

const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '区域',
        type: 'component',
        key: 'region',
        span: 24,
        component: markRaw(RegionSelect),
        props: {
          maxlength: 64,
        },
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formData.value.domain) {
                callback(new Error('请选择云平台'))
              }
              if (!formData.value.resourcePool) {
                callback(new Error('请选择资源池'))
              }
              if (!formData.value.az) {
                callback(new Error('请选择可用区'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: '租户',
        type: 'select',
        key: 'tenant',
        span: 10,
        options: tenantListOptions,
        props: {
          select: {
            valueKey: 'id',
          },
        },
        required: true,
        rules: [{ required: true, message: '请选择租户', trigger: ['blur', 'change'] }],
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '付费类型',
        type: 'component',
        key: 'paymentType',
        span: 24,
        options: [
          {
            label: '按日付费',
            value: 'day',
          },
          {
            label: '按月付费',
            value: 'month',
          },
          {
            label: '按年付费',
            value: 'year',
          },
          {
            label: '按量计费',
            value: 'quant',
          },
          {
            label: '按需计费',
            value: 'require',
          },
        ],
        component: markRaw(CustomRadio),
        props: {
          maxlength: 64,
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '存储',
        type: 'component',
        key: 'obs',
        span: 24,
        component: markRaw(CustomTable),
        columns: [
          {
            prop: 'name',
            label: '名称',
            width: 180,
            type: 'input',
            placeholder: '请输入名称',
          },
          {
            prop: 'type',
            label: '类型',
            width: 180,
            type: 'select',
            placeholder: '请选择类型',
            props: {
              select: {
                valueKey: 'id',
                clearable: true,
              },
            },
            options: flavorMode,
          },
          {
            prop: 'capacity',
            label: '容量',
            width: 200,
            type: 'number',
            min: 1,
            max: 1000,
            unit: 'GB',
            inputWidth: '120px',
          },
          {
            prop: 'quantity',
            label: '数量',
            width: 180,
            type: 'number',
            min: 1,
            max: 10,
            inputWidth: '100px',
          },
          {
            prop: 'action',
            label: '操作',
            width: 60,
            type: 'action',
            action: 'delete',
          },
        ],
        canAdd: true,
        maxRows: 10,
        addButtonText: '添加存储',
        emptyText: '暂无存储配置',
        showEmptyState: true,
        defaultRow: {
          type: 'SSD',
          capacity: 1,
          quantity: 1,
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              配置概要
            </SlBlockTitle>
          )
        },
      },
      {
        label: '区域',
        type: 'text',
        getter: (form: any) =>
          form.az?.name ? `${form.domain?.name} - ${form.resourcePool.name} - ${form.az.name}` : '',
        span: 8,
      },
      {
        label: '所属租户',
        type: 'text',
        getter: (form: any) => form.tenant?.name || '',
        span: 8,
      },
      {
        label: '付费类型',
        type: 'text',
        getter: (form: any) =>
          form.paymentType === 'day'
            ? '按日付费'
            : form.paymentType === 'month'
              ? '按月付费'
              : '按年付费',
        span: 8,
      },
      {
        label: '存储',
        type: 'text',
        getter: (form: any) =>
          form.obs
            ?.map((item: any) => {
              return `${item.name ?? ''} / ${item.type?.name ?? ''} / ${item.capacity}GB / ${item.quantity}`
            })
            .join(',') || '',
        span: 24,
      },
    ],
  },
])

const formRef = ref<InstanceType<typeof SlForm>>()

// 处理取消操作
const handleCancel = () => {
  router.go(-1)
  eventBus.emit('corporateShoppingList:updateCount')
}

// 处理加入清单操作
const handleAddToCart = async () => {
  try {
    // 1. 校验表单数据
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 验证每个存储配置
    for (let i = 0; i < formData.value.obs.length; i++) {
      const obs = formData.value.obs[i]
      // 校验名称

      if (!obs.name) {
        ElMessage.error(`第${i + 1}个存储请输入名称`)
        return
      }

      if (!validObsName(obs.name)) {
        ElMessage.error(`第${i + 1}个存储名称不符合规范`)
        return
      }
      if (!obs.type) {
        ElMessage.error(`第${i + 1}个存储请选择类型`)
        return
      }
      if (!obs.capacity || obs.capacity <= 0) {
        ElMessage.error(`第${i + 1}个存储请输入有效容量`)
        return
      }
      if (!obs.quantity || obs.quantity <= 0) {
        ElMessage.error(`第${i + 1}个存储请输入有效数量`)
        return
      }
    }

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'obs', false)

    // 3. 调用API
    await corporateOrderTemSaveApi(params)
    ElMessage.success('加入清单成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}

// 处理直接开通操作
const handleDirectOrder = async () => {
  try {
    // 1. 校验表单数据
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 验证每个存储配置
    for (let i = 0; i < formData.value.obs.length; i++) {
      const obs = formData.value.obs[i]
      if (!obs.name) {
        ElMessage.error(`第${i + 1}个存储请输入名称`)
        return
      }
      if (!validObsName(obs.name)) {
        ElMessage.error(`第${i + 1}个存储名称不符合规范`)
        return
      }
      if (!obs.type) {
        ElMessage.error(`第${i + 1}个存储请选择类型`)
        return
      }
      if (!obs.capacity || obs.capacity <= 0) {
        ElMessage.error(`第${i + 1}个存储请输入有效容量`)
        return
      }
      if (!obs.quantity || obs.quantity <= 0) {
        ElMessage.error(`第${i + 1}个存储请输入有效数量`)
        return
      }
    }

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'obs')

    // 3. 调用API
    await corporateOrderApi(params)
    ElMessage.success('发起开通成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}
</script>

<style scoped>
/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}
.corporate-products​ {
  min-height: calc(100vh - 190px);
}
/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
