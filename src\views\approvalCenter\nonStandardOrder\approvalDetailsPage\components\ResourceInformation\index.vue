<template>
  <div>
    <div class="sl-card mb8 no-card" v-if="nonStandardOrderTabs.length">
      <sl-block-title title="申请产品明细"> </sl-block-title>
      <div class="orderDetailTab">
        <sl-tabs
          class="mb10"
          show-count
          :tabs="nonStandardOrderTabs"
          v-model="nonStandardOrderType"
        ></sl-tabs>
      </div>

      <template v-for="item in nonStandardOrderTabs" :key="item.label">
        <template v-if="item.name === nonStandardOrderType">
          <ModelList
            v-model:goods-list="item.list"
            :goods-type="nonStandardOrderType"
            :network-dic="networkDic"
            :order-desc="orderDescRef"
          />
        </template>
      </template>

      <!-- 资产信息 -->
      <sl-form
        class="mt50"
        v-if="pageStatus && btnAuth?.response_scheme_manager"
        v-model="orderDescRef"
        :show-block-title="false"
        ref="slFormRef"
        :options="visibleFields"
        :dic-collection="{}"
        :gutter="20"
        label-width="175px"
      >
        <template #productTotalCost>
          <el-input-number
            type="number"
            v-model="orderDescRef.productTotalCost"
            placeholder="请输入"
            :precision="2"
            :min="0"
          >
            <!--   :controls="false"

            v-disable-number-wheel v-positive-number -->
            <template #prefix>￥</template>
            <template #suffix>元</template>
          </el-input-number>
        </template>
      </sl-form>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { inject, nextTick, ref } from 'vue'
import type {
  NonStandardOrderBtnsType,
  NonStandardOrderTabsType,
  NonStandardOrderType,
} from '../../../interface/type'
import { nonStandardOrderNameKey, nonStandardOrderValueEnum } from './goodsColumns'
import ModelList from './ModelList.vue'
import { useVModel } from '@vueuse/core'
import { areAllValuesNotEmpty, isEqualWithTypeCheck, showTips, uniqueByKeys } from '@/utils'
import { getVpcStatusApi, getvpcTreeApi } from '@/api/modules/approvalCenter'
import { useRoute } from 'vue-router'

const props = defineProps<{
  orderDesc: FormDataType
}>()
const emit = defineEmits(['refresh', 'update:disabledBtn', 'update:orderDesc'])

const orderDescRef = useVModel(props, 'orderDesc', emit)

const pageStatus = inject('pageStatus', ref(true))

const btnAuth = inject('btnAuth', ref<NonStandardOrderBtnsType>())

const nonStandardOrderType = ref<NonStandardOrderType>('ecs')
const nonStandardOrderTabs = ref<NonStandardOrderTabsType[]>([])

const initData = (data: any, flag: boolean = true) => {
  const tabs: NonStandardOrderTabsType[] = []
  nonStandardOrderValueEnum.map((item) => {
    if (data[item.goodsList] && data[item.goodsList].length) {
      const list = data[item.goodsList].map((good: any) => {
        const originName =
          good[
            nonStandardOrderNameKey[
              item.code as keyof typeof nonStandardOrderNameKey
            ] as keyof typeof good
          ] ?? undefined
        return {
          ...good,
          goodsName: originName,
          vpcName: good.planeNetworkModel?.[0] ? (good.planeNetworkModel[0]?.name ?? '') : '',
          subnetName: good.planeNetworkModel?.[0]
            ? (good.planeNetworkModel[0]?.subnets
                ?.map((subnet: any) => subnet.subnetName)
                .join(',') ?? '')
            : '',
        }
      })
      tabs.push({
        label: item.desc,
        name: item.code as NonStandardOrderType,
        count: data[item.goodsList].length,
        list,
      })
    }
  })
  nonStandardOrderTabs.value = tabs
  flag && (nonStandardOrderType.value = tabs[0]?.name || 'ecs')

  // 2.其他信息
  nextTick(() => {
    disabledTenant(tabs)
  })

  // 3.获取字典
  getDic(tabs)
}

const route = useRoute()
// 判断是否禁用
const disabledTenant = (tabs: NonStandardOrderTabsType[]) => {
  if (!tabs.length) return true
  let falg = true

  if (btnAuth.value?.resource_creation) {
    falg = tabs.every((goodItem) => {
      return goodItem.list.every((item) => item.status === 'open_success')
    })
  }

  emit('update:disabledBtn', !falg)
}

//--------------------------------------------------

const getDic = (tabs: NonStandardOrderTabsType[]) => {
  if (!btnAuth.value?.resource_creation) return
  const orderId = route.query.workOrderId as string
  let networkParams: {
    orderId: string
    regionCode: string
    azCode: string
  }[] = []
  tabs.map((goodItem) => {
    goodItem.list.map((good: any) => {
      networkParams.push({
        orderId,
        regionCode: good.regionCode,
        azCode: good.azCode,
      })
    })
  })

  networkParams = uniqueByKeys(networkParams, ['regionCode', 'azCode'])
  Promise.all(
    networkParams.map((item) => {
      return getNetworkDic(item, `${item.regionCode}-${item.azCode}`)
    }),
  )
}

const networkDic = ref<FormDataType>({})
//网络接口
const getNetworkDic = async (params: any, key: string) => {
  if (networkDic.value[key]) return

  const { entity } = await getvpcTreeApi(params)

  let arr = entity.map((item: any) => {
    return {
      ...item,
      name: item.vpcName ? item.vpcName : item.name,
      subnetOrderList: item.subnetOrderList ? item.subnetOrderList : item.vpcSubnetOrderList,
    }
  })
  networkDic.value[key] = arr
}

//--------------------------------------------------
const slFormRef = ref()
const validateForm = async () => {
  //
  if (btnAuth.value?.response_scheme_manager) {
    const falg = await slFormRef.value.validate(() => true)
    if (!falg) {
      showTips('请完善表单信息')

      return falg
    }
    console.log(orderDescRef.value.contractCost + '/', orderDescRef.value.productTotalCost)

    if (
      isEqualWithTypeCheck(
        Number(orderDescRef.value.contractCost),
        Number(orderDescRef.value.productTotalCost),
      )
    ) {
      showTips('合同费用与产品合计成本价格不能相同')
      return false
    }

    try {
      if (isEqualWithTypeCheck(0, Number(orderDescRef.value.productTotalCost))) {
        await ElMessageBox.confirm('当前产品合计成本价格为"0"是否继续吗?', '信息提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        })
      }
      return true
    } catch (error) {
      console.error(error)

      return false
    }
  }

  // 信息归档
  if (btnAuth.value?.information_archive) {
    if (!nonStandardOrderTabs.value.length) return true
    let flagList: string[] = []
    nonStandardOrderTabs.value.map((goodItem) => {
      if (!areAllValuesNotEmpty<any>(goodItem.list, ['archivedIp'])) {
        flagList.push(goodItem.label)
      }
    })
    if (flagList.length) {
      showTips(`请填写${flagList.join('、')}的 IP !`)
      return false
    }
  }

  // 网络开通
  if (btnAuth.value?.network_provisioning) {
    try {
      const res = await getVpcStatusApi(route.query.workOrderId as string)
      if (res.message !== 'success') {
        await ElMessageBox.confirm(res.message, '信息提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        })
      }
      return true
    } catch (error) {
      console.error(error)
      return false
    }
  }
  return true
}

const submitForm = async () => {
  if (btnAuth.value?.information_archive) {
    const modelList: {
      id: string
      archivedIp: string
    }[] = []
    nonStandardOrderTabs.value.map((goodItem) => {
      goodItem.list.map((good: any) => {
        modelList.push({
          id: good.id,
          archivedIp: good.archivedIp,
        })
      })
    })
    return {
      modelList,
    }
  }

  return {}
}

//--------------------------------------------------
const visibleFields = ref([
  {
    groupName: '补充信息',
    groupItems: [
      {
        label: '产品合计成本价格',
        type: 'slot',
        key: 'productTotalCost',
        slotName: 'productTotalCost',
        keyName: 'productTotalCost',
        rules: [{ required: true, message: '请输入产品合计成本价格', trigger: 'blur' }],
        beforeText: '￥',
        afterText: '元',
        span: 8,
      },
    ],
  },
])

defineExpose({
  initData,
  validateForm,
  submitForm,
})
</script>

<style lang="scss" scoped>
.orderDetailTab {
  background-color: #f2f3f5;
  position: relative;
}
.tip {
  font-size: 14px;
  line-height: 30px;
  color: red;
}
.tip-message {
  color: #000;
}
</style>
