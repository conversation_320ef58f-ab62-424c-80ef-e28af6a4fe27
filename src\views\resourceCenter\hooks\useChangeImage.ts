import { ref } from 'vue'

/**
 * 更换镜像功能hooks
 */
export function useChangeImage() {
  // 更换镜像相关状态
  const changeImageDialogVisible = ref(false)
  const currentImageChangeRow = ref<any>({})

  /**
   * 判断是否支持更换镜像功能
   * @param row 资源行数据
   * @returns 是否支持更换镜像
   */
  function supportsChangeImage(row: any): boolean {
    // 支持更换镜像的技术栈：VMware、H3C、plf
    return [
      'plf_prov_moc_zj_vmware',
      'plf_prov_moc_zj_h3c',
      'plf_prov_nwc_zj_plf',
      'plf_prov_it_zj_plf',
    ].includes(row.domainCode)
  }

  /**
   * 打开更换镜像弹窗
   * @param row 资源行数据
   */
  function handleChangeImage(row: any) {
    currentImageChangeRow.value = row
    changeImageDialogVisible.value = true
  }

  /**
   * 更换镜像成功回调
   * @param refreshCallback 刷新表格的回调函数
   */
  function handleChangeImageSuccess(refreshCallback?: () => void) {
    changeImageDialogVisible.value = false
    if (refreshCallback) {
      refreshCallback()
    }
  }

  return {
    changeImageDialogVisible,
    currentImageChangeRow,
    supportsChangeImage,
    handleChangeImage,
    handleChangeImageSuccess,
  }
}
