<template>
  <div
    class="table-main"
    v-loading.fullscreen.lock="tableLoading"
    element-loading-text="操作中，请稍候..."
  >
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="getCorporateResourceList"
      :init-param="queryParams"
      :current-change="currentChange"
      @selection-change="handleSelectionChange"
      hidden-table-header
      row-key="goodsOrderId"
    >
    </SlProTable>

    <!-- 资源变更弹窗 -->
    <ResourceChangeDialog
      v-model:visible="changeDialogVisible"
      resource-type="eip"
      :selected-resources="selectedResources"
      :allowed-change-types="allowedChangeTypes"
      @confirm="handleConfirm"
    />

    <!-- 资源延期弹窗 -->
    <ResourceChangeDialog
      v-model:visible="delayDialogVisible"
      resource-type="eip"
      :selected-resources="selectedResources"
      :is-delay="true"
      @confirm="handleConfirm"
    />

    <!-- 绑定设备弹窗 -->
    <DeviceBindDialog
      v-model:visible="bindDialogVisible"
      v-model:business-sys-id="currentEip.businessSysId"
      @selectDevice="handleDeviceSelect"
      source-type="DG"
    />
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import {
  getCorporateResourceList,
  dgRecoveryWorkOrderCreate,
  eipBind,
  eipUnbind,
} from '@/api/modules/resourecenter'
import { ElMessage, ElMessageBox } from 'element-plus'
import SlMessage from '@/components/base/SlMessage'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import DeviceBindDialog from './components/DeviceBindDialog.vue'
import type { ResourceChangeType } from '../types'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import { useRouter } from 'vue-router'

const router = useRouter()
const { queryParams, hideOperations } = defineProps<{
  queryParams: Record<string, any>
  hideOperations?: boolean
}>()

const { validateResources } = useResourceChange()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'deviceName',
    label: '弹性公网名称',
    minWidth: 200,
    fixed: 'left',
    render: ({ row }) => (
      <el-button onClick={() => handleViewDetail(row)} type="primary" link>
        {row.deviceName}
      </el-button>
    ),
  },
  { prop: 'eip', label: '弹性公网地址', minWidth: 150 },
  { prop: 'deviceId', label: '资源ID', width: 200 },
  {
    prop: 'billType',
    label: '计费类型',
    width: 100,
    render: ({ row }) => formatBillType(row.billType),
  },
  { prop: 'bandWidth', label: '带宽', minWidth: 100 },
  { prop: 'relatedDeviceType', label: '绑定设备类型', minWidth: 150 },
  { prop: 'relatedDeviceName', label: '绑定设备名称', minWidth: 150 },
  { prop: 'tenantName', label: '租户', minWidth: 120 },
  { prop: 'cloudPlatform', label: '所属云', minWidth: 100, filter: true },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 150, filter: true },
  { prop: 'orderCode', label: '订单编号', minWidth: 150 },
  { prop: 'effectiveTime', label: '订购时间', minWidth: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 },
  { prop: 'applyUserName', label: '订购人', minWidth: 100 },
])

if (!hideOperations) {
  columns.push({
    prop: 'operation',
    label: '操作',
    width: 100,
    fixed: 'right',
    render: operationRender,
  })
}
function operationRender({ row }: any): VNode {
  return (
    <>
      {row.relatedDeviceId ? (
        <el-button
          onClick={() => handleUnbind(row)}
          type="primary"
          disabled={row.recoveryStatus != 0 || row.changeStatusCn !== '未变更'}
          v-permission="Unbind"
          link
        >
          解绑
        </el-button>
      ) : (
        <el-button
          onClick={() => handleBind(row)}
          type="primary"
          disabled={row.recoveryStatus != 0 || row.changeStatusCn !== '未变更'}
          v-permission="Bind"
          link
        >
          绑定
        </el-button>
      )}
    </>
  )
}

const proTable = ref<ProTableInstance>()
const tableLoading = ref(false)

const currentRecycleIdsList = ref<any[]>([])

// 使用回收校验钩子函数
const { validateUnsubscribe, validateChange, getDgFormData } = useRecycleValidation()

const handleCreateUnsubscribe = async (goodsItems: any[]) => {
  const res = await dgRecoveryWorkOrderCreate(getDgFormData('eip', goodsItems))
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  SlMessage.success('已提交退订')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}

// 批量回收功能
const handleBatchUnsubscribe = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateUnsubscribe(selectedList, 'eip')) {
    currentRecycleIdsList.value = selectedList.map((i) => ({ goodsId: i.id.trim() }))
    handleCreateUnsubscribe(currentRecycleIdsList.value)
  }
}

// 多选数据，使用计算属性优化性能
const multipleSelection = ref<any[]>([])
const changeDialogVisible = ref(false)
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])

// 使用计算属性缓存处理过的资源列表以提高性能
const processedResources = computed(() => {
  if (multipleSelection.value.length === 0) return []
  return multipleSelection.value.map((resource) => ({
    ...resource,
    _resourceId: resource.id, // 添加一个唯一标识，用于跟踪变化
  }))
})

// 使用计算属性判断是否禁用变更和延期按钮
const isChangeDisabled = computed(() => multipleSelection.value.length === 0)

// EIP允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = ['bandwidth_expand']

// 重构资源变更处理方法，性能更好
const handleResourceChange = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要变更的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

const handleResourceDelay = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要延期的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

// 处理确认
const handleConfirm = () => {
  proTable.value?.getTableList()
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 绑定设备相关变量
const bindDialogVisible = ref(false)
const currentEip = ref<any>({})
const bindingLoading = ref('')
const unbindingLoading = ref('')

// 处理绑定设备
const handleBind = (row: any) => {
  currentEip.value = row
  bindDialogVisible.value = true
}

// 处理设备选择
const handleDeviceSelect = async (device: any) => {
  if (bindingLoading.value) return
  if (!currentEip.value.id) return

  try {
    bindingLoading.value = currentEip.value.id
    tableLoading.value = true
    await eipBind({
      id: currentEip.value.id,
      deviceId: device.deviceId,
      deviceType: device.deviceType,
    })

    ElMessage.success('绑定设备成功')
    proTable.value?.getTableList()
    bindDialogVisible.value = false
    currentEip.value = {}
  } catch (error) {
    console.error(error)
  } finally {
    bindingLoading.value = ''
    tableLoading.value = false
  }
}

// 处理解绑设备
const handleUnbind = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要解绑该设备吗？', '解绑确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    unbindingLoading.value = row.id
    tableLoading.value = true
    const res = await eipUnbind({
      id: row.id,
    })

    if (res.code !== 200) {
      return
    }

    ElMessage.success('解绑设备成功')
    proTable.value?.getTableList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  } finally {
    unbindingLoading.value = ''
    tableLoading.value = false
  }
}

// 处理查看详情
const handleViewDetail = (row: any) => {
  // 跳转到EIP详情页面
  router.push({
    path: '/eipDetail',
    query: {
      id: row.id,
      sourceType: 'DG',
    },
  })
}

// 格式化计费类型
const formatBillType = (type: string): string => {
  const billTypeMap: Record<string, string> = {
    day: '按天计费',
    month: '按月计费',
    year: '按年计费',
  }
  return billTypeMap[type] || type || '--'
}

defineExpose({
  handleBatchUnsubscribe,
  handleResourceChange,
  handleResourceDelay,
})
</script>
