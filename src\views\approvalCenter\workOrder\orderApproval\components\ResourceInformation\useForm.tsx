import { getCloudPlatformDic, getCloudTypeDic, getResourcePoolsDic } from '@/api/modules/dic'
import { ref, watchEffect } from 'vue'

/**
 * 公用的资源管理下拉框公用方法
 * 做了专门开通工单的适配 这里使用的时候注意
 */
export const useResourceForm = (
  from: FormDataType = {
    catalogueDomainCode: '', //云类型
    domainCode: '', //云平台
    securityDomain: '', //安全域
    // resourcePoolName: '' //资源池
    resourcePoolId: '', //资源池
  },
  isSecurityDomain: boolean = false,
) => {
  const cloudTypeDic = ref<FormDataType[]>([]) //云类型字典
  const cloudPlatformDic = ref<FormDataType[]>([]) //云平台字典
  const resourcePoolsDic = ref<FormDataType[]>([]) // 资源池字典
  const securityDomainDic = ref<FormDataType[]>([]) // 安全域字典
  // 获取云类型字典
  const ininData = async () => {
    const { entity } = await getCloudTypeDic(null)

    cloudTypeDic.value = entity
  }
  ininData()

  // 获取云平台字典
  const getDomainCode = async () => {
    const { entity } = await getCloudPlatformDic({
      parentCode: from.catalogueDomainCode,
    })
    cloudPlatformDic.value = entity
  }
  watchEffect(() => {
    if (from.catalogueDomainCode) getDomainCode()
  })

  // 获取安全域字典 和 资源池字典
  const getSecurityDomain = async () => {
    if (!from.domainCode) return
    const { entity } = await getResourcePoolsDic({
      domainCode:
        from.domainCode === 'plf_prov_moc_zj_zhisuan' ? 'plf_prov_moc_zj_vmware' : from.domainCode,
    })
    resourcePoolsDic.value = entity
    if (!isSecurityDomain && from.domainCode !== 'plf_prov_nwc_zj_mni') return
  }
  watchEffect(() => {
    if (from.domainCode) getSecurityDomain()
  })

  return {
    cloudTypeDic, // 云类型字典
    cloudPlatformDic, // 云平台字典
    resourcePoolsDic, // 资源池字典
    securityDomainDic, // 安全域字典
    // 事件
    getDomainCode, // 云类型change事件
    getSecurityDomain, // 云平台change事件
  }
}
