<template>
  <div>
    <el-row :gutter="24">
      <el-col :span="4">
        <el-select
          clearable
          filterable
          v-model="model.domain"
          placeholder="请选择云平台"
          value-key="code"
        >
          <el-option
            v-for="item in cloudPlatformOptions"
            :key="item.value.code"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
      <el-col :span="4">
        <el-select
          clearable
          filterable
          v-model="model.resourcePool"
          placeholder="请选择资源池"
          value-key="code"
        >
          <el-option
            v-for="item in resourcePoolOptions"
            :key="item.value.code"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
      <el-col v-if="!props.item.noAz" :span="4">
        <el-select
          clearable
          filterable
          v-model="model.az"
          placeholder="请选择可用区"
          value-key="code"
        >
          <el-option
            v-for="item in azOptions"
            :key="item.value.code"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { getAzListDic, getResourcePoolsDic, getCloudPlatformDic } from '@/api/modules/dic'

const props = defineProps<{
  item: any
  form: any
}>()
const model = props.form

// select 配置项
const resourcePoolOptions = ref<any>([])
const azOptions = ref<any>([])
const cloudPlatformOptions = ref<any>([])

async function getAzOptions(regionId: string) {
  const { entity } = await getAzListDic({ regionId: regionId })
  if (entity) {
    azOptions.value = entity.map((e: any) => ({
      value: e,
      label: e.name,
    }))
  }
}

const getResourcePools = async (domainCode: string = '') => {
  const { entity } = await getResourcePoolsDic({
    domainCode,
    realmType: 'iaas',
  })
  resourcePoolOptions.value = entity.map((e: any) => ({
    value: e,
    label: e.name,
  }))
}

// 云平台
const getCloudPlatform = async () => {
  const { entity } = await getCloudPlatformDic({
    parentCode: model.catalogueDomainCode,
    businessCode: 'corporate',
  })
  cloudPlatformOptions.value = entity.map((e: any) => ({
    value: e,
    label: e.name,
  }))
}

// 监听云平台选择变化
watch(
  () => model.domain?.code,
  (newCode, oldCode) => {
    console.log('云平台变化:', { newCode, oldCode, domain: model.domain })
    // 只有当code真正改变时才执行
    if (newCode !== oldCode) {
      // 重置下级选择
      model.resourcePool = null
      resourcePoolOptions.value = []
      model.az = null
      azOptions.value = []

      // 如果选择了云平台，获取对应的资源池
      if (newCode) {
        getResourcePools(newCode)
      }
    }
  },
)

// 监听资源池选择变化
watch(
  () => model.resourcePool?.code,
  (newCode, oldCode) => {
    console.log('资源池变化:', { newCode, oldCode, resourcePool: model.resourcePool })
    // 只有当code真正改变时才执行
    if (!props.item.noAz && newCode !== oldCode) {
      // 重置可用区
      model.az = null
      azOptions.value = []
      // 如果选择了资源池，获取对应的可用区
      if (model.resourcePool && model.resourcePool.id) {
        getAzOptions(model.resourcePool.id)
      }
    }
  },
)

onMounted(async () => {
  await getCloudPlatform()
})

// 清空可用区字典
const clearAzOptions = () => {
  azOptions.value = []
}

defineExpose({
  clearAzOptions,
})
</script>
