// import SlLayout from '@/views/SlLayout.vue'
import type { RouteRecordRaw } from 'vue-router'

const approvalCenterRouter: RouteRecordRaw[] = [
  {
    path: '/workOrder',
    component: () => import('@/views/approvalCenter/workOrder/index.vue'),
    name: 'workOrder',
    meta: {
      title: '开通工单管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/orderApproval',
    component: () => import('@/views/approvalCenter/workOrder/orderApproval/index.vue'),
    name: 'orderApproval',
    meta: {
      title: '开通工单审批',
      icon: 'icon-gongdan',
      activeMenu: 'workOrder',
    },
  },
  {
    path: '/recycleWorkOrder',
    component: () => import('@/views/approvalCenter/recyclingWorkOrders/index.vue'),
    name: 'recycleWorkOrder',
    meta: {
      title: '回收工单管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/recyclingPage',
    component: () =>
      import('@/views/approvalCenter/recyclingWorkOrders/approvalDetailsPage/index.vue'),
    name: 'recyclingPage',
    meta: {
      title: '回收审批',
      icon: 'icon-gongdan',
      activeMenu: 'recycleWorkOrder',
    },
  },
  {
    path: '/nonStandardOrder',
    component: () => import('@/views/approvalCenter/nonStandardOrder/index.vue'),
    name: 'nonStandardOrder',
    meta: {
      title: '非标工单管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/changeWorkOrder',
    component: () => import('@/views/approvalCenter/changeWorkOrders/index.vue'),
    name: 'changeWorkOrder',
    meta: {
      title: '变更工单管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/nonStandardOrderPage',
    component: () =>
      import('@/views/approvalCenter/nonStandardOrder/approvalDetailsPage/index.vue'),
    name: 'nonStandardOrderPage',
    meta: {
      title: '非标审批',
      icon: 'icon-gongdan',
      activeMenu: 'nonStandardOrder',
    },
  },
  {
    path: '/nonStandardOrderCreate',
    component: () => import('@/views/approvalCenter/nonStandardOrder/create/index.vue'),
    name: 'nonStandardOrderCreate',
    meta: {
      title: '非标工单创建',
      icon: 'icon-gongdan',
      activeMenu: 'nonStandardOrder',
    },
  },
  {
    path: '/changePage',
    component: () =>
      import('@/views/approvalCenter/changeWorkOrders/approvalDetailsPage/index.vue'),
    name: 'changePage',
    meta: {
      title: '变更审批',
      icon: 'icon-gongdan',
      activeMenu: 'changeWorkOrder',
    },
  },
]

export default approvalCenterRouter
