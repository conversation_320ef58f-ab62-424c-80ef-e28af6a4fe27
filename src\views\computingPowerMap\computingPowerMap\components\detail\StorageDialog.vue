<template>
  <div class="bottom-dialog-container">
    <!-- <img @click="closeBottomDialog" src="/images/computingPower/comPowerIconClose.png" alt="" /> -->
    <div class="bottom-dialog-content">
      <div class="bottom-dialog-main">
        <div class="bottom-dialog-title">
          <div>存储</div>
          <div>
            <img src="/images/computingPower/comPowerDialogTitleBg.png" alt="" />
          </div>
        </div>
        <div class="bottom-dialog-tooltip">
          <div class="bottom-dialog-search">
            <el-select
              v-model="selectedCloudName"
              placeholder="请选择云类型"
              :suffix-icon="CaretBottom"
              style="width: 130px"
              clearable
              @change="handleCloudNameChange"
            >
              <el-option
                v-for="item in cloudOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="selectedPlatformTypeName"
              placeholder="请选择云平台"
              :suffix-icon="CaretBottom"
              style="width: 130px"
              clearable
              @change="handlePlatformTypeChange"
            >
              <el-option
                v-for="item in platformTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="selectedRegionId"
              placeholder="请选择资源池"
              :suffix-icon="CaretBottom"
              style="width: 130px"
              @change="handleRegionChange"
            >
              <el-option
                v-for="item in regionOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button class="exportBtn" @click="onExport" :loading="exportLoading">
              <img src="/images/computingPower/comPowerIconExport.png" alt="" />
              导出
            </el-button>
          </div>
        </div>
        <div class="bottom-dialog-table">
          <el-auto-resizer>
            <template #default="{ height }">
              <el-table
                ref="tableRef"
                class="comPowerTable"
                :data="tableData"
                :height="height"
                style="width: 100%"
              >
                <el-table-column prop="index" label="序号" width="60" />
                <el-table-column prop="name" label="数据盘" />
                <el-table-column prop="isAttached" label="是否挂载云主机" />
                <el-table-column show-overflow-tooltip prop="vmName" label="云主机">
                  <template #default="scope">
                    <span class="c-comPower-table-cell-blue-theme">{{ scope.row.vmName }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="volumeStatus" label="状态" />
                <el-table-column prop="domainName" label="所属云平台" />
                <el-table-column show-overflow-tooltip prop="regionName" label="所属资源池">
                  <template #default="scope">
                    <span class="c-comPower-table-cell-blue-theme">{{ scope.row.regionName }}</span>
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="appName" label="关联业务系统">
                  <template #default="scope">
                    <span class="c-comPower-table-cell-blue-theme">{{ scope.row.appName }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-auto-resizer>
        </div>
        <div style="margin-top: 10px">
          <Pagination
            :pageable="pageable"
            :handle-size-change="handleSizeChange"
            :handle-current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CaretBottom } from '@element-plus/icons-vue'
import { reactive, ref, watch, onMounted } from 'vue'
import {
  getStorageListApi,
  getRegionsListApi,
  getCloudGroupStatsApi,
} from '@/api/modules/computingPowerMap'
import { ElTable } from 'element-plus'
import Pagination from '../pagination.vue'
import { useExport } from '../../hooks/useExport'

// 定义接口请求参数类型
interface StorageRequestParams {
  cloudName?: string
  platformTypeName?: string
  cityCode?: string
  areaCode?: string
  pageNum?: number
  pageSize?: number
  regionId?: string | number
}

// 定义存储响应数据类型（根据8.9.4接口文档）
interface StorageRecord {
  id: string
  name: string
  domainName: string
  appName: string
  regionName: string
  volumeStatus: string
  vmName: string
  isAttached: string
}

interface StorageResponse {
  records: StorageRecord[]
  size: number
  current: number
  total: number
}

// 定义资源池响应数据类型（根据8.2.3接口文档）
interface RegionOption {
  id: number
  name: string
  code: string
}

// 定义存储数据项类型
interface StorageItem {
  index: number
  id: string
  name: string
  domainName: string
  appName: string
  regionName: string
  volumeStatus: string
  vmName: string
  isAttached: string
}

// table 实例
const tableRef = ref<InstanceType<typeof ElTable>>()

// 定义props接收父组件传递的参数
const props = defineProps({
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

// 定义需要发出的事件类型
// const emit = defineEmits(['close'])

const pageable = reactive({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: 20,
  // 总条数
  total: 0,
})

const handleSizeChange = (val: number) => {
  pageable.pageNum = 1
  pageable.pageSize = val
  fetchStorageData()
}

const handleCurrentChange = (val: number) => {
  pageable.pageNum = val
  fetchStorageData()
}

// 筛选条件
const selectedCloudName = ref<string>('')
const selectedPlatformTypeName = ref<string>('')
// 资源池选择
const selectedRegionId = ref<string | number>('')

// 选项列表
const cloudOptions = ref<Array<{ label: string; value: string }>>([])
const platformTypeOptions = ref<Array<{ label: string; value: string }>>([])

// 原始云平台数据
const originalCloudData = ref<Array<any>>([])

// 资源池选项列表
const regionOptions = ref<RegionOption[]>([])

const { exportLoading, handleExport } = useExport()

const tableData = ref<StorageItem[]>([])

// 获取云平台数据
const fetchCloudPlatformData = async () => {
  try {
    console.log('获取云平台数据')

    const response: { entity: Array<any> | null; code?: number; message?: string } =
      await getCloudGroupStatsApi()

    if (response.code === 200 && response.entity && Array.isArray(response.entity)) {
      // 保存原始数据
      originalCloudData.value = response.entity

      // 提取云类型选项
      const cloudNames = [...new Set(response.entity.map((item) => item.cloudName))].filter(Boolean)
      cloudOptions.value = cloudNames.map((name) => ({ label: name, value: name }))

      // 根据当前选中的云类型更新平台类型选项
      updatePlatformTypeOptions()

      console.log('云平台数据获取成功:', {
        cloudTypes: cloudOptions.value.length,
        platformTypes: platformTypeOptions.value.length,
      })
    } else {
      console.warn('云平台数据接口返回异常:', response)
      // 返回为null或异常时清空数据
      cloudOptions.value = []
      platformTypeOptions.value = []
    }
  } catch (error) {
    console.error('获取云平台数据失败:', error)
    // 异常时清空数据
    cloudOptions.value = []
    platformTypeOptions.value = []
  }
}

// 根据选中的云类型更新平台类型选项
const updatePlatformTypeOptions = () => {
  if (!selectedCloudName.value || !originalCloudData.value.length) {
    platformTypeOptions.value = []
    return
  }

  // 查找选中云类型对应的数据
  const selectedCloudData = originalCloudData.value.find(
    (item) => item.cloudName === selectedCloudName.value,
  )

  if (
    selectedCloudData &&
    selectedCloudData.platformTypes &&
    Array.isArray(selectedCloudData.platformTypes)
  ) {
    platformTypeOptions.value = selectedCloudData.platformTypes.map((item: any) => ({
      label: item.platformTypeName,
      value: item.platformTypeName,
    }))
  } else {
    platformTypeOptions.value = []
  }

  console.log('更新平台类型选项:', {
    cloudName: selectedCloudName.value,
    platformTypes: platformTypeOptions.value.length,
  })
}

// 筛选条件变化处理
const handleCloudNameChange = () => {
  // 重置下级选择
  selectedPlatformTypeName.value = ''
  selectedRegionId.value = ''
  // 重置分页
  pageable.pageNum = 1
  // 更新平台类型选项
  updatePlatformTypeOptions()
  // 重新获取数据
  fetchRegionsList()
  fetchStorageData()
}

const handlePlatformTypeChange = () => {
  // 重置资源池选择
  selectedRegionId.value = ''
  // 重置分页
  pageable.pageNum = 1
  // 重新获取数据
  fetchRegionsList()
  fetchStorageData()
}

// 资源池选择变化处理
const handleRegionChange = () => {
  // 重置分页
  pageable.pageNum = 1
  // 重新获取数据
  fetchStorageData()
}

// 获取资源池列表
const fetchRegionsList = async () => {
  try {
    const requestParams = {
      cloudName: selectedCloudName.value,
      platformTypeName: selectedPlatformTypeName.value,
      cityCode: props.requestParams?.cityCode,
      areaCode: props.requestParams?.areaCode,
    }

    console.log('请求资源池列表:', requestParams)

    const response: { entity: RegionOption[] | null; code?: number; message?: string } =
      await getRegionsListApi(requestParams)

    if (response.code === 200 && response.entity && Array.isArray(response.entity)) {
      regionOptions.value = response.entity
      console.log('资源池列表获取成功:', regionOptions.value.length, '个')
    } else {
      console.warn('资源池列表接口返回异常:', response)
      regionOptions.value = []
    }
  } catch (error) {
    console.error('获取资源池列表失败:', error)
    regionOptions.value = []
  }
}

// 获取存储列表数据
const fetchStorageData = async () => {
  try {
    const requestParams: StorageRequestParams = {
      cloudName: selectedCloudName.value,
      platformTypeName: selectedPlatformTypeName.value,
      cityCode: props.requestParams?.cityCode,
      areaCode: props.requestParams?.areaCode,
      pageNum: pageable.pageNum,
      pageSize: pageable.pageSize,
    }

    // 如果选择了资源池，添加到请求参数
    if (selectedRegionId.value) {
      requestParams.regionId = selectedRegionId.value
    }

    console.log('请求存储数据:', requestParams)

    const response: { entity: StorageResponse | null; code?: number; message?: string } =
      await getStorageListApi(requestParams)

    if (
      response.code === 200 &&
      response.entity &&
      response.entity.records &&
      Array.isArray(response.entity.records)
    ) {
      // 转换数据格式
      tableData.value = response.entity.records.map((item: StorageRecord, index: number) => ({
        index: (pageable.pageNum - 1) * pageable.pageSize + index + 1,
        id: item.id || '-',
        name: item.name || '-',
        domainName: item.domainName || '-',
        appName: item.appName || '-',
        regionName: item.regionName || '-',
        volumeStatus: item.volumeStatus || '-',
        vmName: item.vmName || '-',
        isAttached: item.isAttached || '-',
      }))

      // 更新分页信息
      pageable.total = response.entity.total || 0
      console.log('存储数据获取成功:', tableData.value.length, '条')
    } else {
      console.warn('存储接口返回异常:', response)
      tableData.value = []
      pageable.total = 0
    }
  } catch (error) {
    console.error('获取存储数据失败:', error)
    tableData.value = []
    pageable.total = 0
  }
}

// 新的导出函数
const onExport = async () => {
  const requestParams: any = {
    cloudName: selectedCloudName.value,
    platformTypeName: selectedPlatformTypeName.value,
    cityCode: props.requestParams?.cityCode,
    areaCode: props.requestParams?.areaCode,
  }
  if (selectedRegionId.value) {
    requestParams.regionId = selectedRegionId.value
  }
  await handleExport({
    type: 'storage',
    params: requestParams,
  })
}

// 监听参数变化自动重新请求
watch(
  () => props.requestParams,
  async (newParams: any) => {
    console.log('父组件参数变化:', newParams)

    // 先获取云平台数据（确保选项列表是最新的）
    await fetchCloudPlatformData()

    // 更新筛选条件
    selectedCloudName.value = newParams?.cloudName || ''
    selectedPlatformTypeName.value = newParams?.platformTypeName || ''

    // 根据新的云类型更新平台类型选项
    updatePlatformTypeOptions()

    // 重置资源池选择
    selectedRegionId.value = ''
    // 重新获取资源池列表和存储数据
    fetchRegionsList()
    fetchStorageData()
  },
  { deep: true },
)

// 组件挂载时初始化数据
onMounted(async () => {
  // 先获取云平台数据
  await fetchCloudPlatformData()

  // 然后设置初始值为父组件传入的参数
  selectedCloudName.value = props.requestParams?.cloudName || ''
  selectedPlatformTypeName.value = props.requestParams?.platformTypeName || ''

  // 根据初始云类型更新平台类型选项
  updatePlatformTypeOptions()

  fetchRegionsList()
  fetchStorageData()
})

// 关闭当前弹窗
// const closeBottomDialog = () => {
//   emit('close')
// }
</script>
<style scoped lang="scss">
.bottom-dialog-container {
  height: 100vh;
  // position: fixed;
  // left: 10px;
  // bottom: 10px;
  // width: calc(100% - 494px);
  // height: 436px;
  // z-index: 20;
  box-sizing: border-box;
  //height: 367px;
  & > img {
    position: absolute;
    top: 0;
    right: 10px;
    cursor: pointer;
  }
  .bottom-dialog-content {
    // position: absolute;
    // left: 0;
    // bottom: 0;
    // width: 100%;
    // height: 400px;
    height: 100%;
    padding: 12px 9px 11px 10px;
    box-shadow: 0px 0px 5px 0px #09155c;
    border-radius: 1px;
    box-sizing: border-box;
    background-color: rgba(170, 198, 245, 0.3);
    //background: url('/images/computingPower/comPowerBottomDialogBg.png') no-repeat 0 0;
    //background-size: 100% 100%;
    .bottom-dialog-main {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      padding: 8px 15px;
      border-radius: 1px;
      box-sizing: border-box;
      background: linear-gradient(0deg, rgba(124, 166, 238, 0.92), rgba(10, 52, 103, 0.92));
    }
    //opacity: 0.92;
    .bottom-dialog-title {
      //display: flex;
      div {
        display: inline-block;
        font-size: 18px;
        color: #ffffff;
        width: calc(100% - 100px);
        vertical-align: top;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei';
        img {
          width: 100%;
        }
      }
      & > div:nth-child(1) {
        width: 70px;
        margin-right: 12px;
      }
    }
    .bottom-dialog-tooltip {
      height: 41px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border-bottom: 2px solid #3161b4;
      padding-top: 4px;
      position: relative;
      display: flex;
      align-items: end;
      justify-content: center;
      .bottom-dialog-search {
        display: inline-block;
        vertical-align: top;
        margin-right: 60px;
        position: absolute;
        right: 0;
        top: 0;
        .el-select {
          background: transparent;
          font-size: 15px;
          color: #ffffff;
          margin-right: 13px;
        }
        .exportBtn {
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          img {
            margin-right: 8px;
          }
        }
      }
    }
    .bottom-dialog-table {
      flex: 1;
      overflow: hidden;
      .el-table {
        background: transparent;
        th.el-table__cell {
          background: transparent;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.comPowerPage.el-pagination {
  .el-select__wrapper.el-tooltip__trigger {
    background: #3f6eb8;
    color: #fff;
    box-shadow: none;
    border-radius: 1px;
    border: 1px solid #42659e;
    .el-select__input.is-default,
    .el-select__selected-item.el-select__placeholder {
      color: #fff;
    }
  }
  .el-pagination__total.is-first {
    color: #ffffff;
    font-size: 15px;
  }
  .el-pagination__goto {
    color: #ffffff;
  }
  .el-input__wrapper {
    background: #3f6eb8;
    border: none;
    box-shadow: none;
    .el-input__inner {
      color: #ffffff;
    }
  }
  .el-pagination__classifier {
    color: #ffffff;
  }
}
.comPowerPage.el-pagination.is-background .btn-next:disabled,
.comPowerPage.el-pagination.is-background .btn-prev:disabled {
  background: transparent;
  color: #3f5d78;
}
.comPowerPage.el-pagination.is-background .btn-next,
.comPowerPage.el-pagination.is-background .btn-prev {
  background: transparent;
  color: #0787de;
}
.comPowerPage.el-pagination.is-background .el-pager li {
  background: #3f6eb8;
  padding: 2px 16px;
}
.comPowerPage.el-pagination.is-background .el-pager li.is-active {
  background: #317ced;
}
.comPowerPage.el-pagination .btn-next .el-icon,
.comPowerPage.el-pagination .btn-prev .el-icon {
  font-size: 24px;
}
</style>
