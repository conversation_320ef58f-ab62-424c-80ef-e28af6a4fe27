'use strict'
module.exports = {
  types: [
    { value: 'feat', name: '新增:    新的内容' },
    { value: 'fix', name: '修复:    修复一个Bug' },
    { value: 'doc', name: '文档:    变更的只有文档' },
    { value: 'style', name: '格式:    代码格式修改, 注意不是css修改' },
    { value: 'refactor', name: '重构:    代码重构，注意和特性、修复区分开' },
    { value: 'merge', name: '合并:    代码合并' },
    { value: 'revert', name: '回滚:    代码回退' },
    { value: 'tool', name: '工具:    开发工具变动(构建、脚手架工具等)' },
    { value: 'init', name: '初始化:    初始化项目' },

  ],
  skipQuestions: ['scope'],
  messages: {
    type: '选择一种你的提交类型:',
    subject: '短说明:\n',
    body: '长说明，使用"|"换行(可选)：\n',
    breaking: '非兼容性说明 (可选):\n',
    footer: '关联关闭的BUG编号，例如：#31, #34(可选):\n',
    confirmCommit: '确定提交说明?(yes/no)',
  },
  allowCustomScopes: false,
  allowBreakingChanges: ['特性', '修复'],
  subjectLimit: 100,
}
