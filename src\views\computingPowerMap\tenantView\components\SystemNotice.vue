<template>
  <div id="SystemNotice" v-if="noticeList?.length">
    <div class="header">
      <div class="header-title">系统公告</div>
      <el-link type="primary" @click="handleRouterGo" v-if="noticeList?.length! > 5">
        查看更多 >
      </el-link>
    </div>
    <div class="notices">
      <div class="notice" v-for="(item, index) in noticeList?.slice(0, 5)" :key="index">
        <div class="notice-icon">
          <img src="@/assets/images/overview/icon_leaf_orange.png" alt="" v-if="index == 0" />
          <img src="@/assets/images/overview/icon_leaf_green.png" alt="" v-else />
        </div>
        <div class="notice-body">
          <div class="notice-body__title">{{ item.title }}</div>
          <div class="notice-body__content">{{ item.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="SystemNotice">
import { ref, onMounted } from 'vue'
import { getNoticeListApi } from '@/api/modules/computingPowerMap'
import type { GetNoticeListType } from '@/views/computingPowerMap/tenantView/interface/type'
import SlMessage from '@/components/base/SlMessage'
const noticeList = ref<GetNoticeListType[]>()

const getNoticeList = async () => {
  try {
    const res = await getNoticeListApi()
    noticeList.value = res.entity || []
  } catch (error) {
    console.error('获取公告列表失败:', error)
  }
}

const handleRouterGo = () => {
  SlMessage({
    message: '暂无更多公告，敬请期待...',
    type: 'success',
  })
}
onMounted(() => {
  getNoticeList()
})
</script>
<style lang="scss" scoped>
#SystemNotice {
  padding: 20px;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .header-title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .notice {
    display: flex;
    margin-bottom: 10px;
    .notice-icon {
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
      }
    }
    .notice-body {
      font-size: 14px;
      margin-left: 5px;
      &__content {
        color: #999999;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 显示的行数 */
        line-clamp: 2; /* 标准属性 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal; /* 允许换行 */
      }
    }
  }
}
</style>
