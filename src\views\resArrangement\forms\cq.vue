<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    label-position="top"
    :options="goodsInfoOptions"
    :model-value="formModel"
  >
    <template #vCpusSlot="{ form, item }">
      <el-input-number :min="0" v-bind="item.props || {}" v-model="form[item.key][0]" clearable />
      <span style="margin: 0 4px; max-width: 14px">核</span>
      <el-input-number :min="0" v-bind="item.props || {}" v-model="form[item.key][1]" clearable />
      <span style="margin-left: 4px; max-width: 14px">G</span>
    </template>
    <template #gpuSlot="{ form, item }">
      <div style="display: flex; align-items: center">
        <el-switch
          style="flex-grow: 0.2; margin-right: 4px"
          v-model="form[item.swithKey]"
          active-value="1"
          inactive-value="0"
        />
        <el-input-number
          :controls="false"
          :min="0"
          v-bind="item.props || {}"
          v-model="form[item.key][0]"
          style="min-width: 80px"
          clearable
        />
        <span style="margin: 0 4px; min-width: 30px">算力</span>
        <el-input-number
          style="min-width: 80px"
          :controls="false"
          :min="0"
          v-bind="item.props || {}"
          v-model="form[item.key][1]"
          clearable
        />
        <span style="margin: 0 4px; max-width: 14px">G</span>
        <el-input-number
          style="min-width: 80px"
          :controls="false"
          :min="0"
          v-bind="item.props || {}"
          v-model="form[item.key][2]"
          clearable
        />
        <span style="margin-left: 4px; max-width: 14px">个</span>
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref, watchEffect } from 'vue'
import { type ICqModel, useCqModel } from '@/views/resArrangement/model'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import slForm from '@/components/form/SlForm.vue'

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: ICqModel
}>()

const formModel = reactive(Object.assign(useCqModel(), props.goods))
const slFormRef = ref()

const gupDisabled = ref(false)
watchEffect(() => {
  if (formModel.isUseGpu === '0') {
    gupDisabled.value = true
    if (slFormRef.value) {
      formModel.gpu[0] = 0
      formModel.gpu[1] = 0
      formModel.gpu[2] = 0
      slFormRef.value.clearValidate(['gpuRatio', 'gpuVirtualMemory', 'gpuCore'])
    }
  } else {
    gupDisabled.value = false
    formModel.gpu[0] = formModel.gpu[0] || 0
    formModel.gpu[1] = formModel.gpu[1] || 0
    formModel.gpu[2] = formModel.gpu[2] || 0
  }
})

const validateGpu = (rule: any, value: any, callback: any) => {
  if (formModel.isUseGpu === '0') {
    callback()
  } else {
    if (formModel.gpu[0] === 0) {
      callback(new Error('请输入算力大小'))
    } else if (formModel.gpu[1] === 0) {
      callback(new Error('请输入GPU内存大小'))
    } else if (formModel.gpu[2] === 0) {
      callback(new Error('请输入GPU核心数'))
    } else {
      callback()
    }
  }
}
const validateVcpus = (rule: any, value: any, callback: any) => {
  if (formModel.cpu[0] === 0) {
    callback(new Error('请输入容器配额核心数'))
  } else if (formModel.cpu[1] === 0) {
    callback(new Error('请输入容器配额内存大小'))
  } else {
    callback()
  }
}
const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '4A账号',
        type: 'input',
        key: 'a4Account',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [{ required: true, message: '请输入4A账号', trigger: ['blur', 'change'] }],
      },
      {
        label: '4A账号绑定的手机号',
        type: 'input',
        key: 'a4Phone',
        span: 8,
        rules: [
          {
            required: true,
            message: '请输入4A账号绑定的手机号',
            trigger: ['blur', 'change'],
            pattern: /^1[3-9]\d{9}$/,
          },
        ],
      },
      {
        label: '配额名称',
        type: 'input',
        key: 'instanceName',
        span: 24,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入配额名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '容器配额',
        type: 'slot',
        key: 'cpu',
        span: 24,
        slotName: 'vCpusSlot',
        required: true,
        rules: [{ validator: validateVcpus, trigger: ['blur', 'change'] }],
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 24,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
      {
        label: '是否使用GPU',
        type: 'slot',
        slotName: 'gpuSlot',
        swithKey: 'isUseGpu',
        key: 'gpu',
        props: {
          min: 0,
          step: 1,
          disabled: gupDisabled,
        },
        rules: [{ validator: validateGpu, trigger: ['blur', 'change'] }],
        span: 16,
      },
    ],
  },
])
const validateForm = async () => {
  const res = await slFormRef.value.validate()
  return res
}
const submitForm = async () => {
  const goods = props.goods
  Object.assign(goods, formModel)
}
defineExpose({
  validateForm,
  submitForm,
})
</script>
<style scoped>
.disaster-recovery-tip {
  margin-left: 8px;
  color: var(--el-color-warning);
  flex-basis: auto;
  position: absolute;
  top: 18px;
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
