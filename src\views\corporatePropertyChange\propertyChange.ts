import { type Reactive } from 'vue'
import type { IPropsListItem, IGoodsType } from './model'
import { uuid } from '@/utils'

export interface IGoodsItem<T> {
  id: number
  goodsType: IGoodsType
  orderJson: T
  ref?: Reactive<any>
}

export type ICartsModel<T extends IGoodsType> = IPropsListItem<T>[]

const transform = {
  resourceType: {
    ecs: '云主机',
    rdsMysql: 'MySQL云数据库',
    redis: '通用Redis',
    gcs: 'GPU云主机',
    obs: '对象存储',
    slb: '负载均衡',
    evs: '云硬盘',
    nat: 'NAT网关',
    eip: '弹性公网',
  },
  changeType: {
    instance_spec_change: '实例规格变更',
    storage_expand: '存储扩容',
    bandwidth_expand: '带宽扩容',
    delay: '延期',
  },
}

export function transformData(data: any) {
  data.forEach((item: any) => {
    item.uuid = uuid()
    item.resourceTypeCn =
      transform.resourceType[item.resourceType as keyof typeof transform.resourceType]
    item.props.forEach((prop: any) => {
      prop.changeTypeCn = transform.changeType[prop.changeType as keyof typeof transform.changeType]
      if (prop.changeType === 'storage_expand' && Array.isArray(prop.after) && !prop.after.length) {
        prop.after = JSON.parse(JSON.stringify(prop.before))
      }
      if (prop.changeType === 'bandwidth_expand') {
        prop.before = Number((String(prop.before) || '').replace('Mbps', ''))
        prop.after = Number(prop.after)
      }
      if (prop.changeType === 'instance_spec_change') {
        if (!prop.after.length) {
          prop.after = []
        }
      }
    })
  })
  return data
}
