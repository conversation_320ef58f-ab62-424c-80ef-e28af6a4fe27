<template>
  <div>
    <el-radio-group v-model="model[props.item.key]" class="custom-radio-group">
      <el-radio
        v-for="option in props.item.options"
        :key="option.value"
        :value="option.value"
        class="custom-radio"
        >{{ option.label }}
      </el-radio>
      <slot name="append" />
    </el-radio-group>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{
  item: any
  form: any
}>()
const model = props.form
</script>

<style scoped>
.custom-radio-group {
  display: flex;
  gap: 8px;
}

.custom-radio {
  margin-right: 16px;
  padding: 4px 20px;
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all 0.3s ease;
  background: #f5f5f5;
  font-weight: 400;
}

.custom-radio:hover {
  background: #e8f4ff;
}

.custom-radio.is-checked {
  border-color: #409eff;
  background: #ecf5ff;
  color: #409eff;
}

/* 隐藏原始的单选按钮圆圈 */
.custom-radio :deep(.el-radio__input) {
  display: none;
}

.custom-radio :deep(.el-radio__label) {
  padding-left: 0;
}
</style>
