/**
 * @name 获取参数 - 优化版本
 */

import { uuid } from '@/utils'

// 通用基础参数构建器
const buildBaseParams = (data: any, productType: string) => {
  return {
    productType,
    billType: data.paymentType,
    domainCode: data.domain?.code,
    domainName: data.domain?.name,
    regionId: data.resourcePool?.id,
    regionCode: data.resourcePool?.code,
    regionName: data.resourcePool?.name,
    azId: data.az?.id,
    azCode: data.az?.code,
    azName: data.az?.name,
    tenantId: data.tenant?.id,
    tenantName: data.tenant?.name,
    applyTime: 'two_years',

    // 写死
    catalogueDomainCode: 'cloudst_group_moc',
    catalogueDomainName: '移动云',
  }
}

// 通用 EIP 参数构建器
const buildEipParams = (data: any) => {
  return data.isBindEip
    ? {
        eipModelList: [
          {
            bandwidth: data.bandwidth || 1,
          },
        ],
      }
    : {}
}

// 通用网络参数构建器
const buildNetworkParams = (data: any) => {
  if (!data.vpc) return {}

  return {
    planeNetworkModel: [
      {
        type: data.vpc.type,
        id: data.vpc.id,
        name: data.vpc.vpcName,
        plane: '',
        subnets: [
          {
            subnetId: data.subnet.id,
            subnetName: data.subnet.subnetName,
            subnetIp: data.subnet.cidr,
            ipAddress: data.subnet.ipAddress,
          },
        ],
        sort: 0,
      },
    ],
  }
}

// 参数验证器
// const validateParams = (data: any, type: string): string[] => {
//   const errors: string[] = []

//   // 通用必填字段验证
//   if (!data.domain) errors.push('请选择云平台')
//   if (!data.resourcePool) errors.push('请选择资源池')
//   if (!data.az) errors.push('请选择可用区')
//   if (!data.tenant) errors.push('请选择租户')
//   if (!data.paymentType) errors.push('请选择付费类型')

//   // 特定类型验证
//   switch (type) {
//     case 'ecs':
//       if (!data.instanceName) errors.push('请输入实例名称')
//       if (!data.loginName) errors.push('请输入登录用户名')
//       if (!data.loginPassword) errors.push('请输入登录密码')
//       if (!data.spec) errors.push('请选择规格')
//       if (!data.image) errors.push('请选择镜像')
//       if (!data.vpc) errors.push('请选择VPC')
//       break
//     case 'evs':
//       if (!data.dataDisk || data.dataDisk.length === 0) errors.push('请添加数据盘')
//       break
//     case 'mysql':
//       if (!data.instanceName) errors.push('请输入实例名称')
//       if (!data.dbVersion) errors.push('请选择数据库版本')
//       if (!data.spec) errors.push('请选择规格')
//       break
//     case 'redis':
//       if (!data.instanceName) errors.push('请输入实例名称')
//       if (!data.version) errors.push('请选择Redis版本')
//       if (!data.spec) errors.push('请选择规格')
//       break
//   }

//   return errors
// }

/**
 *
 * @param data form
 * @param type 产品类型
 * @param flag  是否加入清单
 * @returns
 */
const useGetTheParameters = (data: any, type: string, flag: boolean = true) => {
  // 参数验证
  // const validationErrors = validateParams(data, type)
  // if (validationErrors.length > 0) {
  //   throw new Error(`参数验证失败: ${validationErrors.join(', ')}`)
  // }

  let params: any = {
    tenantId: data.tenant?.id,
    tenantName: data.tenant?.name,
  }

  if (type === 'ecs') {
    const baseParams = buildBaseParams(data, 'ecs')
    const eipParams = buildEipParams(data)
    const networkParams = buildNetworkParams(data)

    const ecsModelList = [
      {
        ...baseParams,
        // ECS 特有参数
        flavorId: data.spec?.id,
        flavorName: data.spec?.name,
        flavorType: '通用型',
        imageId: data.imageVersion.value,
        imageOs: data.image?.name,
        imageVersion: data.imageVersion.label,
        mountDataDisk: data.dataDisk?.length > 0,
        mountDataDiskList:
          data.dataDisk?.map((e: any) => ({
            sysDiskSize: e.capacity,
            sysDiskType: e.type,
            openNum: e.quantity,
          })) || [],
        sysDiskType: data.systemDisk?.[0]?.type,
        sysDiskSize: data.systemDisk?.[0]?.capacity,
        openNum: data.number || 1,
        bindPublicIp: data.isBindEip,
        ...eipParams,
        disasterRecovery: false,
        functionalModule: 'app',
        vmName: data.instanceName,
        userName: data.loginName,
        password: data.loginPassword,
        ...networkParams,
      },
    ]
    if (flag) {
      params['ecsModelList'] = ecsModelList
    } else {
      params['orderJson'] = {
        ecsList: ecsModelList,
      }
    }
  }

  if (type === 'gcs') {
    const baseParams = buildBaseParams(data, 'gcs')
    const eipParams = buildEipParams(data)
    const networkParams = buildNetworkParams(data)

    const gcsModelList = [
      {
        ...baseParams,
        // ECS 特有参数
        flavorId: data.spec?.id,
        flavorName: data.spec?.name,
        flavorType: '通用型',
        imageId: data.imageVersion.value,
        imageOs: data.image?.name,
        imageVersion: data.imageVersion.label,
        mountDataDisk: data.dataDisk?.length > 0,
        mountDataDiskList:
          data.dataDisk?.map((e: any) => ({
            sysDiskSize: e.capacity,
            sysDiskType: e.type,
            openNum: e.quantity,
          })) || [],
        sysDiskType: data.systemDisk?.[0]?.type,
        sysDiskSize: data.systemDisk?.[0]?.capacity,
        openNum: data.number || 1,
        bindPublicIp: data.isBindEip,
        ...eipParams,
        disasterRecovery: false,
        functionalModule: 'app',
        vmName: data.instanceName,
        userName: data.loginName,
        password: data.loginPassword,
        ...networkParams,
      },
    ]
    if (flag) {
      params['gcsModelList'] = gcsModelList
    } else {
      params['orderJson'] = {
        gcsList: gcsModelList,
      }
    }
  }

  if (type === 'evs') {
    const baseParams = buildBaseParams(data, 'evs')
    const evsModelList = data.dataDisk.map((item: any) => {
      return {
        ...baseParams,
        // EVS 特有参数
        evsName: item.instanceName,
        openNum: item.quantity || 1,
        sysDiskSize: item.capacity,
        sysDiskType: item.type,
        vmId: data.ecs?.deviceId,
        vmName: data.ecs?.deviceName,
      }
    })
    if (flag) {
      params['evsModelList'] = evsModelList
    } else {
      params['orderJson'] = {
        evsList: evsModelList,
      }
    }
  }

  if (type === 'nat') {
    const baseParams = buildBaseParams(data, 'nat')

    const natModelList =
      data.nat?.map((natItem: any) => ({
        ...baseParams,
        natName: natItem.name,
        openNum: 1,
        azId: natItem.az?.id,
        azCode: natItem.az?.code,
        azName: natItem.az?.name,
        flavorId: natItem.specType?.id,
        flavorName: natItem.specType?.name,
        flavorType: '通用型',
        bindPublicIp: natItem.isBindEip,
        ...(natItem.isBindEip
          ? {
              eipModelList: [
                {
                  bandwidth: natItem.bandwidth || 5,
                },
              ],
            }
          : {}),
        // NAT 网络参数
        planeNetworkModel: {
          type: natItem.vpc?.type,
          id: natItem.vpc?.id,
          name: natItem.vpc?.vpcName,
          plane: '',
          subnets: natItem.vpcSubnet
            ? [
                {
                  subnetId: natItem.vpcSubnet.id,
                  subnetName: natItem.vpcSubnet.subnetName,
                  subnetIp: natItem.vpcSubnet.cidr,
                  ipAddress: '',
                },
              ]
            : [],
        },
      })) || []

    if (flag) {
      params['natModelList'] = natModelList
    } else {
      params['orderJson'] = {
        natList: natModelList,
      }
    }
  }

  if (type === 'obs') {
    const baseParams = buildBaseParams(data, 'obs')

    const obsModelList =
      data.obs?.map((obsItem: any) => {
        let obj: any = {
          ...baseParams,
        }
        if (['quant', 'require'].includes(obj.billType)) {
          obj['chargeType'] = obj.billType
          delete obj['billType']
        }

        return {
          ...obj,
          obsName: obsItem.name,
          openNum: obsItem.quantity,
          storageDiskSize: obsItem.capacity,
          storageDiskType: obsItem.type?.name,
        }
      }) || []

    if (flag) {
      params['obsModelList'] = obsModelList
    } else {
      params['orderJson'] = {
        obsList: obsModelList,
      }
    }
  }

  if (type === 'slb') {
    const baseParams = buildBaseParams(data, 'slb')

    const slbModelList =
      data.slb?.map((slbItem: any) => ({
        ...baseParams,
        slbName: slbItem.name,
        openNum: 1,
        azId: slbItem.az?.id,
        azCode: slbItem.az?.code,
        azName: slbItem.az?.name,
        flavorId: slbItem.specType?.id,
        flavorName: slbItem.specType?.name,
        flavorType: '通用型',
        bindPublicIp: slbItem.isBindEip,
        ...(slbItem.isBindEip
          ? {
              eipModelList: [
                {
                  bandwidth: slbItem.bandwidth || 5,
                },
              ],
            }
          : {}),
        // SLB 网络参数
        planeNetworkModel: {
          type: slbItem.vpc?.type,
          id: slbItem.vpc?.id,
          name: slbItem.vpc?.vpcName,
          plane: '',
          subnets: slbItem.vpcSubnet
            ? [
                {
                  subnetId: slbItem.vpcSubnet.id,
                  subnetName: slbItem.vpcSubnet.subnetName,
                  subnetIp: slbItem.vpcSubnet.cidr,
                  ipAddress: '',
                },
              ]
            : [],
        },
      })) || []

    if (flag) {
      params['slbModelList'] = slbModelList
    } else {
      params['orderJson'] = {
        slbList: slbModelList,
      }
    }
  }

  if (type === 'eip') {
    const baseParams = buildBaseParams(data, 'eip')

    const eipModelList = [
      {
        ...baseParams,
        openNum: 1,
        bandwidth: data.bandwidth,
        vmId: data.ecs?.deviceId,
        vmName: data.ecs?.deviceName,
        eipName: data.instanceName,
      },
    ]
    if (flag) {
      params['eipModelList'] = eipModelList
    } else {
      params['orderJson'] = {
        eipList: eipModelList,
      }
    }
  }

  if (type === 'vpn') {
    const baseParams = buildBaseParams(data, 'vpn')

    const vpnModelList = [
      {
        ...baseParams,
        name: data.instanceName,
        openNum: 1,
        bandwidth: data.bandwidth,
        maxConnection: data.maxConnection,
        planeNetworkModel: {
          type: data.vpc?.type,
          id: data.vpc?.id,
          name: data.vpc?.vpcName,
          plane: '',
          subnets: [
            {
              subnetId: data.subnet?.id,
              subnetName: data.subnet?.subnetName,
              subnetIp: data.subnet?.cidr,
              ipAddress: '',
            },
          ],
          sort: 0,
        },
      },
    ]
    if (flag) {
      params['vpnModelList'] = vpnModelList
    } else {
      params['orderJson'] = {
        vpnList: vpnModelList,
      }
    }
  }

  if (type === 'vpc') {
    const baseParams = buildBaseParams(data, 'vpc')

    params = {
      ...baseParams,
      networks: [
        {
          vpcName: data.name,
          cidr: data.ipRange,
          plane: '',
          subnetDTOList:
            data.subnet?.map((item: any) => ({
              subnetName: item.name,
              startIp: `${item.ip1}.${item.ip2}.${item.ip3}.${item.ip4}`,
              netmask: item.cidr,
              // 生成uuid
              uuid: uuid(16),
            })) || [],
        },
      ],
    }
    params['networks'][0]['detail'] = JSON.stringify(params)
  }

  if (type === 'backup') {
    const baseParams = buildBaseParams(data, 'backup')

    const backupModelList = [
      {
        ...baseParams,
        jobName: data.instanceName,
        backupType: data.backupType,
        frequency: data.frequency,
        daysOfWeek: data.daysOfWeek,
        backupTime: data.backupTime,
        retentionDays: data.retentionDays,
        openNum: 1,
      },
    ]
    if (flag) {
      params['backupModelList'] = backupModelList
    } else {
      params['orderJson'] = {
        backupList: backupModelList,
      }
    }
  }

  if (type === 'rdsMysql') {
    const baseParams = buildBaseParams(data, 'rdsMysql')
    const rdsMysqlModelList = [
      {
        ...baseParams,
        mysqlName: data.instanceName,
        engineVersion: data.engineVersion,
        deployType: data.deployType,
        flavorId: data.spec?.id,
        flavorName: data.spec?.name,
        flavorCode: data.spec?.flavorModelCode,
        flavorType: '通用型',
        openNum: data.number || 1,
        sysDiskType: data.systemDisk?.[0]?.type,
        sysDiskSize: data.systemDisk?.[0]?.capacity,
        functionalModule: 'app',
        planeNetworkModel: [
          {
            type: data.vpc?.type,
            id: data.vpc?.id,
            name: data.vpc?.vpcName,
            plane: '',
            subnets: [
              {
                subnetId: data.subnet?.id,
                subnetName: data.subnet?.subnetName,
                subnetIp: data.subnet?.cidr,
                ipAddress: '',
              },
            ],
          },
        ],
      },
    ]
    if (flag) {
      params['rdsMysqlModelList'] = rdsMysqlModelList
    } else {
      params['orderJson'] = {
        rdsMysqlList: rdsMysqlModelList,
      }
    }
  }

  return params
}

export default useGetTheParameters
