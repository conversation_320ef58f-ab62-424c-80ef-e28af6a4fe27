<template>
  <div class="table-box">
    <sl-page-header
      :title="titleMap[opStatus]"
      :icon="{
        class: 'page_workOrderApproval',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回工单',
        function: handleBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="shopping-carts-scroll-view" class="scroll-view">
      <!--步骤条 -->
      <div class="steps-con">
        <sl-steps :class="{ closed: orderDesc.orderStatus == 'CLOSE' }"></sl-steps>
      </div>
      <!-- Tabs 组件 -->
      <sl-base-tabs :show-count="false" :tabs="tabs" v-model="activeTab" v-if="currentTask">
      </sl-base-tabs>
      <div
        v-show="['home', 'profile'].includes(activeTab)"
        class="sl-page-content"
        v-if="currentTask"
      >
        <BasicInformation
          ref="basicInformationRef"
          v-show="activeTab == 'home'"
          :order-desc="orderDesc"
          :btn-auth="btnAuth"
        />

        <!-- 资源信息  -->
        <ResourceInformation
          ref="resourceInformationRef"
          v-show="activeTab == 'profile'"
          :order-desc="orderDesc"
          :btn-auth="btnAuth"
          v-model:disabled-btn="disabledBtn"
        />
      </div>

      <div v-if="activeTab == 'settings'" class="sl-page-content table-main auditTable">
        <!-- 审核日志 -->
        <AuditTable :order-desc="orderDesc" />
      </div>
    </el-scrollbar>
    <!-- 按钮组件 -->
    <div
      v-if="pageStatus"
      v-show="['home', 'profile'].includes(activeTab)"
      class="sl-page-content page-footer"
    >
      <div class="sl-card">
        <RenderBtn />
      </div>
    </div>
    <!-- 审核组件 -->
    <SlAuditDialog
      v-show="visible"
      ref="SlAuditDialogRef"
      v-model:visible="visible"
      back-path="/workOrder"
    />
  </div>
</template>

<script setup lang="tsx">
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import SlSteps from '@/components/SlSteps/index.vue'
import { computed, provide, ref, nextTick } from 'vue'
import {
  getNetworkStatusApi,
  getStandardWorkorderDetailApi,
  getVpcStatusApi,
} from '@/api/modules/approvalCenter'
import { useRoute, useRouter } from 'vue-router'
import useWorkOrder from '@/hooks/useWorkOrder'
import BasicInformation from './components/BasicInformation.vue'
import ResourceInformation from './components/ResourceInformation/index.vue'
import AuditTable from '@/views/approvalCenter/components/AuditTable.vue'
import type { AuditDialogInstance } from '@/components/SlAuditDialog/interface'
import { getCloudLeaderDic } from '@/api/modules/dic'
import type { ResourceListType } from '../interface/type'

let orderDesc = ref<ResourceListType>({
  activityTask: undefined,
})

const titleMap = {
  '1': '开通工单审批',
  '2': '开通工单查看',
  '3': '开通工单编辑',
  '4': '开通工单创建',
} as const

type IOpStatus = keyof typeof titleMap

const route = useRoute()
const router = useRouter()
const workOrderId = route.query?.workOrderId
const opStatus = route.query?.opStatus as IOpStatus

/**
 *  @readonly获取页面状态用来做表单权限控制
 *  */

const pageStatus = ref(opStatus === '1') //1:编辑 0:查看
provide('pageStatus', pageStatus)

/**
 *  @readonly 是否为特殊资源池的工单
 *  */

const isOffline = ref(false)
provide('isOffline', isOffline)

const inintData = async () => {
  if (!workOrderId) return
  // 获取工单详情
  const res = await getStandardWorkorderDetailApi({ workOrderId: String(workOrderId) })
  if (!res.entity) return
  // 给予默认值
  orderDesc.value = {
    ...res.entity,
  }

  isOffline.value = !!res.entity?.isOffline

  getWorkOrderNode(orderDesc.value.activityTask!)
  nextTick(() => {
    resourceInformationRef.value?.initData(orderDesc.value)
  })
}
inintData()

// 当前节点
const { allTasks, currentTask, currentSort, historyTasks, getWorkOrderNode } = useWorkOrder()
provide('allTasks', allTasks)
provide('currentSort', currentSort)
provide('currentTask', currentTask)
provide('historyTasks', historyTasks)

const handleBack = () => {
  router.push('/workorder')
}

const tabs = [
  { name: 'home', label: '基础信息' },
  { name: 'profile', label: '资源信息' },
  { name: 'settings', label: '审核日志' },
]
const activeTab = ref('profile') // 默认激活的 tab

// 按钮权限控制
const btnAuth = computed(() => {
  return {
    schema_administrator: currentTask.value === 'schema_administrator', // 架构审核
    tenant_task: currentTask.value === 'tenant_task', // 租户确认
    business_depart_leader2:
      currentTask.value === 'business_depart_leader2' ||
      currentTask.value === 'business2_depart_leader', // 三级业务部门领导审核
    business_depart_leader: currentTask.value === 'business_depart_leader', // 二级业务部门领导审核
    cloud_leader: currentTask.value === 'cloud_leader', // 三级云资源部领导
    cloud_leader_2: currentTask.value === 'cloud_leader_2', // 二级云资源部领导
    network_provisioning: currentTask.value === 'network_provisioning', // 网络开通
    resource_creation: currentTask.value === 'resource_creation', // 资源开通
    autodit_end: currentTask.value === 'access_to_4a' || currentTask.value === 'autodit end', // 4A & 结束节点
  }
})
provide('btnAuth', btnAuth)
//  表单组件
const resourceInformationRef = ref<InstanceType<typeof ResourceInformation> | null>(null)

// ----------------------审核提交-----------------------

// 保存审批意见 架构审核提交
const schemaAuditLogSubmit = (form: any) => {
  return [
    'cloud_resources',
    'cloud_architecture',
    'business_architecture',
    'business_planning',
  ].map((key, index) => ({
    auditType: key,
    auditResult: form[key as keyof typeof form],
    auditResultDetail: form['auditAdvice'] as string,
    sort: index,
  }))
}

// 架构审核人表单
const schema_administrator_fields = ref<any[]>([
  {
    label: '三级云资源部领导',
    type: 'select',
    key: 'cloudLeaderId',
    labelWidth: 90,
    rules: [{ required: true, message: '请选择三级云资源部领导', trigger: 'change' }],
    labelField: 'userName',
    valueField: 'id',
    options: [],
  },
  {
    label: '二级云资源部领导',
    type: 'select',
    key: 'secondLevelLeaderId',
    labelWidth: 90,
    labelField: 'userName',
    valueField: 'id',
    rules: [{ required: true, message: '请选择二级云资源部领导', trigger: 'change' }],
    options: [],
  },
])

async function getAuditNode() {
  //后续别的表单可以根据这个逻辑去获取 添加if语句
  if (btnAuth.value.schema_administrator) {
    // 三级
    const { entity: cloudLeaderDic } = await getCloudLeaderDic({
      roleCode: 'cloud_leader',
    })
    // 二级
    const { entity: cloudLeader2Dic } = await getCloudLeaderDic({
      roleCode: 'cloud_leader_2',
    })
    return schema_administrator_fields.value.map((item: any) => {
      if (item.key === 'cloudLeaderId') {
        item.options = cloudLeaderDic
      }
      if (item.key === 'secondLevelLeaderId') {
        item.options = cloudLeader2Dic
      }
      return {
        ...item,
      }
    })
  }

  return []
}
// 审核组件
const SlAuditDialogRef = ref<AuditDialogInstance>()
const visible = ref(false)
//校验表单
const validateCloudHost = async () => {
  const falg = await resourceInformationRef.value?.validateForm()

  if (falg) return true

  return false
}

/**
 * 网络开通校验是否全部开通
 */
const validateNetwork = async () => {
  try {
    const res = resourceInformationRef.value?.domainCodeStatus
      ? await getNetworkStatusApi(route.query.workOrderId as string)
      : await getVpcStatusApi(route.query.workOrderId as string)
    if (res.message !== 'success') {
      if (resourceInformationRef.value?.domainCodeStatus) {
        ElMessageBox.alert(res.message, '信息提示', {
          confirmButtonText: '确认',
          dangerouslyUseHTMLString: true,
        })
        return true
      }

      await ElMessageBox.confirm(res.message, '信息提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      })
    }

    return false
  } catch {
    return true
  }
}

// 获取提交的参数
async function changeParams(status: string) {
  let params: any = {
    orderId: route.query.workOrderId,
    currentNodeCode: currentTask.value,
    activiteStatus: status === 'pass' ? 1 : 0,
  }
  let fields: any[] = []

  if (status === 'reject') {
    params['nodeCode'] = allTasks.value[0].task
  }
  if (status === 'pass') fields = await getAuditNode()
  params = {
    ...params,
    ...(await resourceInformationRef.value?.submitForm()),
  }
  //架构审核
  if (btnAuth.value.schema_administrator) {
    params['auditLogList'] = schemaAuditLogSubmit(params)
  }

  return {
    params,
    fields,
  }
}
/**
 * 审核提交
 * @param status 审核状态
 * @param falg 是否需要弹窗 默认打开
 *  */
const subAudit = async (status: string, falg: boolean = true) => {
  // 1. 校验表单 - 架构审核节点

  if (status === 'pass' && btnAuth.value.schema_administrator && (await validateCloudHost())) return

  // 1.2 网络开通节点 判断是否开通
  if (status === 'pass' && btnAuth.value.network_provisioning && (await validateNetwork())) return
  // 2.获取数据
  const { params, fields } = await changeParams(status)

  // 3.打开弹窗
  visible.value = true
  nextTick(() => {
    SlAuditDialogRef.value?.openDialog(
      {
        params,
        fields,
      },
      falg,
    )
  })
}

// ----------------------提交按钮-----------------------

// 网络开通->vpc
const openNetwork = () => {
  const path = resourceInformationRef.value?.domainCodeStatus ? '/networkForm' : '/vpcForm'
  openVpcOrrNetwork(path)
}
const openVpcOrrNetwork = (path: string) => {
  // form.value
  const query = {
    orderId: route.query.workOrderId,
    opStatus: route.query.opStatus,
  }

  router.push({
    path,
    query,
  })
}
const disabledBtn = ref(false)
// 渲染按钮组件
const RenderBtn = () => {
  // 1 .查看表单

  // 2. 网络开通
  if (btnAuth.value.network_provisioning) {
    return (
      <>
        <sl-button type="primary" apiFunction={() => subAudit('pass', false)}>
          开通完成
        </sl-button>
        {!isOffline.value && (
          <el-button type="primary" onclick={openNetwork}>
            开通网络
          </el-button>
        )}
      </>
    )
  }
  // 3. 资源开通
  if (btnAuth.value.resource_creation) {
    return (
      <>
        <sl-button
          disabled={!disabledBtn.value}
          type="primary"
          apiFunction={() => subAudit('pass', false)}
        >
          开通完成
        </sl-button>
      </>
    )
  }

  return (
    <>
      <el-button onclick={() => subAudit('reject')}>审批驳回</el-button>
      <el-button disabled={disabledBtn.value} type="primary" onclick={() => subAudit('pass')}>
        审批通过
      </el-button>
    </>
  )
}
</script>

<style lang="scss" scoped>
// 页面通用 标题下面的通用布局

.steps-con {
  background: #fff;
  padding: 20px;
}
.auditTable {
  min-height: 350px;
}
.page-footer {
  text-align: right;
  padding-top: 2px;
  padding-bottom: 0;
}
</style>
