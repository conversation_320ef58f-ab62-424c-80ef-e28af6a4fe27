import type { RouteRecordRaw } from 'vue-router'

const HOME_URL = '/login'
import managementCenterRouter from './managementCenter'
import resourceCenterRouter from './resourceCenter'
import configCenterRouter from './configCenter'
import overviewRouter from './computingPowerMap'
import approvalCenterRouter from './approvalCenter'
import productCenterRouter from './productCenter'
import orderCenterRouter from './orderCenter'
import monitorRouter from './monitor'

/**
 * staticRouter (静态路由)
 */
export const staticRouter: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: HOME_URL,
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
    },
  },
  {
    path: '/verificate',
    name: 'verificate',
    component: () => import('@/views/login/verificate.vue'),
    meta: {
      title: '验证',
    },
  },
  {
    path: '/bizsinfo',
    name: 'bizsinfo',
    component: () => import('@/layout/noMenuIndex.vue'),
    // component: () => import('@/views/computingPowerMap/bizsinfo/index.vue'),
    // name: 'bizsinfo',
    meta: {
      title: 'bizsinfo',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/computingPowerMap',
    // component: () => import('@/views/dashboard/index.vue'),
    component: () => import('@/views/computingPowerMap/computingPowerMap/index.vue'),
    name: 'computingPowerMap',
    meta: {
      title: '算力地图',
      icon: 'icon-gongdan',
      isLink: '/computingPowerMap',
    },
  },
  {
    path: '/computingPowerMapDetail',
    // component: () => import('@/views/dashboard/index.vue'),
    component: () => import('@/views/computingPowerMap/computingPowerMap/detail.vue'),
    name: 'computingPowerMapDetail',
    meta: {
      title: '算力地图详情',
      icon: 'icon-gongdan',
      isLink: '/computingPowerMapDetail',
    },
  },
  {
    path: '/layout',
    name: 'layout',
    component: () => import('@/layout/index.vue'),
    redirect: HOME_URL,
    children: [
      // 用动态路由的话直接删除调保持数组为空
      ...overviewRouter,
      ...managementCenterRouter,
      ...resourceCenterRouter,
      ...configCenterRouter,
      ...approvalCenterRouter,
      ...productCenterRouter,
      ...orderCenterRouter,
      ...monitorRouter,
    ],
  },
  {
    path: '/dashboard',
    component: () => import('@/views/dashboard/index.vue'),
    name: 'dashboard',
    meta: {
      title: 'dashboard',
      icon: 'icon-gongdan',
      isLink: '/dashboard',
    },
  },
]

/**
 * errorRouter (错误页面路由)
 */
export const errorRouter = [
  // {
  //   path: '/403',
  //   name: '403',
  //   component: () => import('@/components/ErrorMessage/403.vue'),
  //   meta: {
  //     title: '403页面',
  //   },
  // },
  // {
  //   path: '/404',
  //   name: '404',
  //   component: () => import('@/components/ErrorMessage/404.vue'),
  //   meta: {
  //     title: '404页面',
  //   },
  // },
  // {
  //   path: '/500',
  //   name: '500',
  //   component: () => import('@/components/ErrorMessage/500.vue'),
  //   meta: {
  //     title: '500页面',
  //   },
  // },
  // Resolve refresh page, route warnings
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/components/SlErrorPage.vue'),
  },
]
