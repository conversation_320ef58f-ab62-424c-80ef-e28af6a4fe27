<template>
  <div class="dashboard-cards">
    <div
      v-for="(cloudItem, index) in cloudPlatforms"
      :key="cloudItem.cloudName"
      :class="{
        active: showTopType == (index + 1).toString(),
        disabled: cloudItem.cloudName === 'IT云',
      }"
      @click="cloudItem.cloudName !== 'IT云' ? changeTopType((index + 1).toString()) : null"
    >
      <div>
        <img :src="getCloudIconPath(cloudItem.cloudName)" :alt="cloudItem.cloudName" />
      </div>
      <div class="active">
        <span>{{ cloudItem.cloudName }}</span>
        <p>
          <b>硬件资源池数量</b>
          <i>{{ cloudItem.hardwarePoolNum }} </i>
        </p>
        <p>
          <b>设备数量</b>
          <i>{{ cloudItem.baseDeviceNum }}</i>
        </p>
      </div>
    </div>
  </div>
  <section class="panel dashboard-map">
    <div
      ref="mapRef"
      style="width: 100%; height: 710px"
      @contextmenu.prevent="handleContextMenu"
    ></div>
  </section>

  <!-- 返回上级按钮 - 只在非浙江省时显示 -->
  <div v-if="props.city !== 'zhejiang'" class="back-to-parent" @click="backToParent">
    <span>返回上级</span>
  </div>
  <!-- 右键菜单 -->
  <div
    v-if="contextMenu.visible"
    class="context-menu"
    :style="{
      left: `${contextMenu.x}px`,
      top: `${contextMenu.y}px`,
    }"
  >
    <div class="menu-header" @click="showLeftDialog">
      <span>{{ contextMenu.name || '位置' }}</span>
    </div>
  </div>
  <div v-if="showTopType !== '0'" class="dashboard-bottom-dialog">
    <div class="dashboard-bottom-dialog-box">
      <div>
        <template v-if="currentCloudPlatformTypes.length > 0">
          <div
            v-for="(item, index) in currentCloudPlatformTypes"
            :key="item"
            @click="changeBottomTootip((index + 1).toString())"
            class="item"
            :class="{ active: showBottomTootipType == (index + 1).toString() }"
          >
            <div>
              <i class="imgIcon"></i>
              <span v-if="item.platformTypeName == '融合边缘云-VMWARE'">
                融合边缘云- <b>vmware</b>
              </span>
              <span v-else>{{ item.platformTypeName }}</span>
            </div>

            <div class="subTooltipInfo">
              <b>硬件资源池数量</b>
              <i>{{ item.hardwarePoolNum }} </i>
              <b>设备数量</b>
              <i>{{ item.baseDeviceNum }}</i>
            </div>
          </div>
        </template>
        <template v-else> 暂无数据 </template>

        <!--        <template v-if="currentCloudPlatformTypes.length > 0">-->
        <!--          <div-->
        <!--            v-for="(item, index) in currentCloudPlatformTypes.slice(0, 2)"-->
        <!--            :key="item"-->
        <!--            @click="changeBottomTootip((index + 1).toString())"-->
        <!--            class="item"-->
        <!--            :class="{ active: showBottomTootipType == (index + 1).toString() }"-->
        <!--          >-->
        <!--            <img src="/images/computingPower/comPowerIconCloud.png" alt="" />-->
        <!--            <span>{{ item }}</span>-->
        <!--          </div>-->
        <!--        </template>-->
        <!--        <template v-else> 暂无数据 </template>-->
      </div>
      <!--      <div v-if="currentCloudPlatformTypes.length > 2">-->
      <!--        -->
      <!--      </div>-->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { ECharts, SeriesOption } from 'echarts'
// import DashboardTitle from './DashboardTitle.vue'
// import Card from './Card.vue'
import { getCloudGroupStatsApi } from '@/api/modules/computingPowerMap'
import { getComPowerMapStatsCloudCityAreas } from '@/api/modules/comPowerCenter'

// 定义props
interface Props {
  city?: string
}

const props = withDefaults(defineProps<Props>(), {
  city: 'zhejiang',
})

// 动态加载的映射数据
const cityNameMap = ref<Record<string, string>>({})
const cityCodeMap = ref<Record<string, number>>({})
const areaCodeMap = ref<Record<string, string>>({})

// 加载映射数据的函数
const loadMappingData = async () => {
  try {
    const [cityNameModule, cityCodeModule, areaCodeModule] = await Promise.all([
      import('../../json/cityNameMap.json'),
      import('../../json/cityCodeMap.json'),
      import('../../json/areaCodeMap.json'),
    ])

    cityNameMap.value = cityNameModule.default
    cityCodeMap.value = cityCodeModule.default
    areaCodeMap.value = areaCodeModule.default
  } catch (error) {
    console.error('加载映射数据失败:', error)
    // 如果加载失败，使用默认值
    cityNameMap.value = {
      hangzhou: '杭州市',
      ningbo: '宁波市',
      wenzhou: '温州市',
      jiaxing: '嘉兴市',
      huzhou: '湖州市',
      shaoxing: '绍兴市',
      jinhua: '金华市',
      quzhou: '衢州市',
      zhoushan: '舟山市',
      taizhou: '台州市',
      lishui: '丽水市',
      zhejiang: '浙江省',
    }
  }
}

// 计算当前显示的地图名称
const currentMapName = computed(() => {
  const mapName = cityNameMap.value[props.city] || '浙江'
  console.log('当前地图名称:', mapName, '城市代码:', props.city)
  return mapName
})

// 监听props.city的变化，确保地图立即更新
watch(
  () => props.city,
  (newCity, oldCity) => {
    console.log('props.city变化:', oldCity, '->', newCity)
    if (newCity !== oldCity && chart) {
      // 立即触发地图更新
      nextTick(() => {
        updateMap()
      })
    }
  },
  { immediate: false },
)

// 更新地图的函数
const updateMap = async () => {
  if (!chart) {
    console.log('updateMap: chart未初始化，跳过更新')
    return
  }

  try {
    console.log('开始更新地图:', props.city)

    // 立即清除地图上的选中状态
    if (selectedRegion.value) {
      chart.dispatchAction({
        type: 'mapUnSelect',
        name: selectedRegion.value,
      })
    }

    // 重置选中状态
    selectedRegion.value = null

    // 重新加载地图数据
    const geojson = await loadMapData()
    console.log('地图数据加载完成，开始注册地图:', currentMapName.value)

    // 重新注册地图
    echarts.registerMap(currentMapName.value, geojson)
    console.log('地图注册完成')

    // 重新设置地图选项
    setMapOption()
    console.log('地图选项设置完成')

    // 强制重新渲染地图
    if (chart) {
      chart.resize()
      console.log('地图重新渲染完成')
    }

    // 重新获取数据
    getTotalCompute()
    getCloudPlatformStats()

    // 触发参数更新
    emitSelectChange()
  } catch (error) {
    console.error('地图更新失败:', error)
  }
}

let chart: ECharts | null = null
const emit = defineEmits(['selectChange', 'rightClick', 'doubleClick', 'showLeftDialog'])
// const powerList = ref<any>([])

// 定义右键菜单类型
interface ContextMenu {
  visible: boolean
  x: number
  y: number
  name: string | null
}

// 右键菜单状态
const contextMenu = ref<ContextMenu>({
  visible: false,
  x: 0,
  y: 0,
  name: null,
})

// 记录右键点击的信息
const activeInfo = ref<any>(null)

// 处理右键菜单事件
const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
}

// 展示左侧总览详情信息
const showLeftDialog = () => {
  chart!.dispatchAction({
    type: 'mapUnSelect',
    seriesId: 'test1',
    name: allMapCityData.value,
  })
  if (activeInfo.value == null) {
    // 浙江省数据
    emit('rightClick', {
      regionName: '',
      cityCode: '',
      areaCode: '',
      cloudName: currentCloudName.value,
      platformTypeName: currentPlatformTypeName.value,
    })
  } else {
    let param = activeInfo.value
    if (param.seriesName == `${currentMapName.value}数据`) {
      // 浙江省数据
      emit('rightClick', {
        regionName: param.name,
        cityCode: param.data.areaCode,
        areaCode: '',
        cloudName: currentCloudName.value,
        platformTypeName: currentPlatformTypeName.value,
      })
    } else {
      // 其他区域数据
      emit('rightClick', {
        regionName: param.name,
        cityCode: '',
        areaCode: param.data.areaCode,
        cloudName: currentCloudName.value,
        platformTypeName: currentPlatformTypeName.value,
      })
    }
  }
  contextMenu.value.visible = false
}

const mapRef = ref<HTMLDivElement | null>(null)
const selectedRegion = ref<string | null>(null)
// const totalPower = ref(0)
const mapData = ref<any>(null)

// 定义城市数据类型
interface CityData {
  areaCode: string
  name: string
}

// 定义城市映射类型
type CityMap = Record<string, CityData>

// 城市映射数据
const cityMap: CityMap = {
  杭州市: {
    areaCode: '571',
    name: 'hangzhou',
  },
  宁波市: {
    areaCode: '574',
    name: 'ningbo',
  },
  温州市: {
    areaCode: '577',
    name: 'wenzhou',
  },
  嘉兴市: {
    areaCode: '573',
    name: 'jiaxing',
  },
  湖州市: {
    areaCode: '572',
    name: 'huzhou',
  },
  绍兴市: {
    areaCode: '575',
    name: 'shaoxing',
  },
  金华市: {
    areaCode: '579',
    name: 'jinhua',
  },
  衢州市: {
    areaCode: '570',
    name: 'quzhou',
  },
  舟山市: {
    areaCode: '580',
    name: 'zhoushan',
  },
  台州市: {
    areaCode: '576',
    name: 'taizhou',
  },
  丽水市: {
    areaCode: '578',
    name: 'lishui',
  },
  柯城区: { areaCode: '5701', name: 'quzhou' },
  江山市: { areaCode: '5702', name: 'quzhou' },
  常山县: { areaCode: '5703', name: 'quzhou' },
  开化县: { areaCode: '5704', name: 'quzhou' },
  龙游县: { areaCode: '5705', name: 'quzhou' },
  衢江区: { areaCode: '5706', name: 'quzhou' },
  上城区: { areaCode: '5711', name: 'hangzhou' },
  萧山区: { areaCode: '5712', name: 'hangzhou' },
  富阳区: { areaCode: '5713', name: 'hangzhou' },
  余杭区: { areaCode: '5714', name: 'hangzhou' },
  建德市: { areaCode: '5715', name: 'hangzhou' },
  淳安县: { areaCode: '5716', name: 'hangzhou' },
  桐庐县: { areaCode: '5717', name: 'hangzhou' },
  临安区: { areaCode: '5718', name: 'hangzhou' },
  滨江区: { areaCode: '5719', name: 'hangzhou' },
  钱塘区: { areaCode: '571A', name: 'hangzhou' },
  西湖区: { areaCode: '571B', name: 'hangzhou' },
  临平区: { areaCode: '571C', name: 'hangzhou' },
  拱墅区: { areaCode: '571D', name: 'hangzhou' },
  销售部: { areaCode: '571F', name: 'hangzhou' },
  吴兴区: { areaCode: '5721', name: 'huzhou' },
  长兴县: { areaCode: '5722', name: 'huzhou' },
  德清县: { areaCode: '5723', name: 'huzhou' },
  安吉县: { areaCode: '5724', name: 'huzhou' },
  南浔区: { areaCode: '5725', name: 'huzhou' },
  秀洲区: { areaCode: '5731', name: 'jiaxing' },
  桐乡市: { areaCode: '5732', name: 'jiaxing' },
  海宁市: { areaCode: '5733', name: 'jiaxing' },
  嘉善县: { areaCode: '5734', name: 'jiaxing' },
  平湖市: { areaCode: '5735', name: 'jiaxing' },
  海盐县: { areaCode: '5736', name: 'jiaxing' },
  南湖区: { areaCode: '5737', name: 'jiaxing' },
  海曙区: { areaCode: '5741', name: 'ningbo' },
  余姚市: { areaCode: '5742', name: 'ningbo' },
  慈溪市: { areaCode: '5743', name: 'ningbo' },
  象山县: { areaCode: '5744', name: 'ningbo' },
  北仑区: { areaCode: '5745', name: 'ningbo' },
  宁海县: { areaCode: '5746', name: 'ningbo' },
  奉化区: { areaCode: '5747', name: 'ningbo' },
  镇海区: { areaCode: '5748', name: 'ningbo' },
  鄞州区: { areaCode: '5749', name: 'ningbo' },
  江北区: { areaCode: '574C', name: 'ningbo' },
  越城区: { areaCode: '5751', name: 'shaoxing' },
  诸暨市: { areaCode: '5752', name: 'shaoxing' },
  上虞区: { areaCode: '5753', name: 'shaoxing' },
  嵊州市: { areaCode: '5754', name: 'shaoxing' },
  新昌县: { areaCode: '5755', name: 'shaoxing' },
  柯桥区: { areaCode: '5756', name: 'shaoxing' },
  椒江区: { areaCode: '5761', name: 'taizhou' },
  黄岩区: { areaCode: '5762', name: 'taizhou' },
  路桥区: { areaCode: '5763', name: 'taizhou' },
  临海市: { areaCode: '5764', name: 'taizhou' },
  温岭市: { areaCode: '5765', name: 'taizhou' },
  仙居县: { areaCode: '5766', name: 'taizhou' },
  天台县: { areaCode: '5767', name: 'taizhou' },
  三门县: { areaCode: '5768', name: 'taizhou' },
  玉环市: { areaCode: '5769', name: 'taizhou' },
  鹿城区: { areaCode: '5771', name: 'wenzhou' },
  乐清市: { areaCode: '5772', name: 'wenzhou' },
  瑞安市: { areaCode: '5773', name: 'wenzhou' },
  平阳县: { areaCode: '5774', name: 'wenzhou' },
  永嘉县: { areaCode: '5775', name: 'wenzhou' },
  洞头区: { areaCode: '5776', name: 'wenzhou' },
  文成县: { areaCode: '5777', name: 'wenzhou' },
  泰顺县: { areaCode: '5778', name: 'wenzhou' },
  苍南县: { areaCode: '5779', name: 'wenzhou' },
  瓯海区: { areaCode: '577A', name: 'wenzhou' },
  龙湾区: { areaCode: '577B', name: 'wenzhou' },
  龙港市: { areaCode: '577D', name: 'wenzhou' },
  景宁畲族自治县: { areaCode: '5789', name: 'wenzhou' },
  南城区: { areaCode: '578B', name: 'wenzhou' },
  婺城区: { areaCode: '5791', name: 'lishui' },
  义乌市: { areaCode: '5792', name: 'lishui' },
  磐安县: { areaCode: '5793', name: 'lishui' },
  兰溪市: { areaCode: '5794', name: 'lishui' },
  东阳市: { areaCode: '5795', name: 'lishui' },
  永康市: { areaCode: '5796', name: 'lishui' },
  浦江县: { areaCode: '5797', name: 'lishui' },
  武义县: { areaCode: '5798', name: 'lishui' },
  金东区: { areaCode: '5799', name: 'lishui' },
  莲都区: { areaCode: '5781', name: 'lishui' },
  缙云县: { areaCode: '5782', name: 'lishui' },
  青田县: { areaCode: '5783', name: 'lishui' },
  云和县: { areaCode: '5784', name: 'lishui' },
  庆元县: { areaCode: '5785', name: 'lishui' },
  龙泉市: { areaCode: '5786', name: 'lishui' },
  遂昌县: { areaCode: '5787', name: 'lishui' },
  松阳县: { areaCode: '5788', name: 'lishui' },
  定海区: { areaCode: '5801', name: 'jinhua' },
  普陀区: { areaCode: '5802', name: 'jinhua' },
  岱山县: { areaCode: '5803', name: 'jinhua' },
  嵊泗县: { areaCode: '5804', name: 'jinhua' },
}

// 存储所有地图城市数据
const allMapCityData = ref<string[]>([])

const getTotalCompute = async (areaCode: string = '') => {
  console.log(areaCode)
}

// 动态加载地图数据
const loadMapData = async () => {
  try {
    console.log('开始加载地图数据:', props.city)
    let mapDataModule
    if (props.city === 'zhejiang') {
      mapDataModule = await import('../../json/zhejiang.json')
    } else {
      mapDataModule = await import(`../../json/cities/${props.city}.json`)
    }
    mapData.value = mapDataModule.default
    console.log('地图数据加载成功:', props.city)
    return mapData.value
  } catch (error) {
    console.error(`加载地图数据失败:`, error)
    // 如果加载失败，回退到浙江省地图
    if (props.city !== 'zhejiang') {
      console.log('回退到浙江省地图')
      const fallbackModule = await import('../../json/zhejiang.json')
      mapData.value = fallbackModule.default
      return mapData.value
    }
    throw error
  }
}

function setMapOption() {
  if (!chart || !mapData.value) {
    console.log('setMapOption: chart或mapData为空', { chart: !!chart, mapData: !!mapData.value })
    return
  }

  console.log('设置地图选项，当前地图名称:', currentMapName.value)

  // 重置所有地图城市数据
  allMapCityData.value = []

  let data = mapData.value.features.map((item: any) => {
    let areaCode = (cityMap[item.properties.name] && cityMap[item.properties.name].areaCode) || ''
    let pinyin = (cityMap[item.properties.name] && cityMap[item.properties.name].name) || ''

    // 收集所有城市名称用于高亮
    allMapCityData.value.push(item.properties.name)

    return {
      name: item.properties.name,
      areaCode: areaCode,
      pinyin: pinyin,
      isSelected: false,
    }
  })

  console.log('地图数据特征数量:', data.length)

  chart.setOption({
    backgroundColor: '',
    geo: [
      {
        map: currentMapName.value,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '50%'],
        layoutSize: '180%',
        show: true,
        roam: false,
        label: {
          emphasis: {
            show: false,
          },
        },
        itemStyle: {
          normal: {
            borderColor: '#c0f3fb',
            borderWidth: 1,
            shadowColor: '#8cd3ef',
            shadowOffsetY: 10,
            shadowBlur: 120,
            areaColor: 'transparent',
          },
        },
      },
      // 重影效果
      {
        type: 'map',
        map: currentMapName.value,
        zlevel: -1,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '51%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.8)',
            shadowColor: 'rgba(172, 122, 255,0.5)',
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: 'rgba(5,21,35,0.1)',
          },
        },
      },
      {
        type: 'map',
        map: currentMapName.value,
        zlevel: -2,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '52%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.6)',
            shadowColor: 'rgba(65, 214, 255,1)',
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: 'transpercent',
          },
        },
      },
      {
        type: 'map',
        map: currentMapName.value,
        zlevel: -3,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '53%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.4)',
            shadowColor: 'rgba(58,149,253,1)',
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: 'transpercent',
          },
        },
      },
      {
        type: 'map',
        map: currentMapName.value,
        zlevel: -4,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '54%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 5,
            borderColor: 'rgba(5,9,57,0.8)',
            shadowColor: 'rgba(29, 111, 165,0.8)',
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: 'rgba(5,21,35,0.1)',
          },
        },
      },
    ],
    series: [
      {
        name: `${currentMapName.value}数据`,
        type: 'map',
        map: currentMapName.value,
        aspectScale: 1,
        id: 'test1',
        zoom: 0.58,
        showLegendSymbol: true,
        label: {
          normal: {
            show: true,
            textStyle: { color: '#fff', fontSize: '120%' },
          },
          emphasis: {
            textStyle: { color: '#ffffff', fontSize: '150%' },
          },
        },
        itemStyle: {
          normal: {
            areaColor: {
              type: 'linear',
              x: 1200,
              y: 0,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(3,27,78,0.75)',
                },
                {
                  offset: 1,
                  color: 'rgba(58,149,253,0.75)',
                },
              ],
              global: true,
            },
            borderColor: '#fff',
            borderWidth: 0.2,
          },
          emphasis: {
            show: false,
            color: '#fff',
            areaColor: 'rgba(0,254,233,0.6)',
          },
        },
        select: {
          label: {
            color: '#ffffff',
          },
          itemStyle: {
            areaColor: 'rgba(0,254,233,0.6)',
          },
        },
        layoutCenter: ['50%', '50%'],
        layoutSize: '180%',
        markPoint: {
          symbol: 'none',
        },
        data: data,
      },
    ],
  })

  if (props.city === 'zhejiang') {
    chart.dispatchAction({
      type: 'highlight',
      seriesId: 'test1',
      name: needHighLightCityDataInfoArr,
    })
  } else {
    chart.dispatchAction({
      type: 'highlight',
      seriesId: 'test1',
      name: needHighLightAreaDataInfoArr,
    })
  }
  prepareRegionPoints()
}
const showTopType = ref('0')
const showBottomTootipType = ref('0')

// 平台类型映射
const platformTypeNameMap: Record<string, string> = {
  '1': '融合边缘云-vmware',
  '2': '融合边缘云-华为',
  '3': '融合边缘云-华三',
  '4': '融合边缘云-浪潮',
}

// 动态云平台数据
const cloudPlatforms = ref<any>([])

const getCloudIconPath = (cloudName: string) => {
  switch (cloudName) {
    case '移动云':
      return '/images/computingPower/comPowerYidongyun.png'
    case '网络云':
      return '/images/computingPower/comPowerWangluoyun.png'
    case 'IT云':
      return '/images/computingPower/comPowerItyun.png'
    default:
      return ''
  }
}

const getCloudPlatformStats = async () => {
  try {
    const { entity }: any = await getCloudGroupStatsApi()
    let data = entity

    // 自动补全IT云功能 - 参照Dashboardmap.vue的逻辑
    if (data.length == 2) {
      data.push({
        baseDeviceNum: 0,
        cloudName: 'IT云',
        hardwarePoolNum: 0,
        platformTypes: [],
      })
    }

    cloudPlatforms.value = data.map((item: any) => ({
      cloudName: item.cloudName,
      hardwarePoolNum: item.hardwarePoolNum,
      baseDeviceNum: item.baseDeviceNum,
      platformTypes: item.platformTypes, // 新增平台类型列表
    }))

    console.log('云平台数据获取成功，包含自动补全的IT云:', cloudPlatforms.value.length, '个平台')
  } catch (error) {
    console.error('获取云平台统计数据失败:', error)
  }
}

// 当前选中的云类型和平台类型，初始都为空
const currentCloudName = ref<string>('')
const currentPlatformTypeName = ref<string>('')
const cityCodeName = ref<string>('')

// 当前选中云平台的平台类型列表
const currentCloudPlatformTypes = computed(() => {
  const selectedIndex = parseInt(showTopType.value) - 1
  const selectedCloudPlatform = cloudPlatforms.value[selectedIndex]
  return selectedCloudPlatform?.platformTypes || []
})

// 查找方法
function findCitiesByCodes(codes: string[], cityMap: CityMap): string[] {
  // 创建映射字典：键为区号，值为城市名
  const codeToCityMap: Record<string, string> = {}

  for (const [cityName, cityInfo] of Object.entries(cityMap)) {
    codeToCityMap[cityInfo.areaCode] = cityName
  }

  // 将区号转换为对应城市名
  return codes.map((code) => codeToCityMap[code] || '')
}

const changeTopType = (type: any) => {
  // 检查选中的云平台是否为IT云
  const selectedIndex = parseInt(type) - 1
  const selectedCloudPlatform = cloudPlatforms.value[selectedIndex]
  highLightCityInfoArr = []
  highLightAreaInfoArr = []
  needHighLightCityDataInfoArr = []
  needHighLightAreaDataInfoArr = []
  selectedRegion.value = ''

  let seriesData = mapData.value.features.map((item: any) => {
    let areaCode = (cityMap[item.properties.name] && cityMap[item.properties.name].areaCode) || ''
    let pinyin = (cityMap[item.properties.name] && cityMap[item.properties.name].name) || ''

    // 收集所有城市名称用于高亮
    allMapCityData.value.push(item.properties.name)

    return {
      name: item.properties.name,
      areaCode: areaCode,
      pinyin: pinyin,
      isSelected: false,
    }
  })
  if (!chart) return
  chart.setOption({
    series: [
      {
        data: seriesData, // 更新数据以反映新的选中状态
      },
    ],
  })
  // 如果是IT云，直接返回，不执行任何操作
  if (selectedCloudPlatform && selectedCloudPlatform.cloudName === 'IT云') {
    return
  }
  // 清除地图高亮
  if (chart) {
    chart.dispatchAction({
      type: 'downplay',
      seriesId: 'test1',
      name: allMapCityData.value,
    })
    chart!.dispatchAction({
      type: 'mapUnSelect',
      seriesId: 'test1',
      name: allMapCityData.value,
    })
  }
  if (showTopType.value == type) {
    showTopType.value = '0'
    currentCloudName.value = ''
    currentPlatformTypeName.value = ''
  } else {
    showTopType.value = type
    // 通过索引获取选中的云平台
    const selectedCloudPlatform = cloudPlatforms.value[selectedIndex]

    if (selectedCloudPlatform) {
      currentCloudName.value = selectedCloudPlatform.cloudName
      currentPlatformTypeName.value = ''
      showBottomTootipType.value = '0'
    }
  }
  // 触发参数更新
  emitSelectChange()
}

const changeBottomTootip = (type: any) => {
  //重置地图，
  setMapOption()
  selectedRegion.value = ''
  cityCodeName.value = props.city
  emitSelectChange()
  if (showBottomTootipType.value == type) {
    showBottomTootipType.value = '5'
    currentPlatformTypeName.value = ''
    highLightCityInfoArr = []
    highLightAreaInfoArr = []
    emitSelectChange()
    if (!chart) return
    // 全部取消高亮
    chart.dispatchAction({
      type: 'downplay',
      seriesId: 'test1',
      name: allMapCityData.value,
    })

    chart!.dispatchAction({
      type: 'mapUnSelect',
      seriesId: 'test1',
      name: allMapCityData.value,
    })
  } else {
    showBottomTootipType.value = type
    // 获取当前选中的云平台
    const selectedIndex = parseInt(showTopType.value) - 1
    const selectedCloudPlatform = cloudPlatforms.value[selectedIndex]

    if (selectedCloudPlatform && selectedCloudPlatform.platformTypes) {
      const platformIndex = parseInt(type) - 1
      currentPlatformTypeName.value =
        selectedCloudPlatform.platformTypes[platformIndex].platformTypeName || ''
    } else {
      // 兜底使用固定映射
      currentPlatformTypeName.value = platformTypeNameMap[type] || ''
    }

    // 触发参数更新
    emitSelectChange()

    // 根据云平台和平台类型获取高亮数据
    if (currentCloudName.value && currentPlatformTypeName.value) {
      let selectCity: any = []
      let selectArea: any = []
      let needHighLightData: any = []

      getComPowerMapStatsCloudCityAreas({
        cloudName: currentCloudName.value,
        platformTypeName: currentPlatformTypeName.value,
      }).then((res) => {
        if (res.code === 200) {
          let selectData: any = res.entity
          selectData.forEach((item: any) => {
            selectCity.push(item.cityCode)
            item.areaCodes.forEach((area: any) => {
              selectArea.push(area)
            })
          })
          highLightCityInfoArr = selectCity
          highLightAreaInfoArr = selectArea
          needHighLightCityDataInfoArr = findCitiesByCodes(selectCity, cityMap)
          needHighLightAreaDataInfoArr = findCitiesByCodes(selectArea, cityMap)
          if (props.city === 'zhejiang') {
            needHighLightData = findCitiesByCodes(selectCity, cityMap)
          } else {
            needHighLightData = findCitiesByCodes(selectArea, cityMap)
          }

          if (!chart) return

          chart.dispatchAction({
            type: 'mapUnSelect',
            seriesId: 'test1',
            name: allMapCityData.value,
          })
          // 全部取消高亮
          chart.dispatchAction({
            type: 'downplay',
            seriesId: 'test1',
            name: allMapCityData.value,
          })

          // 高亮选中的区域
          chart.dispatchAction({
            type: 'highlight',
            seriesId: 'test1',
            name: needHighLightData,
          })
        }
      })
    }
  }
}

// 统一的参数传递函数
const emitSelectChange = () => {
  const selectParams = {
    cityCode: null as number | null,
    areaCode: '',
    cloudName: currentCloudName.value,
    platformTypeName: currentPlatformTypeName.value,
    city: cityCodeName.value,
  }

  // 如果有选中的区域，更新城市和区县代码
  if (selectedRegion.value) {
    if (props.city === 'zhejiang') {
      selectParams.cityCode = cityCodeMap.value[selectedRegion.value] || null
      selectParams.areaCode = ''
    } else {
      const currentCityName = cityNameMap.value[props.city] || ''
      selectParams.cityCode = cityCodeMap.value[currentCityName] || null
      selectParams.areaCode = areaCodeMap.value[selectedRegion.value] || ''
    }
  } else {
    // 没有选中区域时，设置初始值
    if (props.city !== 'zhejiang') {
      // 非浙江省时，设置当前城市的cityCode
      const currentCityName = cityNameMap.value[props.city] || ''
      selectParams.cityCode = cityCodeMap.value[currentCityName] || null
      selectParams.areaCode = ''
    }
    // 浙江省时保持 cityCode: null, areaCode: ''
  }

  emit('selectChange', selectParams)
}

// 返回上级功能
const backToParent = () => {
  console.log('返回上级: 切换到浙江省')

  // 触发双击事件，切换回浙江省地图
  emit('doubleClick', {
    regionName: '浙江省',
    cityCode: 'zhejiang',
    cloudName: currentCloudName.value,
    platformTypeName: currentPlatformTypeName.value,
  })
}

// 存储区域点位置信息（替代getModel）
const regionPoints = ref<Array<{ name: string; point: [number, number] }>>([])

// 扩展 ECharts 选项类型
interface EChartsOptionExt extends echarts.EChartsOption {
  series?: SeriesOption | SeriesOption[]
}
// 为地图系列定义特定类型
interface MapSeriesOption extends echarts.MapSeriesOption {
  data?: RegionData[]
}
// 定义区域数据类型
interface RegionData {
  name: string
  value: number
  allocated: number
  free: number
}
// 准备区域位置数据（使用公共API替代getModel）
const prepareRegionPoints = () => {
  if (!chart) return

  const points: any = []
  // const seriesOption:any = chart.getOption().series?.[0];

  const option = chart.getOption() as EChartsOptionExt

  // 3.1 安全类型检查
  if (!option.series) {
    console.error('No series found in chart options')
    return
  }

  // 3.2 处理单系列或多系列情况
  const seriesArray = Array.isArray(option.series) ? option.series : [option.series]

  // 3.3 类型安全的数据处理
  const mapSeries = seriesArray.find((s) => s.type === 'map') as MapSeriesOption

  if (!mapSeries || !Array.isArray(mapSeries.data)) {
    console.error('Map series data not found or is not an array')
    return
  }

  mapSeries.data.forEach((item) => {
    const point = chart!.convertToPixel('geo', item.name)
    if (point) {
      points.push({
        name: item.name,
        point: point,
      })
    }
  })

  regionPoints.value = points
}

// 单击事件定时器
const timerId: any = ref(null)

let highLightCityInfoArr: any = []
let highLightAreaInfoArr: any = []
// 省级地图和地市地图切换的时候，需要高亮的数据信息
let needHighLightCityDataInfoArr: any = []
let needHighLightAreaDataInfoArr: any = []
// 记录事件日志
const mapEvent = (type: 'click' | 'dblclick', param: any) => {
  if (type == 'click' && timerId.value) {
    return
  } else {
    if (type == 'click') {
      if (!chart) return
      contextMenu.value.visible = false
      const regionName = param.name
      if (
        (props.city === 'zhejiang' && highLightCityInfoArr.length == 0) ||
        (props.city !== 'zhejiang' && highLightAreaInfoArr.length == 0)
      ) {
        if (param.data.isSelected == true) {
          selectedRegion.value = ''
          cityCodeName.value = props.city
        } else {
          selectedRegion.value = regionName
          cityCodeName.value = props.city
        }
        let seriesData = mapData.value.features.map((item: any) => {
          let areaCode =
            (cityMap[item.properties.name] && cityMap[item.properties.name].areaCode) || ''
          let pinyin = (cityMap[item.properties.name] && cityMap[item.properties.name].name) || ''

          // 收集所有城市名称用于高亮
          allMapCityData.value.push(item.properties.name)

          return {
            name: item.properties.name,
            areaCode: areaCode,
            pinyin: pinyin,
            isSelected: param.data.areaCode == areaCode ? !param.data.isSelected : false,
          }
        })
        chart.setOption({
          series: [
            {
              data: seriesData, // 更新数据以反映新的选中状态
            },
          ],
        })
        emitSelectChange()
      } else if (
        highLightCityInfoArr.indexOf(param.data.areaCode) != -1 ||
        highLightAreaInfoArr.indexOf(param.data.areaCode) != -1
      ) {
        // 判断是选中，还是取消选中
        if (selectedRegion.value === regionName) {
          selectedRegion.value = null
        } else {
          selectedRegion.value = regionName
        }

        // 修改选中区域高亮颜色------start
        chart.dispatchAction({
          type: 'mapUnSelect',
          seriesId: 'test1',
          name: allMapCityData.value,
        })
        // 全部取消高亮
        chart.dispatchAction({
          type: 'downplay',
          seriesId: 'test1',
          name: allMapCityData.value,
        })
        let seriesData = mapData.value.features.map((item: any) => {
          let areaCode =
            (cityMap[item.properties.name] && cityMap[item.properties.name].areaCode) || ''
          let pinyin = (cityMap[item.properties.name] && cityMap[item.properties.name].name) || ''
          return {
            name: item.properties.name,
            areaCode: areaCode,
            emphasis: {
              color: '#fff',
              itemStyle: {
                areaColor:
                  item.properties.name === param.data.name && !param.data.isClickedHight
                    ? 'rgba(0,254,233,0.8)'
                    : 'rgba(0,254,233,0.6)',
              },
            },
            select: {
              color: '#fff',
              itemStyle: {
                areaColor:
                  item.properties.name === param.data.name && !param.data.isClickedHight
                    ? 'rgba(0,254,233,0.8)'
                    : 'rgba(0,254,233,0.6)',
              },
            },
            pinyin: pinyin,
            isSelected: false,
            isClickedHight: !param.data.isClickedHight && item.properties.name === param.data.name,
          }
        })
        chart.setOption({
          series: [
            {
              data: seriesData, // 更新数据以反映新的选中状态
            },
          ],
        })
        if (props.city === 'zhejiang') {
          chart.dispatchAction({
            type: 'highlight',
            seriesId: 'test1',
            name: needHighLightCityDataInfoArr,
          })
        } else {
          chart.dispatchAction({
            type: 'highlight',
            seriesId: 'test1',
            name: needHighLightAreaDataInfoArr,
          })
        }
        // 修改选中区域高亮颜色 ------end

        // 使用统一的参数传递函数
        emitSelectChange()
      } else {
        chart.dispatchAction({
          type: 'mapUnSelect',
          seriesId: 'test1',
          name: [param.data.name],
        })

        if (props.city === 'zhejiang') {
          chart.dispatchAction({
            type: 'highlight',
            seriesId: 'test1',
            name: needHighLightCityDataInfoArr,
          })
        } else {
          chart.dispatchAction({
            type: 'highlight',
            seriesId: 'test1',
            name: needHighLightAreaDataInfoArr,
          })
        }
      }
    } else {
      console.log('双击下钻')
      contextMenu.value.visible = false
      if (param.componentType === 'series' && param.seriesType === 'map') {
        const regionName = param.name

        // 只在浙江省地图时启用双击功能
        if (props.city === 'zhejiang') {
          // 城市名称映射
          const cityNameMapping: Record<string, string> = {
            杭州市: 'hangzhou',
            宁波市: 'ningbo',
            温州市: 'wenzhou',
            嘉兴市: 'jiaxing',
            湖州市: 'huzhou',
            绍兴市: 'shaoxing',
            金华市: 'jinhua',
            衢州市: 'quzhou',
            舟山市: 'zhoushan',
            台州市: 'taizhou',
            丽水市: 'lishui',
          }

          const cityCode = cityNameMapping[regionName]
          if (cityCode) {
            // 触发双击事件，通知父组件切换到对应城市
            emit('doubleClick', {
              regionName,
              cityCode,
              cloudName: currentCloudName.value,
              platformTypeName: currentPlatformTypeName.value,
            })
          }
        }
      }
    }
  }
}

onMounted(async () => {
  try {
    // 先初始化映射数据
    await loadMappingData()

    // 动态加载地图数据
    const geojson = await loadMapData()

    // 注册地图
    echarts.registerMap(currentMapName.value, geojson)

    // 初始化图表
    chart = echarts.init(mapRef.value!, 'default', {
      // width: 720,
      // height: 681,
      // renderer: 'svg',
    })

    // 设置地图选项
    setMapOption()

    // 添加点击事件监听
    chart.on('click', (params: any) => {
      if (params.componentType === 'series' && params.seriesType === 'map') {
        timerId.value = setTimeout(() => {
          if (timerId.value == null) {
            return
          }
          clearTimeout(timerId.value)
          timerId.value = null
          mapEvent('click', params)
          // 这里可以执行实际的单击操作
        }, 300)

        // // 根据选中区域更新算力数据
        // let areaCodeForApi = ''
        // if (selectedRegion.value) {
        //   if (props.city === 'zhejiang') {
        //     areaCodeForApi = ''
        //   } else {
        //     areaCodeForApi = areaCodeMap.value[selectedRegion.value] || ''
        //   }
        // }
        // getTotalCompute(areaCodeForApi)
      }
    })

    // 添加右键点击事件监听
    chart.on('contextmenu', (params: any) => {
      console.log(params.componentType === 'series')
      console.log(params.seriesType === 'map')
      if (params.componentType === 'series' && params.seriesType === 'map') {
        activeInfo.value = params
        const event = params.event.event
        const scaleX = window.innerWidth / 1920
        const scaleY = window.innerHeight / 1080
        const x = event && event.clientX / scaleX
        const y = event && event.clientY / scaleY

        // 显示右键菜单
        contextMenu.value = {
          visible: true,
          x,
          y,
          name: '资源概况查看',
        }
      }
    })
    chart.getZr().on('contextmenu', function (params: any) {
      // 阻止浏览器默认右键菜单
      params.event.preventDefault()

      const pointInPixel: [number, number] = [params.offsetX, params.offsetY]

      // 关闭当前菜单
      contextMenu.value.visible = false

      // 检查是否在地图区域内
      if (chart?.containPixel('geo', pointInPixel)) {
        let minDistance = Infinity

        // 查找最近的区域
        regionPoints.value.forEach((region) => {
          const distance = Math.sqrt(
            Math.pow(pointInPixel[0] - region.point[0], 2) +
              Math.pow(pointInPixel[1] - region.point[1], 2),
          )

          if (distance < minDistance) {
            minDistance = distance
          }
        })

        const threshold = 30 // 像素距离阈值

        if (minDistance < threshold) {
          // activeInfo.value = null
        } else {
          // 点击地图区域空白部分
          activeInfo.value = null
          const event = params.event
          const scaleX = window.innerWidth / 1920
          const scaleY = window.innerHeight / 1080
          const x = event && event.clientX / scaleX
          const y = event && event.clientY / scaleY
          // 显示右键菜单
          contextMenu.value = {
            visible: true,
            x,
            y,
            name: '资源概况查看',
          }
        }
      }
    })

    // 添加双击事件监听
    chart.on('dblclick', (params: any) => {
      if (timerId.value) {
        clearTimeout(timerId.value)
        timerId.value = null
      }
      mapEvent('dblclick', params)

      // if (params.componentType === 'series' && params.seriesType === 'map') {
      //   const regionName = params.name
      //
      //   // 只在浙江省地图时启用双击功能
      //   if (props.city === 'zhejiang') {
      //     // 城市名称映射
      //     const cityNameMapping: Record<string, string> = {
      //       杭州市: 'hangzhou',
      //       宁波市: 'ningbo',
      //       温州市: 'wenzhou',
      //       嘉兴市: 'jiaxing',
      //       湖州市: 'huzhou',
      //       绍兴市: 'shaoxing',
      //       金华市: 'jinhua',
      //       衢州市: 'quzhou',
      //       舟山市: 'zhoushan',
      //       台州市: 'taizhou',
      //       丽水市: 'lishui',
      //     }
      //
      //     const cityCode = cityNameMapping[regionName]
      //     if (cityCode) {
      //       // 触发双击事件，通知父组件切换到对应城市
      //       emit('doubleClick', {
      //         regionName,
      //         cityCode,
      //         cloudName: currentCloudName.value,
      //         platformTypeName: currentPlatformTypeName.value,
      //       })
      //     }
      //   }
      // }
    })

    // 获取数据
    // getTotalCompute()
    getCloudPlatformStats() // 获取动态云平台数据

    // 触发初始参数传递
    emitSelectChange()

    // 初始化完成后，如果props.city不是默认值，确保地图正确显示
    console.log('地图初始化完成，当前城市:', props.city)
  } catch (error) {
    console.error('地图初始化失败:', error)
  }
})

// 原来的watch现在由新的updateMap函数处理
// watch(
//   toRef(props, 'city'),
//   async (newCity, oldCity) => {
//     // 现在由updateMap函数处理
//   },
//   { immediate: false },
// )
</script>

<style scoped>
@font-face {
  font-family: 'ziHuiJingDianYaHei';
  src: url('/fonts/zhihui.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.card-content {
  padding: 2px 10px 6px 6px;
}
.card-content-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1px;
  .card-content-item-title {
    font-size: 10px;
    color: #858688;
  }
  .card-content-item-value {
    font-size: 10px;
    font-weight: bold;
  }
}
.card-title {
  display: flex;
  width: 200px;
  justify-content: space-between;
  align-items: center;
  background: #e5ebfd;
  border-radius: 8px;
  padding: 2px 6px;
  .left {
    font-size: 12px;
    font-weight: bold;
  }
  .right {
    display: flex;
    align-items: center;
    .left {
      display: flex;
      flex-direction: column;
      .top {
        font-size: 8px;
        font-weight: bold;
        color: #437ae7;
        display: flex;
        justify-content: flex-end;
      }
      .bottom {
        font-size: 6px;
        font-weight: 400;
      }
    }
    .right {
      font-size: 12px;
    }
  }
}
.dashboard-map {
  width: 100%;
  height: 720px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #b3cfff;
  background: transparent;
  position: relative;
}

.back-to-parent {
  position: absolute;
  bottom: 180px;
  right: 60px;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  text-align: center;
  font-size: 20px;
  font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei', Arial, sans-serif;
  color: #033176;

  span {
    font-weight: 500;
    letter-spacing: 0.5px;
    display: block;
    text-align: center;
  }
}
.dashboard-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
  width: 100%;
  height: 127px;
  padding: 0 4%;
  justify-content: center;
}
.dashboard-cards > div {
  display: flex;
  padding: 12px 20px;
  cursor: pointer;
}
.dashboard-cards > div.active {
  background: url('/images/computingPower/comPowerTopBg.png') no-repeat 100% 100%;
  background-size: 100% 100%;
}

.dashboard-cards > div.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.dashboard-cards > div > div {
  margin-right: 10px;
}
.dashboard-cards > div > div > p {
  font-size: 15px;
  color: #080c13;
}

.dashboard-cards > div > div > p > b {
  font-weight: normal;
  display: inline-block;
  width: 120px;
}
.dashboard-cards > div > div > p > i {
  font-style: normal;
  color: #004fb1;
}
.dashboard-cards > div > div > span {
  display: inline-block;
  margin-top: 6px;
  font-size: 24px;
  font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei', Arial, sans-serif;
  color: #033176;
}
.card {
  flex: 1 1 calc(33.333% - 11px);
  min-width: 200px;
  background: #eaf2ff;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 18px;
  color: #2a6bff;
  box-shadow: 0 1px 4px rgba(42, 107, 255, 0.06);
}
.dashboard-bottom-dialog {
  position: fixed;
  bottom: 10px;
  width: 655px;
  left: 50%;
  margin-left: -317px;
  background: url('/images/computingPower/comPowerTopDialogBg.png') no-repeat 100% 100%;
  background-size: 100% 100%;

  .dashboard-bottom-dialog-box {
    display: flex;
    align-content: center;
    justify-content: center;
    padding: 10px;

    div {
      width: 100%;
      text-align: center;
      margin: 0 1%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      align-items: flex-start;

      .item {
        width: 48%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #033176;
        padding: 5px 0;
        cursor: pointer;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei', Arial, sans-serif;
        white-space: nowrap;

        i.imgIcon {
          width: 29px;
          height: 20px;
          display: inline-block;
          vertical-align: middle;
          margin-right: 10px;
          font-style: normal;
          background: url('/images/computingPower/comPowerIconCloud.png') no-repeat 100% 100%;
          background-size: 100% 100%;
        }

        img {
          display: inline-block;
        }

        span {
          display: inline-block;
          vertical-align: middle;
        }

        &.active {
          background: #1f62cc;
          color: #ffffff;
          border-radius: 5px;

          b {
            color: #ffffff;
            font-family: 'Microsoft YaHei';
          }

          i.imgIcon {
            background: url('/images/computingPower/comPowerIconCloudActive.png') no-repeat 100%
              100%;
            background-size: 100% 100%;
          }
        }
        b {
          font-weight: normal;
          font-family: 'Microsoft YaHei';
        }
        & > div.subTooltipInfo {
          margin: 10px 0 0;
          b {
            display: inline-block;
            vertical-align: middle;
            font-size: 14px;
            margin: 0 10px;
          }
          i {
            display: inline-block;
            vertical-align: middle;
            font-size: 14px;
            font-style: normal;
            font-family: 'Microsoft YaHei';
          }
        }
      }
    }
  }
}
.context-menu {
  position: fixed;
  background: linear-gradient(90deg, rgba(93, 188, 254, 0.78), rgba(255, 255, 254, 0.78));
  border-radius: 5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;

  .menu-header {
    padding: 10px 15px;
    cursor: pointer;
    font-size: 16px;
    color: #333;
    font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei', Arial, sans-serif;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
