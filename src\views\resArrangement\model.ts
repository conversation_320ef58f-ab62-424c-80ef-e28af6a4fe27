import { uuid } from '@/utils'
export interface IEcsModel {
  // @产品名称
  instanceName: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @产品类型
  productType: 'ecs'
  ref?: any
  uid?: string
}
export interface IPmModel {
  // @产品名称
  instanceName: string
  // 物理CPU
  cpu: number
  // 物理内存
  memory: number
  // 物理硬盘
  disk: number
  // 是否使用GPU
  isUseGpu: '0' | '1'
  // GPU型号
  gpuType: string
  // 卡类型
  gpuCardType: string
  // GPU数量
  gpuCount: number
  // 申请时长
  time: string
  // @产品类型
  productType: 'pm'
  // @ productType 对应的产品名称
  goodsName: '裸金属'
  goodsId: string
  placeholder: string
}
export interface ISecurityGroupModel {
  // @产品名称
  instanceName: string
  domainCode: string // 云平台
  regionCode: string // 资源池code
  regionName: string // 资源池名称
  azCode: string // 可用区编码
  vpcId: string // VPC ID
  description: string // 描述
  // 以下为级联选择需要的中间字段，不会提交到接口
  catalogueDomainCode: string
  catalogueDomainName: string
  domainName: string
  resourcePoolId: string
  azName: string
  vpcName: string
  // @产品类型
  productType: 'securityGroup'
  ref?: any
  uid?: string
  ruleList: any[]
}
export interface IMysqlModel {
  // @产品名称
  instanceName: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @产品类型
  productType: 'mysql'

  // @ productType 对应的产品名称
  goodsName: 'MySQL云数据库'
  goodsId: string
  ref?: any
  uid?: string
}

export interface IRedisModel {
  // @产品名称
  instanceName: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @产品类型
  productType: 'redis'

  // @ productType 对应的产品名称
  goodsName: '通用Redis'
  goodsId: string
  ref?: any
  uid?: string
}

export interface IGcsModel {
  // @产品名称
  instanceName: string
  // @实例规格
  gcs: any[]
  // @系统
  imageOs: any[]
  // @系统盘
  sysDisk: any[]
  // @是否容灾
  disasterRecovery: string
  // @网络平面
  planeValue: any[]
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @产品类型
  productType: 'gcs'
  goodsId: string
  ref?: any
  uid?: string
}
export interface IEvsModel {
  // @产品名称
  instanceName: string
  // @数据盘
  evs: any[][]
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @产品类型
  productType: 'evs'
  goodsId: string
  ref?: any
  uid?: string
}
export interface IEipModel {
  // @产品名称
  instanceName: string
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @产品类型
  productType: 'eip'

  // @ productType 对应的产品名称
  goodsName: '弹性IP'
  eipValue: number
  ref?: any
  uid?: string
}
export interface IObsModel {
  goodsId: string
  // @产品名称
  instanceName: string
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'obs'
  obs: [string, number]
  ref?: any
  uid?: string
}
export interface ISlbModel {
  // @产品名称
  instanceName: string
  // @实例规格
  slb: string
  desc: string
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'slb'
  // @ productType 对应的产品名称
  goodsName: '负载均衡'
  goodsId: string
  ref?: any
  uid?: string
}
export interface INatModel {
  // @产品名称
  instanceName: string
  // @实例规格
  nat: string
  // @开通数量
  numbers: 1
  // @申请时长
  time: string
  // @产品类型
  productType: 'nat'
  goodsId: string
  ref?: any
  uid?: string
}
export interface ICloudPortModel {
  // 产品名称
  instanceName: string
  vlanId: string
  srcIp: string
  peerIp: string
  peerPassword: string
  // 申请时长
  time: string
  // 产品类型
  productType: 'cloudPort'
  uid?: string
}

export interface IVpChildNetFormModel {
  subnetName: string
  startIp: string
  netmask: string
  ref?: any
  old: false
  uid?: string
}

export interface INetworkChildNetModel {
  ipVersion: string
  cidr: string
  uid?: string
}
export interface INetworkModel {
  // @产品名称
  instanceName: string
  // 网络类型
  networkType: string
  plane: string
  // @产品类型
  productType: 'network'
  childNet: INetworkChildNetModel[]
  uid?: string
}

export interface IVpcModel {
  // @产品名称
  instanceName: string
  // @网段
  netRange: string
  childNet: IVpChildNetFormModel[]
  // @网络平面
  plane: string[]
  // @产品类型
  productType: 'vpc'
  ref?: any
  uid: string
}

export function useNetworkModel() {
  const uid = `network_${uuid(8)}`
  const childNetUid = `network_subnet_${uuid(8)}`
  const model: INetworkModel = {
    instanceName: uid,
    networkType: 'vxlan',
    plane: '公网地址',
    childNet: [
      {
        ipVersion: 'IPv4',
        cidr: '127.0.0.1',
        uid: childNetUid,
      },
    ],
    productType: 'network',
    uid,
  }
  return model
}
export function useNetworkChildNetModel(ipVersion = 'IPv4') {
  const uid = `network_subnet_${uuid(8)}`
  const model: INetworkChildNetModel = {
    ipVersion,
    cidr: '',
    uid,
  }
  return model
}
export function useVpcModel() {
  const uid = `vpc_${uuid(8)}`
  const childNetUid = `vpc_subnet_${uuid(8)}`
  const model: IVpcModel = {
    instanceName: uid,
    netRange: '10.0.0.0/8',
    plane: ['私网地址'],
    childNet: [
      {
        subnetName: childNetUid,
        startIp: '********',
        netmask: '*************',
        ref: {
          fields: [],
        },
        old: false,
        uid: childNetUid,
      },
    ],
    productType: 'vpc',
    uid,
  }
  return model
}
export function useVpcChildNetModel() {
  const uid = `vpc_subnet_${uuid(8)}`
  const model: IVpChildNetFormModel = {
    subnetName: uid,
    startIp: '',
    netmask: '',
    ref: undefined,
    old: false,
    uid,
  }
  return model
}

export function useEcsModel() {
  const uid = `ecs_${uuid(8)}`
  const model: IEcsModel = {
    instanceName: uid,
    ecs: ['通用型', '2C4GB'],
    ecsLabel: '',
    imageOs: ['CentOS', '7.9'],
    imageOsLabel: '',
    sysDisk: ['SAS', 40],
    sysDiskLabel: '',
    disasterRecovery: '0',
    disasterRecoveryLabel: '',
    planeValue: ['公网地址'],
    planeLabel: '',
    numbers: 1,
    time: 'one_month',
    productType: 'ecs',
    uid,
  }
  return model
}
export function usePmModel() {
  const model: IPmModel = {
    // @产品名称
    instanceName: '',
    // 物理CPU
    cpu: 0,
    // 物理内存
    memory: 0,
    // 物理硬盘
    disk: 0,
    // 是否使用GPU
    isUseGpu: '0',
    // GPU型号
    gpuType: '',
    // 卡类型
    gpuCardType: '',
    // GPU数量
    gpuCount: 0,
    // @申请时长
    time: '',
    // @产品类型
    productType: 'pm',
    // @ productType 对应的产品名称
    goodsName: '裸金属',
    goodsId: '',
    placeholder: 'placeholder',
  }
  return model
}
export function useSecurityGroupModel() {
  const uid = `securityGroup_${uuid(8)}`
  const model: ISecurityGroupModel = {
    instanceName: uid,
    domainCode: '',
    regionCode: '',
    regionName: '',
    azCode: '',
    vpcId: '',
    description: '',
    catalogueDomainCode: '',
    catalogueDomainName: '',
    domainName: '',
    resourcePoolId: '',
    azName: '',
    vpcName: '',
    productType: 'securityGroup',
    uid,
    ruleList: [],
  }
  return model
}
export function useRedisModel() {
  const uid = `redis_${uuid(8)}`
  const model: IRedisModel = {
    instanceName: uid,
    ecs: ['通用型', '2C4GB'],
    ecsLabel: '',
    imageOs: ['CentOS', '7.9'],
    imageOsLabel: '',
    sysDisk: ['SAS', 40],
    sysDiskLabel: '',
    disasterRecovery: '0',
    disasterRecoveryLabel: '',
    planeValue: ['私网地址'],
    planeLabel: '',
    numbers: 1,
    time: 'one_month',
    productType: 'redis',
    goodsName: '通用Redis',
    goodsId: '',
    uid,
  }
  return model
}
export function useMysqlModel() {
  const uid = `mysql_${uuid(8)}`
  const model: IMysqlModel = {
    instanceName: uid,
    ecs: ['通用型', '2C4GB'],
    ecsLabel: '',
    imageOs: ['CentOS', '7.9'],
    imageOsLabel: '',
    sysDisk: ['SAS', 40],
    sysDiskLabel: '',
    disasterRecovery: '0',
    disasterRecoveryLabel: '',
    planeValue: ['私网地址'],
    planeLabel: '',
    numbers: 1,
    time: 'one_month',
    productType: 'mysql',
    goodsName: 'MySQL云数据库',
    goodsId: '',
    uid,
  }
  return model
}
export function useGcsModel() {
  const uid = `gcs_${uuid(8)}`
  const model: IGcsModel = {
    instanceName: uid,
    gcs: ['通用型', '4C32GB/1T4'],
    imageOs: ['CentOS', '7.9'],
    sysDisk: ['SAS', 40],
    disasterRecovery: '0',
    planeValue: ['私网地址'],
    numbers: 1,
    time: 'one_month',
    productType: 'gcs',
    goodsId: '',
    uid,
  }
  return model
}
export function useEvsModel() {
  const uid = `evs_${uuid(8)}`
  const model: IEvsModel = {
    instanceName: uid,
    evs: [['SAS', 20]],
    numbers: 1,
    time: 'one_month',
    productType: 'evs',
    goodsId: '',
    uid,
  }
  return model
}
export function useEipModel() {
  const uid = `eip_${uuid(8)}`
  const model: IEipModel = {
    instanceName: uid,
    numbers: 1,
    time: 'one_month',
    productType: 'eip',
    goodsName: '弹性IP',
    eipValue: 5,
    uid,
  }
  return model
}

export function useSlbModel() {
  const uid = `slb_${uuid(8)}`
  const model: ISlbModel = {
    instanceName: uid,
    slb: '普通型',
    desc: '',
    numbers: 1,
    time: 'one_month',
    remarks: '',
    productType: 'slb',
    goodsName: '负载均衡',
    goodsId: '',
    uid,
  }
  return model
}
export function useCloudPortModel() {
  const uid = `cloudPort_${uuid(8)}`
  const model: ICloudPortModel = {
    // 产品名称
    instanceName: uid,
    vlanId: 'vlanIdxxxx',
    srcIp: '********/30,********/30',
    peerIp: '********/30,********/30',
    peerPassword: 'xxx',
    // 申请时长
    time: '',
    // 产品类型
    productType: 'cloudPort',
    uid,
  }
  return model
}
export function useNatModel() {
  const uid = `nat_${uuid(8)}`
  const model: INatModel = {
    // @产品名称
    instanceName: uid,
    // @实例规格
    nat: '通用型',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: 'one_month',
    // @产品类型
    productType: 'nat',
    goodsId: '',
    uid,
  }
  return model
}
export function useObsModel() {
  const uid = `obs_${uuid(8)}`
  const model: IObsModel = {
    goodsId: '',
    instanceName: uid.replace('obs_', ''),
    numbers: 1,
    time: 'one_month',
    remarks: '',
    productType: 'obs',
    obs: ['OSS', 10],
    uid,
  }
  return model
}
