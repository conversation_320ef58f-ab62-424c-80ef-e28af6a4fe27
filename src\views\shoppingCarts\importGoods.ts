import * as XLSX from 'xlsx'
import SlMessage from '@/components/base/SlMessage'
import type { ICartsModel } from '@/views/resourceCenter/hooks/useShoppingCarts'
import {
  useEvsModel,
  useEcsModel,
  useGcsModel,
  useNatModel,
  useObsModel,
  useSlbModel,
  useEipModel,
  useCqModel,
  useMysqlModel,
  useRedisModel,
  useBackupModel,
  useNasModel,
  useVpnModel,
  useKafkaModel,
  useEsModel,
  useFlinkModel,
  usePmModel,
  useBldRedisModel,
} from '@/views/resourceCenter/hooks/useGoodsModels'
import { generateRandom8DigitNumber } from '@/utils'

const isDisasterRecoveryMap = {
  是: '1',
  否: '0',
}

const applyTimeMap = {
  '1个月': 'one_month',
  '3个月': 'three_months',
  '6个月': 'six_months',
  '1年': 'one_year',
  '2年': 'two_years',
}

const deployTypeMap = {
  单机版本: 'ALONE',
  主备版本: 'COLONY',
} as const

const backupTypeMap = {
  云主机: 'ECS',
  云硬盘: 'EVS',
} as const

const backupFrequencyMap = {
  每天: 'days',
  每周: 'weeks',
} as const
const weekMap = {
  周一: '1',
  周二: '2',
  周三: '3',
  周四: '4',
  周五: '5',
  周六: '6',
  周日: '7',
} as const
// sheetName -> goodsType
const goodsTypeMap = {
  云主机: 'ecs',
  GPU云主机: 'gcs',
  云硬盘: 'evs',
  弹性公网: 'eip',
  对象存储: 'obs',
  负载均衡: 'slb',
  NAT网关: 'nat',
  容器配额: 'cq',
  MySQL云数据库: 'mysql',
  通用Redis: 'redis',
  云灾备: 'backup',
  VPN: 'vpn',
  NAS: 'nas',
  Kafka: 'kafka',
  Flink: 'flink',
  裸金属: 'pm',
  ElasticSearch: 'es',
  国产Redis: 'bldRedis',
} as const

const fieldMap = {
  ecs: {
    主机名称: 'instanceName',
    功能模块: 'functionalModule',
    实例规格: 'spec',
    操作系统: 'os',
    系统盘: 'systemDisk',
    是否容灾: 'isDisasterRecovery',
    是否绑定公网IP: 'eip',
    开通数量: 'numbers',
    申请时长: 'time',
    网络平面: 'plane',
    是否挂载数据盘: 'evs',
  },
  gcs: {
    主机名称: 'instanceName',
    功能模块: 'functionalModule',
    实例规格: 'spec',
    操作系统: 'os',
    系统盘: 'systemDisk',
    是否容灾: 'isDisasterRecovery',
    是否绑定公网IP: 'eip',
    开通数量: 'numbers',
    申请时长: 'time',
    网络平面: 'plane',
    是否挂载数据盘: 'evs',
  },
  evs: {
    数据盘: 'evs',
    功能模块: 'functionalModule',
    是否挂载云主机: 'vm',
    申请时长: 'time',
    开通数量: 'numbers',
  },
  eip: {
    弹性IP名称: 'instanceName',
    功能模块: 'functionalModule',
    带宽大小: 'bandwidth',
    IP类型: 'ipType',
    云主机: 'vm',
    申请时长: 'time',
    开通数量: 'numbers',
  },
  obs: {
    对象存储名称: 'instanceName',
    功能模块: 'functionalModule',
    存储: 'storage',
    开通数量: 'numbers',
    申请时长: 'time',
  },
  slb: {
    负载均衡名称: 'instanceName',
    功能模块: 'functionalModule',
    实例规格: 'spec',
    开通数量: 'numbers',
    是否绑定公网IP: 'eip',
    申请时长: 'time',
  },
  nat: {
    网关名称: 'instanceName',
    功能模块: 'functionalModule',
    实例规格: 'spec',
    开通数量: 'numbers',
    是否绑定公网IP: 'eip',
    申请时长: 'time',
  },
  mysql: {
    MySQL云数据库名称: 'instanceName',
    功能模块: 'functionalModule',
    系列: 'series',
    实例规格: 'spec',
    版本: 'version',
    系统盘: 'systemDisk',
    是否容灾: 'isDisasterRecovery',
    是否绑定公网IP: 'eip',
    申请时长: 'time',
    网络平面: 'plane',
    是否挂载数据盘: 'evs',
  },
  redis: {
    通用Redis名称: 'instanceName',
    功能模块: 'functionalModule',
    实例规格: 'spec',
    版本: 'version',
    系统盘: 'systemDisk',
    是否容灾: 'isDisasterRecovery',
    是否绑定公网IP: 'eip',
    开通数量: 'numbers',
    申请时长: 'time',
    网络平面: 'plane',
    是否挂载数据盘: 'evs',
  },
  backup: {
    云灾备名称: 'instanceName',
    备份类型: 'backupType',
    备份频率: 'backupFrequency',
    星期: 'week',
  },
  vpn: {
    VPN名称: 'instanceName',
    最大客户端数量: 'maxClient',
    带宽: 'bandwidth',
    申请时长: 'time',
    开通数量: 'numbers',
  },
  nas: {
    NAS名称: 'instanceName',
    存储路径: 'path',
    存储大小: 'size',
    申请时长: 'time',
    开通数量: 'numbers',
  },
  kafka: {
    Kafka名称: 'instanceName',
    数据流量: 'dataFlow',
    分区: 'partition',
    副本数: 'replica',
    保留时间: 'retentionTime',
    数据存储总量: 'dataStorage',
    申请时长: 'time',
  },
  flink: {
    Flink名称: 'instanceName',
    vCPU: 'vCPU',
    内存: 'memory',
    申请时长: 'time',
  },
  pm: {
    裸金属名称: 'instanceName',
    CPU: 'cpu',
    内存: 'memory',
    硬盘: 'disk',
    '使用GPU/NPU': 'gpu',
    申请时长: 'time',
  },
  cq: {
    '4A账号': 'account',
    '4A账号绑定的手机号': 'phone',
    配额名称: 'quotaName',
    容器配额: 'cpu',
    申请时长: 'time',
    是否使用GPU: 'gpu',
  },
  es: {
    索引模版名称: 'instanceName',
    日均增量数据: 'dailyData',
    数据保留时间: 'retentionTime',
    索引副本数: 'replica',
    磁盘大小: 'diskSize',
    申请时长: 'time',
    索引模版: 'indexTemplate',
  },
  bldRedis: {
    实例名称: 'instanceName',
    CPU架构: 'cpuArch',
    实例IP: 'ip',
    申请时长: 'time',
  },
} as const

// 精简类型定义
type SheetNameCn = keyof typeof goodsTypeMap

type GoodsType = keyof typeof fieldMap

type FieldMap = typeof fieldMap

type GoodsItem<T extends GoodsType> = {
  [P in keyof FieldMap[T] as Extract<FieldMap[T][P], PropertyKey>]: any
}

type Goods = Partial<{
  [T in GoodsType]: GoodsItem<T>[]
}>

// 校验工作表是否支持
function isSupportedSheetName(name: string): name is SheetNameCn {
  return Object.prototype.hasOwnProperty.call(goodsTypeMap, name)
}

// 读取并解析 Excel 文件，返回结构化后的结果
async function importGoods(file: File, cartModel: ICartsModel): Promise<Goods> {
  try {
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (fileType !== 'xlsx' && fileType !== 'xls') {
      SlMessage.error('只支持导入Excel文件（.xlsx或.xls格式）')
      return {}
    }

    const data = await readFileAsArrayBuffer(file)
    const workbook = XLSX.read(new Uint8Array(data), { type: 'array' })

    const result = parseWorkbook(workbook)
    addCart(result, cartModel)
    SlMessage.success('Excel文件解析成功')
    return result
  } catch (error) {
    console.error('Excel解析错误:', error)
    SlMessage.error('Excel文件解析失败')
    return {}
  }
}

function readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target?.result as ArrayBuffer)
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsArrayBuffer(file)
  })
}

function parseWorkbook(workbook: XLSX.WorkBook): Goods {
  const goods: Goods = {}
  const sheetNames = workbook.SheetNames

  sheetNames.forEach((sheetName) => {
    if (!isSupportedSheetName(sheetName)) return

    const goodsType = goodsTypeMap[sheetName]
    const worksheet = workbook.Sheets[sheetName]
    const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]
    const nonEmptyRows = rows.filter(
      (r) =>
        Array.isArray(r) && r.some((c) => c !== undefined && c !== null && `${c}`.trim() !== ''),
    )

    if (goodsType === 'cq') {
      // 模板：
      // 第1个非空行：基础信息表头（4A账号、4A账号绑定的手机号）
      // 第2个非空行：基础信息取值行
      // 第3个非空行：配额明细表头（配额名称、容器配额、申请时长、是否使用GPU）
      // 第4个非空行起：配额明细数据
      if (nonEmptyRows.length < 3) return

      const baseHeader = nonEmptyRows[0] as string[]
      const baseValue = nonEmptyRows[1] as any[] | undefined
      const detailHeader = nonEmptyRows[2] as string[]
      const detailRows = nonEmptyRows.slice(3)

      const base: GoodsItem<'cq'> = {} as GoodsItem<'cq'>
      baseHeader.forEach((cnKey, idx) => {
        const fieldKey = (fieldMap as any)[goodsType]?.[cnKey] as keyof GoodsItem<'cq'>
        if (!fieldKey) return // 仅将已知字段写入（例如 account、phone）
        ;(base as any)[fieldKey] = baseValue ? (baseValue[idx] ?? '') : ''
      })

      detailRows.forEach((row) => {
        if (!Array.isArray(row) || row.length === 0) return
        const item: GoodsItem<'cq'> = { ...(base as GoodsItem<'cq'>) }
        detailHeader.forEach((cnKey, idx) => {
          const fieldKey = (fieldMap as any)[goodsType]?.[cnKey] as keyof GoodsItem<'cq'>
          if (!fieldKey) return
          ;(item as any)[fieldKey] = row[idx] ?? ''
        })
        if (Object.keys(item).length === 0) return
        if (!goods[goodsType]) (goods as any)[goodsType] = []
        ;(goods[goodsType] as any[]).push(item)
      })
      return
    }

    // 非 cq：默认第一个非空行即表头
    const headerRowIndex = 0
    if (nonEmptyRows.length <= headerRowIndex) return
    const header = (nonEmptyRows[headerRowIndex] || []) as string[]
    const dataRows = nonEmptyRows.slice(headerRowIndex + 1)

    dataRows.forEach((row) => {
      if (!Array.isArray(row) || row.length === 0) return
      const item: Record<string, any> = {}
      header.forEach((cnKey, idx) => {
        const fieldKey = (fieldMap as any)[goodsType]?.[cnKey] as string
        if (!fieldKey) return
        item[fieldKey] = row[idx] ?? ''
      })
      if (Object.keys(item).length === 0) return
      if (!goods[goodsType]) (goods as any)[goodsType] = []
      ;(goods[goodsType] as any[]).push(item as any)
    })
  })
  return goods
}

function addCart(goods: Goods, cartModel: ICartsModel) {
  for (const goodsType of Object.keys(goods) as GoodsType[]) {
    const goodsList = goods[goodsType]
    if (!goodsList) continue
    if (goodsType === 'ecs') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'ecs'>
        const model = useEcsModel()
        const orderJson = Object.assign(model, {
          numbers: goodsItem.numbers,
          disasterRecovery:
            isDisasterRecoveryMap[
              goodsItem.isDisasterRecovery as keyof typeof isDisasterRecoveryMap
            ],
          eipValue: goodsItem.eip || 0,
          isBindPublicNetworkIp: goodsItem.eip ? '1' : '0',
          planeValue: goodsItem.plane.split(';'),
          functionalModule: goodsItem.functionalModule,
          ecs: goodsItem.spec
            ? [goodsItem.spec.split('/')[0], goodsItem.spec.split('/').slice(1).join('/')]
            : [],
          isMountEvs: goodsItem.evs ? '1' : '0',
          evs: goodsItem.evs ? goodsItem.evs.split(';').map((item: any) => item.split('/')) : [],
          sysDisk: goodsItem.systemDisk ? goodsItem.systemDisk.split('/') : [],
          imageOs: goodsItem.os ? goodsItem.os.split('/') : [],
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.ecsList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'gcs') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'gcs'>
        const model = useGcsModel()
        const orderJson = Object.assign(model, {
          numbers: goodsItem.numbers,
          disasterRecovery:
            isDisasterRecoveryMap[
              goodsItem.isDisasterRecovery as keyof typeof isDisasterRecoveryMap
            ],
          eipValue: goodsItem.eip || 0,
          isBindPublicNetworkIp: goodsItem.eip ? '1' : '0',
          planeValue: goodsItem.plane.split(';'),
          functionalModule: goodsItem.functionalModule,
          gcs: goodsItem.spec
            ? [goodsItem.spec.split('/')[0], goodsItem.spec.split('/').slice(1).join('/')]
            : [],
          isMountEvs: goodsItem.evs ? '1' : '0',
          evs: goodsItem.evs ? goodsItem.evs.split(';').map((e: any) => e.split('/')) : [],
          sysDisk: goodsItem.systemDisk ? goodsItem.systemDisk.split('/') : [],
          imageOs: goodsItem.os ? goodsItem.os.split('/') : [],
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.gcsList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'evs') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'evs'>
        const model = useEvsModel()
        const orderJson = Object.assign(model, {
          isMountEcs: goodsItem.vm ? '1' : '0',
          ecsName: goodsItem.vm ? goodsItem.vm.split('/')[1] : '',
          vmId: goodsItem.vm ? goodsItem.vm.split('/')[0] : '',
          numbers: goodsItem.numbers,
          functionalModule: goodsItem.functionalModule,
          evs: goodsItem.evs ? goodsItem.evs.split(';').map((e: any) => e.split('/')) : [],
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.evsList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'eip') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'eip'>
        const model = useEipModel()
        const orderJson = Object.assign(model, {
          eipValue: goodsItem.bandwidth || 0,
          ecsName: goodsItem.vm ? goodsItem.vm.split('/')[1] : '',
          vmId: goodsItem.vm ? goodsItem.vm.split('/')[0] : '',
          numbers: 1,
          functionalModule: goodsItem.functionalModule,
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
          ipVersion: goodsItem.ipType,
        })
        cartModel.eipList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'obs') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'obs'>
        const model = useObsModel()
        const storage = goodsItem.storage ? `${goodsItem.storage}`.split('/') : []
        const orderJson = Object.assign(model, {
          numbers: goodsItem.numbers,
          functionalModule: goodsItem.functionalModule,
          obs: storage.length ? [storage[0], Number(storage[1]) || 0] : ['', 0],
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.obsList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'slb') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'slb'>
        const model = useSlbModel()
        const orderJson = Object.assign(model, {
          numbers: 1,
          eipValue: goodsItem.eip || 0,
          isBindPublicNetworkIp: goodsItem.eip ? '1' : '0',
          functionalModule: goodsItem.functionalModule,
          slb: goodsItem.spec || '',
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.slbList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'nat') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'nat'>
        const model = useNatModel()
        const orderJson = Object.assign(model, {
          numbers: 1,
          eipValue: goodsItem.eip || 0,
          isBindPublicNetworkIp: goodsItem.eip ? '1' : '0',
          functionalModule: goodsItem.functionalModule,
          nat: goodsItem.spec || '',
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.natList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'cq') {
      const firstGoods = goodsList[0] as GoodsItem<'cq'>
      cartModel.baseList[0].orderJson.a4Account = firstGoods.account || ''
      cartModel.baseList[0].orderJson.a4Phone = firstGoods.phone || ''
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'cq'>
        const model = useCqModel()
        const orderJson = Object.assign(model, {
          instanceName: goodsItem.quotaName || '',
          // cpu, gpu 留默认
          cpu: goodsItem.cpu ? goodsItem.cpu.split('/') : [],
          gpu: goodsItem.gpu ? goodsItem.gpu.split('/') : [],
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
          isUseGpu: goodsItem.gpu ? '1' : '0',
          account: goodsItem.account || '',
          phone: goodsItem.phone || '',
        })
        cartModel.cqList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'mysql') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'mysql'>
        const model = useMysqlModel()
        const orderJson = Object.assign(model, {
          numbers: 1,
          disasterRecovery:
            isDisasterRecoveryMap[
              goodsItem.isDisasterRecovery as keyof typeof isDisasterRecoveryMap
            ],
          eipValue: goodsItem.eip || 0,
          isBindPublicNetworkIp: goodsItem.eip ? '1' : '0',
          planeValue: goodsItem.plane.split(';'),
          functionalModule: goodsItem.functionalModule,
          ecs: goodsItem.spec
            ? [goodsItem.spec.split('/')[0], goodsItem.spec.split('/').slice(1).join('/')]
            : [],
          isMountEvs: goodsItem.evs ? '1' : '0',
          evs: goodsItem.evs ? goodsItem.evs.split(';').map((e: any) => e.split('/')) : [],
          sysDisk: goodsItem.systemDisk ? goodsItem.systemDisk.split('/') : [],
          imageOs: goodsItem.version ? goodsItem.version.split('/') : [],
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
          deployType: goodsItem.series
            ? deployTypeMap[goodsItem.series as keyof typeof deployTypeMap] || 'ALONE'
            : 'ALONE',
        })
        cartModel.mysqlList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'redis') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'redis'>
        const model = useRedisModel()
        const orderJson = Object.assign(model, {
          numbers: goodsItem.numbers,
          disasterRecovery:
            isDisasterRecoveryMap[
              goodsItem.isDisasterRecovery as keyof typeof isDisasterRecoveryMap
            ],
          eipValue: goodsItem.eip || 0,
          isBindPublicNetworkIp: goodsItem.eip ? '1' : '0',
          planeValue: goodsItem.plane.split(';'),
          functionalModule: goodsItem.functionalModule,
          ecs: goodsItem.spec
            ? [goodsItem.spec.split('/')[0], goodsItem.spec.split('/').slice(1).join('/')]
            : [],
          isMountEvs: goodsItem.evs ? '1' : '0',
          evs: goodsItem.evs ? goodsItem.evs.split(';').map((e: any) => e.split('/')) : [],
          sysDisk: goodsItem.systemDisk ? goodsItem.systemDisk.split('/') : [],
          imageOs: goodsItem.version ? goodsItem.version.split('/') : [],
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.redisList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'backup') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'backup'>
        const model = useBackupModel()
        const orderJson = Object.assign(model, {
          jobName: goodsItem.instanceName || '',
          backupType: backupTypeMap[goodsItem.backupType as keyof typeof backupTypeMap],
          frequency:
            backupFrequencyMap[goodsItem.backupFrequency as keyof typeof backupFrequencyMap],
          daysOfWeek:
            goodsItem.backupFrequency === '每天'
              ? ''
              : weekMap[goodsItem.week as keyof typeof weekMap],
        })
        cartModel.backupList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'nas') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'nas'>
        const model = useNasModel()
        const orderJson = Object.assign(model, {
          numbers: 1,
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
          storageSize: Number(goodsItem.size) || 0,
          storagePath: goodsItem.path || '',
        })
        cartModel.nasList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'vpn') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'vpn'>
        const model = useVpnModel()
        const orderJson = Object.assign(model, {
          numbers: 1,
          instanceName: goodsItem.instanceName,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
          maxClient: Number(goodsItem.maxClient) || 0,
          bandwidth: Number(goodsItem.bandwidth) || 0,
        })
        cartModel.vpnList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'kafka') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'kafka'>
        const model = useKafkaModel()
        const orderJson = Object.assign(model, {
          instanceName: goodsItem.instanceName,
          partition: Number(goodsItem.partition) || 0,
          replication: Number(goodsItem.replica) || 0,
          retentionTime: Number(goodsItem.retentionTime) || 0,
          dataFlow: Number(goodsItem.dataFlow) || 0,
          dataStorage: Number(goodsItem.dataStorage) || 0,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.kafkaList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'es') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'es'>
        const model = useEsModel()
        const orderJson = Object.assign(model, {
          instanceName: goodsItem.instanceName,
          dailyIncrementData: Number(goodsItem.dailyData) || 0,
          retentionTime: Number(goodsItem.retentionTime) || 0,
          replication: Number(goodsItem.replica) || 0,
          diskSize: Number(goodsItem.diskSize) || 0,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
          template: goodsItem.indexTemplate || '',
        })
        cartModel.esList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'flink') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'flink'>
        const model = useFlinkModel()
        const orderJson = Object.assign(model, {
          instanceName: goodsItem.instanceName,
          vCPU: Number(goodsItem.vCPU) || 0,
          memory: Number(goodsItem.memory) || 0,
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.flinkList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'pm') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'pm'>
        const model = usePmModel()
        const orderJson = Object.assign(model, {
          instanceName: goodsItem.instanceName,
          cpu: Number(goodsItem.cpu) || 0,
          memory: Number(goodsItem.memory) || 0,
          disk: Number(goodsItem.disk) || 0,
          isUseGpu: goodsItem.gpu ? '1' : '0',
          gpuType: goodsItem.gpu.split('/')[0],
          gpuCount: goodsItem.gpu.split('/')[2],
          gpuCardType: goodsItem.gpu.split('/')[1],
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
        })
        cartModel.pmList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    } else if (goodsType === 'bldRedis') {
      goodsList.forEach((item) => {
        const goodsItem = item as GoodsItem<'bldRedis'>
        const model = useBldRedisModel()
        const orderJson = Object.assign(model, {
          instanceName: goodsItem.instanceName,
          ip: goodsItem.ip || '',
          time: applyTimeMap[goodsItem.time as keyof typeof applyTimeMap],
          cpuArch: goodsItem.cpuArch || '',
        })
        cartModel.bldRedisList.push({
          id: generateRandom8DigitNumber(),
          goodsType,
          orderJson,
        })
      })
    }
  }
}

export default importGoods
