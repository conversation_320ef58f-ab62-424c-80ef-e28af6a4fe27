define([], function () {
  /*!-----------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
   * Released under the MIT license
   * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
   *-----------------------------------------------------------*/ ;(globalThis._VSCODE_NLS_MESSAGES =
    [
      '{0} ({1})',
      'input',
      'Maiuscole/minuscole',
      'Parola intera',
      'Usa espressione regolare',
      'input',
      'Mantieni maiuscole/minuscole',
      'Ispezionarlo nella visualizzazione accessibile con {0}.',
      'Ispezionarlo nella visualizzazione accessibile tramite il comando Apri visualizzazione accessibile che attualmente non \xE8 attivabile tramite il tasto di scelta rapida.',
      'Errore: {0}',
      'Avviso: {0}',
      'Info: {0}',
      ' o {0} per la cronologia',
      ' ({0} per la cronologia)',
      'Input cancellato',
      'Non associato',
      'Casella di selezione',
      'Altre azioni...',
      'Filtro',
      'Corrispondenza fuzzy',
      'Digitare per filtrare',
      'Digitare per la ricerca',
      'Digitare per la ricerca',
      'Chiudi',
      'Nessun risultato',
      'Non sono stati trovati risultati.',
      null,
      '(vuoto)',
      '{0}: {1}',
      'Si \xE8 verificato un errore di sistema ({0})',
      'Si \xE8 verificato un errore sconosciuto. Per altri dettagli, vedere il log.',
      'Si \xE8 verificato un errore sconosciuto. Per altri dettagli, vedere il log.',
      '{0} ({1} errori in totale)',
      'Si \xE8 verificato un errore sconosciuto. Per altri dettagli, vedere il log.',
      'CTRL',
      'MAIUSC',
      'ALT',
      'Windows',
      'CTRL',
      'MAIUSC',
      'ALT',
      'Super',
      'CTRL',
      'MAIUSC',
      'Opzione',
      'Comando',
      'CTRL',
      'MAIUSC',
      'ALT',
      'Windows',
      'CTRL',
      'MAIUSC',
      'ALT',
      'Super',
      null,
      null,
      null,
      null,
      null,
      'Si attiene alla fine anche quando si passa a righe pi\xF9 lunghe',
      'Si attiene alla fine anche quando si passa a righe pi\xF9 lunghe',
      'Cursori secondari rimossi',
      '&&Annulla',
      'Annulla azione',
      '&&Ripeti',
      'Ripeti',
      '&&Seleziona tutto',
      'Seleziona tutto',
      'Tieni premuto il tasto {0} per passare il mouse',
      'Caricamento\u2026',
      "Il numero di cursori \xE8 stato limitato a {0}. Provare a usare [Trova e sostituisci](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) per modifiche di dimensioni maggiori o aumentare l'impostazione del limite di pi\xF9 cursori dell'editor.",
      'Aumentare limite multi-cursore',
      'Attiva/Disattiva comprimi aree non modificate',
      'Attiva/Disattiva mostra blocchi di codice spostati',
      'Attiva/disattiva la visualizzazione inline quando lo spazio \xE8 limitato',
      'Editor diff',
      'Interruttore laterale',
      'Esci da Sposta confronto',
      'Comprimi tutte le aree non modificate',
      'Mostra tutte le aree non modificate',
      'Ripristina',
      'Visualizzatore differenze accessibile',
      'Vai alla differenza successiva',
      'Vai alla differenza precedente',
      'Icona per "Inserisci" nel visualizzatore differenze accessibile.',
      'Icona per "Rimuovi" nel visualizzatore differenze accessibile.',
      'Icona per "Chiudi" nel visualizzatore differenze accessibile.',
      'Chiudi',
      'Visualizzatore differenze accessibile. Usare le frecce SU e GI\xD9 per spostarsi.',
      'nessuna riga modificata',
      '1 riga modificata',
      '{0} righe modificate',
      'Differenza {0} di {1}: riga originale {2}, {3}, riga modificata {4}, {5}',
      'vuota',
      '{0} riga non modificata {1}',
      '{0} riga originale {1} riga modificata {2}',
      '+ {0} riga modificata {1}',
      '- {0} riga originale {1}',
      " utilizzare {0} per aprire la Guida all'accessibilit\xE0.",
      'Copia le righe eliminate',
      'Copia la riga eliminata',
      'Copia righe modificate',
      'Copia riga modificata',
      'Copia la riga eliminata ({0})',
      'Copia riga modificata ({0})',
      'Ripristina questa modifica',
      'Usa la visualizzazione inline quando lo spazio \xE8 limitato',
      'Mostra blocchi di codice spostati',
      'Ripristina blocco',
      'Ripristina selezione',
      'Apri Visualizzatore differenze accessibile',
      'Ridurre area non modificata',
      '{0} righe nascoste',
      'Fai clic o trascina per visualizzare altri elementi sopra',
      'Mostra area non modificata',
      'Fai clic o trascina per visualizzare altri elementi sotto',
      '{0} righe nascoste',
      'Fare doppio clic per espandere',
      'Codice spostato con modifiche alla riga {0}-{1}',
      'Codice spostato con modifiche dalla riga {0}-{1}',
      'Codice spostato alla riga {0}-{1}',
      'Codice spostato dalla riga {0}-{1}',
      'Ripristina modifiche selezionate',
      'Annulla modifica',
      "Colore del bordo per il testo spostato nell'editor diff.",
      "Colore del bordo attivo per il testo spostato nell'editor diff.",
      "Colore dell'ombreggiatura intorno ai widget dell'area non modificata.",
      "Effetto di riga per gli inserimenti nell'editor diff.",
      "Effetto di riga per le rimozioni nell'editor diff.",
      "Colore di sfondo dell'intestazione dell'editor diff",
      "Colore di sfondo dell'editor diff a pi\xF9 file",
      'Colore del bordo dell\u2019editor diff multi file',
      'Nessun file modificato',
      'Editor',
      'Numero di spazi a cui \xE8 uguale una scheda. Questa impostazione viene sottoposta a override in base al contenuto del file quando {0} \xE8 attivo.',
      'Numero di spazi utilizzati per il rientro o `"tabSize"` per usare il valore di `#editor.tabSize#`. Questa impostazione viene sostituita in base al contenuto del file quando `#editor.detectIndentation#` \xE8 attivo.',
      "Inserire spazi quando si preme 'TAB'. Questa impostazione viene sottoposta a override in base al contenuto del file quando {0} \xE8 attivo.",
      'Controlla se {0} e {1} verranno rilevati automaticamente quando un file viene aperto in base al contenuto del file.',
      'Rimuovi gli spazi finali inseriti automaticamente.',
      'Gestione speciale dei file di grandi dimensioni per disabilitare alcune funzionalit\xE0 che fanno un uso intensivo della memoria.',
      'Disattivare i suggerimenti basati su Word.',
      'Suggerisci parole solo dal documento attivo.',
      'Suggerisci parole da tutti i documenti aperti della stessa lingua.',
      'Suggerisci parole da tutti i documenti aperti.',
      'Controlla se i completamenti devono essere calcolati in base alle parole nel documento e dai documenti da cui vengono calcolati.',
      "L'evidenziazione semantica \xE8 abilitata per tutti i temi colore.",
      "L'evidenziazione semantica \xE8 disabilitata per tutti i temi colore.",
      "La configurazione dell'evidenziazione semantica \xE8 gestita tramite l'impostazione `semanticHighlighting` del tema colori corrente.",
      "Controlla se l'evidenziazione semanticHighlighting \xE8 visualizzata per i linguaggi che la supportano.",
      "Consente di mantenere aperti gli editor rapidi anche quando si fa doppio clic sul contenuto o si preme 'ESC'.",
      'Per motivi di prestazioni le righe di lunghezza superiore non verranno tokenizzate',
      'Controlla se la tokenizzazione deve essere eseguita in modo asincrono in un web worker.',
      'Controlla se deve essere registrata la tokenizzazione asincrona. Solo per il debug.',
      'Controlla se la tokenizzazione asincrona deve essere verificata rispetto alla tokenizzazione legacy in background. Potrebbe rallentare la tokenizzazione. Solo per il debug.',
      "Consente di stabilire se attivare l'analisi di Tree-sitter e se acquisire i dati di telemetria. L\u2019impostazione di `editor.experimental.preferTreeSitter` per linguaggi specifici avr\xE0 la precedenza.",
      'Definisce i simboli di parentesi quadra che aumentano o riducono il rientro.',
      'Sequenza di stringa o carattere parentesi quadra di apertura.',
      'Sequenza di stringa o carattere parentesi quadra di chiusura.',
      'Definisce le coppie di bracket colorate in base al livello di annidamento se \xE8 abilitata la colorazione delle coppie di bracket.',
      'Sequenza di stringa o carattere parentesi quadra di apertura.',
      'Sequenza di stringa o carattere parentesi quadra di chiusura.',
      'Timeout in millisecondi dopo il quale il calcolo delle differenze viene annullato. Usare 0 per indicare nessun timeout.',
      'Dimensioni massime del file in MB per cui calcolare le differenze. Usare 0 per nessun limite.',
      "Controlla se l'editor diff mostra le differenze affiancate o incorporate.",
      "Se la larghezza dell'editor diff \xE8 inferiore a questo valore, verr\xE0 utilizzata la visualizzazione inline.",
      "Se questa opzione \xE8 abilitata e la larghezza dell'editor \xE8 troppo piccola, verr\xE0 usata la visualizzazione inline.",
      "Se questa opzione \xE8 abilitata, l'editor diff mostra le frecce nel margine del glifo per ripristinare le modifiche.",
      "Se questa opzione \xE8 abilitata, nell'editor diff viene visualizzata una barra di navigazione speciale per le azioni di ripristino e anteprima.",
      "Se abilitato, l'editor differenze ignora le modifiche relative a spazi vuoti iniziali e finali.",
      "Controlla se l'editor diff mostra gli indicatori +/- per le modifiche aggiunte/rimosse.",
      "Controlla se l'editor visualizza CodeLens.",
      'Il ritorno a capo automatico delle righe non viene mai applicato.',
      'Il ritorno a capo automatico delle righe viene applicato in corrispondenza della larghezza del viewport.',
      "Le righe andranno a capo in base all'impostazione {0}.",
      "Usare l'algoritmo diffing legacy.",
      "Usare l'algoritmo diffing avanzato.",
      "Controlla se l'editor diff mostra aree non modificate.",
      'Controlla il numero di righe usate per le aree non modificate.',
      'Controlla il numero minimo di righe usate per le aree non modificate.',
      'Controlla il numero di righe usate come contesto durante il confronto delle aree non modificate.',
      "Controlla se l'editor diff deve mostrare gli spostamenti di codice rilevati.",
      "Controlla se l'editor diff mostra decorazioni vuote per vedere dove sono stati inseriti o eliminati caratteri.",
      "Se questa opzione \xE8 abilitata e l'editor usa la visualizzazione inline, verr\xE0 eseguito il rendering delle modifiche delle parole inline.",
      "Usare le API della piattaforma per rilevare quando viene collegata un'utilit\xE0 per la lettura dello schermo.",
      "Ottimizzare l'utilizzo con un'utilit\xE0 per la lettura dello schermo.",
      "Si presuppone che un'utilit\xE0 per la lettura dello schermo non sia collegata.",
      "Controllare se l'interfaccia utente deve essere eseguito in una modalit\xE0 ottimizzata per le utilit\xE0 per la lettura dello schermo.",
      'Consente di controllare se viene inserito uno spazio quando si aggiungono commenti.',
      'Controlla se ignorare le righe vuote con le opzioni per attivare/disattivare, aggiungere o rimuovere relative ai commenti di riga.',
      'Controlla se, quando si copia senza aver effettuato una selezione, viene copiata la riga corrente.',
      'Controlla se il cursore deve passare direttamente alla ricerca delle corrispondenze durante la digitazione.',
      "Non fornire mai la stringa di ricerca dalla selezione dell'editor.",
      "Fornisci sempre la stringa di ricerca dalla selezione dell'editor, inclusa la parola alla posizione del cursore.",
      "Fornisci la stringa di ricerca solo dalla selezione dell'editor.",
      "Controlla se inizializzare la stringa di ricerca nel Widget Trova con il testo selezionato nell'editor.",
      'Non attivare mai automaticamente la funzione Trova nella selezione (impostazione predefinita).',
      'Attiva sempre automaticamente la funzione Trova nella selezione.',
      'Attiva automaticamente la funzione Trova nella selezione quando sono selezionate pi\xF9 righe di contenuto.',
      'Controlla la condizione per attivare automaticamente la funzione Trova nella selezione.',
      'Controlla se il widget Trova deve leggere o modificare gli appunti di ricerca condivisi in macOS.',
      "Controlla se il widget Trova deve aggiungere altre righe nella parte superiore dell'editor. Quando \xE8 true, \xE8 possibile scorrere oltre la prima riga quando il widget Trova \xE8 visibile.",
      "Controlla se la ricerca viene riavviata automaticamente dall'inizio o dalla fine quando non \xE8 possibile trovare ulteriori corrispondenze.",
      "Abilita/Disabilita i caratteri legatura (funzionalit\xE0 dei tipi di carattere 'calt' e 'liga'). Impostare su una stringa per un controllo pi\xF9 specifico sulla propriet\xE0 CSS 'font-feature-settings'.",
      "Propriet\xE0 CSS 'font-feature-settings' esplicita. Se \xE8 necessario solo attivare/disattivare le legature, \xE8 possibile passare un valore booleano.",
      "Consente di configurare i caratteri legatura o le funzionalit\xE0 dei tipi di carattere. Pu\xF2 essere un valore booleano per abilitare/disabilitare le legature o una stringa per il valore della propriet\xE0 CSS 'font-feature-settings'.",
      'Abilita/disabilita la conversione dada font-weight a font-variation-settings. Modificare questa impostazione in una stringa per il controllo con granularit\xE0 fine della propriet\xE0 CSS Font-variation.',
      "Propriet\xE0 CSS esplicita 'font-variation-settings'. \xC8 invece possibile passare un valore booleano se \xE8 sufficiente convertire font-weight in font-variation-settings.",
      "Configura le varianti di carattere. Pu\xF2 essere un valore booleano per abilitare/disabilitare la conversione da font-weight a font-variation-settings o una stringa per il valore della propriet\xE0 'font-variation-settings' CSS.",
      'Controlla le dimensioni del carattere in pixel.',
      'Sono consentiti solo le parole chiave "normal" e "bold" o i numeri compresi tra 1 e 1000.',
      'Controlla lo spessore del carattere. Accetta le parole chiave "normal" e "bold" o i numeri compresi tra 1 e 1000.',
      'Mostra la visualizzazione in anteprima dei risultati (impostazione predefinita)',
      'Passa al risultato principale e mostra una visualizzazione in anteprima',
      "Passa al risultato principale e abilita l'esplorazione senza anteprima per gli altri",
      "Questa impostazione \xE8 deprecata. In alternativa, usare impostazioni diverse, come 'editor.editor.gotoLocation.multipleDefinitions' o 'editor.editor.gotoLocation.multipleImplementations'.",
      "Controlla il comportamento del comando 'Vai alla definizione' quando esistono pi\xF9 posizioni di destinazione.",
      "Controlla il comportamento del comando 'Vai alla definizione di tipo' quando esistono pi\xF9 posizioni di destinazione.",
      "Controlla il comportamento del comando 'Vai a dichiarazione' quando esistono pi\xF9 posizioni di destinazione.",
      "Controlla il comportamento del comando 'Vai a implementazioni' quando esistono pi\xF9 posizioni di destinazione.",
      "Controlla il comportamento del comando 'Vai a riferimenti' quando esistono pi\xF9 posizioni di destinazione.",
      "ID comando alternativo eseguito quando il risultato di 'Vai alla definizione' \xE8 la posizione corrente.",
      "ID comando alternativo eseguito quando il risultato di 'Vai alla definizione di tipo' \xE8 la posizione corrente.",
      "ID comando alternativo eseguito quando il risultato di 'Vai a dichiarazione' \xE8 la posizione corrente.",
      "ID comando alternativo eseguito quando il risultato di 'Vai a implementazione' \xE8 la posizione corrente.",
      "ID comando alternativo eseguito quando il risultato di 'Vai a riferimento' \xE8 la posizione corrente.",
      "Controlla se mostrare l'area sensibile al passaggio del mouse.",
      'Controlla il ritardo in millisecondi dopo il quale viene mostrato il passaggio del mouse.',
      "Controlla se l'area sensibile al passaggio del mouse deve rimanere visibile quando vi si passa sopra con il puntatore del mouse.",
      "Controlla il ritardo in millisecondi dopo il quale viene nascosto il passaggio del mouse. Richiede l'abilitazione di `editor.hover.sticky`.",
      "Preferisci la visualizzazione al passaggio del mouse sopra la riga, se c'\xE8 spazio.",
      'Presuppone che la larghezza sia identica per tutti caratteri. Si tratta di un algoritmo veloce che funziona correttamente per i tipi di carattere a spaziatura fissa e determinati script (come i caratteri latini) in cui i glifi hanno larghezza identica.',
      'Delega il calcolo dei punti di ritorno a capo al browser. Si tratta di un algoritmo lento che potrebbe causare blocchi con file di grandi dimensioni, ma funziona correttamente in tutti gli altri casi.',
      "Controlla l'algoritmo che calcola i punti di wrapping. Si noti che quando \xE8 attiva la modalit\xE0 di accessibilit\xE0, la modalit\xE0 avanzata verr\xE0 usata per un'esperienza ottimale.",
      'Disabilita il menu azione codice.',
      'Visualizza il menu azione codice quando il cursore \xE8 su righe di codice.',
      'Mostra il menu azione codice quando il cursore \xE8 su righe con codice o su righe vuote.',
      "Abilita la lampadina delle azioni codice nell'editor.",
      "Mostra gli ambiti correnti annidati durante lo scorrimento nella parte superiore dell'editor.",
      'Definisce il numero massimo di righe permanenti da mostrare.',
      'Definisce il modello da utilizzare per determinare quali linee applicare. Se il modello di struttura non esiste, verr\xE0 eseguito il fallback sul modello del provider di riduzione che rientra nel modello di rientro. Questo ordine viene rispettato in tutti e tre i casi.',
      "Abilitare lo scorrimento di scorrimento permanente con la barra di scorrimento orizzontale dell'editor.",
      "Abilita i suggerimenti incorporati nell'Editor.",
      'Gli hint di inlay sono abilitati',
      'Gli hint di inlay vengono visualizzati per impostazione predefinita e vengono nascosti quando si tiene premuto {0}',
      'Gli hint di inlay sono nascosti per impostazione predefinita e vengono visualizzati solo quando si tiene premuto {0}',
      'Gli hint di inlay sono disabilitati',
      "Controlla le dimensioni del carattere dei suggerimenti di inlay nell'editor. Per impostazione predefinita, {0} viene usato quando il valore configurato \xE8 minore di {1} o maggiore delle dimensioni del carattere dell'editor.",
      "Controlla la famiglia di caratteri dei suggerimenti inlay nell'editor. Se impostato su vuoto, viene usato {0}.",
      "Abilita il riempimento attorno ai suggerimenti incorporati nell'editor.",
      `Controlla l'altezza della riga. \r
 - Usare 0 per calcolare automaticamente l'altezza della riga dalle dimensioni del carattere.\r
 - I valori compresi tra 0 e 8 verranno usati come moltiplicatore con le dimensioni del carattere.\r
 - I valori maggiori o uguali a 8 verranno usati come valori effettivi.`,
      'Controlla se la minimappa \xE8 visualizzata.',
      'Controlla se la minimappa viene nascosta automaticamente.',
      "La minimappa ha le stesse dimensioni del contenuto dell'editor (e potrebbe supportare lo scorrimento).",
      "Se necessario, la minimappa si ridurr\xE0 o si ingrandir\xE0 in modo da adattarsi all'altezza dell'editor (nessuno scorrimento).",
      "Se necessario, la minimappa si ridurr\xE0 in modo che la larghezza non superi mai quella dell'editor (nessuno scorrimento).",
      'Controlla le dimensioni della minimappa.',
      'Definisce il lato in cui eseguire il rendering della minimappa.',
      'Controlla se il dispositivo di scorrimento della minimappa \xE8 visualizzato.',
      'Scala del contenuto disegnato nella minimappa: 1, 2 o 3.',
      'Esegue il rendering dei caratteri effettivi di una riga in contrapposizione ai blocchi colore.',
      'Limita la larghezza della minimappa in modo da eseguire il rendering al massimo di un certo numero di colonne.',
      'Controlla se le aree denominate vengono visualizzate come intestazioni di sezione nella minimappa.',
      'Controlla se i commenti MARK: vengono visualizzati come intestazioni di sezione nella minimappa.',
      'Controlla le dimensioni del carattere delle intestazioni di sezione nella minimappa.',
      'Controllare la quantit\xE0 di spazio (in pixel) tra i caratteri dell\u2019intestazione di sezione. Ci\xF2 contribuisce alla leggibilit\xE0 dell\u2019intestazione in caratteri di piccole dimensioni.',
      "Controlla la quantit\xE0 di spazio tra il bordo superiore dell'editor e la prima riga.",
      "Controlla la quantit\xE0 di spazio tra il bordo inferiore dell'editor e l'ultima riga.",
      'Abilita un popup che mostra documentazione sui parametri e informazioni sui tipi mentre si digita.',
      "Controlla se il menu dei suggerimenti per i parametri esegue un ciclo o si chiude quando viene raggiunta la fine dell'elenco.",
      "I suggerimenti rapidi vengono visualizzati all'interno del widget dei suggerimenti",
      'I suggerimenti rapidi vengono visualizzati come testo fantasma',
      'I suggerimenti rapidi sono disabilitati',
      "Abilita i suggerimenti rapidi all'interno di stringhe.",
      "Abilita i suggerimenti rapidi all'interno di commenti.",
      "Abilita i suggerimenti rapidi all'esterno di stringhe e commenti.",
      "Controlla se i suggerimenti devono essere visualizzati automaticamente durante la digitazione. Questo pu\xF2 essere controllato per digitare commenti, stringhe e altro codice. Il suggerimento rapido pu\xF2 essere configurato per essere visualizzato come testo fantasma o con il widget dei suggerimenti. Tenere anche conto dell'impostazione {0}che controlla se i suggerimenti vengono attivati da caratteri speciali.",
      'I numeri di riga non vengono visualizzati.',
      'I numeri di riga vengono visualizzati come numeri assoluti.',
      'I numeri di riga vengono visualizzati come distanza in linee alla posizione del cursore.',
      'I numeri di riga vengono visualizzati ogni 10 righe.',
      'Controlla la visualizzazione dei numeri di riga.',
      "Numero di caratteri a spaziatura fissa in corrispondenza del quale verr\xE0 eseguito il rendering di questo righello dell'editor.",
      "Colore di questo righello dell'editor.",
      'Esegue il rendering dei righelli verticali dopo un certo numero di caratteri a spaziatura fissa. Usare pi\xF9 valori per pi\xF9 righelli. Se la matrice \xE8 vuota, non viene disegnato alcun righello.',
      'La barra di scorrimento verticale sar\xE0 visibile solo quando necessario.',
      'La barra di scorrimento verticale sar\xE0 sempre visibile.',
      'La barra di scorrimento verticale sar\xE0 sempre nascosta.',
      'Controlla la visibilit\xE0 della barra di scorrimento verticale.',
      'La barra di scorrimento orizzontale sar\xE0 visibile solo quando necessario.',
      'La barra di scorrimento orizzontale sar\xE0 sempre visibile.',
      'La barra di scorrimento orizzontale sar\xE0 sempre nascosta.',
      'Controlla la visibilit\xE0 della barra di scorrimento orizzontale.',
      'Larghezza della barra di scorrimento verticale.',
      'Altezza della barra di scorrimento orizzontale.',
      'Controlla se i clic consentono di attivare lo scorrimento per pagina o di passare direttamente alla posizione di clic.',
      "Se impostata, la barra di scorrimento orizzontale non aumenter\xE0 le dimensioni del contenuto dell'editor.",
      'Controlla se tutti i caratteri ASCII non di base sono evidenziati. Solo i caratteri compresi tra U+0020 e U+007E, tabulazione, avanzamento riga e ritorno a capo sono considerati ASCII di base.',
      'Controlla se i caratteri che riservano spazio o non hanno larghezza sono evidenziati.',
      "Controlla se i caratteri che possono essere confusi con i caratteri ASCII di base sono evidenziati, ad eccezione di quelli comuni nelle impostazioni locali dell'utente corrente.",
      'Controlla se anche i caratteri nei commenti devono essere soggetti a evidenziazione Unicode.',
      "Controlla se anche i caratteri nelle stringhe devono essere soggetti all'evidenziazione Unicode.",
      'Definisce i caratteri consentiti che non vengono evidenziati.',
      'I caratteri Unicode comuni nelle impostazioni locali consentite non vengono evidenziati.',
      "Controlla se visualizzare automaticamente i suggerimenti inline nell'Editor.",
      'Mostra la barra degli strumenti dei suggerimenti in linea ogni volta che viene visualizzato un suggerimento in linea.',
      'Mostra la barra degli strumenti dei suggerimenti in linea quando al passaggio del mouse su un suggerimento in linea.',
      'Non mostrare mai la barra dei suggerimenti in linea.',
      'Controlla quando mostrare la barra dei suggerimenti in linea.',
      'Controlla la modalit\xE0 di interazione dei suggerimenti inline con il widget dei suggerimenti. Se questa opzione \xE8 abilitata, il widget dei suggerimenti non viene visualizzato automaticamente quando sono disponibili suggerimenti inline.',
      'Controlla la famiglia di caratteri dei suggerimenti inline.',
      null,
      null,
      null,
      null,
      null,
      null,
      "Controlla se la colorazione delle coppie di parentesi \xE8 abilitata. Usare {0} per eseguire l'override dei colori di evidenziazione delle parentesi.",
      'Controlla se ogni tipo di parentesi ha un pool di colori indipendente.',
      'Abilita le guide per coppie di parentesi quadre.',
      'Abilita le guide delle coppie di parentesi solo per la coppia di parentesi attive.',
      'Disabilita le guide per coppie di parentesi quadre.',
      'Controlla se le guide delle coppie di parentesi sono abilitate o meno.',
      'Abilita le guide orizzontali come aggiunta alle guide per coppie di parentesi verticali.',
      'Abilita le guide orizzontali solo per la coppia di parentesi attive.',
      'Disabilita le guide per coppie di parentesi orizzontali.',
      'Controlla se le guide orizzontali delle coppie di parentesi sono abilitate o meno.',
      "Controlla se l'editor debba evidenziare la coppia di parentesi attive.",
      "Controlla se l'editor deve eseguire il rendering delle guide con rientro.",
      'Evidenzia la guida di rientro attiva.',
      'Evidenzia la guida di rientro attiva anche se le guide delle parentesi quadre sono evidenziate.',
      'Non evidenziare la guida di rientro attiva.',
      "Controlla se l'editor deve evidenziare la guida con rientro attiva.",
      'Inserisce il suggerimento senza sovrascrivere il testo a destra del cursore.',
      'Inserisce il suggerimento e sovrascrive il testo a destra del cursore.',
      'Controlla se le parole vengono sovrascritte quando si accettano i completamenti. Tenere presente che questa opzione dipende dalle estensioni che accettano esplicitamente questa funzionalit\xE0.',
      'Controlla se i suggerimenti di filtro e ordinamento valgono per piccoli errori di battitura.',
      "Controlla se l'ordinamento privilegia le parole che appaiono pi\xF9 vicine al cursore.",
      'Controlla se condividere le selezioni dei suggerimenti memorizzati tra aree di lavoro e finestre (richiede `#editor.suggestSelection#`).',
      'Selezionare sempre un suggerimento quando si attiva automaticamente IntelliSense.',
      'Non selezionare mai un suggerimento quando si attiva automaticamente IntelliSense.',
      'Selezionare un suggerimento solo quando si attiva IntelliSense da un carattere di trigger.',
      'Selezionare un suggerimento solo quando si attiva IntelliSense durante la digitazione.',
      "Controlla se viene selezionato un suggerimento quando viene visualizzato il widget. Si noti che questo si applica solo ai suggerimenti attivati automaticamente ({0} e {1}) e che un suggerimento viene sempre selezionato quando viene richiamato in modo esplicito, ad esempio tramite 'CTRL+BARRA SPAZIATRICE'.",
      'Controlla se un frammento attivo impedisce i suggerimenti rapidi.',
      'Controlla se mostrare o nascondere le icone nei suggerimenti.',
      'Controlla la visibilit\xE0 della barra di stato nella parte inferiore del widget dei suggerimenti.',
      "Controlla se visualizzare in anteprima il risultato del suggerimento nell'Editor.",
      "Controlla se i dettagli del suggerimento vengono visualizzati inline con l'etichetta o solo nel widget dei dettagli.",
      'Questa impostazione \xE8 deprecata. Il widget dei suggerimenti pu\xF2 ora essere ridimensionato.',
      "Questa impostazione \xE8 deprecata. In alternativa, usare impostazioni diverse, come 'editor.suggest.showKeywords' o 'editor.suggest.showSnippets'.",
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `method`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `function`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `constructor`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `deprecated`.',
      "Quando \xE8 abilitato, il filtro IntelliSense richiede che il primo carattere corrisponda all'inizio di una parola, ad esempio 'c' per 'Console' o 'WebContext' ma _non_ per 'description'. Quando \xE8 disabilitato, IntelliSense mostra pi\xF9 risultati, ma li ordina comunque in base alla qualit\xE0 della corrispondenza.",
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `field`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `variable`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `class`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `struct`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `interface`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `module`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `property`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `event`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `operator`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `unit`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `value`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `constant`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `enum`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `enumMember`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `keyword`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `text`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `color`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `file`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `reference`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `customcolor`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `folder`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `typeParameter`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `snippet`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `user`.',
      'Se \xE8 abilitata, IntelliSense mostra i suggerimenti relativi a `issues`.',
      'Indica se gli spazi vuoti iniziali e finali devono essere sempre selezionati.',
      "Indica se \xE8 necessario selezionare le sottoparole ( come 'foo' in 'fooBar' o 'foo_bar').",
      'Impostazioni locali da usare per la segmentazione delle parole quando si eseguono operazioni o spostamenti correlati alle parole. Specifica il tag di lingua BCP 47 della parola che si vuole riconoscere (ad es. ja, zh-CN, zh-Hant-TW, ecc.).',
      'Impostazioni locali da usare per la segmentazione delle parole quando si eseguono operazioni o spostamenti correlati alle parole. Specifica il tag di lingua BCP 47 della parola che si vuole riconoscere (ad es. ja, zh-CN, zh-Hant-TW, ecc.).',
      'Nessun rientro. Le righe con ritorno a capo iniziano dalla colonna 1. ',
      'Le righe con ritorno a capo hanno lo stesso rientro della riga padre.',
      'Le righe con ritorno a capo hanno un rientro di +1 rispetto alla riga padre.',
      'Le righe con ritorno a capo hanno un rientro di +2 rispetto alla riga padre.',
      'Controlla il rientro delle righe con ritorno a capo.',
      'Controlla se \xE8 possibile trascinare un file in un editor di testo tenendo premuto il tasto \u201CMAIUSC\u201D (invece di aprire il file in un editor).',
      "Controlla se viene visualizzato un widget quando si rilasciano file nell'editor. Questo widget consente di controllare la modalit\xE0 di rilascio del file.",
      "Mostra il widget del selettore di rilascio dopo il rilascio di un file nell'editor.",
      'Non visualizzare mai il widget del selettore di rilascio. Usare sempre il provider di rilascio predefinito.',
      'Controlla se \xE8 possibile incollare il contenuto in modi diversi.',
      "Controlla se viene visualizzato un widget quando si incolla il contenuto nell'editor. Questo widget consente di controllare il modo in cui il file viene incollato.",
      "Mostra il widget del selettore dell'operazione Incolla dopo che il contenuto \xE8 stato incollato nell'editor.",
      "Non visualizzare mai il widget del selettore dell'operazione Incolla. Usare sempre il comportamento dell'operazione Incolla predefinito.",
      "Controlla se accettare i suggerimenti con i caratteri di commit. Ad esempio, in JavaScript il punto e virgola (';') pu\xF2 essere un carattere di commit che accetta un suggerimento e digita tale carattere.",
      "Accetta un suggerimento con 'Invio' solo quando si apporta una modifica al testo.",
      "Controlla se i suggerimenti devono essere accettati con 'INVIO' in aggiunta a 'TAB'. In questo modo \xE8 possibile evitare ambiguit\xE0 tra l'inserimento di nuove righe e l'accettazione di suggerimenti.",
      "Controlla il numero di righe nell'Editor che possono essere lette alla volta da un utilit\xE0 per la lettura dello schermo. Quando viene rilevata un'utilit\xE0 per la lettura dello schermo, questo valore viene impostato su 500 per impostazione predefinita. Avviso: questa opzione pu\xF2 influire sulle prestazioni se il numero di righe \xE8 superiore a quello predefinito.",
      'Contenuto editor',
      "Controllare se i suggerimenti inline vengono annunciati da un'utilit\xE0 per la lettura dello schermo.",
      'Usa le configurazioni del linguaggio per determinare la chiusura automatica delle parentesi.',
      'Chiudi automaticamente le parentesi solo quando il cursore si trova alla sinistra di uno spazio vuoto.',
      "Controlla se l'editor deve chiudere automaticamente le parentesi quadre dopo che sono state aperte.",
      'Usare le configurazioni del linguaggio per determinare la chiusura automatica dei commenti.',
      'Chiudere automaticamente i commenti solo quando il cursore si trova alla sinistra di uno spazio vuoto.',
      "Controlla se l'editor deve chiudere automaticamente i commenti dopo che sono stati aperti.",
      'Rimuove le virgolette o le parentesi quadre di chiusura adiacenti solo se sono state inserite automaticamente.',
      "Controlla se l'editor deve rimuovere le virgolette o le parentesi quadre di chiusura adiacenti durante l'eliminazione.",
      'Digita sopra le virgolette o le parentesi quadre di chiusura solo se sono state inserite automaticamente.',
      "Controlla se l'editor deve digitare su virgolette o parentesi quadre.",
      'Usa le configurazioni del linguaggio per determinare la chiusura automatica delle virgolette.',
      'Chiudi automaticamente le virgolette solo quando il cursore si trova alla sinistra di uno spazio vuoto.',
      "Controlla se l'editor deve chiudere automaticamente le citazioni dopo che sono state aperte.",
      "L'editor non inserir\xE0 automaticamente il rientro.",
      "L'editor manterr\xE0 il rientro della riga corrente.",
      "L'editor manterr\xE0 il rientro della riga corrente e rispetter\xE0 le parentesi definite dalla lingua.",
      "L'editor manterr\xE0 il rientro della riga corrente, rispetter\xE0 le parentesi definite dalla lingua e richiamer\xE0 le regole onEnterRules speciali definite dalle lingue.",
      "L'editor manterr\xE0 il rientro della riga corrente, rispetter\xE0 le parentesi definite dalla lingua, richiamer\xE0 le regole onEnterRules speciali definite dalle lingue e rispetter\xE0 le regole indentationRules definite dalle lingue.",
      "Controlla se l'editor deve regolare automaticamente il rientro quando gli utenti digitano, incollano, spostano le righe o applicano il rientro.",
      'Usa le configurazioni del linguaggio per determinare quando racchiudere automaticamente le selezioni tra parentesi quadre o virgolette.',
      'Racchiude la selezione tra virgolette ma non tra parentesi quadre.',
      'Racchiude la selezione tra parentesi quadre ma non tra virgolette.',
      "Controlla se l'editor deve racchiudere automaticamente le selezioni quando si digitano virgolette o parentesi quadre.",
      'Emula il comportamento di selezione dei caratteri di tabulazione quando si usano gli spazi per il rientro. La selezione verr\xE0 applicata alle tabulazioni.',
      "Controlla se l'editor visualizza CodeLens.",
      'Controlla la famiglia di caratteri per CodeLens.',
      "Controlla le dimensioni del carattere in pixel per CodeLens. Quando \xE8 impostata su 0, viene usato il 90% del valore di '#editor.fontSize#'.",
      "Controlla se l'editor deve eseguire il rendering della selezione colori e degli elementi Decorator di tipo colore inline.",
      'Fare in modo che la selezione colori venga visualizzata sia al clic che al passaggio del mouse sull\u2019elemento Decorator colore',
      "Fare in modo che la selezione colori venga visualizzata al passaggio del mouse sull'elemento Decorator colore",
      "Fare in modo che la selezione colori venga visualizzata quando si fa clic sull'elemento Decorator colore",
      'Controlla la condizione in modo che venga visualizzata la selezione colori da un elemento Decorator colore.',
      'Controlla il numero massimo di elementi Decorator a colori di cui \xE8 possibile eseguire il rendering in un editor contemporaneamente.',
      "Abilita l'uso di mouse e tasti per la selezione delle colonne.",
      "Controlla se l'evidenziazione della sintassi deve essere copiata negli Appunti.",
      'Controllo dello stile di animazione del cursore.',
      "L'animazione con cursore arrotondato \xE8 disabilitata.",
      "L'animazione con cursore uniforme \xE8 abilitata solo quando l'utente sposta il cursore con un movimento esplicito.",
      "L'animazione con cursore uniforme \xE8 sempre abilitata.",
      "Controlla se l'animazione del cursore con anti-aliasing deve essere abilitata.",
      'Controlla lo stile del cursore in modalit\xE0 di inserimento input.',
      "Controllare il numero minimo di linee iniziali visibili (minimo 0) e finali (minimo 1) visibili che circondano il cursore. Noto come 'scrollOff' o 'scrollOffset' in altri editor.",
      "`cursorSurroundingLines` viene applicato solo quando \xE8 attivato tramite la tastiera o l'API.",
      '`cursorSurroundingLines` viene sempre applicato.',
      "Controlla quando deve essere applicato '#editor.cursorSurroundingLines#'.",
      'Controlla la larghezza del cursore quando `#editor.cursorStyle#` \xE8 impostato su `line`.',
      "Controlla se l'editor deve consentire lo spostamento di selezioni tramite trascinamento della selezione.",
      'Usare un nuovo metodo di rendering con svgs.',
      'Usare un nuovo metodo di rendering con tipi di caratteri.',
      'Usare il metodo di rendering stabile.',
      'Controlla se viene eseguito il rendering degli spazi vuoti con un nuovo metodo sperimentale.',
      'Moltiplicatore della velocit\xE0 di scorrimento quando si preme `Alt`.',
      "Controlla se per l'editor \xE8 abilitata la riduzione del codice.",
      'Usa una strategia di riduzione specifica della lingua, se disponibile; altrimenti ne usa una basata sui rientri.',
      'Usa la strategia di riduzione basata sui rientri.',
      'Controlla la strategia per il calcolo degli intervalli di riduzione.',
      "Controlla se l'editor deve evidenziare gli intervalli con riduzione del codice.",
      "Controlla se l'editor comprime automaticamente gli intervalli di importazione.",
      "Numero massimo di aree riducibili. Se si aumenta questo valore, l'editor potrebbe diventare meno reattivo quando l'origine corrente contiene un numero elevato di aree riducibili.",
      'Controlla se, facendo clic sul contenuto vuoto dopo una riga ridotta, la riga viene espansa.',
      'Controlla la famiglia di caratteri.',
      "Controlla se l'editor deve formattare automaticamente il contenuto incollato. Deve essere disponibile un formattatore che deve essere in grado di formattare un intervallo in un documento.",
      "Controlla se l'editor deve formattare automaticamente la riga dopo la digitazione.",
      "Controlla se l'editor deve eseguire il rendering del margine verticale del glifo. Il margine del glifo viene usato principalmente per il debug.",
      'Controlla se il cursore deve essere nascosto nel righello delle annotazioni.',
      'Controlla la spaziatura tra le lettere in pixel.',
      "Controlla se la modifica collegata \xE8 abilitata per l'editor. A seconda del linguaggio, i simboli correlati, ad esempio i tag HTML, vengono aggiornati durante la modifica.",
      "Controlla se l'editor deve individuare i collegamenti e renderli selezionabili.",
      'Evidenzia le parentesi graffe corrispondenti.',
      'Moltiplicatore da usare sui valori `deltaX` e `deltaY` degli eventi di scorrimento della rotellina del mouse.',
      'Ingrandisce il carattere dell\u2019editor quando si usa la rotella del mouse e si tiene premuto `Cmd`.',
      "Ingrandisce il carattere dell'editor quando si usa la rotellina del mouse e si tiene premuto 'CTRL'.",
      'Unire i cursori multipli se sovrapposti.',
      "Rappresenta il tasto 'Control' in Windows e Linux e il tasto 'Comando' in macOS.",
      "Rappresenta il tasto 'Alt' in Windows e Linux e il tasto 'Opzione' in macOS.",
      'Modificatore da usare per aggiungere pi\xF9 cursori con il mouse. I movimenti del mouse Vai alla definizione e Apri collegamento si adatteranno in modo da non entrare in conflitto con il [modificatore di selezione multipla](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).',
      'Ogni cursore incolla una singola riga del testo.',
      'Ogni cursore incolla il testo completo.',
      "Controlla l'operazione Incolla quando il conteggio delle righe del testo incollato corrisponde al conteggio dei cursori.",
      'Controlla il numero massimo di cursori che possono essere presenti in un editor attivo contemporaneamente.',
      'Non evidenzia le occorrenze.',
      'Evidenzia le occorrenze solo nel file corrente.',
      'Sperimentale: evidenzia le occorrenze in tutti i file aperti validi.',
      'Controlla se le occorrenze devono essere evidenziate nei file aperti.',
      'Controlla se deve essere disegnato un bordo intorno al righello delle annotazioni.',
      "Sposta lo stato attivo sull'albero quando si apre l'anteprima",
      "Sposta lo stato attivo sull'editor quando si apre l'anteprima",
      "Controlla se spostare lo stato attivo sull'editor inline o sull'albero nel widget di anteprima.",
      'Controlla se il movimento del mouse Vai alla definizione consente sempre di aprire il widget di anteprima.',
      'Controlla il ritardo in millisecondi dopo il quale verranno visualizzati i suggerimenti rapidi.',
      "Controlla se l'editor viene rinominato automaticamente in base al tipo.",
      'Deprecata. In alternativa, usare `editor.linkedEditing`.',
      "Controlla se l'editor deve eseguire il rendering dei caratteri di controllo.",
      "Esegue il rendering dell'ultimo numero di riga quando il file termina con un carattere di nuova riga.",
      'Mette in evidenza sia la barra di navigazione sia la riga corrente.',
      "Controlla in che modo l'editor deve eseguire il rendering dell'evidenziazione di riga corrente.",
      "Controlla se l'editor deve eseguire il rendering dell'evidenziazione della riga corrente solo quando l'editor ha lo stato attivo.",
      'Esegue il rendering dei caratteri di spazio vuoto ad eccezione dei singoli spazi tra le parole.',
      'Esegui il rendering dei caratteri di spazio vuoto solo nel testo selezionato.',
      'Esegui il rendering solo dei caratteri di spazio vuoto finali.',
      "Controlla in che modo l'editor deve eseguire il rendering dei caratteri di spazio vuoto.",
      'Controlla se le selezioni devono avere gli angoli arrotondati.',
      "Controlla il numero di caratteri aggiuntivi oltre i quali l'editor scorrer\xE0 orizzontalmente.",
      "Controlla se l'editor scorrer\xE0 oltre l'ultima riga.",
      "Scorre solo lungo l'asse predominante durante lo scorrimento verticale e orizzontale simultaneo. Impedisce la deviazione orizzontale quando si scorre in verticale su un trackpad.",
      'Controlla se gli appunti primari di Linux devono essere supportati.',
      "Controlla se l'editor deve evidenziare gli elementi corrispondenti simili alla selezione.",
      'Mostra sempre i comandi di riduzione.',
      'Non visualizzare mai i controlli di riduzione e diminuire le dimensioni della barra di navigazione.',
      'Mostra i comandi di riduzione solo quando il mouse \xE8 posizionato sul margine della barra di scorrimento.',
      'Controlla se i controlli di riduzione sul margine della barra di scorrimento vengono visualizzati.',
      'Controllo dissolvenza del codice inutilizzato.',
      'Controlla le variabili deprecate barrate.',
      'Visualizza i suggerimenti del frammento prima degli altri suggerimenti.',
      'Visualizza i suggerimenti del frammento dopo gli altri suggerimenti.',
      'Visualizza i suggerimenti del frammento insieme agli altri suggerimenti.',
      'Non mostrare i suggerimenti del frammento.',
      'Controlla se i frammenti di codice sono visualizzati con altri suggerimenti e il modo in cui sono ordinati.',
      "Controlla se per lo scorrimento dell'editor verr\xE0 usata un'animazione.",
      "Controlla se l'hint di accessibilit\xE0 deve essere fornito agli utenti dell'utilit\xE0 per la lettura dello schermo quando viene visualizzato un completamento inline.",
      'Dimensioni del carattere per il widget dei suggerimenti. Se impostato su {0}, viene usato il valore di {1}.',
      'Altezza della riga per il widget dei suggerimenti. Se impostato su {0}, viene usato il valore {1}. Il valore minimo \xE8 8.',
      'Controlla se i suggerimenti devono essere visualizzati automaticamente durante la digitazione dei caratteri trigger.',
      'Consente di selezionare sempre il primo suggerimento.',
      'Consente di selezionare suggerimenti recenti a meno che continuando a digitare non ne venga selezionato uno, ad esempio `console.| ->; console.log` perch\xE9 `log` \xE8 stato completato di recente.',
      'Consente di selezionare i suggerimenti in base a prefissi precedenti che hanno completato tali suggerimenti, ad esempio `co ->; console` e `con -> const`.',
      "Controlla la modalit\xE0 di preselezione dei suggerimenti durante la visualizzazione dell'elenco dei suggerimenti.",
      'La funzionalit\xE0 di completamento con tasto TAB inserir\xE0 il migliore suggerimento alla pressione del tasto TAB.',
      'Disabilita le funzionalit\xE0 di completamento con tasto TAB.',
      "Completa i frammenti con il tasto TAB quando i rispettivi prefissi corrispondono. Funziona in modo ottimale quando 'quickSuggestions' non \xE8 abilitato.",
      'Abilit\xE0 la funzionalit\xE0 di completamento con tasto TAB.',
      'I caratteri di terminazione di riga insoliti vengono rimossi automaticamente.',
      'I caratteri di terminazione di riga insoliti vengono ignorati.',
      'Prompt per i caratteri di terminazione di riga insoliti da rimuovere.',
      'Rimuovi caratteri di terminazione di riga insoliti che potrebbero causare problemi.',
      'Gli spazi e le tabulazioni vengono inseriti ed eliminati in allineamento con le tabulazioni.',
      'Usare la regola di interruzione di riga predefinita.',
      'Le interruzioni di parola non devono essere usate per il testo cinese/giapponese/coreano (CJK). Il comportamento del testo non CJK \xE8 uguale a quello normale.',
      'Controlla le regole di interruzione delle parole usate per il testo cinese/giapponese/coreano (CJK).',
      'Caratteri che verranno usati come separatori di parola quando si eseguono operazioni o spostamenti correlati a parole.',
      'Il ritorno a capo automatico delle righe non viene mai applicato.',
      'Il ritorno a capo automatico delle righe viene applicato in corrispondenza della larghezza del viewport.',
      'Il ritorno a capo automatico delle righe viene applicato in corrispondenza di `#editor.wordWrapColumn#`.',
      'Il ritorno a capo automatico delle righe viene applicato in corrispondenza della larghezza minima del viewport e di `#editor.wordWrapColumn#`.',
      'Controlla il ritorno a capo automatico delle righe.',
      "Controlla la colonna per il ritorno a capo automatico dell'editor quando il valore di `#editor.wordWrap#` \xE8 `wordWrapColumn` o `bounded`.",
      'Controllare se visualizzare le decorazioni colori incorporate usando il provider colori predefinito del documento',
      "Controlla se l'editor riceve le schede o le rinvia al workbench per lo spostamento.",
      "Colore di sfondo per l'evidenziazione della riga alla posizione del cursore.",
      'Colore di sfondo per il bordo intorno alla riga alla posizione del cursore.',
      'Colore di sfondo degli intervalli evidenziati, ad esempio dalle funzionalit\xE0 Quick Open e Trova. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore di sfondo del bordo intorno agli intervalli selezionati.',
      'Colore di sfondo del simbolo evidenziato, ad esempio per passare alla definizione o al simbolo successivo/precedente. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore di sfondo del bordo intorno ai simboli selezionati.',
      "Colore del cursore dell'editor.",
      'Colore di sfondo del cursore editor. Permette di personalizzare il colore di un carattere quando sovrapposto da un blocco cursore.',
      "Il colore dei cursori dell'editor primario quando sono presenti pi\xF9 cursori.",
      "Il colore di sfondo dei cursori dell'editor primario quando sono presenti pi\xF9 cursori. Permette di personalizzare il colore di un carattere quando sovrapposto da un blocco cursore.",
      "Colore dei cursori dell'editor secondario quando sono presenti pi\xF9 cursori.",
      "Il colore di sfondo dei cursori dell'editor secondario quando sono presenti pi\xF9 cursori. Permette di personalizzare il colore di un carattere quando sovrapposto da un blocco cursore.",
      "Colore dei caratteri di spazio vuoto nell'editor.",
      "Colore dei numeri di riga dell'editor.",
      "Colore delle guide per i rientri dell'editor.",
      "'editorIndentGuide.background' \xE8 deprecato. Usare 'editorIndentGuide.background1'.",
      "Colore delle guide di indentazione dell'editor attivo",
      "'editorIndentGuide.activeBackground' \xE8 deprecato. Usare 'editorIndentGuide.activeBackground1'.",
      "Colore delle guide per i rientri dell'editor (1).",
      "Colore delle guide per i rientri dell'editor (2).",
      "Colore delle guide per i rientri dell'editor (3).",
      "Colore delle guide per i rientri dell'editor (4).",
      "Colore delle guide per i rientri dell'editor (5).",
      "Colore delle guide per i rientri dell'editor (6).",
      "Colore delle guide di indentazione dell'editor attivo (1).",
      "Colore delle guide di indentazione dell'editor attivo (2).",
      "Colore delle guide di indentazione dell'editor attivo (3).",
      "Colore delle guide di indentazione dell'editor attivo (4).",
      "Colore delle guide di indentazione dell'editor attivo (5).",
      "Colore delle guide di indentazione dell'editor attivo (6).",
      "Colore del numero di riga attivo dell'editor",
      "Id \xE8 deprecato. In alternativa usare 'editorLineNumber.activeForeground'.",
      "Colore del numero di riga attivo dell'editor",
      "Colore della riga dell'editor finale quando editor.renderFinalNewline \xE8 impostato su in grigio.",
      "Colore dei righelli dell'editor.",
      "Colore primo piano delle finestre di CodeLens dell'editor",
      'Colore di sfondo delle parentesi corrispondenti',
      'Colore delle caselle di parentesi corrispondenti',
      'Colore del bordo del righello delle annotazioni.',
      "Colore di sfondo del righello delle annotazioni dell'editor.",
      "Colore di sfondo della barra di navigazione dell'editor. La barra contiene i margini di glifo e i numeri di riga.",
      "Colore del bordo del codice sorgente non necessario (non usato) nell'editor.",
      `Opacit\xE0 del codice sorgente non necessario (non usato) nell'editor. Ad esempio, con "#000000c0" il rendering del codice verr\xE0 eseguito con il 75% di opacit\xE0. Per i temi a contrasto elevato, usare il colore del tema 'editorUnnecessaryCode.border' per sottolineare il codice non necessario invece di opacizzarlo.`,
      "Colore del bordo del testo fantasma nell'Editor.",
      "Colore primo piano del testo fantasma nell'Editor.",
      "Colore di sfondo del testo fantasma nell'editor.",
      'Colore del marcatore del righello delle annotazioni per le evidenziazioni degli intervalli. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore del marcatore del righello delle annotazioni per gli errori.',
      'Colore del marcatore del righello delle annotazioni per gli avvisi.',
      'Colore del marcatore del righello delle annotazioni per i messaggi di tipo informativo.',
      "Colore primo piano delle parentesi quadre (1). Richiede l'abilitazione della colorazione delle coppie di parentesi quadre.",
      "Colore primo piano delle parentesi quadre (2). Richiede l'abilitazione della colorazione delle coppie di parentesi quadre.",
      "Colore primo piano delle parentesi quadre (3). Richiede l'abilitazione della colorazione delle coppie di parentesi quadre.",
      "Colore primo piano delle parentesi quadre (4). Richiede l'abilitazione della colorazione delle coppie di parentesi quadre.",
      "Colore primo piano delle parentesi quadre (5). Richiede l'abilitazione della colorazione delle coppie di parentesi quadre.",
      "Colore primo piano delle parentesi quadre (6). Richiede l'abilitazione della colorazione delle coppie di parentesi quadre.",
      'Colore di primo piano delle parentesi impreviste.',
      "Colore di sfondo delle guide per coppie di parentesi inattive (1). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi inattive (2). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi inattive (3). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi inattive (4). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi inattive (5). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi inattive (6). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi attive (1). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi attive (2). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi attive (3). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi attive (4). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi attive (5). Richiede l'abilitazione delle guide per coppie di parentesi.",
      "Colore di sfondo delle guide per coppie di parentesi attive (6). Richiede l'abilitazione delle guide per coppie di parentesi.",
      'Colore del bordo utilizzato per evidenziare i caratteri Unicode.',
      'Colore di sfondo usato per evidenziare i caratteri Unicode.',
      "Indica se il testo dell'editor ha lo stato attivo (il cursore lampeggia)",
      "Indica se l'editor o un widget dell'editor ha lo stato attivo (ad esempio, lo stato attivo si trova nel widget di ricerca)",
      'Indica se un editor o un input RTF ha lo stato attivo (il cursore lampeggia)',
      "Indica se l'editor \xE8 di sola lettura",
      'Indica se il contesto \xE8 un editor diff',
      'Indica se il contesto \xE8 un editor diff incorporato',
      null,
      "Indica se tutti i file nell'editor con pi\xF9 differenze sono compressi",
      "Indica se l'editor diff ha delle modifiche",
      'Indica se un blocco di codice spostato \xE8 selezionato per il confronto',
      'Indica se il visualizzatore differenze accessibile \xE8 visibile',
      "Indica se viene raggiunto il punto di interruzione inline side-by-side per il rendering dell'editor diff",
      'Indica se la modalit\xE0 inline \xE8 attiva',
      "Indica se la modifica \xE8 scrivibile nell'editor diff",
      "Indica se la modifica \xE8 scrivibile nell'editor diff",
      "L'URI del documento originale",
      "L'URI del documento modificato",
      'Indica se `editor.columnSelection` \xE8 abilitato',
      "Indica se per l'editor esiste testo selezionato",
      "Indica se per l'editor esistono pi\xF9 selezioni",
      "Indica se premendo `TAB`, lo stato attivo verr\xE0 spostato all'esterno dell'editor",
      "Indica se il passaggio del puntatore nell'editor \xE8 visibile",
      "Indica se l'area sensibile al passaggio del mouse dell'edito \xE8 attivata",
      'Indica se lo scorrimento permanente \xE8 attivo',
      'Indica se lo scorrimento permanente \xE8 visibile',
      'Indicare se la selezione colori autonoma \xE8 visibile',
      'Indicare se la selezione colori autonoma \xE8 evidenziata',
      "Indica se l'editor fa parte di un editor pi\xF9 esteso (ad esempio notebook)",
      "Identificatore lingua dell'editor",
      "Indica se per l'editor esiste un provider di voci di completamento",
      "Indica se per l'editor esiste un provider di azioni codice",
      "Indica se per l'editor esiste un provider di CodeLens",
      "Indica se per l'editor esiste un provider di definizioni",
      "Indica se per l'editor esiste un provider di dichiarazioni",
      "Indica se per l'editor esiste un provider di implementazioni",
      "Indica se per l'editor esiste un provider di definizioni di tipo",
      "Indica se per l'editor esiste un provider di passaggi del mouse",
      "Indica se per l'editor esiste un provider di evidenziazione documenti",
      "Indica se per l'editor esiste un provider di simboli di documenti",
      "Indica se per l'editor esiste un provider di riferimenti",
      "Indica se per l'editor esiste un provider di ridenominazione",
      "Indica se per l'editor esiste un provider della guida per la firma",
      "Indica se per l'editor esiste un provider di suggerimenti inline",
      "Indica se per l'editor esiste un provider di formattazione documenti",
      "Indica se per l'editor esiste un provider di formattazione di selezioni documento",
      "Indica se per l'editor esistono pi\xF9 provider di formattazione documenti",
      "Indica se per l'editor esistono pi\xF9 provider di formattazione di selezioni documento",
      'matrice',
      'valore booleano',
      'classe',
      'costante',
      'costruttore',
      'enumerazione',
      'membro di enumerazione',
      'evento',
      'campo',
      'file',
      'funzione',
      'interfaccia',
      'chiave',
      'metodo',
      'modulo',
      'spazio dei nomi',
      'Null',
      'numero',
      'oggetto',
      'operatore',
      'pacchetto',
      'propriet\xE0',
      'stringa',
      'struct',
      'parametro di tipo',
      'variabile',
      '{0} ({1})',
      'Testo normale',
      'Digitazione',
      'Sviluppatore: Controlla token',
      'Vai a Riga/Colonna...',
      'Mostra tutti i provider di accesso rapido',
      'Riquadro comandi',
      'Mostra ed esegui comandi',
      'Vai al simbolo...',
      'Vai al simbolo per categoria...',
      'Contenuto editor',
      'Attiva/disattiva tema a contrasto elevato',
      'Effettuate {0} modifiche in {1} file',
      'Mostra di pi\xF9 ({0})',
      '{0} caratteri',
      'Ancoraggio della selezione',
      'Ancoraggio impostato alla posizione {0}:{1}',
      'Imposta ancoraggio della selezione',
      'Vai ad ancoraggio della selezione',
      'Seleziona da ancoraggio a cursore',
      'Annulla ancoraggio della selezione',
      'Colore del marcatore del righello delle annotazioni per la corrispondenza delle parentesi.',
      'Vai alla parentesi quadra',
      'Seleziona fino alla parentesi',
      'Rimuovi parentesi quadre',
      'Vai alla parentesi &&quadra',
      "Selezionare il testo all'interno includendo le parentesi o le parentesi graffe",
      'Sposta testo selezionato a sinistra',
      'Sposta testo selezionato a destra',
      'Trasponi lettere',
      '&&Taglia',
      'Taglia',
      'Taglia',
      'Taglia',
      '&&Copia',
      'Copia',
      'Copia',
      'Copia',
      '&&Incolla',
      'Incolla',
      'Incolla',
      'Incolla',
      'Copia con evidenziazione sintassi',
      'Copia con nome',
      'Copia con nome',
      'Condividi',
      'Condividi',
      "Si \xE8 verificato un errore sconosciuto durante l'applicazione dell'azione del codice",
      "Tipo dell'azione codice da eseguire.",
      'Controlla quando vengono applicate le azioni restituite.',
      'Applica sempre la prima azione codice restituita.',
      "Applica la prima azione codice restituita se \xE8 l'unica.",
      'Non applicare le azioni codice restituite.',
      'Controlla se devono essere restituite solo le azioni codice preferite.',
      'Correzione rapida...',
      'Azioni codice non disponibili',
      "Non sono disponibili azioni codice preferite per '{0}'",
      "Non sono disponibili azioni codice per '{0}'",
      'Non sono disponibili azioni codice preferite',
      'Azioni codice non disponibili',
      'Effettua refactoring...',
      "Non sono disponibili refactoring preferiti per '{0}'",
      "Non sono disponibili refactoring per '{0}'",
      'Non sono disponibili refactoring preferiti',
      'Refactoring non disponibili',
      'Azione origine...',
      "Non sono disponibili azioni origine preferite per '{0}'",
      "Non sono disponibili azioni origine per '{0}'",
      'Non sono disponibili azioni origine preferite',
      'Azioni origine non disponibili',
      'Organizza import',
      'Azioni di organizzazione Imports non disponibili',
      'Correggi tutto',
      'Non \xE8 disponibile alcuna azione Correggi tutto',
      'Correzione automatica...',
      'Non sono disponibili correzioni automatiche',
      'Abilita/disabilita la visualizzazione delle intestazioni gruppo nel menu Azione codice.',
      "Abilita/disabilita la visualizzazione della correzione rapida pi\xF9 vicino all'interno di una riga quando non \xE8 attualmente in una diagnostica.",
      "Abilitare l'attivazione {0} quando {1} \xE8 impostato su {2}. Le azioni codice devono essere impostate su {3} per essere attivate per le modifiche della finestra e dello stato attivo.",
      'Contesto: {0} alla riga {1} e alla colonna {2}.',
      'Nascondi elementi disabilitati',
      'Mostra elementi disabilitati',
      'Altre azioni...',
      'Correzione rapida',
      'Estrai',
      'Inline',
      'Riscrivi',
      'Sposta',
      'Racchiudi tra',
      'Azione di origine',
      "Icona che genera il menu delle azioni codice dalla barra di navigazione quando non \xE8 presente spazio nell'editor.",
      "Icona che genera il menu delle azioni codice dalla barra di navigazione quando non c'\xE8 spazio nell'editor ed \xE8 disponibile una rapida.",
      "Icona che genera il menu delle azioni codice dalla barra di navigazione quando non c'\xE8 spazio nell'editor ed \xE8 disponibile una correzione con l'intelligenza artificiale.",
      "Icona che genera il menu delle azioni codice dalla barra di navigazione quando non c'\xE8 spazio nell'editor e sono disponibili una correzione con l'intelligenza artificiale e una correzione rapida.",
      "Icona che genera il menu delle azioni codice dalla barra di navigazione quando non c'\xE8 spazio nell'editor e sono disponibili una correzione con l'intelligenza artificiale e una correzione rapida.",
      'Esegui: {0}',
      'Mostra azioni codice. Correzione rapida preferita disponibile ({0})',
      'Mostra Azioni codice ({0})',
      'Mostra Azioni codice',
      'Mostra comandi di CodeLens per la riga corrente',
      'Selezionare un comando',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      'Attiva/disattiva commento per la riga',
      'Attiva/Disattiva commento per la &&riga',
      'Aggiungi commento per la riga',
      'Rimuovi commento per la riga',
      'Attiva/Disattiva commento per il blocco',
      'Attiva/Disattiva commento per il &&blocco',
      'Minimappa',
      'Esegui rendering dei caratteri',
      'Dimensioni verticali',
      'Proporzionale',
      'Riempimento',
      'Adatta',
      'Dispositivo di scorrimento',
      'Passaggio del mouse',
      'Sempre',
      'Mostra il menu di scelta rapida editor',
      'Cursore - Annulla',
      'Cursore - Ripeti',
      `Il tipo di modifica di incollaggio con cui provare l'incollaggio.\r
Esistono pi\xF9 modifiche per questo tipo; l'editor mostrer\xE0 una selezione. Se non esistono modifiche di questo tipo, l'editor mostrer\xE0 un messaggio di errore.`,
      'Incolla come...',
      'Incolla come testo',
      "Indica se il widget dell'operazione Incolla viene visualizzato",
      'Mostra opzioni operazione Incolla...',
      "Non \xE8 stata trovata alcuna modifica incolla per '{0}'",
      'Risoluzione della modifica incolla. Fare clic per annullare',
      'Esecuzione dei gestori incolla. Fare clic per annullare ed eseguire incolla di base',
      'Seleziona azione Incolla',
      'Esecuzione dei gestori Incolla in corso',
      'Inserire testo normale',
      "Inserire l'URL",
      "Inserire l'Uri",
      'Inserire percorsi',
      'Inserire percorso',
      'Inserire percorsi relativi',
      'Inserire percorso relativo',
      'Inserisci HTML',
      null,
      'Indica se il widget di rilascio viene visualizzato',
      'Mostra opzioni di rilascio...',
      'Esecuzione dei gestori di rilascio. Fare clic per annullare',
      `Errore durante la risoluzione della modifica '{0}':\r
{1}`,
      `Errore durante l'applicazione della modifica '{0}':\r
{1}`,
      "Indica se l'editor esegue un'operazione annullabile, ad esempio 'Anteprima riferimenti'",
      "Il file \xE8 troppo grande per eseguire un'operazione di sostituzione.",
      'Trova',
      '&&Trova',
      'Trova con gli argomenti',
      'Trova con selezione',
      'Trova successivo',
      'Trova precedente',
      'Andare a Corrispondenza...',
      "Nessuna corrispondenza. Provare a cercare qualcos'altro.",
      'Digitare un numero per passare a una corrispondenza specifica (tra 1 e {0})',
      'Digitare un numero compreso tra 1 e {0}',
      'Digitare un numero compreso tra 1 e {0}',
      'Trova selezione successiva',
      'Trova selezione precedente',
      'Sostituisci',
      '&&Sostituisci',
      "Icona per indicare che il widget di ricerca dell'editor \xE8 compresso.",
      "Icona per indicare che il widget di ricerca dell'editor \xE8 espanso.",
      "Icona per 'Trova nella selezione' nel widget di ricerca dell'editor.",
      "Icona per 'Sostituisci' nel widget di ricerca dell'editor.",
      "Icona per 'Sostituisci tutto' nel widget di ricerca dell'editor.",
      "Icona per 'Trova precedente' nel widget di ricerca dell'editor.",
      "Icona per 'Trova successivo' nel widget di ricerca dell'editor.",
      'Trova/Sostituisci',
      'Trova',
      'Trova',
      'Risultato precedente',
      'Risultato successivo',
      'Trova nella selezione',
      'Chiudi',
      'Sostituisci',
      'Sostituisci',
      'Sostituisci',
      'Sostituisci tutto',
      'Attiva/Disattiva sostituzione',
      'Solo i primi {0} risultati vengono evidenziati, ma tutte le operazioni di ricerca funzionano su tutto il testo.',
      '{0} di {1}',
      'Nessun risultato',
      '{0} trovato',
      "{0} trovati per '{1}'",
      "{0} trovati per '{1}' alla posizione {2}",
      "{0} trovati per '{1}'",
      "Il tasto di scelta rapida CTRL+INVIO ora consente di inserire l'interruzione di linea invece di sostituire tutto. Per eseguire l'override di questo comportamento, \xE8 possibile modificare il tasto di scelta rapida per editor.action.replaceAll.",
      'Espandi',
      'Espandi in modo ricorsivo',
      'Riduci',
      'Attiva/Disattiva riduzione',
      'Riduci in modo ricorsivo',
      'Attiva/disattiva riduzione in modo ricorsivo',
      'Riduci tutti i blocchi commento',
      'Riduci tutte le regioni',
      'Espandi tutte le regioni',
      'Riduci tutto tranne selezionato',
      'Espandi tutto tranne selezionato',
      'Riduci tutto',
      'Espandi tutto',
      'Vai alla cartella principale',
      "Passa all'intervallo di riduzione precedente",
      "Passa all'intervallo di riduzione successivo",
      'Creare intervallo di riduzione dalla selezione',
      'Rimuovi intervalli di riduzione manuale',
      'Livello riduzione {0}',
      'Colore di sfondo degli intervalli con riduzione. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore del testo compresso dopo la prima riga di un intervallo ridotto.',
      "Colore del controllo di riduzione nella barra di navigazione dell'editor.",
      "Icona per gli intervalli espansi nel margine del glifo dell'editor.",
      "Icona per gli intervalli compressi nel margine del glifo dell'editor.",
      "Icona per gli intervalli compressi nel margine del glifo dell'editor.",
      "Icona per gli intervalli espansi manualmente nel margine del glifo dell'editor.",
      'Fare clic per espandere l\u2019intervallo.',
      "Fare clic per comprimere l'intervallo.",
      "Aumenta le dimensioni del carattere dell'editor",
      "Riduci le dimensioni del carattere dell'editor",
      'Reimposta dimensioni carattere editor',
      'Formatta documento',
      'Formatta selezione',
      'Vai al problema successivo (Errore, Avviso, Informazioni)',
      'Icona per il marcatore Vai a successivo.',
      'Vai al problema precedente (Errore, Avviso, Informazioni)',
      'Icona per il marcatore Vai a precedente.',
      'Vai al problema successivo nei file (Errore, Avviso, Informazioni)',
      '&&Problema successivo',
      'Vai al problema precedente nei file (Errore, Avviso, Informazioni)',
      '&&Problema precedente',
      'Errore',
      'Avviso',
      'Info',
      'Suggerimento',
      '{0} a {1}. ',
      '{0} di {1} problemi',
      '{0} di {1} problema',
      "Colore per gli errori del widget di spostamento tra marcatori dell'editor.",
      "Intestazione errore per lo sfondo del widget di spostamento tra marcatori dell'editor.",
      "Colore per gli avvisi del widget di spostamento tra marcatori dell'editor.",
      "Intestazione avviso per lo sfondo del widget di spostamento tra marcatori dell'editor.",
      "Colore delle informazioni del widget di navigazione marcatori dell'editor.",
      "Intestazione informativa per lo sfondo del widget di spostamento tra marcatori dell'editor.",
      "Sfondo del widget di spostamento tra marcatori dell'editor.",
      'Anteprima',
      'Definizioni',
      "Non \xE8 stata trovata alcuna definizione per '{0}'",
      'Non \xE8 stata trovata alcuna definizione',
      'Vai alla &&definizione',
      'Dichiarazioni',
      "Non \xE8 stata trovata alcuna dichiarazione per '{0}'",
      'Dichiarazione non trovata',
      'Vai a &&dichiarazione',
      "Non \xE8 stata trovata alcuna dichiarazione per '{0}'",
      'Dichiarazione non trovata',
      'Definizioni di tipo',
      "Non sono state trovate definizioni di tipi per '{0}'",
      'Non sono state trovate definizioni di tipi',
      'Vai alla &&definizione di tipo',
      'Implementazioni',
      "Non sono state trovate implementazioni per '{0}'",
      'Non sono state trovate implementazioni',
      'Vai a &&Implementazioni',
      "Non sono stati trovati riferimenti per '{0}'",
      'Non sono stati trovati riferimenti',
      'Vai a &&riferimenti',
      'Riferimenti',
      'Riferimenti',
      'Posizioni',
      "Nessun risultato per '{0}'",
      'Riferimenti',
      'Vai alla definizione',
      'Apri definizione lateralmente',
      'Visualizza in anteprima la definizione',
      'Vai a dichiarazione',
      'Anteprima dichiarazione',
      'Vai alla definizione di tipo',
      'Anteprima definizione di tipo',
      'Vai a implementazioni',
      'Visualizza implementazioni',
      'Vai a Riferimenti',
      'Anteprima riferimenti',
      'Vai a qualsiasi simbolo',
      'Fare clic per visualizzare {0} definizioni.',
      "Indica se l'anteprima riferimenti \xE8 visibile, come 'Visualizza in anteprima riferimenti' o 'Visualizza in anteprima la definizione'",
      'Caricamento...',
      '{0} ({1})',
      '{0} riferimenti',
      '{0} riferimento',
      'Riferimenti',
      'anteprima non disponibile',
      'Nessun risultato',
      'Riferimenti',
      'in {0} alla riga {1} della colonna {2}',
      '{0} in {1} alla riga {2} della colonna {3}',
      '1 simbolo in {0}, percorso completo {1}',
      '{0} simboli in {1}, percorso completo {2}',
      'Non sono stati trovati risultati',
      'Trovato 1 simbolo in {0}',
      'Trovati {0} simboli in {1}',
      'Trovati {0} simboli in {1} file',
      'Indica se sono presenti posizioni dei simboli a cui \xE8 possibile passare solo tramite la tastiera.',
      'Simbolo {0} di {1}, {2} per il successivo',
      'Simbolo {0} di {1}',
      'Aumenta livello di dettaglio al passaggio del mouse',
      'Riduci livello di dettaglio al passaggio del mouse',
      'Mostra o sposta lo stato attivo al passaggio del mouse',
      'Il passaggio del mouse non attiver\xE0 automaticamente lo stato attivo.',
      'Il passaggio del mouse attiver\xE0 lo stato attivo solo se \xE8 gi\xE0 visibile.',
      'Il passaggio del mouse assume automaticamente lo stato attivo quando viene visualizzato.',
      'Mostra anteprima definizione al passaggio del mouse',
      "Scorri verso l'alto al passaggio del mouse",
      'Scorri verso il basso al passaggio del mouse',
      'Scorri a sinistra al passaggio del mouse',
      'Scorri a destra al passaggio del mouse',
      'Vai alla pagina precedente al passaggio del mouse',
      'Vai alla pagina successiva al passaggio del mouse',
      'Vai in alto al passaggio del mouse',
      'Vai in basso al passaggio del mouse',
      "Mostra o sposta lo stato attivo sull'editor al passaggio del mouse, che mostra documentazione, riferimenti e altro contenuto per un simbolo nella posizione corrente del cursore.",
      "Mostra l'anteprima della definizione nell'editor al passaggio del mouse.",
      "Scorri verso l'alto l'editor al passaggio del mouse.",
      "Scorri in basso nell'editor al passaggio del mouse.",
      "Scorri a sinistra dell'editor al passaggio del mouse.",
      "Scorri a destra dell'editor al passaggio del mouse.",
      "Vai alla pagina precedente dell'editor al passaggio del mouse.",
      "Vai alla pagina successiva dell'editor al passaggio del mouse.",
      "Vai alla parte superiore dell'editor al passaggio del mouse.",
      "Vai alla parte inferiore dell'editor al passaggio del mouse.",
      'Icona per aumentare il livello di dettaglio al passaggio del mouse.',
      'Icona per ridurre il livello di dettaglio al passaggio del mouse.',
      'Caricamento...',
      "Rendering sospeso per una linea lunga per motivi di prestazioni. Pu\xF2 essere configurato tramite 'editor.stopRenderingLineAfter'.",
      'Per motivi di prestazioni la tokenizzazione viene ignorata per le righe lunghe. \xC8 possibile effettuare questa configurazione tramite `editor.maxTokenizationLineLength`.',
      'Aumenta livello di dettaglio al passaggio del mouse ({0})',
      'Aumenta livello di dettaglio al passaggio del mouse',
      'Riduci livello di dettaglio al passaggio del mouse ({0})',
      'Riduci livello di dettaglio al passaggio del mouse',
      'Visualizza problema',
      'Non sono disponibili correzioni rapide',
      'Verifica disponibilit\xE0 correzioni rapide...',
      'Non sono disponibili correzioni rapide',
      'Correzione rapida...',
      'Converti rientro in spazi',
      'Converti rientro in tabulazioni',
      'Dimensione tabulazione configurata',
      'Dimensioni predefinite della scheda',
      'Dimensioni della scheda corrente',
      'Seleziona dimensione tabulazione per il file corrente',
      'Imposta rientro con tabulazioni',
      'Imposta rientro con spazi',
      'Modifica dimensioni visualizzazione scheda',
      'Rileva rientro dal contenuto',
      'Imposta nuovo rientro per righe',
      'Re-Indenta le Linee Selezionate',
      'Converti il rientro della tabulazione in spazi.',
      'Converti il rientro degli spazi in tabulazioni.',
      'Usa il rientro con le tabulazioni.',
      'Usa il rientro con gli spazi.',
      "Modifica l'equivalente della dimensione dello spazio della scheda.",
      'Rilevare il rientro dal contenuto.',
      "Imposta nuovamente un rientro per le righe dell'editor.",
      "Imposta nuovamente un rientro per determinate righe dell'editor.",
      'Fare doppio clic per inserire',
      'CMD+clic',
      'CTRL+clic',
      'Opzione+clic',
      'ALT+clic',
      'Vai alla definizione ({0}), fai clic con il pulsante destro del mouse per altre informazioni',
      'Vai alla definizione ({0})',
      'Esegui il comando',
      'Mostrare suggerimento inline successivo',
      'Mostrare suggerimento inline precedente',
      'Attiva suggerimento inline',
      'Accettare suggerimento inline per la parola successiva',
      'Accetta parola',
      'Accetta la riga successiva del suggerimento in linea',
      'Accetta riga',
      'Accetta suggerimento inline',
      'Accetta',
      'Nascondi suggerimento inline',
      'Mostra sempre la barra degli strumenti',
      'Se \xE8 visibile un suggerimento inline',
      'Se il suggerimento in linea inizia con spazi vuoti',
      'Indica se il suggerimento inline inizia con uno spazio vuoto minore di quello che verrebbe inserito dalla tabulazione',
      'Indica se i suggerimenti devono essere eliminati per il suggerimento corrente',
      'Ispezionarlo nella visualizzazione accessibile ({0})',
      'Suggerimento:',
      'Icona per visualizzare il suggerimento del parametro successivo.',
      'Icona per visualizzare il suggerimento del parametro precedente.',
      '{0} ({1})',
      'Indietro',
      'Avanti',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      'Sostituisci con il valore precedente',
      'Sostituisci con il valore successivo',
      'Espandere selezione riga',
      'Copia la riga in alto',
      '&&Copia la riga in alto',
      'Copia la riga in basso',
      'Co&&pia la riga in basso',
      'Duplica selezione',
      '&&Duplica selezione',
      'Sposta la riga in alto',
      'Sposta la riga in &&alto',
      'Sposta la riga in basso',
      'Sposta la riga in &&basso',
      'Ordinamento righe crescente',
      'Ordinamento righe decrescente',
      'Elimina righe duplicate',
      'Taglia spazio vuoto finale',
      'Elimina riga',
      'Imposta un rientro per la riga',
      'Riduci il rientro per la riga',
      'Inserisci la riga sopra',
      'Inserisci la riga sotto',
      'Elimina tutto a sinistra',
      'Elimina tutto a destra',
      'Unisci righe',
      'Trasponi caratteri intorno al cursore',
      'Converti in maiuscolo',
      'Converti in minuscolo',
      'Trasforma in Tutte Iniziali Maiuscole',
      'Trasforma in snake case',
      'Trasforma in caso Camel',
      'Trasforma in Pascal Case',
      'Trasformare in caso Kebab',
      'Avvia modifica collegata',
      "Colore di sfondo quando l'editor viene rinominato automaticamente in base al tipo.",
      'Non \xE8 stato possibile aprire questo collegamento perch\xE9 il formato non \xE8 valido: {0}',
      'Non \xE8 stato possibile aprire questo collegamento perch\xE9 manca la destinazione.',
      'Esegui il comando',
      'Visita il collegamento',
      'CMD+clic',
      'CTRL+clic',
      'Opzione+clic',
      'ALT+clic',
      'Esegue il comando {0}',
      'Apri collegamento',
      "Indica se l'editor visualizza attualmente un messaggio inline",
      'Cursore aggiunto: {0}',
      'Cursori aggiunti: {0}',
      'Aggiungi cursore sopra',
      '&&Aggiungi cursore sopra',
      'Aggiungi cursore sotto',
      'A&&ggiungi cursore sotto',
      'Aggiungi cursori a fine riga',
      'Aggiungi c&&ursori a fine riga',
      'Aggiungi cursori alla fine',
      "Aggiungi cursori all'inizio",
      'Aggiungi selezione a risultato ricerca successivo',
      'Aggiungi &&occorrenza successiva',
      'Aggiungi selezione a risultato ricerca precedente',
      'Aggiungi occorrenza &&precedente',
      'Sposta ultima selezione a risultato ricerca successivo',
      'Sposta ultima selezione a risultato ricerca precedente',
      'Seleziona tutte le occorrenze del risultato ricerca',
      'Seleziona &&tutte le occorrenze',
      'Cambia tutte le occorrenze',
      'Attival cursore successivo',
      'Attiva il cursore successivo',
      'Cursore precedente stato attivo',
      'Imposta lo stato attivo sul cursore precedente',
      'Attiva i suggerimenti per i parametri',
      'Icona per visualizzare il suggerimento del parametro successivo.',
      'Icona per visualizzare il suggerimento del parametro precedente.',
      '{0}, suggerimento',
      'Colore di primo piano dell\u2019articolo attivo nel suggerimento di parametro.',
      "Indica se l'editor di codice corrente \xE8 incorporato nell'anteprima",
      'Chiudi',
      "Colore di sfondo dell'area del titolo della visualizzazione rapida.",
      'Colore del titolo della visualizzazione rapida.',
      'Colore delle informazioni del titolo della visualizzazione rapida.',
      'Colore dei bordi e della freccia della visualizzazione rapida.',
      "Colore di sfondo dell'elenco risultati della visualizzazione rapida.",
      "Colore primo piano dei nodi riga nell'elenco risultati della visualizzazione rapida.",
      "Colore primo piano dei nodi file nell'elenco risultati della visualizzazione rapida.",
      "Colore di sfondo della voce selezionata nell'elenco risultati della visualizzazione rapida.",
      "Colore primo piano della voce selezionata nell'elenco risultati della visualizzazione rapida.",
      "Colore di sfondo dell'editor di visualizzazioni rapide.",
      "Colore di sfondo della barra di navigazione nell'editor visualizzazione rapida.",
      "Colore di sfondo della barra di scorrimento permanente nell'editor visualizzazione rapida.",
      "Colore dell'evidenziazione delle corrispondenze nell'elenco risultati della visualizzazione rapida.",
      "Colore dell'evidenziazione delle corrispondenze nell'editor di visualizzazioni rapide.",
      "Bordo dell'evidenziazione delle corrispondenze nell'editor di visualizzazioni rapide.",
      "Colore primo piano del testo segnaposto nell'editor.",
      'Aprire prima un editor di testo per passare a una riga.',
      'Vai a riga {0} e carattere {1}.',
      'Vai alla riga {0}.',
      'Riga corrente: {0}, carattere: {1}. Digitare un numero di riga a cui passare compreso tra 1 e {2}.',
      'Riga corrente: {0}, Carattere: {1}. Digitare un numero di riga a cui passare.',
      'Per passare a un simbolo, aprire prima un editor di testo con informazioni sui simboli.',
      "L'editor di testo attivo non fornisce informazioni sui simboli.",
      "Non ci sono simboli dell'editor corrispondenti",
      "Non ci sono simboli dell'editor",
      'Apri lateralmente',
      'Apri in basso',
      'simboli ({0})',
      'propriet\xE0 ({0})',
      'metodi ({0})',
      'funzioni ({0})',
      'costruttori ({0})',
      'variabili ({0})',
      'classi ({0})',
      'struct ({0})',
      'eventi ({0})',
      'operatori ({0})',
      'interfacce ({0})',
      'spazi dei nomi ({0})',
      'pacchetti ({0})',
      'parametri di tipo ({0})',
      'moduli ({0})',
      'propriet\xE0 ({0})',
      'enumerazioni ({0})',
      'membri di enumerazione ({0})',
      'stringhe ({0})',
      'file ({0})',
      'matrici ({0})',
      'numeri ({0})',
      'valori booleani ({0})',
      'oggetti ({0})',
      'chiavi ({0})',
      'campi ({0})',
      'costanti ({0})',
      "Non \xE8 possibile modificare nell'input di sola lettura",
      "Non \xE8 possibile modificare nell'editor di sola lettura",
      'Nessun risultato.',
      'Si \xE8 verificato un errore sconosciuto durante la risoluzione del percorso di ridenominazione',
      "Ridenominazione di '{0}' in '{1}'",
      'Ridenominazione di {0} in {1}',
      "Correttamente rinominato '{0}' in '{1}'. Sommario: {2}",
      'La ridenominazione non \xE8 riuscita ad applicare le modifiche',
      'La ridenominazione non \xE8 riuscita a calcolare le modifiche',
      'Rinomina simbolo',
      "Abilita/Disabilita l'opzione per visualizzare le modifiche in anteprima prima della ridenominazione",
      'Sposta lo stato attivo sul suggerimento di ridenominazione successiva',
      'Sposta lo stato attivo sul suggerimento di ridenominazione precedente',
      'Indica se il widget di ridenominazione input \xE8 visibile',
      'Indica se il widget di ridenominazione input \xE8 attivo',
      '{0} per rinominare, {1} per visualizzare in anteprima',
      '{0} suggerimenti di ridenominazione ricevuti',
      "Consente di rinominare l'input. Digitare il nuovo nome e premere INVIO per eseguire il commit.",
      'Genera nuovi suggerimenti per i nomi',
      'Annulla',
      'Espandi selezione',
      'Espan&&di selezione',
      'Riduci selezione',
      '&&Riduci selezione',
      "Indica se l'editor \xE8 quello corrente nella modalit\xE0 frammenti",
      'Indica se \xE8 presente una tabulazione successiva in modalit\xE0 frammenti',
      'Indica se \xE8 presente una tabulazione precedente in modalit\xE0 frammenti',
      'Vai al segnaposto successivo...',
      'Domenica',
      'Luned\xEC',
      'Marted\xEC',
      'Mercoled\xEC',
      'Gioved\xEC',
      'Venerd\xEC',
      'Sabato',
      'Dom',
      'Lun',
      'Mar',
      'Mer',
      'Gio',
      'Ven',
      'Sab',
      'Gennaio',
      'Febbraio',
      'Marzo',
      'Aprile',
      'Mag',
      'Giugno',
      'Luglio',
      'Agosto',
      'Settembre',
      'Ottobre',
      'Novembre',
      'Dicembre',
      'Gen',
      'Feb',
      'Mar',
      'Apr',
      'Mag',
      'Giu',
      'Lug',
      'Ago',
      'Set',
      'Ott',
      'Nov',
      'Dic',
      "&&Attiva/disattiva scorrimento permanente dell'editor",
      'Scorrimento permanente',
      '&&Scorrimento permanente',
      '&&Sposta stato attivo su Scorrimento permanente',
      "Attiva/disattiva scorrimento permanente dell'editor",
      "Attiva/disattiva/abilita lo scorrimento permanente dell'editor che mostra gli ambiti annidati nella parte superiore del riquadro di visualizzazione",
      "Sposta lo stato attivo sullo scorrimento permanente dell'editor",
      "Seleziona la riga di scorrimento permanente precedente successiva dell'editor",
      'Seleziona la riga di scorrimento permanente precedente',
      'Vai alla linea di scorrimento permanente con stato attivo',
      "Selezionare l'editor",
      'Indica se i suggerimenti sono evidenziati',
      'Indica se i dettagli dei suggerimenti sono visibili',
      'Indica se sono presenti pi\xF9 suggerimenti da cui scegliere',
      "Indica se l'inserimento del suggerimento corrente comporta una modifica oppure se completa gi\xE0 l'input",
      'Indica se i suggerimenti vengono inseriti quando si preme INVIO',
      'Indica se il suggerimento corrente include il comportamento di inserimento e sostituzione',
      'Indica se il comportamento predefinito \xE8 quello di inserimento o sostituzione',
      'Indica se il suggerimento corrente supporta la risoluzione di ulteriori dettagli',
      "In seguito all'accettazione di '{0}' sono state apportate altre {1} modifiche",
      'Attiva suggerimento',
      'Inserisci',
      'Inserisci',
      'Sostituisci',
      'Sostituisci',
      'Inserisci',
      'Mostra meno',
      'Mostra di pi\xF9',
      'Reimposta le dimensioni del widget dei suggerimenti',
      'Colore di sfondo del widget dei suggerimenti.',
      'Colore del bordo del widget dei suggerimenti.',
      'Colore primo piano del widget dei suggerimenti.',
      'Colore primo piano della voce selezionata del widget dei suggerimenti.',
      'Colore primo piano dell\u2019icona della voce selezionata del widget dei suggerimenti.',
      'Colore di sfondo della voce selezionata del widget dei suggerimenti.',
      'Colore delle evidenziazioni corrispondenze nel widget dei suggerimenti.',
      'Colore delle evidenziazioni corrispondenze nel widget dei suggerimenti quando lo stato attivo si trova su un elemento.',
      'Colore primo piano dello stato del widget dei suggerimenti.',
      'Caricamento...',
      'Non ci sono suggerimenti.',
      'Suggerisci',
      '{0} {1}, {2}',
      '{0} {1}',
      '{0}, {1}',
      '{0}, documenti: {1}',
      'Chiudi',
      'Caricamento...',
      'Icona per visualizzare altre informazioni nel widget dei suggerimenti.',
      'Altre informazioni',
      "Colore primo piano per i simboli di matrice. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli booleani. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di classe. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di colore. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di costante. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di costruttore. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di enumeratore. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di membro di enumeratore. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di evento. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di campo. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di file. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di cartella. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di funzione. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di interfaccia. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di chiave. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di parola chiave. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di metodo. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di modulo. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di spazio dei nomi. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli Null. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli numerici. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di oggetto. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di operatore. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di pacchetto. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di propriet\xE0. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di riferimento. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di frammento. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di stringa. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di struct. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di testo. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di parametro di tipo. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di unit\xE0. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Colore primo piano per i simboli di variabile. Questi simboli vengono visualizzati nella struttura, nell'elemento di navigazione e nel widget dei suggerimenti.",
      "Se si preme TAB, lo stato attivo verr\xE0 spostato sull'elemento con stato attivabile successivo.",
      'Se si preme TAB, verr\xE0 inserito il carattere di tabulazione',
      "Attiva/Disattiva l'uso di TAB per spostare lo stato attivo",
      "Determina se il tasto TAB sposta lo stato attivo intorno all'area di lavoro o inserisce il carattere di tabulazione nell'editor corrente. \xC8 anche denominato intercettazione delle tabulazioni, spostamento tra le tabulazioni, o modalit\xE0 focus delle tabulazioni.",
      'Sviluppatore: Forza retokenizzazione',
      "Icona visualizzata con un messaggio di avviso nell'editor delle estensioni.",
      'Questo documento contiene molti caratteri Unicode ASCII non di base',
      'Il documento contiene molti caratteri Unicode ambigui',
      'Questo documento contiene molti caratteri Unicode invisibili',
      'Configurare opzioni evidenziazione Unicode',
      'Il carattere {0} potrebbe essere confuso con il carattere ASCII {1}, che \xE8 pi\xF9 comune nel codice sorgente.',
      'Il carattere {0} potrebbe essere confuso con il carattere {1}, che \xE8 pi\xF9 comune nel codice sorgente.',
      'Il carattere {0} \xE8 invisibile.',
      'Il carattere {0} non \xE8 un carattere ASCII di base.',
      'Modificare impostazioni',
      'Disabilita evidenziazione nei commenti',
      "Disabilita l'evidenziazione dei caratteri nei commenti",
      'Disabilita evidenziazione nelle stringhe',
      "Disabilita l'evidenziazione dei caratteri nelle stringhe",
      'Disabilitare evidenziazione ambigua',
      "Disabilitare l'evidenziazione dei caratteri ambigui",
      'Disabilitare evidenziazione invisibile',
      "Disabilitare l'evidenziazione dei caratteri invisibili",
      'Disabilitare evidenziazione non ASCII',
      "Disabilitare l'evidenziazione di caratteri ASCII non di base",
      'Mostrare opzioni di esclusione',
      "Escludere {0} (carattere invisibile) dall'evidenziazione",
      'Escludere {0} dall\u2019essere evidenziata',
      'Consentire i caratteri Unicode pi\xF9 comuni nel linguaggio "{0}".',
      'Caratteri di terminazione di riga insoliti',
      'Sono stati rilevati caratteri di terminazione di riga insoliti',
      'Il file "\r\n" contiene uno o pi\xF9 caratteri di terminazione di riga insoliti, ad esempio separatore di riga (LS) o separatore di paragrafo (PS).{0}\r\n\xC8 consigliabile rimuoverli dal file. \xC8 possibile configurare questa opzione tramite `editor.unusualLineTerminators`.',
      '&&Rimuovi i caratteri di terminazione di riga insoliti',
      'Ignora',
      "Colore di sfondo di un simbolo durante l'accesso in lettura, ad esempio durante la lettura di una variabile. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      "Colore di sfondo di un simbolo durante l'accesso in scrittura, ad esempio durante la scrittura in una variabile. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      "Colore di sfondo di un'occorrenza testuale per un simbolo. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      "Colore del bordo di un simbolo durante l'accesso in lettura, ad esempio durante la lettura di una variabile.",
      "Colore del bordo di un simbolo durante l'accesso in scrittura, ad esempio durante la scrittura in una variabile.",
      "Colore del bordo di un'occorrenza testuale per un simbolo.",
      'Colore del marcatore del righello delle annotazioni per le evidenziazioni dei simboli. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore del marcatore del righello delle annotazioni per le evidenziazioni dei simboli di accesso in scrittura. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      "Colore del marcatore del righello delle annotazioni di un'occorrenza testuale per un simbolo. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      'Vai al prossimo simbolo evidenziato',
      'Vai al precedente simbolo evidenziato',
      'Attiva/disattiva evidenziazione simbolo',
      'Elimina parola',
      'Errore nella funzione',
      'Errore',
      'Avviso nella funzione',
      'Avviso',
      'Errore sulla riga',
      'Errore sulla riga',
      'Avviso sulla riga',
      'Avviso sulla riga',
      'Area piegata sulla linea',
      'Ridotto',
      'Punto di interruzione sulla riga',
      'Punto di interruzione',
      'Suggerimento inline sulla riga',
      'Correzione rapida terminale',
      'Correzione rapida',
      'Debugger arrestato sul punto di interruzione',
      'Punto di interruzione',
      "Nessun suggerimento per l'inlay nella riga",
      'Nessun suggerimento di inlay',
      'Attivit\xE0 completata',
      'Attivit\xE0 completata',
      'Attivit\xE0 non riuscita',
      'Attivit\xE0 non riuscita',
      'Comando terminale non riuscito',
      'Comando non riuscito',
      'Comando terminale riuscito',
      'Comando riuscito',
      'Campanello terminale',
      'Campanello terminale',
      'Cella del notebook completata',
      'Cella del notebook completata',
      'La cella del notebook ha avuto esito negativo',
      'La cella del notebook ha avuto esito negativo',
      'Riga diff inserita',
      'Riga diff eliminata',
      'Riga diff modificata',
      'Richiesta chat inviata',
      'Richiesta di chat inviata',
      'Risposta chat ricevuta',
      'Avanzamento',
      'Avanzamento',
      'Cancella',
      'Cancella',
      'Salva',
      'Salva',
      'Formato',
      'Formato',
      'Registrazione vocale avviata',
      'Registrazione vocale interrotta',
      'Visualizza',
      'Guida',
      'Test',
      'FILE',
      'Preferenze',
      'Sviluppatore',
      '{0} ({1})',
      '{0} ({1})',
      `{0}\r
[{1}] {2}`,
      '{1} per {0}',
      '{0} ({1})',
      'Nascondi',
      'Reimposta menu',
      "Nascondi '{0}'",
      'Configura tasto di scelta rapida',
      '{0} per Applica, {1} per Anteprima',
      '{0} da applicare',
      '{0}, Motivo disabilitato: {1}',
      'Widget azione',
      'Colore di sfondo per le azioni attivate o disattivate nella barra delle azioni.',
      "Indica se l'elenco di widget azione \xE8 visibile",
      'Nascondi widget azione',
      'Seleziona azione precedente',
      'Seleziona azione successiva',
      "Accetta l'azione selezionata",
      'Anteprima azione selezionata',
      'Override configurazione predefinita del linguaggio',
      "Consente di configurare le impostazioni di cui eseguire l'override per il linguaggio {0}.",
      "Consente di configurare le impostazioni dell'editor di cui eseguire l'override per un linguaggio.",
      'Questa impostazione non supporta la configurazione per lingua.',
      "Consente di configurare le impostazioni dell'editor di cui eseguire l'override per un linguaggio.",
      'Questa impostazione non supporta la configurazione per lingua.',
      'Non \xE8 possibile registrare una propriet\xE0 vuota',
      "Non \xE8 possibile registrare '{0}'. Corrisponde al criterio di propriet\xE0 '\\\\[.*\\\\]$' per la descrizione delle impostazioni dell'editor specifiche del linguaggio. Usare il contributo 'configurationDefaults'.",
      "Non \xE8 possibile registrare '{0}'. Questa propriet\xE0 \xE8 gi\xE0 registrata.",
      "Impossibile registrare '{0}'. Il {1} dei criteri associato \xE8 gi\xE0 registrato con {2}.",
      'Comando che restituisce informazioni sulle chiavi di contesto',
      'Espressione chiave di contesto vuota',
      "Si \xE8 dimenticato di scrivere un'espressione? \xC8 anche possibile inserire 'false' o 'true' per restituire sempre rispettivamente false o true.",
      "'in' dopo 'not'.",
      "Parentesi chiusa ')'",
      'Token imprevisto',
      'Si \xE8 dimenticato di inserire && o || prima del token?',
      "Fine imprevista dell'espressione",
      'Si \xE8 dimenticato di inserire una chiave di contesto?',
      `Previsto: {0}\r
Ricevuto: '{1}'.`,
      'Indica se il sistema operativo \xE8 macOS',
      'Indica se il sistema operativo \xE8 Linux',
      'Indica se il sistema operativo \xE8 Windows',
      'Indica se la piattaforma \xE8 un Web browser',
      'Indica se il sistema operativo \xE8 macOS in una piattaforma non basata su browser',
      'Indica se il sistema operativo \xE8 iOS',
      'Indica se la piattaforma \xE8 un Web browser per dispositivi mobili',
      'Tipo di qualit\xE0 del VS Code',
      "Indica se lo stato attivo della tastiera si trova all'interno di una casella di input",
      'Si intendeva {0}?',
      'Si intendeva {0} o {1}?',
      'Si intendeva {0}, {1} o {2}?',
      'Si \xE8 dimenticato di aprire o chiudere la citazione?',
      "Si \xE8 dimenticato di eseguire il carattere di escape '/' (slash)? Inserire due barre rovesciate prima del carattere di escape, ad esempio '\\\\/'.",
      'Indica se i suggerimenti sono visibili',
      '\xC8 stato premuto ({0}). In attesa del secondo tasto...',
      '\xC8 stato premuto ({0}). In attesa del prossimo tasto...',
      'La combinazione di tasti ({0}, {1}) non \xE8 un comando.',
      'La combinazione di tasti ({0}, {1}) non \xE8 un comando.',
      'Workbench',
      "Rappresenta il tasto 'Control' in Windows e Linux e il tasto 'Comando' in macOS.",
      "Rappresenta il tasto 'Alt' in Windows e Linux e il tasto 'Opzione' in macOS.",
      "Il modificatore da utilizzare per aggiungere un elemento di alberi e liste ad una selezione multipla con il mouse (ad esempio in Esplora Risorse, apre gli editor e le viste scm). Le gesture del mouse 'Apri a lato' - se supportate - si adatteranno in modo da non creare conflitti con il modificatore di selezione multipla.",
      "Controlla l'apertura degli elementi di alberi ed elenchi tramite il mouse (se supportato). Tenere presente che alcuni alberi ed elenchi potrebbero scegliere di ignorare questa impostazione se non \xE8 applicabile.",
      "Controlla se elenchi e alberi supportano lo scorrimento orizzontale nell'area di lavoro. Avviso: l'attivazione di questa impostazione pu\xF2 influire sulle prestazioni.",
      'Controlla se i clic nella barra di scorrimento scorrono pagina per pagina.',
      "Controlla il rientro dell'albero in pixel.",
      "Controlla se l'albero deve eseguire il rendering delle guide per i rientri.",
      'Controlla se elenchi e alberi prevedono lo scorrimento uniforme.',
      'Moltiplicatore da usare sui valori `deltaX` e `deltaY` degli eventi di scorrimento della rotellina del mouse.',
      'Moltiplicatore della velocit\xE0 di scorrimento quando si preme `Alt`.',
      "Evidenziare gli elementi durante la ricerca. L'ulteriore spostamento verso l'alto e verso il basso attraverser\xE0 solo gli elementi evidenziati.",
      'Filtra gli elementi durante la ricerca.',
      'Controlla la modalit\xE0 di ricerca predefinita per elenchi e alberi nel workbench.',
      "Con lo stile di spostamento da tastiera simple lo stato attivo si trova sugli elementi che corrispondono all'input da tastiera. L'abbinamento viene effettuato solo in base ai prefissi.",
      "Con lo stile di spostamento da tastiera highlight vengono evidenziati gli elementi corrispondenti all'input da tastiera. Spostandosi ulteriormente verso l'alto o verso il basso ci si sposter\xE0 solo negli elementi evidenziati.",
      "Con lo stile di spostamento da tastiera filter verranno filtrati e nascosti tutti gli elementi che non corrispondono all'input da tastiera.",
      'Controlla lo stile di spostamento da tastiera per elenchi e alberi nel workbench. Le opzioni sono: simple, highlight e filter.',
      "In alternativa, usare 'workbench.list.defaultFindMode' e 'workbench.list.typeNavigationMode'.",
      'Usa la corrispondenza fuzzy durante la ricerca.',
      'Usa corrispondenza contigua durante la ricerca.',
      'Controlla il tipo di corrispondenza usato per la ricerca di elenchi e alberi nel workbench.',
      "Controlla l'espansione delle cartelle di alberi quando si fa clic sui nomi delle cartelle. Tenere presente che alcuni alberi ed elenchi potrebbero scegliere di ignorare questa impostazione se non \xE8 applicabile.",
      'Controlla se lo scorrimento permanente \xE8 abilitato negli alberi.',
      "Controlla il numero di elementi permanenti visualizzati nell'albero quando {0} \xE8 abilitato.",
      "Controlla il funzionamento dello spostamento dei tipi in elenchi e alberi nel workbench. Se impostato su 'trigger', l'esplorazione del tipo inizia dopo l'esecuzione del comando 'list.triggerTypeNavigation'.",
      'Errore',
      'Avviso',
      'Info',
      'usate di recente',
      'comandi simili',
      'pi\xF9 usato',
      'altri comandi',
      'comandi simili',
      '{0}, {1}',
      "Il comando '{0}' ha restituito un errore",
      '{0}, {1}',
      "Indica se lo stato attivo della tastiera si trova all'interno del controllo di input rapido",
      "Tipo dell'input rapido attualmente visibile",
      "Indica se il cursore nell'input rapido si trova alla fine della casella di input",
      'Indietro',
      "Premere 'INVIO' per confermare l'input oppure 'ESC' per annullare",
      '{0}/{1}',
      'Digitare per ridurre il numero di risultati.',
      'Usato nel contesto della selezione rapida. Se si modifica un tasto di scelta rapida per questo comando, \xE8 necessario modificare anche tutti gli altri tasti di scelta rapida (varianti del modificatore) di questo comando.',
      "Se \xE8 attiva la modalit\xE0 di accesso rapido, si passer\xE0 all'elemento successivo. Se non \xE8 attiva la modalit\xE0 di accesso rapido, si passer\xE0 al separatore successivo.",
      "Se \xE8 attiva la modalit\xE0 di accesso rapido, si passer\xE0 all'elemento precedente. Se non \xE8 attiva la modalit\xE0 di accesso rapido, si passer\xE0 al separatore precedente.",
      'Attivare/Disattivare tutte le caselle di controllo',
      '{0} risultati',
      '{0} selezionati',
      'OK',
      'Personalizzato',
      'Indietro ({0})',
      'Indietro',
      'Input rapido',
      "Fare clic per eseguire il comando '{0}'",
      'Colore primo piano generale. Questo colore viene usato solo se non \xE8 sostituito da quello di un componente.',
      'Primo piano generale per gli elementi disabilitati. Questo colore viene usato solo e non \xE8 sostituito da quello di un componente.',
      'Colore primo piano globale per i messaggi di errore. Questo colore viene usato solo se non \xE8 sostituito da quello di un componente.',
      "Colore primo piano del testo che fornisce informazioni aggiuntive, ad esempio per un'etichetta di testo.",
      'Colore predefinito per le icone nel workbench.',
      'Colore del bordo globale per gli elementi evidenziati. Questo colore viene usato solo se non \xE8 sostituito da quello di un componente.',
      'Un bordo supplementare attorno agli elementi per contrastarli maggiormente rispetto agli altri.',
      'Un bordo supplementare intorno agli elementi attivi per contrastarli maggiormente rispetto agli altri.',
      "Il colore di sfondo delle selezioni di testo in workbench (ad esempio per i campi di input o aree di testo). Si noti che questo non si applica alle selezioni all'interno dell'editor.",
      'Colore primo piano dei link nel testo.',
      'Colore primo piano per i collegamenti nel testo quando vengono selezionati o al passaggio del mouse.',
      'Colore dei separatori di testo.',
      'Colore primo piano dei segmenti di testo preformattato.',
      'Colore di sfondo dei segmenti di testo preformattato.',
      'Colore di sfondo per le citazioni nel testo.',
      'Colore del bordo per le citazioni nel testo.',
      'Colore di sfondo per i blocchi di codice nel testo.',
      'Colore primo piano usato nei grafici.',
      'Colore usato per le linee orizzontali nei grafici.',
      'Colore rosso usato nelle visualizzazioni grafico.',
      'Colore blu usato nelle visualizzazioni grafico.',
      'Colore giallo usato nelle visualizzazioni grafico.',
      'Colore arancione usato nelle visualizzazioni grafico.',
      'Colore verde usato nelle visualizzazioni grafico.',
      'Colore viola usato nelle visualizzazioni grafico.',
      "Colore di sfondo dell'editor.",
      "Colore primo piano predefinito dell'editor.",
      "Colore di sfondo della barra di scorrimento permanente nell'editor.",
      "Colore di sfondo dello scorrimento permanente al passaggio del mouse nell'editor",
      'Colore del bordo dello scorrimento permanente nell\u2019editor',
      ' Colore ombreggiatura dello scorrimento permanente nell\u2019editor',
      "Colore di sfondo dei widget dell'editor, ad esempio Trova/Sostituisci.",
      "Colore primo piano dei widget dell'editor, ad esempio Trova/Sostituisci.",
      "Colore del bordo dei widget dell'editor. Il colore viene usato solo se il widget sceglie di avere un bordo e se il colore non \xE8 sottoposto a override da un widget.",
      "Colore del bordo della barra di ridimensionamento dei widget dell'editor. Il colore viene usato solo se il widget sceglie di avere un bordo di ridimensionamento e se il colore non \xE8 sostituito da quello di un widget.",
      "Colore di sfondo del testo dell'errore nell'editor. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      "Colore primo piano degli indicatori di errore nell'editor.",
      "Se impostato, colore delle doppie sottolineature per gli errori nell'editor.",
      "Colore di sfondo del testo dell'avviso nell'editor. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      "Colore primo piano degli indicatori di avviso nell'editor.",
      "Se impostato, colore delle doppie sottolineature per gli avvisi nell'editor.",
      "Colore di sfondo del testo delle informazioni nell'editor. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      "Colore primo piano degli indicatori di informazioni nell'editor.",
      "Se impostato, colore delle doppie sottolineature per i messaggi informativi nell'editor.",
      "Colore primo piano degli indicatori di suggerimento nell'editor.",
      "Se impostato, colore delle doppie sottolineature per i suggerimenti nell'editor.",
      'Colore dei collegamenti attivi.',
      "Colore della selezione dell'editor.",
      'Colore del testo selezionato per il contrasto elevato.',
      'Colore della selezione in un editor inattivo. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore delle aree con lo stesso contenuto della selezione. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore del bordo delle regioni con lo stesso contenuto della selezione.',
      'Colore della corrispondenza di ricerca corrente.',
      'Colore del testo della corrispondenza di ricerca corrente.',
      'Colore degli altri risultati della ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore primo piano delle altre corrispondenze di ricerca.',
      "Colore dell'intervallo di limite della ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      'Colore del bordo della corrispondenza della ricerca corrente.',
      'Colore del bordo delle altre corrispondenze della ricerca.',
      "Colore del bordo dell'intervallo che limita la ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      "Evidenziazione sotto la parola per cui \xE8 visualizzata un'area sensibile al passaggio del mouse. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      "Colore di sfondo dell'area sensibile al passaggio del mouse dell'editor.",
      "Colore primo piano dell'area sensibile al passaggio del mouse dell'editor.",
      "Colore del bordo dell'area sensibile al passaggio del mouse dell'editor.",
      "Colore di sfondo della barra di stato sensibile al passaggio del mouse dell'editor.",
      'Colore primo piano dei suggerimenti inline',
      'Colore di sfondo dei suggerimenti inline',
      'Colore primo piano dei suggerimenti inline per i tipi',
      'Colore di sfondo dei suggerimenti inline per i tipi',
      'Colore primo piano dei suggerimenti inline per i parametri',
      'Colore di sfondo dei suggerimenti inline per i parametri',
      "Colore usato per l'icona delle azioni con lampadina.",
      "Colore usato per l'icona delle azioni di correzione automatica con lampadina.",
      "Colore usato per l'icona dell'intelligenza artificiale con lampadina.",
      "Colore di sfondo dell'evidenziazione della tabulazione di un frammento.",
      "Colore del bordo dell'evidenziazione della tabulazione di un frammento.",
      "Colore di sfondo dell'evidenziazione della tabulazione finale di un frammento.",
      "Colore del bordo dell'evidenziazione della tabulazione finale di un frammento.",
      'Colore di sfondo per il testo che \xE8 stato inserito. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore di sfondo per il testo che \xE8 stato rimosso. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore di sfondo per le righe che sono state inserite. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore di sfondo per le righe che sono state rimosse. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore di sfondo per il margine in cui sono state inserite le righe.',
      'Colore di sfondo per il margine in cui sono state rimosse le righe.',
      'Primo piano del righello delle annotazioni delle differenze per il contenuto inserito.',
      'Primo piano del righello delle annotazioni delle differenze per il contenuto rimosso.',
      'Colore del contorno del testo che \xE8 stato inserito.',
      'Colore del contorno del testo che \xE8 stato rimosso.',
      'Colore del bordo tra due editor di testo.',
      "Colore del riempimento diagonale dell'editor diff. Il riempimento diagonale viene usato nelle visualizzazioni diff affiancate.",
      "Colore di sfondo dei blocchi non modificati nell'editor diff.",
      "Colore di primo piano dei blocchi non modificati nell'editor diff.",
      "Colore di sfondo del codice non modificato nell'editor diff.",
      "Colore ombreggiatura dei widget, ad es. Trova/Sostituisci all'interno dell'editor.",
      "Colore del bordo dei widget, ad es. Trova/Sostituisci all'interno dell'editor.",
      'Sfondo della barra degli strumenti al passaggio del mouse sulle azioni',
      'Contorno della barra degli strumenti al passaggio del mouse sulle azioni',
      'Sfondo della barra degli strumenti quando si tiene premuto il mouse sulle azioni',
      'Colore degli elementi di navigazione in evidenza.',
      'Colore di sfondo degli elementi di navigazione.',
      'Colore degli elementi di navigazione in evidenza.',
      'Colore degli elementi di navigazione selezionati.',
      'Colore di sfondo del controllo di selezione elementi di navigazione.',
      "Sfondo dell'intestazione delle modifiche correnti nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      'Sfondo del contenuto delle modifiche correnti nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      "Sfondo dell'intestazione delle modifiche in ingresso nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      'Sfondo del contenuto delle modifiche in ingresso nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      "Sfondo dell'intestazione del predecessore comune nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.",
      'Sfondo del contenuto del predecessore comune nei conflitti di merge inline. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore del bordo nelle intestazioni e sulla barra di divisione di conflitti di merge in linea.',
      'Colore primo piano del righello delle annotazioni delle modifiche correnti per i conflitti di merge inline.',
      'Colore primo piano del righello delle annotazioni delle modifiche in ingresso per i conflitti di merge inline.',
      'Colore primo piano del righello delle annotazioni del predecessore comune per i conflitti di merge inline.',
      'Colore del marcatore del righello delle annotazioni per la ricerca di corrispondenze. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      'Colore del marcatore del righello delle annotazioni per le evidenziazioni delle selezioni. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.',
      "Colore usato per l'icona di errore dei problemi.",
      "Colore usato per l'icona di avviso dei problemi.",
      "Colore usato per l'icona informazioni dei problemi.",
      'Sfondo della casella di input.',
      'Primo piano della casella di input.',
      'Bordo della casella di input.',
      'Colore del bordo di opzioni attivate nei campi di input.',
      'Colore di sfondo di opzioni attivate nei campi di input.',
      'Colore di sfondo al passaggio del mouse delle opzioni nei campi di input.',
      'Colore primo piano di opzioni attivate nei campi di input.',
      'Colore primo piano di casella di input per il testo segnaposto.',
      "Colore di sfondo di convalida dell'input di tipo Informazione.",
      "Colore primo piano di convalida dell'input di tipo Informazione.",
      "Colore del bordo della convalida dell'input di tipo Informazione.",
      "Colore di sfondo di convalida dell'input di tipo Avviso.",
      "Colore primo piano di convalida dell'input di tipo Avviso.",
      "Colore del bordo della convalida dell'input di tipo Avviso.",
      "Colore di sfondo di convalida dell'input di tipo Errore.",
      "Colore primo piano di convalida dell'input di tipo Errore.",
      "Colore del bordo della convalida dell'input di tipo Errore.",
      "Sfondo dell'elenco a discesa.",
      "Sfondo dell'elenco a discesa.",
      "Primo piano dell'elenco a discesa.",
      "Bordo dell'elenco a discesa.",
      'Colore primo piano del pulsante.',
      'Colore del separatore pulsante.',
      'Colore di sfondo del pulsante.',
      'Colore di sfondo del pulsante al passaggio del mouse.',
      'Colore del bordo del pulsante.',
      'Colore primo piano secondario del pulsante.',
      'Colore di sfondo secondario del pulsante.',
      'Colore di sfondo secondario del pulsante al passaggio del mouse.',
      'Colore in primo piano del pulsante di opzione attivo.',
      'Colore di sfondo del pulsante di opzione attivo.',
      'Colore del bordo del pulsante di opzione attivo.',
      'Colore in primo piano del pulsante di opzione inattivo.',
      'Colore di sfondo del pulsante di opzione inattivo.',
      'Colore del bordo del pulsante di opzione inattivo.',
      'Colore di sfondo del pulsante di opzione inattivo/attivo al passaggio del mouse.',
      'Colore di sfondo del widget della casella di controllo.',
      "Colore di sfondo del widget della casella di controllo quando \xE8 selezionato l'elemento in cui si trova.",
      'Colore primo piano del widget della casella di controllo.',
      'Colore del bordo del widget della casella di controllo.',
      "Colore del bordo del widget della casella di controllo quando \xE8 selezionato l'elemento in cui si trova.",
      "Colore di sfondo dell'etichetta del tasto di scelta rapida. L'etichetta del tasto di scelta rapida viene usata per rappresentare una scelta rapida da tastiera.",
      "Colore primo piano dell'etichetta del tasto di scelta rapida. L'etichetta del tasto di scelta rapida viene usata per rappresentare una scelta rapida da tastiera.",
      "Colore del bordo dell'etichetta del tasto di scelta rapida. L'etichetta del tasto di scelta rapida viene usata per rappresentare una scelta rapida da tastiera.",
      "Colore inferiore del bordo dell'etichetta del tasto di scelta rapida. L'etichetta del tasto di scelta rapida viene usata per rappresentare una scelta rapida da tastiera.",
      "Colore di sfondo dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero \xE8 attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore primo piano dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero \xE8 attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore del contorno dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero \xE8 attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore del contorno dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero \xE8 attivo e selezionato. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore di sfondo dell'elenco/albero per l'elemento selezionato quando l'elenco/albero \xE8 attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore primo piano dell'elenco/albero per l'elemento selezionato quando l'elenco/albero \xE8 attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore primo piano dell\u2019icona dell'elenco/albero per l'elemento selezionato quando l'elenco/albero \xE8 attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore di sfondo dell'elenco/albero per l'elemento selezionato quando l'elenco/albero \xE8 inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore primo piano dell'elenco/albero per l'elemento selezionato quando l'elenco/albero \xE8 inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore primo piano dell\u2019icona dell'elenco/albero per l'elemento selezionato quando l'elenco/albero \xE8 inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Colore di sfondo dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero \xE8 inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, uno inattivo no.",
      "Colore del contorno dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero \xE8 inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.",
      "Sfondo dell'elenco/albero al passaggio del mouse sugli elementi.",
      "Primo piano dell'elenco/albero al passaggio del mouse sugli elementi.",
      "Sfondo dell'elenco/albero durante il trascinamento degli elementi su altri elementi quando si usa il mouse.",
      'Il colore del bordo del trascinamento dell\u2019elenco/albero durante lo spostamento di elementi quando si usa il mouse.',
      "Colore primo piano Elenco/Struttura ad albero delle occorrenze trovate durante la ricerca nell'Elenco/Struttura ad albero.",
      "Colore primo piano Elenco/Struttura ad albero delle occorrenze trovate in elementi con lo stato attivo durante la ricerca nell'Elenco/Struttura ad albero.",
      "Colore primo piano dell'elenco/albero delle occorrenze trovate durante la ricerca nell'elenco/albero.",
      'Colore primo piano delle voci di elenco contenenti errori.',
      'Colore primo piano delle voci di elenco contenenti avvisi.',
      'Colore di sfondo del widget del filtro per tipo in elenchi e alberi.',
      'Colore del contorno del widget del filtro per tipo in elenchi e alberi.',
      'Colore del contorno del widget del filtro per tipo in elenchi e alberi quando non sono presenti corrispondenze.',
      'Colore ombreggiatura del widget del filtro sul tipo negli elenchi e alberi.',
      'Colore di sfondo della corrispondenza filtrata.',
      'Colore del bordo della corrispondenza filtrata.',
      "Colore primo piano dell'elenco/albero per gli elementi non evidenziati.",
      "Colore del tratto dell'albero per le guide per i rientri.",
      "Colore del tratto dell'albero per le guide di rientro non attive.",
      'Colore del bordo della tabella tra le colonne.',
      'Colore di sfondo per le righe di tabella dispari.',
      "Colore delle sfondo dell'elenco delle azioni.",
      "Colore in primo piano dell'elenco delle azioni.",
      "Colore in primo dell\u2019elenco delle azioni per l'elemento con stato attivo.",
      "Colore dello sfondo dell\u2019elenco di azioni per l'elemento con stato attivo.",
      'Colore del bordo del menu.',
      'Colore primo piano delle voci di menu.',
      'Colore di sfondo delle voci di menu.',
      'Colore primo piano della voce di menu selezionata nei menu.',
      'Colore di sfondo della voce di menu selezionata nei menu.',
      'Colore del bordo della voce di menu selezionata nei menu.',
      'Colore di un elemento separatore delle voci di menu.',
      'Colore del marcatore della minimappa per la ricerca delle corrispondenze.',
      "Colore del marcatore della minimappa per le selezioni ripetute dell'editor.",
      "Colore del marcatore della minimappa per la selezione dell'editor.",
      'Colore del marcatore della minimappa per le informazioni.',
      'Colore del marcatore della minimappa per gli avvisi.',
      'Colore del marcatore della minimappa per gli errori.',
      'Colore di sfondo della minimappa.',
      'Opacit\xE0 degli elementi in primo piano di cui \xE8 stato eseguito il rendering nella minimappa. Ad esempio, con "#000000c0" il rendering degli elementi verr\xE0 eseguito con il 75% di opacit\xE0.',
      'Colore di sfondo del dispositivo di scorrimento della minimappa.',
      'Colore di sfondo del dispositivo di scorrimento della minimappa al passaggio del mouse.',
      'Colore di sfondo del dispositivo di scorrimento della minimappa quando si fa clic con il mouse.',
      'Colore dei bordi di ridimensionamento attivi.',
      'Colore di sfondo del badge. I badge sono piccole etichette informative, ad esempio per mostrare il conteggio dei risultati della ricerca.',
      'Colore primo piano del badge. I badge sono piccole etichette informative, ad esempio per mostrare il conteggio dei risultati di una ricerca.',
      'Ombra della barra di scorrimento per indicare lo scorrimento della visualizzazione.',
      'Colore di sfondo del cursore della barra di scorrimento.',
      'Colore di sfondo del cursore della barra di scorrimento al passaggio del mouse.',
      'Colore di sfondo del cursore della barra di scorrimento quando si fa clic con il mouse.',
      "Colore di sfondo dell'indicatore di stato che pu\xF2 essere mostrato per operazioni a esecuzione prolungata.",
      'Colore di sfondo di Selezione rapida. Il widget Selezione rapida \xE8 il contenitore di selezioni quali il riquadro comandi.',
      'Colore primo piano di Selezione rapida. Il widget Selezione rapida \xE8 il contenitore di selezioni quali il riquadro comandi.',
      'Colore di sfondo del titolo di Selezione rapida. Il widget Selezione rapida \xE8 il contenitore di selezioni quali il riquadro comandi.',
      'Colore di selezione rapida per il raggruppamento delle etichette.',
      'Colore di selezione rapida per il raggruppamento dei bordi.',
      'In alternativa, usare quickInputList.focusBackground',
      "Colore primo piano di Selezione rapida per l'elemento con lo stato attivo.",
      "Colore primo piano dell\u2019icona di Selezione rapida per l'elemento con lo stato attivo.",
      "Colore di sfondo di Selezione rapida per l'elemento con lo stato attivo.",
      'Colore del testo nel messaggio di completamento del viewlet di ricerca.',
      "Colore delle corrispondenze query dell'editor della ricerca.",
      "Colore del bordo delle corrispondenze query dell'editor della ricerca.",
      'Questo colore deve essere trasparente, altrimenti oscurer\xE0 il contenuto',
      'Usare il colore predefinito.',
      'ID del tipo di carattere da usare. Se non \xE8 impostato, viene usato il tipo di carattere definito per primo.',
      'Tipo di carattere associato alla definizione di icona.',
      "Icona dell'azione di chiusura nei widget.",
      'Icona per la posizione di Vai a editor precedente.',
      'Icona per la posizione di Vai a editor successivo.',
      'I file seguenti sono stati chiusi e modificati nel disco: {0}.',
      'I file seguenti sono stati modificati in modo incompatibile: {0}.',
      "Non \xE8 stato possibile annullare '{0}' in tutti i file. {1}",
      "Non \xE8 stato possibile annullare '{0}' in tutti i file. {1}",
      "Non \xE8 stato possibile annullare '{0}' in tutti i file perch\xE9 sono state apportate modifiche a {1}",
      "Non \xE8 stato possibile annullare '{0}' su tutti i file perch\xE9 \xE8 gi\xE0 in esecuzione un'operazione di annullamento o ripetizione su {1}",
      "Non \xE8 stato possibile annullare '{0}' su tutti i file perch\xE9 nel frattempo \xE8 stata eseguita un'operazione di annullamento o ripetizione",
      "Annullare '{0}' in tutti i file?",
      '&&Annulla in {0} file',
      'Annulla questo &&file',
      "Non \xE8 stato possibile annullare '{0}' perch\xE9 \xE8 gi\xE0 in esecuzione un'operazione di annullamento o ripetizione.",
      "Annullare '{0}'?",
      '&&S\xEC',
      'No',
      "Non \xE8 stato possibile ripetere '{0}' in tutti i file. {1}",
      "Non \xE8 stato possibile ripetere '{0}' in tutti i file. {1}",
      "Non \xE8 stato possibile ripetere '{0}' in tutti i file perch\xE9 sono state apportate modifiche a {1}",
      "Non \xE8 stato possibile ripetere l'operazione '{0}' su tutti i file perch\xE9 \xE8 gi\xE0 in esecuzione un'operazione di annullamento o ripetizione sull'elenco di file {1}",
      "Non \xE8 stato possibile ripetere '{0}' su tutti i file perch\xE9 nel frattempo \xE8 stata eseguita un'operazione di annullamento o ripetizione",
      "Non \xE8 stato possibile ripetere '{0}' perch\xE9 \xE8 gi\xE0 in esecuzione un'operazione di annullamento o ripetizione.",
      'Area di lavoro del codice',
    ]),
    (globalThis._VSCODE_NLS_LANGUAGE = 'it')

  //# sourceMappingURL=../min-maps/nls.messages.it.js.map
})
