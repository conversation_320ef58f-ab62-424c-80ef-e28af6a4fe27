/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/
define('vs/basic-languages/pascaligo/pascaligo', ['require', 'require'], (require) => {
  'use strict'
  var moduleExports = (() => {
    var s = Object.defineProperty
    var i = Object.getOwnPropertyDescriptor
    var a = Object.getOwnPropertyNames
    var l = Object.prototype.hasOwnProperty
    var c = (o, e) => {
        for (var t in e) s(o, t, { get: e[t], enumerable: !0 })
      },
      m = (o, e, t, r) => {
        if ((e && typeof e == 'object') || typeof e == 'function')
          for (let n of a(e))
            !l.call(o, n) &&
              n !== t &&
              s(o, n, { get: () => e[n], enumerable: !(r = i(e, n)) || r.enumerable })
        return o
      }
    var p = (o) => m(s({}, '__esModule', { value: !0 }), o)
    var u = {}
    c(u, { conf: () => d, language: () => g })
    var d = {
        comments: { lineComment: '//', blockComment: ['(*', '*)'] },
        brackets: [
          ['{', '}'],
          ['[', ']'],
          ['(', ')'],
          ['<', '>'],
        ],
        autoClosingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '<', close: '>' },
          { open: "'", close: "'" },
        ],
        surroundingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '<', close: '>' },
          { open: "'", close: "'" },
        ],
      },
      g = {
        defaultToken: '',
        tokenPostfix: '.pascaligo',
        ignoreCase: !0,
        brackets: [
          { open: '{', close: '}', token: 'delimiter.curly' },
          { open: '[', close: ']', token: 'delimiter.square' },
          { open: '(', close: ')', token: 'delimiter.parenthesis' },
          { open: '<', close: '>', token: 'delimiter.angle' },
        ],
        keywords: [
          'begin',
          'block',
          'case',
          'const',
          'else',
          'end',
          'fail',
          'for',
          'from',
          'function',
          'if',
          'is',
          'nil',
          'of',
          'remove',
          'return',
          'skip',
          'then',
          'type',
          'var',
          'while',
          'with',
          'option',
          'None',
          'transaction',
        ],
        typeKeywords: [
          'bool',
          'int',
          'list',
          'map',
          'nat',
          'record',
          'string',
          'unit',
          'address',
          'map',
          'mtz',
          'xtz',
        ],
        operators: [
          '=',
          '>',
          '<',
          '<=',
          '>=',
          '<>',
          ':',
          ':=',
          'and',
          'mod',
          'or',
          '+',
          '-',
          '*',
          '/',
          '@',
          '&',
          '^',
          '%',
        ],
        symbols: /[=><:@\^&|+\-*\/\^%]+/,
        tokenizer: {
          root: [
            [
              /[a-zA-Z_][\w]*/,
              { cases: { '@keywords': { token: 'keyword.$0' }, '@default': 'identifier' } },
            ],
            { include: '@whitespace' },
            [/[{}()\[\]]/, '@brackets'],
            [/[<>](?!@symbols)/, '@brackets'],
            [/@symbols/, { cases: { '@operators': 'delimiter', '@default': '' } }],
            [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
            [/\$[0-9a-fA-F]{1,16}/, 'number.hex'],
            [/\d+/, 'number'],
            [/[;,.]/, 'delimiter'],
            [/'([^'\\]|\\.)*$/, 'string.invalid'],
            [/'/, 'string', '@string'],
            [/'[^\\']'/, 'string'],
            [/'/, 'string.invalid'],
            [/\#\d+/, 'string'],
          ],
          comment: [
            [/[^\(\*]+/, 'comment'],
            [/\*\)/, 'comment', '@pop'],
            [/\(\*/, 'comment'],
          ],
          string: [
            [/[^\\']+/, 'string'],
            [/\\./, 'string.escape.invalid'],
            [/'/, { token: 'string.quote', bracket: '@close', next: '@pop' }],
          ],
          whitespace: [
            [/[ \t\r\n]+/, 'white'],
            [/\(\*/, 'comment', '@comment'],
            [/\/\/.*$/, 'comment'],
          ],
        },
      }
    return p(u)
  })()
  return moduleExports
})
