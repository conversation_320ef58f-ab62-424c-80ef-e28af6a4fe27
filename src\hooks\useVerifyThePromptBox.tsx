/*
@description: 验证提示框
@author: 判断选中的表格的每一行数据
1. 有数据盘无EIP的情况下提示【是否组合回收数据盘】；
2. 无数据盘有EIP的情况下提示【是否组合回收弹性公网】；
3. 有数据盘有EIP的情况下提示【是否组合回收数据盘和弹性公网】；
4. 无数据盘无EIP的情况下不提示，默认传否。
eip有值是EIP  ,dataDisk 有值是数据盘
*/
import { ElMessageBox } from 'element-plus'

// 定义提示信息类型
interface PromptMessage {
  hasDataDisk: boolean
  hasEIP: boolean
  message: string
}

// 定义行数据类型
interface RowData {
  id?: string
  eip?: any
  dataDisk?: any
  [key: string]: any
}

// 定义返回的数据结构
type item = {
  goodsId: string // 每一行的id集合
  syncRecovery: boolean // 确认为true 取消为false
}

type goodsItems = item[]

/**
 * 验证提示框
 * @param rows 选中的表格行数据数组
 * @param customMessages 自定义提示信息数组，可选
 * @returns Promise<goodsItems> 返回处理后的数据结构
 */
const useVerifyThePromptBox = async (
  rows: RowData[] | RowData,
  customMessages?: PromptMessage[],
  isDg?: boolean,
): Promise<goodsItems> => {
  // 默认提示信息
  const defaultMessages: PromptMessage[] = [
    { hasDataDisk: true, hasEIP: false, message: '是否组合回收数据盘？' },
    { hasDataDisk: false, hasEIP: true, message: '是否组合回收弹性公网？' },
    { hasDataDisk: true, hasEIP: true, message: '是否组合回收数据盘和弹性公网？' },
  ]

  // 使用自定义提示信息或默认提示信息
  const messages = customMessages || defaultMessages

  // 将单个行对象转换为数组
  const rowArray = Array.isArray(rows) ? rows : [rows]

  // 如果没有选中行，直接返回空数组
  if (rowArray.length === 0) {
    return Promise.resolve([])
  }

  // 统计不同情况的行数
  const stats = {
    hasDataDiskOnly: 0,
    hasEIPOnly: 0,
    hasBoth: 0,
    hasNone: 0,
  }

  // 遍历所有行，统计不同情况
  rowArray.forEach((row) => {
    const hasDataDisk = !!row.dataDisk
    const hasEIP = !!row.eip

    if (hasDataDisk && hasEIP) {
      stats.hasBoth++
    } else if (hasDataDisk) {
      stats.hasDataDiskOnly++
    } else if (hasEIP) {
      stats.hasEIPOnly++
    } else {
      stats.hasNone++
    }
  })

  // 根据统计结果确定提示信息
  let message = ''

  if (stats.hasBoth > 0) {
    // 有数据盘有EIP的情况
    const promptInfo = messages.find((m) => m.hasDataDisk && m.hasEIP)
    message = promptInfo?.message || '是否组合回收数据盘和弹性公网？'
  } else if (stats.hasDataDiskOnly > 0) {
    // 有数据盘无EIP的情况
    const promptInfo = messages.find((m) => m.hasDataDisk && !m.hasEIP)
    message = promptInfo?.message || '是否组合回收数据盘？'
  } else if (stats.hasEIPOnly > 0) {
    // 无数据盘有EIP的情况
    const promptInfo = messages.find((m) => !m.hasDataDisk && m.hasEIP)
    message = promptInfo?.message || '是否组合回收弹性公网？'
  } else {
    // 无数据盘无EIP的情况，不提示，返回所有行的ID，syncRecovery为false
    const result: goodsItems = rowArray.map((row) => ({
      goodsId: row.id || '',
      syncRecovery: false,
    }))
    return Promise.resolve(result)
  }

  // 显示确认对话框
  return ElMessageBox.confirm(
    message +
      ` ${isDg ? '(当产品绑定弹性公网IP类型为IPv6时将必定一起回收弹性公网)' : '(当产品的所属云为平台云的时候无论选择是或者否都为组合回收；当产品绑定弹性公网IP类型为IPv6时将必定一起回收弹性公网)'}`,
    '提示',
    {
      type: 'warning',
      confirmButtonText: '是',
      cancelButtonText: '否',
      distinguishCancelAndClose: true,
      showClose: true,
    },
  )
    .then(() => {
      // 用户点击确认，返回所有行的ID
      const result: goodsItems = rowArray.map((row) => {
        return {
          goodsId: row.id || '',
          syncRecovery: true,
        }
      })
      return result
    })
    .catch((action) => {
      // 点击关闭图标时，返回空数组
      if (action === 'close') {
        return []
      }
      // 用户点击取消按钮，返回所有行的ID，syncRecovery为false
      const result: goodsItems = rowArray.map((row) => ({
        goodsId: row.id || '',
        syncRecovery: row.domainCode == 'plf_prov_nwc_zj_plf' ? true : false,
      }))
      return result
    })
}

export { useVerifyThePromptBox, type goodsItems, type item }
