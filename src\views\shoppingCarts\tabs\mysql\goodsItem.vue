<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="goods.orderJson"
  >
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>
    <template #sysDiskSlot="{ form, item }">
      <div style="display: flex; flex-grow: 1">
        <el-select style="flex: 1" clearable v-model="form[item.key][0]">
          <el-option
            :key="option.value"
            v-for="option in item.options"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <el-input-number
          v-model="form[item.key][1]"
          v-bind="item.props"
          style="margin: 0 4px; min-width: 90px"
        >
        </el-input-number>
        GB
      </div>
    </template>
    <template #disasterRecoverySlot="{ form, item }">
      <el-radio-group style="flex-basis: auto" v-bind="item.props?.group" v-model="form[item.key]">
        <el-radio
          v-bind="item.props?.radio"
          v-for="option in item.options"
          :key="option[item.valueField || 'value']"
          :value="option[item.valueField || 'value']"
        >
          {{ option[item.labelField || 'label'] }}
        </el-radio>
      </el-radio-group>
      <div v-if="form[item.key] === '0'" class="disaster-recovery-tip">
        <el-tooltip content="容灾选“否”可能导致业务系统高可用缺失!" placement="top">
          容灾选“否”可能导致业务系统高可用缺失!
        </el-tooltip>
      </div>
    </template>
    <!-- 数据盘插槽 -->
    <template #evsSlot="{ form, item }">
      <div style="flex-grow: 0.12; display: flex; align-items: flex-start; height: 100%">
        <el-switch v-model="form[item.swithKey]" active-value="1" inactive-value="0" />
      </div>
      <div>
        <el-form-item
          class="evs-item"
          v-for="(evs, evsIndex) in form[item.key]"
          :key="evsIndex"
          :prop="item.key + '.' + evsIndex"
          :rules="evsRules"
        >
          <div class="evs-item-content">
            <el-select
              style="flex: 1"
              clearable
              :disabled="item.disabled"
              v-model="form[item.key][evsIndex][0]"
            >
              <el-option
                :key="option.value"
                v-for="option in item.options"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <el-input-number
              :disabled="item.disabled"
              v-bind="item.props"
              v-model="form[item.key][evsIndex][1]"
              style="margin: 0 4px; min-width: 90px"
            />
            <span>GB</span>
            <div class="evs-icons" v-if="form.isMountEvs == 1">
              <el-icon
                @click="handleEvsRemove(form[item.key], evsIndex)"
                v-if="form[item.key].length > 1"
              >
                <RemoveFilled />
              </el-icon>
              <el-icon
                @click="handleEvsAdd(form[item.key])"
                v-if="form[item.key].length == evsIndex + 1"
              >
                <CirclePlusFilled />
              </el-icon>
            </div>
          </div>
        </el-form-item>
      </div>
    </template>
    <template #networkIpSlot="{ form, item }">
      <el-switch
        style="flex-grow: 0.2"
        v-model="form[item.swithKey]"
        active-value="1"
        inactive-value="0"
      />
      <el-input-number v-bind="item.props || {}" v-model="form[item.key]" clearable />
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, computed, ref, watchEffect } from 'vue'
import type { IMysqlModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CirclePlusFilled, RemoveFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<IMysqlModel>
  imageOptions: any
  flavorOptions: any
}>()

function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: route.query.orderId ? true : false,
  })
}
const formModel = props.goods.orderJson
// 数据盘配置
const handleEvsRemove = (item: any, index: number) => {
  item.splice(index, 1)
}

const handleEvsAdd = (item: Array<any>) => {
  item.push(['', 20])
}

const evsDisabled = ref(false)
const netSizeDisabled = ref(false)
const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IMysqlModel>) {
  goods.ref = slFormRef
}
watchEffect(() => {
  if (formModel.isMountEvs === '0') {
    evsDisabled.value = true
    if (slFormRef.value) {
      formModel.evs = [['', 0]]
      slFormRef.value.clearValidate(['evs'])
    }
  } else {
    evsDisabled.value = false
    if (formModel.evs.length) {
      formModel.evs.forEach((evs) => {
        if (evs[1] === 0) evs[1] = 20
      })
    }
  }
})
watchEffect(() => {
  if (formModel.isBindPublicNetworkIp === '0') {
    netSizeDisabled.value = true
    if (slFormRef.value) {
      formModel.eipValue = 0
      slFormRef.value.clearValidate(['eipValue'])
    }
  } else {
    netSizeDisabled.value = false
    formModel.eipValue = formModel.eipValue || 1
  }
})
const disabledNumber = ref(false)
watchEffect(() => {
  if (formModel.deployType === 'ALONE') {
    disabledNumber.value = false
    formModel.numbers = 1
  } else {
    disabledNumber.value = true
    formModel.numbers = 2
  }
})

const validateevsTypeevsSize = (rule: any, value: any, callback: any) => {
  if (evsDisabled.value) callback()
  let error = ''
  if (!value[0] && !value[1]) {
    error = '请选择数据盘类型并输入数据盘大小'
  } else if (!value[0] && value[1]) {
    error = '请选择数据盘类型'
  } else if (value[0] && !value[1]) {
    error = '请输入数据盘大小'
  }
  error ? callback(new Error(error)) : callback()
}

const validateDiskTypeSiskSize = (rule: any, value: any, callback: any) => {
  if (!formModel.sysDisk[0]) {
    callback(new Error('请选择系统盘类型'))
  } else if (!formModel.sysDisk[1]) {
    callback(new Error('请输入系统盘大小'))
  } else {
    callback()
  }
}

const validateInstanceSpecificationMemorySize = (rule: any, value: any, callback: any) => {
  if (!formModel.ecs || formModel.ecs.length === 0) {
    callback(new Error('请选择实例规格'))
  } else {
    callback()
  }
}

const computedMin = computed(() => {
  return formModel.isMountEvs === '0' ? 0 : 20
})
const evsRules = [
  {
    validator: validateevsTypeevsSize,
    trigger: 'change',
  },
]

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '云数据库名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入云数据库名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '功能模块',
        type: 'select',
        key: 'functionalModule',
        options: getDic('functionalModule'),
        span: 8,
        rules: {
          required: true,
          message: '请选择功能模块',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '系列',
        type: 'radio',
        key: 'deployType',
        options: [
          {
            label: '单机版本',
            value: 'ALONE',
          },
          {
            label: '主备版本',
            value: 'COLONY',
          },
        ],
        span: 8,
        rules: [{ required: true, message: '请选择系列', trigger: ['blur', 'change'] }],
      },
      {
        label: '实例规格',
        type: 'cascader',
        key: 'ecs',
        options: props.flavorOptions,
        span: 8,
        required: true,
        rules: [{ validator: validateInstanceSpecificationMemorySize, trigger: 'change' }],
      },
      {
        label: '版本',
        type: 'cascader',
        key: 'imageOs',
        options: props.imageOptions,
        span: 8,
        rules: [{ required: true, message: '请选择版本', trigger: ['blur', 'change'] }],
      },
      {
        label: '系统盘',
        type: 'slot',
        slotName: 'sysDiskSlot',
        key: 'sysDisk',
        options: getDic('sysDisk'),
        props: {
          min: 40,
          max: 500,
          step: 1,
        },
        span: 8,
        required: true,
        rules: [{ validator: validateDiskTypeSiskSize, trigger: ['blur', 'change'] }],
      },
      {
        label: '是否容灾',
        type: 'slot',
        slotName: 'disasterRecoverySlot',
        key: 'disasterRecovery',
        options: getDic('trueOrFalse'),
        span: 8,
        rules: [{ required: true, message: '请选择是否容灾', trigger: ['blur', 'change'] }],
      },
      {
        label: '是否绑定公网IP',
        type: 'slot',
        slotName: 'networkIpSlot',
        swithKey: 'isBindPublicNetworkIp',
        key: 'eipValue',
        suffix: 'M',
        props: {
          min: 0,
          step: 1,
          disabled: netSizeDisabled,
        },
        rules: [
          {
            validator: validateEmpty,
            disabled: netSizeDisabled,
            message: '请输入带宽大小',
            trigger: ['blur', 'change'],
          },
        ],
        span: 8,
        required: true,
      },
      {
        label: '开通数量',
        type: 'inputNumber',
        key: 'numbers',
        hidden: true,
        props: {
          min: 1,
          step: 1,
          max: 100,
          disabled: disabledNumber,
        },
        required: true,
        span: 8,
        rules: [
          {
            validator: validateEmpty,
            message: '请选择开通数量',
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
      {
        label: '网络平面',
        type: 'select',
        key: 'planeValue',
        span: 8,
        props: {
          select: {
            multiple: true,
            collapseTags: true,
            collapseTagsTooltip: true,
          },
        },
        options: getDic('plane'),
        rules: [{ required: true, message: '请选择网络平面', trigger: ['blur', 'change'] }],
      },
      {
        label: '是否挂载数据盘',
        type: 'slot',
        slotName: 'evsSlot',
        disabled: evsDisabled,
        swithKey: 'isMountEvs',
        key: 'evs',
        options: getDic('evs'),
        span: 10,
        required: true,
        props: {
          min: computedMin,
          max: 2048,
          step: 1,
        },
      },
    ],
  },
])
</script>
<style scoped>
.disaster-recovery-tip {
  margin-left: 8px;
  color: var(--el-color-warning);
  flex-basis: auto;
  position: absolute;
  top: 18px;
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
