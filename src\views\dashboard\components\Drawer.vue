<template>
  <el-drawer body-class="drawer-body-dashboard" :with-header="false" size="600px">
    <div>
      <Title :title="typeName + '详情'" />
      <!-- 基本信息区域 -->
      <div class="info-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">所属地区：</span>
            <span class="info-value">{{ areaCode }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">所属平台：</span>
            <span class="info-value">{{ regionName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">时间：</span>
            <div class="time-controls">
              <el-date-picker
                @change="handleDateChange"
                v-model="selectedDate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                size="small"
                :disabled-date="disabledDate"
                :shortcuts="timeShortcuts"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <!-- 算力利用率图表 -->
        <div class="chart-container">
          <div class="chart-header">
            <span class="chart-title">
              <span class="chart-icon chart-icon-blue"></span>算力利用率
            </span>
          </div>
          <div ref="powerChart" class="chart-content"></div>
        </div>
        <!-- 算力利用率图表2 -->
        <div class="chart-container">
          <div class="chart-header">
            <span class="chart-title">
              <span class="chart-icon chart-icon-orange"></span>显存利用率
            </span>
          </div>
          <div ref="powerChart2" class="chart-content"></div>
        </div>
      </div>
      <div
        class="title-export"
        style="display: flex; justify-content: space-between; align-items: center; margin-top: 12px"
      >
        <Title :title="typeName + '列表'" style="flex: 1" />
        <div class="select-input">
          <el-select
            class="select-item"
            v-if="props.type === 'physics'"
            v-model="deptName"
            placeholder="请选择归属部门"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in deptOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
          <el-select
            class="select-item"
            v-show="props.type === 'physics'"
            v-model="businessSystemName"
            placeholder="请选择所属业务"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in businessOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
              @change="loadTableData"
            />
          </el-select>
          <el-select
            class="select-item"
            v-show="props.type === 'virtual'"
            v-model="businessSystemName"
            placeholder="请选择所属业务"
            size="small"
            clearable
            filterable
          >
            <el-option
              v-for="item in virtualBusinessOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </div>
        <div style="margin-right: 20px">
          <el-button @click="handleExport" type="primary" size="small"> 导出 </el-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <div class="table-section">
        <el-table
          :data="tableData"
          class="power-table"
          size="small"
          v-loading="loading"
          element-loading-text="加载中..."
          highlight-current-row
          @current-change="handleCurrentChange"
          ref="tableRef"
        >
          <el-table-column show-overflow-tooltip prop="datacenter" label="所属平台" width="260" />
          <el-table-column
            v-if="props.type === 'physics'"
            prop="deptName"
            label="归属部门"
            width="180"
          />
          <el-table-column show-overflow-tooltip prop="business" label="所属业务" width="180" />
          <el-table-column
            v-if="props.type === 'physics'"
            show-overflow-tooltip
            prop="subModelName"
            label="型号"
            width="180"
          />
          <el-table-column prop="powerUtilization" label="算力利用率（%）" width="180" />
          <el-table-column prop="memoryUtilization" label="显存利用率（%）" width="180" />
          <el-table-column prop="memorySize" label="显存大小（G）" width="180" />
          <el-table-column
            v-if="props.type === 'physics'"
            prop="temperature"
            label="温度（℃）"
            width="180"
          />
          <el-table-column prop="taskCount" label="任务数" />
          <el-table-column prop="runStatus" label="运行状态" width="180" />
          <el-table-column prop="collectStatus" label="底层采集状态" width="180" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            layout="total,prev, pager, next, sizes, jumper"
            :page-sizes="[5, 10, 20, 30]"
            size="small"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import Title from './Title.vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import {
  pageDeviceGpuInfoListApi,
  pageDeviceVirtualGpuInfoListApi,
  deviceMetricsListApi,
  exportDeviceGpuInfoApi,
  exportDeviceVirtualGpuInfoApi,
  deviceGpuDicApi,
} from '@/api/modules/zsMap'
import { ElMessage } from 'element-plus'
import { useDownload } from '@/hooks/useDownload'

let chartInstance1: ECharts | null = null
let chartInstance2: ECharts | null = null
const areaCode = ref<string>('')
const regionName = ref<string>('')
const tableRef = ref<any>()
const props = defineProps({
  type: {
    type: String,
    default: 'physics', // physics 物理算力列表, virtual 切片算力列表
  },
  modelName: {
    type: String,
    default: '',
  },
})

const handleExport = () => {
  const params: any = {
    modelName: props.modelName,
    deptName: deptName.value,
    businessSystemName: businessSystemName.value,
  }
  useDownload(
    props.type === 'physics' ? exportDeviceGpuInfoApi : exportDeviceVirtualGpuInfoApi,
    `${props.modelName}_设备信息`,
    params,
    true,
    '.xlsx',
  )
}

const deviceId = ref<string>('')

const typeName = computed(() => {
  return props.type === 'physics' ? '算力' : '切片'
})
async function getDevicesMetricPercent() {
  const params: any = {
    deviceId: deviceId.value,
  }
  if (selectedDate.value.length > 0) {
    params.startTime = selectedDate.value[0]
    params.endTime = selectedDate.value[1]
  }
  const { entity }: any = await deviceMetricsListApi(params)
  const keys = Object.keys(entity)
  if (keys.length === 0) return
  const tempXAxisData = entity[keys[0]].map((ele: any) => ele.gpuTime)
  const tempMemLineData: any[] = []
  const tempGpuLineData: any[] = []
  const chartColors1 = [
    '#5470C6', // 蓝色
    '#91CC75', // 绿色
    '#FAC858', // 黄色
    '#EE6666', // 红色
    '#73C0DE', // 浅蓝色
    '#3BA272', // 深绿色
    '#FC8452', // 橙色
    '#9A60B4', // 紫色
  ]
  const chartColors2 = [
    '#FF6B6B', // 珊瑚红
    '#4ECDC4', // 青绿色
    '#45B7D1', // 天蓝色
    '#96CEB4', // 薄荷绿
    '#FFEAA7', // 浅黄色
    '#DDA0DD', // 梅花色
    '#FFB347', // 桃橙色
    '#87CEEB', // 天空蓝
  ]
  keys.forEach((key: any, index: number) => {
    tempMemLineData.push({
      city: {
        name: key,
        color: chartColors1[index],
      },
      data: entity[key].map((ele: any) => ele.gpuUtilPercent),
    })
    tempGpuLineData.push({
      city: {
        name: key,
        color: chartColors2[index],
      },
      data: entity[key].map((ele: any) => ele.memUtilpercent),
    })
  })

  if (!powerChart.value) return
  if (!powerChart2.value) return
  chartInstance1 = initChart(powerChart.value, '#5470C6', tempMemLineData, tempXAxisData)
  chartInstance2 = initChart(powerChart2.value, '#FF9800', tempGpuLineData, tempXAxisData)
}
// 定义API请求参数类型
interface DeviceGpuListParams {
  pageSize: number
  pageNum: number
  areaCode?: string
  deviceType?: string
  catalogueDomainCode?: string
  domainCode?: string
  regionId?: number
  regionCode?: string
  startTime?: string
  endTime?: string
  businessSystemId?: number
  deviceIds?: string[]
  deviceId?: string
  modelName?: string
  deptName?: string
  businessSystemName?: string
}

// 定义表格数据类型
interface TableDataItem {
  datacenter: string
  business: string
  powerUtilization: string
  memoryUtilization: string
  memorySize: string
  taskCount: number
  temperature: string
  deviceId?: string
  modelName?: string
  areaCode: string
  regionName: string
  runStatus: string
  collectStatus: string
  subModelName: string
}

// 基本数据
const selectedDate = ref<string[]>([])
const handleDateChange = (value: string[]) => {
  console.log(value)

  // 如果选择了时间范围，验证区间是否超过两个月
  if (value && value.length === 2) {
    if (!validateDateRange(value[0], value[1])) {
      // 如果验证失败，清空选择
      selectedDate.value = []
      return
    }
  }

  // 时间变化时重新加载数据
  getDevicesMetricPercent()
}

// 添加时间区间限制和快捷选项
const disabledDate = (time: Date) => {
  // 禁用未来日期
  if (time.getTime() > Date.now()) {
    return true
  }
  return false
}

// 两个月区间验证
const validateDateRange = (startDate: string, endDate: string): boolean => {
  const start = new Date(startDate)
  const end = new Date(endDate)

  // 计算两个月的毫秒数（按平均每月30天计算）
  const twoMonthsInMs = 2 * 30 * 24 * 60 * 60 * 1000

  if (end.getTime() - start.getTime() > twoMonthsInMs) {
    ElMessage.warning('时间区间不能超过两个月')
    return false
  }
  return true
}

// 时间快捷选项
const timeShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近两个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 60)
      return [start, end]
    },
  },
]

const currentPage = ref(1)
const pageSize = ref(5)
const total = ref(0)
const loading = ref(false)

// 图表实例
const powerChart = ref<HTMLElement>()
const powerChart2 = ref<HTMLElement>()

// 删除原有的静态数据生成逻辑，替换为API调用
const allTableData = ref<TableDataItem[]>([])

const deptOptions = ref<any[]>([])
const businessOptions = ref<any[]>([])
const virtualBusinessOptions = ref<any[]>([
  {
    name: '智能视频',
    id: '智能视频',
  },
])

const businessSystemName = ref<string>('')
const deptName = ref<string>('')
//下拉字典
const gitDic = async () => {
  if (props.type === 'virtual') {
    businessSystemName.value = virtualBusinessOptions.value[0].name
    return
  }
  const { entity: dept } = await deviceGpuDicApi({ type: 'dept' })
  const { entity: business } = await deviceGpuDicApi({ type: 'business' })
  deptOptions.value = dept.map((item: string) => ({ name: item, id: name }))
  businessOptions.value = business.map((item: string) => ({ name: item, id: name }))
}
gitDic()
// API调用函数
const loadTableData = async () => {
  try {
    loading.value = true

    // 构建请求参数
    const params: DeviceGpuListParams = {
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      // 根据需要设置其他参数
      modelName: props.modelName, // 浙江省编码，可根据实际情况调整
      businessSystemName: businessSystemName.value ? businessSystemName.value : undefined,
      deptName: deptName.value ? deptName.value : undefined,
    }

    const response: any =
      props.type === 'physics'
        ? await pageDeviceGpuInfoListApi(params)
        : await pageDeviceVirtualGpuInfoListApi(params)

    // 处理不同可能的响应结构
    let records: any[] = []
    let totalCount = 0

    if (response && response.entity) {
      // 如果数据在entity字段中
      if (response.entity.records) {
        records = response.entity.records
        totalCount = Number(response.entity.total) || response.entity.records.length
      } else if (Array.isArray(response.entity)) {
        records = response.entity
        totalCount = records.length
      }
    } else if (response && response.data) {
      // 如果数据在data字段中
      if (response.data.records) {
        records = response.data.records
        totalCount = Number(response.data.total) || response.data.records.length
      } else if (Array.isArray(response.data)) {
        records = response.data
        totalCount = records.length
      }
    } else if (Array.isArray(response)) {
      // 如果直接返回数组
      records = response
      totalCount = records.length
    }

    if (records && records.length > 0) {
      // 转换API数据为表格数据格式
      allTableData.value = records.map((item: any) => ({
        datacenter: item.regionName,
        business: item.businessSystemName,
        powerUtilization: item.gpuUtilPercent,
        memoryUtilization: item.memUtilpercent,
        memorySize: item.memory,
        taskCount: item.taskNum,
        temperature: item.temperature,
        deviceId: item.deviceId || item.id,
        modelName: item.modelName || item.model,
        regionName: item.regionName,
        areaCode: item.areaCode,
        deptName: item.deptName,
        runStatus: item.runStatus,
        collectStatus: item.collectStatus,
        subModelName: item.subModelName || item.modelName,
      }))

      total.value = totalCount
      tableRef.value.setCurrentRow(allTableData.value[0])
      if (allTableData.value.length > 0) {
        areaCode.value = allTableData.value[0].areaCode
        regionName.value = allTableData.value[0].regionName
      }
      console.log('成功加载数据:', allTableData.value.length, '条记录，总计:', totalCount)
    } else {
      allTableData.value = []
      total.value = 0
      areaCode.value = ''
      regionName.value = ''
      if (!powerChart.value) return
      if (!powerChart2.value) return
      chartInstance1 = initChart(powerChart.value, '#5470C6', [], [])
      chartInstance2 = initChart(powerChart2.value, '#FF9800', [], [])
      console.warn('API返回数据为空或格式异常:', response)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 计算当前页显示的数据
const tableData = computed(() => {
  // 由于我们现在直接从API获取分页数据，直接返回所有数据
  return allTableData.value
})

// 处理页码变化
const handleCurrentChange = (row: any) => {
  if (!row?.deviceId) return
  deviceId.value = row?.deviceId
  getDevicesMetricPercent()
}

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTableData() // 重新加载数据
}

// 处理页面变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTableData()
}

// 图表配置
const initChart = (chartRef: HTMLElement, color: string, dataSet?: number[], xAxisData?: any[]) => {
  const chart = echarts.init(chartRef, { renderer: 'svg' })
  if (!dataSet?.length) {
    chart.clear()
    chart.setOption({
      graphic: {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: '暂无数据',
          fontSize: 14,
          fill: '#999',
          textAlign: 'center',
          textVerticalAlign: 'middle',
        },
      },
      xAxis: {
        show: false,
      },
      yAxis: {
        show: false,
      },
      grid: {
        show: false,
      },
    })
    return chart
  }
  chart.clear()
  // 模拟数据
  const data1 = dataSet || [[100, 88, 60, 58, 80, 70, 68, 50, 70]]

  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 12,
      },
      formatter: function (params: any) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;z-index: 1000;">${params[0].axisValue}</div>`
        // params按照value降序排序
        params.sort((a: any, b: any) => b.value - a.value)
        params.forEach((param: any) => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
            <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${param.color}; margin-right: 8px;"></span>
            <span style="margin-left: auto; font-weight: bold;">${param.value}%</span>
          </div>`
        })
        return result
      },
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6',
        },
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
      axisLabel: {
        color: '#666',
        fontSize: 10,
        formatter: '{value}%',
      },
    },
    series: data1.map(({ city, data }: any) => ({
      name: city.name,
      data: data,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: {
        color: city.color,
        width: 2,
      },
      itemStyle: {
        color: city.color,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: `${city.color}60`,
            },
            {
              offset: 1,
              color: `${city.color}10`,
            },
          ],
        },
      },
    })),
  }

  chart.setOption(option)
  return chart
}

// 窗口调整大小处理
const handleResize = () => {
  chartInstance1?.resize()
  chartInstance2?.resize()
}

onMounted(() => {
  // 初始加载数据
  loadTableData()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance1?.dispose()
  chartInstance2?.dispose()
})
</script>
<style lang="scss">
.drawer-body-dashboard {
  padding: 0 !important;
  background: #e2edfb;

  .pagination-container {
    display: flex;
    justify-content: flex-start;
    margin-top: 8px;
    margin-left: 8px;
    background: #f3f8fd !important;
    .btn-next {
      background: #f3f8fd !important;
      color: #333;
    }
    .btn-prev {
      background: #f3f8fd !important;
      color: #333;
    }
    .el-pager {
      background: #f3f8fd !important;
      .number {
        background: #f3f8fd !important;
        border: 1px solid #e6ebf5;
        color: #333;
      }
      .more {
        background: #f3f8fd !important;
        color: #333;
      }
    }
  }

  .el-table__header {
    background-color: #cbe2ff;
  }
  .el-table__row.current-row td {
    background: #cbe2ff !important;
  }
  .el-table th {
    color: #333;
    font-weight: 500;
    border-bottom: 1px solid #e6ebf5;
    background: #cbe2ff !important;
  }
  .el-table__header-wrapper {
    background: #cbe2ff !important;
  }
  .el-table__body-wrapper {
    background: #f3f8fd !important;
  }

  .el-table td {
    padding: 12px 0;
    border-bottom: 1px solid #f0f2f7;
    background-color: #f3f8fd;
  }

  .el-table tr:hover > td {
    background-color: #e8f2ff !important;
  }

  .el-table__body tr:last-child td {
    border-bottom: none;
  }
  .charts-section {
    display: flex;
    gap: 16px;
    margin: 0 16px 0px;

    .chart-container {
      flex: 1;
      border-radius: 8px;
      box-shadow: none;
      border: none;

      .chart-header {
        padding-top: 16px;
        text-align: center;
        background: white;

        .chart-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .chart-icon {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;

            &.chart-icon-blue {
              background-color: #5470c6;
            }

            &.chart-icon-orange {
              background-color: #ff9800;
            }
          }
        }
      }

      .chart-content {
        background: white;
        height: 170px;
        width: 100%;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.info-section {
  margin: 0 16px 0px;
  border-radius: 8px;
  padding: 16px 0px;
  box-shadow: none;
  border: none;

  .info-row {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;

    .info-item {
      display: flex;
      align-items: center;

      .info-label {
        color: #666;
        font-size: 14px;
        margin-right: 8px;
      }

      .info-value {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }

      .time-controls {
        width: 400px;
        display: flex;
        align-items: center;
        gap: 8px;

        .time-range {
          color: #333;
          font-size: 14px;
          padding: 4px 8px;
          background: #f5f5f5;
          border-radius: 4px;
          border: 1px solid #e0e0e0;
        }
      }
    }
  }
}

.table-section {
  background: #f3f8fd;
  margin: 0 16px;
  border-radius: 8px;
  padding-bottom: 8px;
  box-shadow: none;
  border: none;
  margin-top: 8px;

  .power-table {
    height: 226px;
    border: none;
    border-radius: 6px;
    overflow: hidden;
  }
}

:deep(.el-date-editor) {
  --el-date-editor-width: 140px;
  height: 28px;

  .el-input__inner {
    font-size: 12px;
    padding: 0 8px;
  }
}

:deep(.el-pagination) {
  .el-pagination__sizes {
    .el-select {
      .el-input {
        width: 90px;
      }
    }
  }
}

.title-export {
  position: relative;
  .select-input {
    position: absolute;
    width: 60%;
    top: 3px;
    left: 120px;
    display: flex;
    gap: 8px;
    padding: 0 16px;
    box-sizing: border-box;
  }
  .select-item {
    width: 33%;
  }
}
</style>
