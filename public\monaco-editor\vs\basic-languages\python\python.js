/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/
define('vs/basic-languages/python/python', ['require', 'require'], (require) => {
  'use strict'
  var moduleExports = (() => {
    var d = Object.create
    var o = Object.defineProperty
    var m = Object.getOwnPropertyDescriptor
    var f = Object.getOwnPropertyNames
    var _ = Object.getPrototypeOf,
      u = Object.prototype.hasOwnProperty
    var b = ((e) =>
      typeof require < 'u'
        ? require
        : typeof Proxy < 'u'
          ? new Proxy(e, { get: (n, t) => (typeof require < 'u' ? require : n)[t] })
          : e)(function (e) {
      if (typeof require < 'u') return require.apply(this, arguments)
      throw Error('Dynamic require of "' + e + '" is not supported')
    })
    var y = (e, n) => () => (n || e((n = { exports: {} }).exports, n), n.exports),
      h = (e, n) => {
        for (var t in n) o(e, t, { get: n[t], enumerable: !0 })
      },
      i = (e, n, t, a) => {
        if ((n && typeof n == 'object') || typeof n == 'function')
          for (let s of f(n))
            !u.call(e, s) &&
              s !== t &&
              o(e, s, { get: () => n[s], enumerable: !(a = m(n, s)) || a.enumerable })
        return e
      },
      l = (e, n, t) => (i(e, n, 'default'), t && i(t, n, 'default')),
      p = (e, n, t) => (
        (t = e != null ? d(_(e)) : {}),
        i(n || !e || !e.__esModule ? o(t, 'default', { value: e, enumerable: !0 }) : t, e)
      ),
      x = (e) => i(o({}, '__esModule', { value: !0 }), e)
    var g = y((B, c) => {
      var w = p(b('vs/editor/editor.api'))
      c.exports = w
    })
    var D = {}
    h(D, { conf: () => k, language: () => S })
    var r = {}
    l(r, p(g()))
    var k = {
        comments: { lineComment: '#', blockComment: ["'''", "'''"] },
        brackets: [
          ['{', '}'],
          ['[', ']'],
          ['(', ')'],
        ],
        autoClosingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"', notIn: ['string'] },
          { open: "'", close: "'", notIn: ['string', 'comment'] },
        ],
        surroundingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"' },
          { open: "'", close: "'" },
        ],
        onEnterRules: [
          {
            beforeText: new RegExp(
              '^\\s*(?:def|class|for|if|elif|else|while|try|with|finally|except|async|match|case).*?:\\s*$',
            ),
            action: { indentAction: r.languages.IndentAction.Indent },
          },
        ],
        folding: {
          offSide: !0,
          markers: { start: new RegExp('^\\s*#region\\b'), end: new RegExp('^\\s*#endregion\\b') },
        },
      },
      S = {
        defaultToken: '',
        tokenPostfix: '.python',
        keywords: [
          'False',
          'None',
          'True',
          '_',
          'and',
          'as',
          'assert',
          'async',
          'await',
          'break',
          'case',
          'class',
          'continue',
          'def',
          'del',
          'elif',
          'else',
          'except',
          'exec',
          'finally',
          'for',
          'from',
          'global',
          'if',
          'import',
          'in',
          'is',
          'lambda',
          'match',
          'nonlocal',
          'not',
          'or',
          'pass',
          'print',
          'raise',
          'return',
          'try',
          'type',
          'while',
          'with',
          'yield',
          'int',
          'float',
          'long',
          'complex',
          'hex',
          'abs',
          'all',
          'any',
          'apply',
          'basestring',
          'bin',
          'bool',
          'buffer',
          'bytearray',
          'callable',
          'chr',
          'classmethod',
          'cmp',
          'coerce',
          'compile',
          'complex',
          'delattr',
          'dict',
          'dir',
          'divmod',
          'enumerate',
          'eval',
          'execfile',
          'file',
          'filter',
          'format',
          'frozenset',
          'getattr',
          'globals',
          'hasattr',
          'hash',
          'help',
          'id',
          'input',
          'intern',
          'isinstance',
          'issubclass',
          'iter',
          'len',
          'locals',
          'list',
          'map',
          'max',
          'memoryview',
          'min',
          'next',
          'object',
          'oct',
          'open',
          'ord',
          'pow',
          'print',
          'property',
          'reversed',
          'range',
          'raw_input',
          'reduce',
          'reload',
          'repr',
          'reversed',
          'round',
          'self',
          'set',
          'setattr',
          'slice',
          'sorted',
          'staticmethod',
          'str',
          'sum',
          'super',
          'tuple',
          'type',
          'unichr',
          'unicode',
          'vars',
          'xrange',
          'zip',
          '__dict__',
          '__methods__',
          '__members__',
          '__class__',
          '__bases__',
          '__name__',
          '__mro__',
          '__subclasses__',
          '__init__',
          '__import__',
        ],
        brackets: [
          { open: '{', close: '}', token: 'delimiter.curly' },
          { open: '[', close: ']', token: 'delimiter.bracket' },
          { open: '(', close: ')', token: 'delimiter.parenthesis' },
        ],
        tokenizer: {
          root: [
            { include: '@whitespace' },
            { include: '@numbers' },
            { include: '@strings' },
            [/[,:;]/, 'delimiter'],
            [/[{}\[\]()]/, '@brackets'],
            [/@[a-zA-Z_]\w*/, 'tag'],
            [/[a-zA-Z_]\w*/, { cases: { '@keywords': 'keyword', '@default': 'identifier' } }],
          ],
          whitespace: [
            [/\s+/, 'white'],
            [/(^#.*$)/, 'comment'],
            [/'''/, 'string', '@endDocString'],
            [/"""/, 'string', '@endDblDocString'],
          ],
          endDocString: [
            [/[^']+/, 'string'],
            [/\\'/, 'string'],
            [/'''/, 'string', '@popall'],
            [/'/, 'string'],
          ],
          endDblDocString: [
            [/[^"]+/, 'string'],
            [/\\"/, 'string'],
            [/"""/, 'string', '@popall'],
            [/"/, 'string'],
          ],
          numbers: [
            [/-?0x([abcdef]|[ABCDEF]|\d)+[lL]?/, 'number.hex'],
            [/-?(\d*\.)?\d+([eE][+\-]?\d+)?[jJ]?[lL]?/, 'number'],
          ],
          strings: [
            [/'$/, 'string.escape', '@popall'],
            [/f'{1,3}/, 'string.escape', '@fStringBody'],
            [/'/, 'string.escape', '@stringBody'],
            [/"$/, 'string.escape', '@popall'],
            [/f"{1,3}/, 'string.escape', '@fDblStringBody'],
            [/"/, 'string.escape', '@dblStringBody'],
          ],
          fStringBody: [
            [/[^\\'\{\}]+$/, 'string', '@popall'],
            [/[^\\'\{\}]+/, 'string'],
            [/\{[^\}':!=]+/, 'identifier', '@fStringDetail'],
            [/\\./, 'string'],
            [/'/, 'string.escape', '@popall'],
            [/\\$/, 'string'],
          ],
          stringBody: [
            [/[^\\']+$/, 'string', '@popall'],
            [/[^\\']+/, 'string'],
            [/\\./, 'string'],
            [/'/, 'string.escape', '@popall'],
            [/\\$/, 'string'],
          ],
          fDblStringBody: [
            [/[^\\"\{\}]+$/, 'string', '@popall'],
            [/[^\\"\{\}]+/, 'string'],
            [/\{[^\}':!=]+/, 'identifier', '@fStringDetail'],
            [/\\./, 'string'],
            [/"/, 'string.escape', '@popall'],
            [/\\$/, 'string'],
          ],
          dblStringBody: [
            [/[^\\"]+$/, 'string', '@popall'],
            [/[^\\"]+/, 'string'],
            [/\\./, 'string'],
            [/"/, 'string.escape', '@popall'],
            [/\\$/, 'string'],
          ],
          fStringDetail: [
            [/[:][^}]+/, 'string'],
            [/[!][ars]/, 'string'],
            [/=/, 'string'],
            [/\}/, 'identifier', '@pop'],
          ],
        },
      }
    return x(D)
  })()
  return moduleExports
})
