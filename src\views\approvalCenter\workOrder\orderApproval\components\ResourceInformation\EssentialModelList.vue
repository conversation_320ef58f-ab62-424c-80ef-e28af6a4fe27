<template>
  <div
    class="no-card"
    :class="{ 'show-checkbox-all': showCheckboxAll() }"
    v-if="goodsType && goodsType !== 'unknown'"
  >
    <div
      v-if="pageStatus && batchOperationType.length"
      class="flx-align-center"
      style="width: 100%; min-width: 900px"
    >
      <BatchAssignment
        :az-list="azList"
        :batch-operation-type="batchOperationType"
        :sele-pool="selePool"
        :network-dic="networkDic"
        @handleAzChange="handleAzChange"
        :multiple="isMultiple"
        @submit="handleBatchSubmit"
        ref="batchAssignmentRef"
      />
    </div>
    <sl-pro-table
      :columns="tableColumns()"
      :data="goodsAllDetails"
      :pagination="false"
      :is-show-search="false"
      row-key="ids"
      ref="proTable"
    >
    </sl-pro-table>
    <!-- 弹窗 -->
    <el-dialog
      title="资源详情"
      v-model="dialogVisible"
      width="80%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <sl-pro-table
        :columns="tableColumns(false)"
        :data="goodsDetails"
        :pagination="false"
        :is-show-search="false"
      >
        <template #originName="{ row }">
          {{ row.goodsName }}
        </template>
      </sl-pro-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button :disabled="disCancel" @click="dialogVisible = false">
            {{ isCanOperation ? '取 消' : '关 闭' }}
            <el-tooltip
              v-if="disCancel"
              key="disCancel"
              class="box-item"
              effect="dark"
              content="当前有资源状态发生改变，请点击“提交”按钮合并工单。"
              placement="top-start"
            >
              <el-icon class="ml5">
                <el-icon><Warning /></el-icon>
              </el-icon>
            </el-tooltip>
          </el-button>
          <el-button v-if="isCanOperation" type="primary" @click="submit">提 交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { computed, inject, ref, toRefs, type VNode } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { goodsTypeEnum, type goodsTypeCodeEnum } from '../../../interface/type'
import { useVModel } from '@vueuse/core'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import goodsAllColumns from './goodsColumns'
import useModelList, { type goodsDetailsType } from './useModelList'
import {
  getNetworkTreeApi,
  getvpcTreeApi,
  standardWorkOrderResOpenApi,
} from '@/api/modules/approvalCenter'
import { showTips } from '@/utils'
import BatchAssignment from './BatchAssignment.vue'

// 列表状态  开通
type PropsType = {
  goodsList: FormDataType[]
  resourcePoolsDic: any[] //资源池字典
  goodsType: goodsTypeCodeEnum
  orderId: string
  azList: FormDataType
  orderDesc: FormDataType //工单详情信息
  domainCodeStatus: boolean //某些云平台集合的权限
}
const props = withDefaults(defineProps<PropsType>(), {})
const { goodsType, azList } = toRefs(props)
const pageStatus = inject('pageStatus', ref(true))
const isInnovationPool = inject('isInnovationPool', ref(false))
const innovationWorkList = inject('innovationWorkList', ref<FormDataType>({})) //网络列表
const isOffline = inject('isOffline', ref(false))

const btnAuth = inject(
  'btnAuth',
  ref({
    resource_creation: false,
    schema_administrator: false,
    autodit_end: false,
  }),
)

// 是否可多选子网
const isMultiple = computed(
  () => isInnovationPool.value && ['ecs', 'mysql', 'redis'].includes(goodsType.value),
)

// 是否可操作合并拆分 且 添加开通数据以及分配资源池
const isCanOperation = computed(() => {
  return pageStatus.value && (btnAuth.value.resource_creation || btnAuth.value.schema_administrator)
})

const emit = defineEmits(['update:goodsList'])
const goodsListRef = useVModel(props, 'goodsList', emit)

/**
 * 生成查重的keys
 */
const getKeys = () => {
  const keys: string[] = ['idHash', 'status', 'regionId']
  if (btnAuth.value.resource_creation) keys.push(...goodsTypeEnum[goodsType.value]?.params)
  return keys
}

const { insertAtIndexAndClear, mergeParent, uniqueByPropertiesWithCustomKey, createObjectsArray } =
  useModelList(getKeys)

const goodsAllDetails = computed({
  get() {
    if (!goodsListRef.value?.length) return []

    const newGoods = JSON.parse(JSON.stringify(goodsListRef.value))
    return uniqueByPropertiesWithCustomKey<goodsDetailsType>(newGoods, getKeys(), 'openNum')
  },
  set(val) {
    const arrall: any[] = []
    val.map((row: any) => {
      arrall.push(...createObjectsArray(row))
    })

    goodsListRef.value = arrall
  },
})

const undateGoodsAllDetails = () => {
  if (!dialogVisible.value) goodsAllDetails.value = mergeParent(goodsAllDetails.value)
}

// -----------------------弹窗数据---------------------

const goodsDetails = ref<goodsDetailsType[]>([])
const goodIndex = ref<number>(-1)
const dialogVisible = ref(false)

// 是否禁用取消
const disCancel = ref<boolean>(false)

const close = () => {
  goodIndex.value = -1
  goodsDetails.value = []
  disCancel.value = false
}

const submit = () => {
  const newGoods = JSON.parse(JSON.stringify(goodsDetails.value))
  const items = uniqueByPropertiesWithCustomKey(newGoods, getKeys(), 'openNum').map((item) => {
    return { ...item }
  })
  // 合并父级
  const goods = JSON.parse(JSON.stringify(goodsAllDetails.value))

  insertAtIndexAndClear(goods, goodIndex.value, items)

  goodsAllDetails.value = mergeParent(goods)
  dialogVisible.value = false
}
// ===============================================获取字典================================================================

// 公网字典
const changeAz = async (value: number, row: goodsDetailsType, falg: boolean = true) => {
  row['azName'] = ''
  row['azCode'] = ''
  row['azId'] = ''
  row['subnetIds'] = []
  row['subnetName'] = ''
  row['vpcName'] = ''
  row['vpcCode'] = ''
  row['vpcId'] = ''
  row['templateCode'] = ''
  const obj = azList.value[row.regionId].find((item: any) => item.id === value)
  if (obj) {
    row['azName'] = obj.name
    row['azCode'] = obj.code
    row['azId'] = obj.id
    let params = {
      orderId: props.orderId,
      regionCode: row.regionCode,
      azCode: obj.code,
    }

    falg && (await getNetworkDic(params, `${row.regionId}-${row.azId}`))
  }
  falg && undateGoodsAllDetails()
  return {
    ...row,
  }
}

const networkDic = ref<{ [key: string]: any[] }>({}) //网络字典

//网络接口
const getNetworkDic = async (params: any, key: string) => {
  if (isInnovationPool.value) return
  if (networkDic.value[key]) return

  const { entity } = props.domainCodeStatus
    ? await getNetworkTreeApi(params)
    : await getvpcTreeApi(params)

  let arr = entity.map((item: any) => {
    return {
      ...item,
      name: item.vpcName ? item.vpcName : item.name,
      subnetOrderList: item.subnetOrderList ? item.subnetOrderList : item.vpcSubnetOrderList,
    }
  })
  networkDic.value[key] = arr
}

// 私网字典
const getPrivateNetworkDic = async (value: number, row: any) => {
  row['subnetIds'] = []
  row['subnetName'] = ''
  row['vpcName'] = ''
  row['vpcCode'] = ''
  if (value) {
    const obj = (
      isInnovationPool.value
        ? innovationWorkList.value[row.regionCode]
        : networkDic.value[`${row.regionId}-${row.azId}`]
    ).find((item: any) => item.id === value)
    row['vpcName'] = obj.name
    row['vpcCode'] = obj.code
  }
  undateGoodsAllDetails()
}

// =========================================表单操作=============================================================
/**
 * 当维开通中 和 开通成功时 禁用
 * @param status 状态
 */
const rowStatusBtn = (status: string) => {
  return status && ['opening', 'open_success'].includes(status)
}
/**
 * 当为开通中 和 开通成功时 禁用
 * @param status 状态
 */

const rowStatus = (status: string) => {
  return status && ['opening', 'open_success', 'open_fail'].includes(status)
}

// 禁用表单
const disabledNetwork = (row: goodsDetailsType) => {
  if (!pageStatus.value || !btnAuth.value.resource_creation) return true

  if (rowStatus(row.status)) return true

  // 当为云硬盘的时候判断
  if (['evs', 'eip'].includes(goodsType.value) && row['vmName']) return true

  return false
}

// 是否显示列
const ishideColumn = (prop: string) => {
  return goodsTypeEnum[goodsType.value]?.params?.some((item) => item === prop)
}

const azListColumn = () => {
  const isShow = ishideColumn('azId')
  return {
    prop: 'azId',
    label: '可用区',
    align: 'center',
    width: '220px',
    isShow,
    render: ({ row }: { row: any }): VNode => {
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.azName ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              v-model={row.azId}
              style="width: 100%"
              placeholder="请选择可用区"
              onChange={(value: number) => {
                changeAz(value, row)
              }}
            >
              {azList.value[row.regionId]?.map((option: any) => (
                <el-option key={option.id} label={option.name} value={option.id} />
              ))}
              {!azList.value[row.regionId]?.length && (
                <el-option label="数据不存在请刷新页面" value="1" disabled={true} />
              )}
            </el-select>
          )}
        </>
      )
    },
  }
}

const vpcIdColumn = () => {
  const isShow = ishideColumn('vpcId')
  return {
    prop: 'vpcId',
    label: '网络',
    align: 'center',
    width: '250px',
    isShow,
    render: ({ row }: { row: any }): VNode => {
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.vpcName ?? '--'}</span>
          ) : (
            <el-select
              class="mr10"
              clearable
              filterable
              v-model={row.vpcId}
              style="width: 100%"
              placeholder="请选择网络"
              onChange={(value: number) => getPrivateNetworkDic(value, row)}
            >
              {(isInnovationPool.value
                ? innovationWorkList.value[row.regionCode]
                : networkDic.value[`${row.regionId}-${row.azId}`]
              )?.map((option: any) => (
                <el-option key={option.id} label={option.name} value={option.id} />
              ))}
            </el-select>
          )}
        </>
      )
    },
  }
}

const subnetIdColumn = () => {
  const isShow = ishideColumn('subnetId')

  return {
    prop: 'subnetId',
    label: '子网',
    align: 'center',
    width: '250px',
    isShow,
    render: ({ row }: { row: any }): VNode => {
      const privateNetworkDic = computed(() => {
        const obj = (
          isInnovationPool.value
            ? innovationWorkList.value[row.regionCode]
            : networkDic.value[`${row.regionId}-${row.azId}`]
        )?.find((item: any) => item.id === row.vpcId)

        return obj?.subnetOrderList ?? []
      })
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.subnetNames ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              multiple
              multiple-limit={isMultiple.value ? 0 : 1}
              v-model={row.subnetIds}
              style="width: 100%"
              placeholder="请选择子网络"
              onChange={(value: string[]) => {
                row.subnetNames = ''
                if (value.length > 0) {
                  row['subnets'] = []
                  row.subnetId = value.join(',')
                  row.subnetNames = privateNetworkDic.value
                    ?.filter((item: any) => value.includes(item.id))
                    .map((item: any) => {
                      row['subnets'].push({
                        subnetId: item.id,
                        subnetName: item.subnetName,
                        subnetIp: item.cidr,
                      })
                      return item.subnetName
                    })
                    .join(',')
                }

                undateGoodsAllDetails()
              }}
            >
              {privateNetworkDic.value?.map((option: any) => (
                <el-option key={option.id} label={option.subnetName} value={option.id} />
              ))}
              {!privateNetworkDic.value?.length && (
                <el-option label="请先选择网络,若不存在请删除重选" value="1" disabled={true} />
              )}
            </el-select>
          )}
        </>
      )
    },
  }
}
// -----------------------表单列表---------------------

const editFn = (row: any, index: number) => {
  goodsDetails.value = createObjectsArray(row)
  goodIndex.value = index
  dialogVisible.value = true
}

// 操作按钮
// 网络开通函数
const networkOpen = async (row: any) => {
  // 1.组合数据 校验
  let obj: any = {
    standardWorkOrderId: props.orderId,
    openResType: goodsType.value,
  }
  // 详情内 id 与外层的id参数不同
  if (dialogVisible.value) obj['openResIds'] = [row.id]
  else obj['openResIds'] = row.ids

  let flag = true
  let goodsParams = goodsTypeEnum[goodsType.value]?.params ?? []

  goodsParams.forEach((item) => {
    flag = flag && row[item]
  })
  if (!flag) {
    return showTips('请先选择要提交的数据！')
  }
  const setVlueKey = ['azCode', 'azId', 'azName']
  setVlueKey.map((item) => {
    if (row[item]) obj[item] = row[item]
  })

  // 包含网络
  if (ishideColumn('subnetId')) {
    obj['planeNetworkModelList'] = [
      {
        type: row.vpcType,
        id: row.vpcId,
        name: row.vpcName,
        plane: row.plane,
        subnets: row.subnets,
        sort: 0,
      },
    ]
  }

  // 重新开通先做个假的
  if (row.status === 'open_fail' && goodsType.value !== 'cq') {
    row.status = 'opening'
    row.statusCn = '开通中'
    ElMessage.success('已提交开通')
    return
  }

  try {
    await standardWorkOrderResOpenApi(obj)
    row.status = 'opening'
    row.statusCn = '开通中'
    ElMessage.success('已提交开通')
  } catch (e) {
    console.error(e)
    disCancel.value = false
  }
}
const operationRender = (
  { row, $index }: { row: any; $index: number },
  falg: boolean = true,
): VNode => {
  return (
    <>
      {btnAuth.value.resource_creation && pageStatus.value && !isOffline.value && (
        <el-button
          type="primary"
          disabled={rowStatusBtn(row.status)}
          onClick={() => networkOpen(row)}
          link
        >
          {row.status === 'open_fail' ? '重新开通' : '开通'}
        </el-button>
      )}
      {falg && (
        <el-button type="primary" onClick={() => editFn(row, $index)} link>
          详情
        </el-button>
      )}
    </>
  )
}

const operationColumns = (falg: boolean = true) => {
  const width = btnAuth.value.resource_creation ? '140px' : '80px'

  return {
    prop: 'operation',
    label: '操作',
    align: 'center',
    fixed: 'right',
    width,
    render: ({ row, $index }: { row: any; $index: number }) =>
      operationRender({ row, $index }, falg),
  }
}

const columns = ref<ColumnProps<goodsDetailsType>[]>([
  {
    prop: 'openNum',
    label: '数量',
    align: 'center',
    width: '100px',
  },
  {
    prop: 'regionName',
    label: '资源池',
    align: 'center',
    width: '220px',
  },
])

const statuses = [
  { value: 'wait_open', label: '待开通' },
  { value: 'opening', label: '开通中' },
  { value: 'open_success', label: '开通成功' },
  { value: 'open_fail', label: '开通失败' },
]

const statusColumn = (falg: boolean): ColumnProps[] => [
  {
    prop: 'message',
    label: '失败原因',
    align: 'center',
    width: '300px',
    isShow: !falg,
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    width: '100px',
    fixed: 'right',

    render: ({ row }: { row: any }): VNode => {
      let obj = {
        wait_open: 'primary',
        opening: 'warning',
        open_success: 'success',
        open_fail: 'danger',
      }
      const type = obj[row.status as keyof typeof obj] ?? 'primary'
      return (
        <el-text type={type} underline={false}>
          {statuses.find((item) => item.value === row.status)?.label}
        </el-text>
      )
    },
  },
]

const tableColumns = computed(() => (falg: boolean = true) => {
  if (!goodsType.value || goodsType.value === 'unknown') return []

  let indexColumn: ColumnProps = { type: 'index', label: '序号', width: 55 }
  const newColumns = [indexColumn, ...goodsAllColumns[goodsType.value], ...columns.value]
  // 添加表单
  if (btnAuth.value.resource_creation || btnAuth.value.autodit_end) {
    newColumns.push(azListColumn())
    newColumns.push(vpcIdColumn())
    newColumns.push(subnetIdColumn())
    // newColumns.push(templateCodeColumn())
  }

  if (btnAuth.value.resource_creation) {
    newColumns.push(...statusColumn(falg))
  }

  if (falg || btnAuth.value.resource_creation) newColumns.push(operationColumns(falg))
  if (
    falg &&
    pageStatus.value &&
    (btnAuth.value.resource_creation || btnAuth.value.schema_administrator)
  )
    newColumns.unshift(selectedColumns())
  return newColumns
})

//  ==========================================批量赋值操作- ==========================================
const proTable = ref<ProTableInstance>()
const batchAssignmentRef = ref<InstanceType<typeof BatchAssignment>>()

// 批量操作类型
const batchOperationType = computed(() => {
  let arr = []
  if (ishideColumn('azId'))
    arr.push({
      label: '可用区',
      value: 'az',
    })
  if (ishideColumn('vpcId'))
    arr.push({
      label: '可用区 + 网络',
      value: 'network',
    })
  return arr
})

const selePool = computed<any>(() => {
  if (!proTable.value?.selectedList.length) return ''
  return proTable.value?.selectedList[0]
})

const showCheckboxAll = () => {
  if (!btnAuth.value.resource_creation) return false
  if (!goodsAllDetails.value.length) return false

  const firstValue = goodsAllDetails.value[0]['regionId']
  return !goodsAllDetails.value.every((item: any) => item['regionId'] === firstValue)
}

const selectable = (row: any) => {
  // 当为云硬盘的时候判断
  if (['evs', 'eip'].includes(goodsType.value) && row['vmName']) return false

  if (btnAuth.value.resource_creation) {
    if (rowStatus(row.status)) return false
    const pooold = proTable.value?.selectedList.map((item: any) => item.regionId) ?? []
    return !pooold.length || !row.regionId || pooold.includes(row.regionId)
  }

  return true
}
const selectedColumns = (): ColumnProps => {
  return {
    type: 'selection',
    align: 'center',
    fixed: 'left',
    width: '55px',
    selectable: selectable,
  }
}

const handleAzChange = (value: any) => {
  let params = {
    orderId: props.orderId,
    regionCode: selePool.value.regionCode,
    azCode: value.code,
  }
  getNetworkDic(params, `${selePool.value.regionId}-${value.id}`)
}

const chageDetails = async (poolIdList: string[], params: any) => {
  const arr = goodsAllDetails.value.map((item: any) => {
    const orderGoodsIdString = item.ids?.join('|')
    if (poolIdList.includes(orderGoodsIdString!)) {
      item = {
        ...item,
        ...params,
      }
    }
    return {
      ...item,
    }
  })

  return arr
}

const handleBatchSubmit = async (params: any) => {
  if (!proTable.value?.isSelected) return showTips('请选择要操作的数据')
  await ElMessageBox.confirm('确定要批量赋值吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })

  const poolIdList = proTable.value?.selectedListIds.map((item: any) => item.join('|'))

  const arr = await chageDetails(poolIdList, params)

  goodsAllDetails.value = mergeParent(arr)
  proTable.value?.clearSelection()
  batchAssignmentRef.value?.resetForm()
}
</script>

<style lang="scss" scoped>
.title-input {
  flex: 1;
  padding-top: 20px;
  margin-right: 10px;
}

/* 隐藏全选复选框 */
.show-checkbox-all {
  :deep() {
    .el-table .el-table__header th .el-checkbox {
      display: none;
    }
  }
}
</style>
