<template>
  <div class="dashboard-title-power">
    <div class="dashboard-title-power-text">
      <span
        class="dashboard-title-power-text-item"
        v-for="(num, index) in numList"
        :key="num + index"
        >{{ num }}
      </span>
    </div>
    <div class="dashboard-title-power-total">算力总数</div>
    <div class="dashboard-title-power-unit">TFLOPS</div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps({
  totalPower: {
    type: Number,
    default: 0,
  },
})

const numList = computed(() => {
  return props.totalPower.toString().padStart(7, '0').split('')
})
</script>
<style scoped>
.dashboard-title-power-total {
  top: 14px;
  position: absolute;
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', Arial, sans-serif;
  color: #ffffff;
  font-size: 18px;
  font-weight: 100;
  top: -10px;
  left: -70px;
  letter-spacing: 4px;
}
.dashboard-title-power-unit {
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', Arial, sans-serif;
  top: -10px;
  right: -84px;
  position: absolute;
  font-size: 18px;
  color: #ffffff;
  letter-spacing: 2px;
}
.dashboard-title-power-text {
  position: absolute;
  top: -24px;
  width: 100%;
  height: 44px;
  line-height: 44px;
  z-index: 1000;
  color: #32517c;
  font-weight: bold;
  font-size: 32px;
}
.dashboard-title-power-text-item {
  display: inline-block;
  width: 36px;
  /* 磨砂白色背景 */
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  margin: 2px 4px;
}

.dashboard-title-power {
  position: relative;
  z-index: 100;
  min-width: 360px;
  text-align: center;
  font-size: 28px;
  height: 80px;
}

.dashboard-title-power::before {
  content: '';
  position: absolute;
  left: -360px;
  top: -30px;
  width: 150%;
  height: 120%;
  background-image: url('/images/dashboard/decoration.png');
  background-repeat: no-repeat;
  background-size: auto 80%;
  transform: scaleX(-1);
  z-index: -1;
}

.dashboard-title-power::after {
  content: '';
  position: absolute;
  right: -360px;
  top: -30px;
  width: 150%;
  height: 120%;
  background-image: url('/images/dashboard/decoration.png');
  background-repeat: no-repeat;
  background-size: auto 80%;
  z-index: -1;
}
</style>
