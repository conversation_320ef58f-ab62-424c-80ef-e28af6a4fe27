# URL参数地图访问示例

## 功能说明

现在可以通过URL查询参数 `city` 来动态切换显示不同城市的地图。

## 使用方法

### 主页面访问

- 浙江省地图（默认）：`/computingPowerMap`
- 杭州市地图：`/computingPowerMap?city=hangzhou`
- 温州市地图：`/computingPowerMap?city=wenzhou`
- 宁波市地图：`/computingPowerMap?city=ningbo`

### 详情页面访问

- 浙江省地图（默认）：`/computingPowerMap/Detail`
- 杭州市地图：`/computingPowerMap/Detail?city=hangzhou`
- 温州市地图：`/computingPowerMap/Detail?city=wenzhou`
- 宁波市地图：`/computingPowerMap/Detail?city=ningbo`

## 交互功能

### 地图点击事件

- **功能**: 用户可以点击地图上的区县来选择和切换区域
- **效果**:
  - 点击区县会触发父组件的`selectChange`方法
  - 选中的区域会有视觉高亮效果
  - 再次点击相同区域可以取消选中
  - 会根据选中区域更新算力数据

### 事件传递

- **selectChange事件**: 当用户点击地图区县时触发
- **参数**: 传递区域代码（去掉"市"/"区"/"县"等后缀）
- **使用示例**:
  ```javascript
  const handleSelectChange = (selectedRegion: string) => {
    console.log('选中的区域:', selectedRegion)
    // 更新相关数据或状态
  }
  ```

## 支持的城市参数

| 参数值   | 城市名称 | 说明         |
| -------- | -------- | ------------ |
| zhejiang | 浙江省   | 默认显示     |
| hangzhou | 杭州市   | 浙江省会城市 |
| ningbo   | 宁波市   | 副省级城市   |
| wenzhou  | 温州市   | 地级市       |
| jiaxing  | 嘉兴市   | 地级市       |
| huzhou   | 湖州市   | 地级市       |
| shaoxing | 绍兴市   | 地级市       |
| jinhua   | 金华市   | 地级市       |
| quzhou   | 衢州市   | 地级市       |
| zhoushan | 舟山市   | 地级市       |
| taizhou  | 台州市   | 地级市       |
| lishui   | 丽水市   | 地级市       |

## 示例URL

```
# 开发环境示例
http://localhost:3000/computingPowerMap?city=hangzhou
http://localhost:3000/computingPowerMap/Detail?city=wenzhou

# 生产环境示例
https://your-domain.com/computingPowerMap?city=ningbo
https://your-domain.com/computingPowerMap/Detail?city=taizhou
```

## 错误处理

- 如果传入不支持的城市参数，系统会自动回退到浙江省地图
- 如果没有传入city参数，默认显示浙江省地图
- 地图JSON文件加载失败时，会自动回退到浙江省地图

## 技术实现

1. 使用Vue Router的`useRoute()`获取查询参数
2. 通过计算属性`cityParam`验证和处理城市参数
3. 将处理后的参数传递给`DashboardCityMap`组件
4. 组件根据参数动态加载对应的地图JSON文件
5. **新增**: 地图点击事件监听器，支持区县选择和数据更新

## 组件事件

### DashboardCityMap组件事件

#### selectChange

- **类型**: `(areaCode: string) => void`
- **描述**: 当用户点击地图区县时触发
- **参数**:
  - `areaCode`: 选中的区域代码（去掉"市"/"区"/"县"后缀）
- **示例**:

  ```vue
  <template>
    <DashboardCityMap :city="cityParam" @selectChange="handleSelectChange" />
  </template>

  <script setup>
  const handleSelectChange = (selectedRegion: string) => {
    console.log('用户选择了区域:', selectedRegion)
    // 处理区域切换逻辑
  }
  </script>
  ```
