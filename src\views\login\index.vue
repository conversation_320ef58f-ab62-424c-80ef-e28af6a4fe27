<template>
  <div class="login-container">
    <div class="header">
      <img src="@/assets/images/img/login/logo.png" alt="China Mobile Logo" class="logo" />
    </div>

    <div class="login-card">
      <div class="login-tabs">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="密码登录" name="password"></el-tab-pane>
          <el-tab-pane label="短信登录" name="sms"></el-tab-pane>
        </el-tabs>
      </div>

      <el-form ref="loginForm" :model="formData" :rules="formRules" class="login-form">
        <!-- 密码模式 -->
        <template v-if="activeTab === 'password'">
          <el-form-item prop="username">
            <el-input v-model="formData.username" placeholder="请输入用户名" :prefix-icon="User" />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Key"
            />
          </el-form-item>
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input v-model="formData.captcha" placeholder="请输入验证码" :prefix-icon="Key" />
              <img :src="captchaImage" alt="验证码" class="captcha-image" @click="refreshCaptcha" />
            </div>
          </el-form-item>
        </template>

        <!-- 短信登录 -->
        <template v-if="activeTab === 'sms'">
          <el-form-item prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入手机号" :prefix-icon="Phone" />
          </el-form-item>

          <el-form-item prop="smsCode">
            <div class="verification-code">
              <el-input
                v-model="formData.smsCode"
                placeholder="请输入短信验证码"
                :prefix-icon="Message"
              />
              <el-button
                type="primary"
                class="send-code-btn"
                :disabled="countdown > 0"
                @click="sendVerificationCode"
              >
                {{ countdown > 0 ? `${countdown}s` : '发送短信验证码' }}
              </el-button>
            </div>
          </el-form-item>
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input v-model="formData.captcha" placeholder="请输入验证码" :prefix-icon="Key" />
              <img :src="captchaImage" alt="验证码" class="captcha-image" @click="refreshCaptcha" />
            </div>
          </el-form-item>
        </template>
        <el-button type="primary" class="login-btn" @click="handleLogin"> 登录 </el-button>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import SlMessage from '@/components/base/SlMessage'
import type { FormInstance, FormRules } from 'element-plus'
import { Phone, Message, Key, User } from '@element-plus/icons-vue'
import { loginApi, getCaptchaImage, sendSmsCode } from '@/api/modules'
import { useUserStore } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
import { useKeepAliveStore } from '@/stores/modules/keepAlive'
import { useAuthStore } from '@/stores/modules/auth'

interface FormData {
  phone: string
  password: string
  smsCode: string
  captcha: string
  username: string
}

const keepAliveStore = useKeepAliveStore()
const authStore = useAuthStore()
const userStore = useUserStore()
const router = useRouter()

const activeTab = ref('password')
const countdown = ref(0)
const loginForm = ref<FormInstance>()

const formData = reactive<FormData>({
  password: '',
  username: '',
  phone: '',
  smsCode: '',
  captcha: '',
})

const formRules: FormRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'change' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'change' },
  ],
  smsCode: [{ required: true, message: '请输入短信验证码', trigger: 'change' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'change' }],
  password: [{ required: true, message: '请输入密码', trigger: 'change' }],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'change' }],
}

const sendVerificationCode = async () => {
  if (!loginForm.value) return

  try {
    await loginForm.value.validateField('phone')
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

    try {
      await sendSmsCode({ phone: formData.phone })
    } catch (error) {
      clearInterval(timer)
      countdown.value = 0
      console.error(error)
    }
  } catch (error) {
    console.error('Phone number validation failed:', error)
  }
}

const captchaUuid = ref('')
const captchaImage = ref('')

const refreshCaptcha = async () => {
  try {
    const res = await getCaptchaImage({})
    if (res.code === 200) {
      captchaUuid.value = res.entity.uuid
      captchaImage.value = `data:image/png;base64,${res.entity.img}`
    } else {
      SlMessage.error(res.message || '验证码刷新失败')
      throw new Error('Failed to get captcha')
    }
  } catch (error) {
    console.error('Failed to refresh captcha:', error)
  }
}

// 初始化时获取验证码
refreshCaptcha()

const handleLogin = async () => {
  if (!loginForm.value) return

  try {
    await loginForm.value.validate()

    let loginParams = {}
    if (activeTab.value === 'password') {
      // Password login
      loginParams = {
        loginType: 'ACCOUNT',
        loginName: formData.username,
        passWord: btoa(formData.password),
        captchaCode: formData.captcha,
        captchaUuid: captchaUuid.value,
      }
    } else {
      // SMS login
      loginParams = {
        loginType: 'PHONE',
        phone: formData.phone,
        smsVerify: formData.smsCode,
        captchaCode: formData.captcha,
        captchaUuid: captchaUuid.value,
      }
    }

    try {
      const { entity } = await loginApi(loginParams)
      userStore.setUser(entity)
      userStore.setToken(entity.token)
    } catch (error) {
      console.error(error)
      refreshCaptcha()
      return
    }

    await authStore.getAuthMenuList()
    keepAliveStore.setKeepAliveName([])

    SlMessage.success('登录成功')
    router.push({
      name: authStore.initialRouteNameGet || 'operationsOverview',
    })
  } catch (error) {
    console.error(error)
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: url('@/assets/images/img/login/bg.png') no-repeat center center;
  background-size: cover;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 20px;
}

.logo,
.platform-logo {
  height: 30px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 30px;
  width: 100%;
  max-width: 360px;
  margin: 140px 100px auto auto;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.login-form {
  margin-top: 20px;
}

.verification-code {
  display: flex;
  gap: 10px;
}

.send-code-btn {
  white-space: nowrap;
}

.captcha-container {
  display: flex;
  gap: 10px;
}

.captcha-image {
  height: 30px;
  width: 100px;
  cursor: pointer;
}

.login-btn {
  width: 100%;
  margin-top: 20px;
  height: 40px;
  font-size: 16px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__active-bar) {
  height: 2px;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
}

:deep(.el-tabs__item:hover) {
  color: #409eff;
}
</style>
