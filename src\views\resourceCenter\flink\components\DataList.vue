<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getResourceList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="goodsOrderId"
  >
  </SlProTable>
</template>

<script setup lang="tsx" name="flinkDataList">
import { ref, reactive } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getResourceList, cycleBinCreate } from '@/api/modules/resourecenter'
import { useRouter } from 'vue-router'
import { useRecycleValidation } from '@/views/resourceCenter/hooks/useRecycleValidation'
import { ElMessage } from 'element-plus'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const { queryParams } = defineProps<{
  queryParams: Record<string, any>
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'deviceName',
    label: 'Flink名称',
    minWidth: 200,
    fixed: 'left',
    render: ({ row }) => (
      <el-button onClick={() => handleViewDetail(row)} type="primary" link>
        {row.deviceName}
      </el-button>
    ),
  },
  { prop: 'spec', label: '规格', minWidth: 100 },
  { prop: 'applyTime', label: '申请时长', minWidth: 120 },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'businessSysName', label: '业务系统', minWidth: 150 },
  { prop: 'cloudPlatform', label: '所属云', minWidth: 150 },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 200 },
  { prop: 'orderCode', label: '工单编号', minWidth: 150 },
  { prop: 'effectiveTime', label: '开通时间', minWidth: 150 },
  { prop: 'expireTime', label: '到期时间', minWidth: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'applyUserName', label: '申请人', minWidth: 100 },
])

const proTable = ref<ProTableInstance>()

// 使用回收校验钩子函数
const { validateRecycle } = useRecycleValidation()

const handleCreateRecycle = async (goodsItems: any[]) => {
  const res = await cycleBinCreate({
    goodsType: 'flink',
    goodsItems,
  })
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  ElMessage.success('已加入回收站')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
  eventBus.emit('cycleBins:updateCount')
}

// 批量回收功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateRecycle(selectedList, 'flink')) {
    const currentRecycleIdsList = selectedList.map((i) => ({ goodsId: i.id.trim() }))
    handleCreateRecycle(currentRecycleIdsList)
  }
}

// 多选数据
const multipleSelection = ref<any[]>([])

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 处理查看详情
const handleViewDetail = (row: any) => {
  // 跳转到Flink详情页面
  router.push({
    path: '/flinkDetail',
    query: {
      id: row.id,
    },
  })
}

defineExpose({
  handleBatchRecycle,
})
</script>
