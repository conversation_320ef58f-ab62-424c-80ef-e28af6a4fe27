import type {
  BusinessSystemListType,
  GetCloudHostUsageListParamsType,
  CloudHostUsageListType,
  GetResourceNumberListParamsType,
  ResourceNumberListType,
  GetPropertyConfigParamsType,
  GetPropertyConfigType,
  PropertyConfigFormDataType,
  GetNoticeListType,
} from '@/views/computingPowerMap/tenantView/interface/type'
import http from '@/api'
import { USER, PERFORMANCE, WOC, V1CLOUD } from '../config/servicePort'
import { changeDateFormat } from '@/utils'

// ------------------------租户视图-------------------------
/**
 * 获取业务系统列表
 */
export const getBusinessSystemListApi = (config: any = {}) =>
  http.post<BusinessSystemListType[]>(WOC + '/business/query/busiSystemlist', config)

/**
 * 新增业务系统
 * @param config
 * @returns
 */
export const businessSystemSubmit = (config: any) => http.post(USER + '/app/create', config)

/**
 * 编辑业务系统
 * @param config
 * @returns
 */
export const businessSystemUpdate = (config: any) => http.put(USER + '/app/info/update', config)

/**
 * 删除业务系统
 * @param id
 * @returns
 */
export const businessSystemDelete = (id: number | string) => http.post(USER + '/app/delete/' + id)

/**
 * 获取云主机使用情况
 */
export const getCloudHostUsageListApi = (config: GetCloudHostUsageListParamsType) => {
  return http.post<CloudHostUsageListType[]>(PERFORMANCE + '/view/vm/tenant', config)
}

/**
 * 获取租户视图资源数据
 * @param config
 * @returns
 */
export const getResourceNumberListApi = (config: GetResourceNumberListParamsType) => {
  return http.post<Array<ResourceNumberListType>>(WOC + '/home/<USER>/resourceOpen', config)
}

/**
 * @name 配置管理-配置属性列表
 */
export const getPropertyConfigApi = (config: GetPropertyConfigParamsType) => {
  return http.get<GetPropertyConfigType>(WOC + '/config/queryCurrentUserConfig', config, {
    loading: true,
  })
}
/**
 * @name 配置管理-修改,加配置属性列表
 */
export const submitPropertyConfigApi = (config: PropertyConfigFormDataType) =>
  http.post(WOC + '/config/upset', config, { loading: true })

/**
 * 获取公告列表
 * @returns
 */
export const getNoticeListApi = () => http.get<GetNoticeListType[]>(WOC + '/config/listNotice')

/**
 * @name 获取资源列表
 */
export const getResourceList = (config: any) => {
  return http.post<any>(
    WOC + '/resource/pageCustom',
    changeDateFormat(config, ['effectiveTime', 'expireTime', 'createTime']),
  )
}

/**
 * 获取客户Top5资源
 * @param params { customId: string, orderType: 'vCPU' | 'CPU' | 'IO' }
 */
export const getTop5ResourceOfCustomApi = (params: { customId: string; orderType: string }) => {
  return http.get<any>(WOC + '/managerView/top5ResourceOfCustom', params)
}

/**
 * 获取客户信息列表
 * @param params { customId: string }
 */
export const getCustomDetailApi = (params: { customId: string }) => {
  return http.get<any>(WOC + '/managerView/customDetail', params)
}

/**
 * @name 客户经理数据统计
 */
export const getCustomCountApi = (params: any) =>
  http.get<any>(WOC + '/managerView/countCustom', params)

/**
 * @name vCPU利用率排行
 */
export const getVcpuRankingApi = (params: any) =>
  http.get<any>(WOC + '/managerView/top5vCPUGroupByCustom', params)

/**
 * @name 客户订购数排行
 */
export const getTotalProductRankingApi = (params: any) =>
  http.get<any>(WOC + '/managerView/totalProductTop5', params)

/**
 * @name 内存利用率排行
 */
export const getMemoryRankingApi = (params: any) =>
  http.get<any>(WOC + '/managerView/top5MemGroupByCustom', params)

/**
 * @name 资源利用率排行
 */
export const getResourceRankingApi = (params: any) =>
  http.get<any>(WOC + '/managerView/top5Resource', params)

/**
 * @name 集团客户概览
 */
export const getCustomsApi = (params: any) => http.get<any>(WOC + '/managerView/customs', params)

/**
 * @name 客户列表
 */
export const getCustomListApi = (params: any) =>
  http.post<any>(WOC + '/managerView/customList', changeDateFormat(params, ['createTime']))

/**
 * @name 客户资源数量
 */
export const getCountCustomApi = (params: any) =>
  http.post<any>(WOC + '/resource/countCustom', params)

/**
 * @name 获取公网IP列表
 */
export const getNetworkEipListApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/page/res/EIP', params)

/**
 * @name 获取DCN列表
 */
export const getNetworkDcnListApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/page/base_device/DCN', params)

/**
 * @name 获取虚拟机列表
 */
export const getVirtualMachineListApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/page/base_device/VIRTUAL_MACHINE', params)

/**
 * @name 获取虚拟机告警数据
 */
export const getVirtualMachineAlarmStatsApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/alarm_stats/vm', params)

/**
 * @name 获取存储(云硬盘)列表
 */
export const getStorageListApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/page/res/STORAGE', params)

/**
 * @name 下载存储(云硬盘)列表
 */
export const downloadStorageListApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/res/evs/download', params, {
    responseType: 'blob',
  })

/**
 * @name 获取资源池列表
 */
export const getRegionsListApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/regions', params)

/**
 * @name 获取虚拟资源池列表
 */
export const getVirtualResourcePoolListApi = (params: any) =>
  http.post<any>(
    V1CLOUD + '/console/compute_power_map/page/base_device/VIRTUAL_RESOURCE_POOL',
    params,
  )

/**
 * @name 下载虚拟机列表
 */
export const downloadVirtualMachineListApi = (params: any) =>
  http.post<any>(
    V1CLOUD + '/console/compute_power_map/download/base_device/VIRTUAL_MACHINE',
    params,
    {
      responseType: 'blob',
    },
  )

/**
 * @name 下载公网IP列表
 */
export const downloadNetworkEipListApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/res/eip/download', params, {
    responseType: 'blob',
  })

/**
 * @name 下载DCN列表
 */
export const downloadDcnListApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/download/base_device/DCN', params, {
    responseType: 'blob',
  })

/**
 * @name 获取平台类型
 */
export const getCloudGroupStatsApi = () =>
  http.get<any>(V1CLOUD + '/console/compute_power_map/stats/cloud_group')

/**
 * @name 获取业务系统-数据库服务、重保业务、公网IP-网络云、虚拟机
 */
export const getComputerPowerMapListBusinessApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/list/business/' + params.type, params)

/**
 * @name 获取业务系统-公网IP-移动云
 */
export const getComputerPowerMapListAppApi = (params: any) =>
  http.post<any>(V1CLOUD + '/console/compute_power_map/list/app', params)
