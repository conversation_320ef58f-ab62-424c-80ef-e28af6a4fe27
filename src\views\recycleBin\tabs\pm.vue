<template>
  <div class="form-container">
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :data="goodsList"
      hidden-table-header
      :pagination="false"
      row-key="goodsOrderId"
    >
    </SlProTable>
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { reactive } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { useOp } from './useOp'

const { operationRender } = useOp()
const { goodsList = [] } = defineProps<{
  goodsList?: any[]
}>()
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55 },
  {
    prop: 'deviceName',
    label: '裸金属名称',
    minWidth: 200,
  },
  { prop: 'handoverStatus', label: '交维状态', minWidth: 100 },
  { prop: 'osVersion', label: '系统版本', minWidth: 120 },
  {
    prop: 'gpuInfo',
    label: 'GPU/NPU信息',
    minWidth: 150,
    render: ({ row }) => (
      <span>{row.gpuNum && row.gpuType ? `${row.gpuNum}*${row.gpuType}` : '-'}</span>
    ),
  },
  { prop: 'spec', label: '规格', minWidth: 120 },
  { prop: 'dataDisk', label: '硬盘', minWidth: 100 },
  { prop: 'ip', label: 'IP', minWidth: 120 },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'businessSysName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', minWidth: 150 },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 200 },
  { prop: 'orderCode', label: '工单编号', minWidth: 150 },
  { prop: 'createTime', label: '开通时间', minWidth: 150 },
  { prop: 'expireTime', label: '到期时间', minWidth: 150 },
  { prop: 'applyUserName', label: '申请人', minWidth: 100 },
  { prop: 'operation', label: '操作', width: 100, fixed: 'right', render: operationRender },
])
</script>
<style lang="scss" scoped>
.form-container {
  margin: 8px;
}
</style>
