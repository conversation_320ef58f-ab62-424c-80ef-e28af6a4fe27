<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getReportList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
  <dialogDetail v-if="dialogVisible" v-model="dialogVisible" :detail-id="detailId"></dialogDetail>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { getReportList } from '@/api/modules/managementCenter'
import { deleteReport } from '@/api/modules/managementCenter'
import { WOC } from '@/api/config/servicePort'
import dialogDetail from './dialogDetail.vue'
import { ElNotification } from 'element-plus'

const detailId = ref<string>('')
const { queryParams } = defineProps<{
  queryParams: any
}>()
const dialogVisible = ref(false)
const emit = defineEmits(['currentChange', 'selectDevice', 'refresh'])
const radioValue = ref()
const selectionItems = ref<any[]>([])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  if (!currentRow) return
  radioValue.value = currentRow.goodsOrderId
  emit('currentChange', currentRow, oldCurrentRow)
}

const handleSelectionChange = (selection: any) => {
  selectionItems.value = selection
}

const aDownload = (item: any) => {
  const a = document.createElement('a')
  a.href = `${WOC}/exportTask/download?id=${item.id}`
  a.download = `${item.reportName}.xlsx` // 设置下载文件名
  a.style.display = 'none' // 隐藏a标签
  document.body.appendChild(a)

  try {
    a.click()
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败，请稍后重试')
  } finally {
    // 下载完成后移除a标签
    document.body.removeChild(a)
  }
}

const viewDetail = (id: string) => {
  dialogVisible.value = true
  detailId.value = id
}

const handleDownload = async (items: any[]) => {
  items.forEach((item: any) => {
    aDownload(item)
  })
  ElNotification({
    title: '下载提示🔔',
    message: '下载已启动,请在浏览器右上角下载器中查看',
    type: 'success',
  })
}

const handleDelete = async (ids: string[]) => {
  await ElMessageBox.confirm('确定删除所选报表吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  deleteReport({ ids })
  ElMessage.success('删除成功')
  emit('refresh')
}

const batchDelete = async () => {
  if (selectionItems.value.length === 0) {
    ElMessage.warning('请选择要删除的报表')
    return
  }
  // 确认框
  await ElMessageBox.confirm('确定删除所选报表吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  const ids = selectionItems.value.map((item: any) => item.id)
  await deleteReport({ ids })
  ElMessage.success('删除成功')
  emit('refresh')
}

const batchDownload = () => {
  if (selectionItems.value.length === 0) {
    ElMessage.warning('请选择要下载的报表')
    return
  }
  if (selectionItems.value.some((item: any) => item.status !== 1)) {
    ElMessage.warning('只能下载已完成的报表')
    return
  }
  handleDownload(selectionItems.value)
}

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55 },
  {
    prop: 'reportName',
    label: '报表名称',
    width: 250,
    render: ({ row }) => (
      <el-link onClick={() => viewDetail(row.id)} type="primary">
        {row.reportName}
      </el-link>
    ),
  },
  {
    prop: 'businessType',
    label: '报表类型',
    width: 250,
    render: ({ row }) => (
      <span>
        {row.businessType === 'REGION' ? '资源池' : row.businessType === 'TENANT' ? '租户' : 'GPU'}
      </span>
    ),
  },
  { prop: 'creator', label: '创建人', width: 250 },
  { prop: 'createTime', label: '创建时间', width: 250 },
  {
    prop: 'status',
    label: '状态',
    render: ({ row }) => {
      return (
        <el-tag type={row.status === 0 ? 'warning' : row.status === 1 ? 'success' : 'danger'}>
          {row.status === 0 ? '生成中' : row.status === 1 ? '完 成' : '失 败'}
        </el-tag>
      )
    },
  },
  {
    prop: 'operation',
    label: '操作',
    width: 150,
    fixed: 'right',
    render: ({ row }) => {
      return (
        <div>
          <el-button
            onClick={() => handleDownload([row])}
            disabled={row.status !== 1}
            type="primary"
            link
            size="small"
          >
            下载
          </el-button>
          <el-button onClick={() => handleDelete([row.id])} type="danger" link size="small">
            删除
          </el-button>
        </div>
      )
    },
  },
])

defineExpose({
  batchDelete,
  batchDownload,
})
</script>
<style lang="scss" scoped>
.warning {
  width: 90%;
  margin: 0 auto;
  margin-top: -10px;
  word-break: break-all;
  color: red;
}
</style>
<style lang="scss">
.operation-column .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
