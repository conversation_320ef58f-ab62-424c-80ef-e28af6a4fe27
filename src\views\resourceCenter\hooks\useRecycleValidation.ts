import { ElMessage } from 'element-plus'
import type { ResourceType, PublicResourceType } from '../types'

export function useRecycleValidation() {
  /**
   * 验证资源是否可回收
   * @param resources 待回收资源列表
   * @param resourceType 资源类型
   * @returns 验证结果
   */
  const validateRecycle = (resources: any[], resourceType: ResourceType): boolean => {
    // 验证步骤1：检查是否有选择的资源
    if (resources.length === 0) {
      ElMessage.warning('请先选择资源')
      return false
    }

    // 验证步骤2：检查回收状态
    if (!resources.every((item) => item.recoveryStatus == 0)) {
      ElMessage.warning('当前有资源已提交回收工单，请重新选择')
      return false
    }

    // 验证步骤3：检查变更状态
    if (
      resources.every((item) => item.changeStatus) &&
      !resources.every((item) => item.changeStatus === 'un_change')
    ) {
      ElMessage.warning('当前有资源已提交变更工单，请重新选择')
      return false
    }

    // 验证步骤4：对于云主机和GPU云主机，还需要验证设备状态
    if (['ecs', 'gcs', 'mysql', 'redis'].includes(resourceType)) {
      const msg: Record<string, string> = {
        ecs: '云主机',
        gcs: 'GPU云主机',
        mysql: '云数据库',
        redis: '通用Redis',
        evs: '云硬盘',
        obs: '对象存储',
        slb: '负载均衡',
        nat: 'NAT网关',
        eip: '弹性公网IP',
        kafka: 'Kafka',
        flink: 'Flink',
        es: 'ElasticSearch',
        pm: '裸金属',
      }
      if (!resources.every((item) => ['RUNING', 'STOPED'].includes(item.deviceStatus))) {
        ElMessage.warning(`仅运行中和关机的${msg[resourceType] || '资源'}可以进行回收`)
        return false
      }
    }

    return true
  }

  /**
   * 验证资源是否可变更
   * @param resources 待变更资源列表
   * @returns 验证结果
   */
  const validateChange = (resources: any[]): boolean => {
    // 验证步骤1：检查是否有选择的资源
    if (resources.length === 0) {
      ElMessage.warning('请先选择资源')
      return false
    }

    // 验证步骤2：检查回收状态
    if (!resources.every((item) => item.recoveryStatus == 0)) {
      ElMessage.warning('当前有资源已提交回收工单，请重新选择')
      return false
    }

    // 验证步骤3：检查变更状态
    if (!resources.every((item) => item.changeStatus === 'un_change')) {
      ElMessage.warning('当前有资源已提交变更工单，请重新选择')
      return false
    }

    return true
  }

  /**
   * 验证资源是否可退订
   * @param resources 待退订资源列表
   * @param resourceType 资源类型
   * @returns 验证结果
   */
  const validateUnsubscribe = (resources: any[], resourceType: PublicResourceType): boolean => {
    // 验证步骤1：检查是否有选择的资源
    if (resources.length === 0) {
      ElMessage.warning('请先选择资源')
      return false
    }

    // 验证步骤2：检查回收状态
    if (!resources.every((item) => item.recoveryStatus == 0)) {
      ElMessage.warning('当前有资源已提交退订，请重新选择')
      return false
    }

    // // 验证步骤3：检查变更状态
    // if (
    //   resources.every((item) => item.changeStatus) &&
    //   !resources.every((item) => item.changeStatus === 'un_change')
    // ) {
    //   ElMessage.warning('当前有资源已提交变更工单，请重新选择')
    //   return false
    // }

    // 验证步骤4：对于云主机和GPU云主机，还需要验证设备状态
    if (['ecs', 'gcs'].includes(resourceType)) {
      const msg: Record<string, string> = {
        ecs: '云主机',
        gcs: 'GPU云主机',
        evs: '云硬盘',
        obs: '对象存储',
        nat: 'NAT网关',
        slb: '负载均衡',
        eip: '弹性公网IP',
        vpn: 'VPN',
        rdsMysql: '云数据库',
      }
      if (!resources.every((item) => ['RUNING', 'STOPED'].includes(item.deviceStatus))) {
        ElMessage.warning(`仅运行中和关机的${msg[resourceType] || '资源'}可以进行退订`)
        return false
      }
    }

    return true
  }

  const getDgFormData = (sourceType: PublicResourceType, goodsItems: any[]) => {
    return {
      [`${sourceType}IdList`]: goodsItems.map((item) => item.goodsId),
      syncRecoveryIdList: goodsItems
        .filter((item) => item.syncRecovery)
        .map((item) => item.goodsId),
    }
  }
  return {
    validateRecycle,
    validateChange,
    validateUnsubscribe,
    getDgFormData,
  }
}
