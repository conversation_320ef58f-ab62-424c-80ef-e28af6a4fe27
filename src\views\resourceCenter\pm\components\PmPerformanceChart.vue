<template>
  <div id="PmPerformanceChart">
    <div class="header">
      <div class="header-title"></div>
      <div class="header-date">
        <el-date-picker
          @change="handleDateChange"
          v-model="selectedDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          size="small"
          :disabled-date="disabledDate"
          :shortcuts="timeShortcuts"
        />
      </div>
    </div>

    <!-- 空数据展示 -->
    <el-empty v-if="isEmpty" description="暂无数据" />

    <!-- 图表区域 -->
    <div v-else class="charts-section">
      <!-- 算力利用率图表 -->
      <div class="chart-container">
        <div class="chart-header">
          <span class="chart-title">
            <span class="chart-icon chart-icon-blue"></span>算力利用率
          </span>
        </div>
        <div ref="powerChart" class="chart-content"></div>
      </div>
      <!-- 显存利用率图表 -->
      <div class="chart-container" style="margin-top: 16px">
        <div class="chart-header">
          <span class="chart-title">
            <span class="chart-icon chart-icon-orange"></span>显存利用率
          </span>
        </div>
        <div ref="powerChart2" class="chart-content"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="PmPerformanceChart">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import { queryDeviceMetricsByIpApi } from '@/api/modules/zsMap'
import { ElMessage } from 'element-plus'

const props = defineProps({
  ip: {
    type: String,
    required: true,
  },
})

let chartInstance1: ECharts | null = null
let chartInstance2: ECharts | null = null

// 图表实例引用
const powerChart = ref<HTMLElement>()
const powerChart2 = ref<HTMLElement>()

// 时间选择
const selectedDate = ref<string[]>([])

// 空数据状态
const isEmpty = ref<boolean>(false)

// 获取设备性能数据
async function getDevicesMetricPercent() {
  const params: any = {
    dncIp: props.ip,
  }
  if (selectedDate.value.length > 0) {
    params.startTime = selectedDate.value[0]
    params.endTime = selectedDate.value[1]
  }

  try {
    const { entity }: any = await queryDeviceMetricsByIpApi(params)

    // 检查数据是否为空
    if (!entity || Object.keys(entity).length === 0) {
      isEmpty.value = true
      return
    }

    isEmpty.value = false
    const keys = Object.keys(entity)
    const tempXAxisData = entity[keys[0]].map((ele: any) => ele.gpuTime)
    const tempMemLineData: any[] = []
    const tempGpuLineData: any[] = []

    const chartColors1 = [
      '#5470C6', // 蓝色
      '#91CC75', // 绿色
      '#FAC858', // 黄色
      '#EE6666', // 红色
      '#73C0DE', // 浅蓝色
      '#3BA272', // 深绿色
      '#FC8452', // 橙色
      '#9A60B4', // 紫色
    ]
    const chartColors2 = [
      '#FF6B6B', // 珊瑚红
      '#4ECDC4', // 青绿色
      '#45B7D1', // 天蓝色
      '#96CEB4', // 薄荷绿
      '#FFEAA7', // 浅黄色
      '#DDA0DD', // 梅花色
      '#FFB347', // 桃橙色
      '#87CEEB', // 天空蓝
    ]

    keys.forEach((key: any, index: number) => {
      tempMemLineData.push({
        city: {
          name: key,
          color: chartColors1[index],
        },
        data: entity[key].map((ele: any) => ele.gpuUtilPercent),
      })
      tempGpuLineData.push({
        city: {
          name: key,
          color: chartColors2[index],
        },
        data: entity[key].map((ele: any) => ele.memUtilpercent),
      })
    })

    if (!powerChart.value || !powerChart2.value) return
    chartInstance1 = initChart(powerChart.value, '#5470C6', tempMemLineData, tempXAxisData)
    chartInstance2 = initChart(powerChart2.value, '#FF9800', tempGpuLineData, tempXAxisData)
  } catch (error) {
    console.error('获取性能数据失败:', error)
    isEmpty.value = true
  }
}

// 处理时间变化
const handleDateChange = (value: string[]) => {
  // 如果选择了时间范围，验证区间是否超过两个月
  if (value && value.length === 2) {
    if (!validateDateRange(value[0], value[1])) {
      // 如果验证失败，清空选择
      selectedDate.value = []
      return
    }
  }

  // 时间变化时重新加载数据
  getDevicesMetricPercent()
}

// 禁用未来日期
const disabledDate = (time: Date) => {
  if (time.getTime() > Date.now()) {
    return true
  }
  return false
}

// 两个月区间验证
const validateDateRange = (startDate: string, endDate: string): boolean => {
  const start = new Date(startDate)
  const end = new Date(endDate)

  // 计算两个月的毫秒数（按平均每月30天计算）
  const twoMonthsInMs = 2 * 30 * 24 * 60 * 60 * 1000

  if (end.getTime() - start.getTime() > twoMonthsInMs) {
    ElMessage.warning('时间区间不能超过两个月')
    return false
  }
  return true
}

// 时间快捷选项
const timeShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近两个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 60)
      return [start, end]
    },
  },
]

// 图表配置（完全参照Drawer.vue的实现）
const initChart = (chartRef: HTMLElement, color: string, dataSet?: any[], xAxisData?: any[]) => {
  const chart = echarts.init(chartRef, { renderer: 'svg' })
  if (!dataSet?.length) {
    chart.clear()
    chart.setOption({
      graphic: {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: '暂无数据',
          fontSize: 14,
          fill: '#999',
          textAlign: 'center',
          textVerticalAlign: 'middle',
        },
      },
      xAxis: {
        show: false,
      },
      yAxis: {
        show: false,
      },
      grid: {
        show: false,
      },
    })
    return chart
  }
  chart.clear()

  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 12,
      },
      formatter: function (params: any) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;z-index: 1000;">${params[0].axisValue}</div>`
        // params按照value降序排序
        params.sort((a: any, b: any) => b.value - a.value)
        params.forEach((param: any) => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
            <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${param.color}; margin-right: 8px;"></span>
            <span style="margin-left: auto; font-weight: bold;">${param.value}%</span>
          </div>`
        })
        return result
      },
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6',
        },
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
      axisLabel: {
        color: '#666',
        fontSize: 10,
        formatter: '{value}%',
      },
    },
    series: dataSet.map(({ city, data }: any) => ({
      name: city.name,
      data: data,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: {
        color: city.color,
        width: 2,
      },
      itemStyle: {
        color: city.color,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: `${city.color}60`,
            },
            {
              offset: 1,
              color: `${city.color}10`,
            },
          ],
        },
      },
    })),
  }

  chart.setOption(option)
  return chart
}

// 窗口调整大小处理
const handleResize = () => {
  chartInstance1?.resize()
  chartInstance2?.resize()
}

onMounted(() => {
  // 初始加载数据
  getDevicesMetricPercent()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance1?.dispose()
  chartInstance2?.dispose()
})
</script>

<style lang="scss" scoped>
#PmPerformanceChart {
  width: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-title {
      font-size: 16px;
      font-weight: bold;
    }

    .header-date {
      display: flex;
      align-items: center;
    }
  }

  .charts-section {
    gap: 16px;

    .chart-container {
      flex: 1;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e6e6e6;

      .chart-header {
        padding: 16px;
        text-align: center;
        background: white;
        border-bottom: 1px solid #f0f0f0;

        .chart-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .chart-icon {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;

            &.chart-icon-blue {
              background-color: #5470c6;
            }

            &.chart-icon-orange {
              background-color: #ff9800;
            }
          }
        }
      }

      .chart-content {
        background: white;
        height: 400px;
        width: 100%;
      }
    }
  }
}

:deep(.el-date-editor) {
  --el-date-editor-width: 320px;
  height: 32px;

  .el-input__inner {
    font-size: 12px;
    padding: 0 8px;
  }
}
</style>
