<template>
  <!-- GPU 信息卡片组件 -->
  <div class="gpu-card">
    <!-- 卡片头部：显示型号标签 -->
    <div class="gpu-card-header">
      <span class="dot"></span>
      <span class="title">{{ props.item.name }}</span>
    </div>
    <!-- 业务系统信息 -->
    <div class="gpu-card-system">
      <div class="label">业务系统：</div>
      <!-- 提示 -->
      <el-tooltip
        class="business-name-tooltip"
        effect="dark"
        :content="formatDisplayValue(props.item.businessName, 'businessName')"
        placement="top"
      >
        <div class="value">{{ formatDisplayValue(props.item.businessName, 'businessName') }}</div>
      </el-tooltip>
    </div>
    <!-- 双仪表盘区域 -->
    <div class="gpu-card-gauges">
      <!-- 显存利用率仪表盘 -->

      <div class="gauge-item" v-show="formatDisplayValue(props.item.memUtilpercent)">
        <div ref="memGaugeRef" class="gauge-echart" />
        <div class="gauge-label">显存利用率</div>
        <div class="gauge-value">
          {{ formatDisplayValue(props.item.memUtilpercent) }}<span class="percent">%</span>
        </div>
      </div>
      <div class="gauge-item" v-show="!formatDisplayValue(props.item.memUtilpercent)">
        <div class="empty-container">暂无数据</div>
      </div>

      <!-- 算力利用率仪表盘 -->
      <div class="gauge-item" v-show="formatDisplayValue(props.item.gpuUtilPercent)">
        <div ref="computeGaugeRef" class="gauge-echart" />
        <div class="gauge-label">算力利用率</div>
        <div class="gauge-value">
          {{ formatDisplayValue(props.item.gpuUtilPercent) }}<span class="percent">%</span>
        </div>
      </div>
      <div class="gauge-item" v-show="!formatDisplayValue(props.item.gpuUtilPercent)">
        <div class="empty-container-tal">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, nextTick, watchEffect } from 'vue'
import * as echarts from 'echarts'

// 1. 支持 props 传入 item
const props = defineProps<{
  item: {
    name: string
    businessName?: string
    memUtilpercent?: string
    gpuUtilPercent?: string
  }
}>()

// 2. 格式化显示函数
function formatDisplayValue(val: any, type?: 'businessName') {
  if (type === 'businessName') {
    if (val === null || val === undefined || val === '') {
      return '--'
    }
    return val
  }
  if ((val === null || val === undefined || val === '') && val !== 0) {
    return false
  }
  if (val == 0) {
    return '0'
  }
  return val
}

// 定义仪表盘DOM引用
const memGaugeRef = ref<HTMLDivElement | null>(null)
const computeGaugeRef = ref<HTMLDivElement | null>(null)
// 定义ECharts实例
let memChart: echarts.ECharts | null = null
let computeChart: echarts.ECharts | null = null

// 显存利用率仪表盘配置
const memOption = {
  grid: {
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  series: [
    {
      type: 'gauge',
      startAngle: 180,
      endAngle: 0,
      min: 0,
      max: 100,
      splitNumber: 4,
      radius: '100%',
      center: ['50%', '65%'],
      itemStyle: {
        color: '#FF5B5B',
        shadowColor: 'rgba(0,138,255,0.100)',
        shadowBlur: 1,
        shadowOffsetX: 2,
        shadowOffsetY: 2,
      },
      progress: {
        show: true,
        roundCap: true,
        width: 3,
      },
      pointer: {
        length: '45%',
        width: 2,
        offsetCenter: [0, '10%'],
      },
      axisLine: {
        roundCap: true,
        lineStyle: {
          width: 3,
        },
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        length: 3,
        distance: 1,
        lineStyle: {
          width: 1,
          color: '#999',
        },
      },
      axisLabel: {
        show: false,
      },
      title: {
        show: false,
      },
      detail: { show: false },
      data: [
        {
          value: 30,
        },
      ],
    },
  ],
}

// 算力利用率仪表盘配置
const computeOption = {
  grid: {
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  series: [
    {
      type: 'gauge',
      startAngle: 180,
      endAngle: 0,
      min: 0,
      max: 100,
      splitNumber: 4,
      radius: '100%',
      center: ['50%', '65%'],
      itemStyle: {
        color: '#FFA940',
        shadowColor: 'rgba(0,138,255,0.100)',
        shadowBlur: 1,
        shadowOffsetX: 2,
        shadowOffsetY: 2,
      },
      progress: {
        show: true,
        roundCap: true,
        width: 3,
      },
      pointer: {
        length: '45%',
        width: 2,
        offsetCenter: [0, '10%'],
      },
      axisLine: {
        roundCap: true,
        lineStyle: {
          width: 3,
        },
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        length: 3,
        distance: 1,
        lineStyle: {
          width: 1,
          color: '#999',
        },
      },
      axisLabel: {
        show: false,
      },
      title: {
        show: false,
      },
      detail: { show: false },
      data: [
        {
          value: 30,
        },
      ],
    },
  ],
}

// 组件挂载后初始化ECharts
watchEffect(() => {
  nextTick(() => {
    // 初始化显存仪表盘
    if (memGaugeRef.value) {
      memChart = echarts.init(memGaugeRef.value)
      memChart.setOption({
        ...memOption,
        series: [
          {
            ...memOption.series[0],
            data: [
              {
                value: props.item.memUtilpercent ?? 0,
              },
            ],
          },
        ],
      })
    }
    // 初始化算力仪表盘
    if (computeGaugeRef.value) {
      computeChart = echarts.init(computeGaugeRef.value)
      computeChart.setOption({
        ...computeOption,
        series: [
          {
            ...computeOption.series[0],
            data: [
              {
                value: props.item.gpuUtilPercent ?? 0,
              },
            ],
          },
        ],
      })
    }
  })
})

// 组件卸载前销毁ECharts实例，防止内存泄漏
onUnmounted(() => {
  if (memChart) {
    memChart.dispose()
    memChart = null
  }
  if (computeChart) {
    computeChart.dispose()
    computeChart = null
  }
})
</script>

<style lang="scss" scoped>
.gpu-card {
  position: relative;
  width: 100%;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.gpu-card-header {
  position: absolute;
  top: -10px;
  left: -3px;
  background: #388bff;
  color: #fff;
  border-radius: 20px;
  padding: 2px 18px 2px 10px;
  font-weight: bold;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.gpu-card-header .dot {
  width: 8px;
  height: 8px;
  background: #fff;
  border-radius: 50%;
  margin-right: 8px;
}
.gpu-card-header .title {
  font-style: italic;
  font-family: inherit;
}
.gpu-card-system {
  padding-top: 10px;
  padding-left: 10px;
  text-align: left;
  .label {
    color: #888;
    font-size: 8px;
  }
  .value {
    font-size: 10px;
    font-weight: 500;
    color: #333;
    // 不换行省略号
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 120px;
  }
}
.gpu-card-gauges {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
}
.gauge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
}
.gauge-echart {
  width: 100%;
  height: 60px;
  max-width: 160px;
  min-width: 60px;
}
.gauge-label {
  color: #888;
  font-size: 8px;
  margin-top: -15px;
  margin-left: -15px;
}
.gauge-value {
  font-size: 12px;
  margin-left: -15px;
  font-weight: bold;
  color: #222;
  margin-top: 2px;
  .percent {
    font-size: 8px;
    font-weight: 400;
    margin-left: 2px;
  }
}

.empty-container,
.empty-container-tal {
  padding-top: 30px;
  font-size: 12px;
  color: #999;
}
</style>
