<template>
  <div class="bottom-dialog-container">
    <!-- <img @click="closeBottomDialog" src="/images/computingPower/comPowerIconClose.png" alt="" /> -->
    <div class="bottom-dialog-content">
      <div class="bottom-dialog-main">
        <div class="bottom-dialog-title">
          <div>国产化</div>
          <div>
            <img src="/images/computingPower/comPowerDialogTitleBg.png" alt="" />
          </div>
        </div>
        <div class="bottom-dialog-tooltip">
          <div class="bottom-dialog-infrastructure">
            <div
              @click="changeInfrastructureTabValue('domesticDB')"
              class="bottom-dialog-infrastructure-item"
              :class="{ active: infrastructureTabValue == 'domesticDB' }"
            >
              国产化
            </div>
            <div
              @click="changeInfrastructureTabValue('foreignDB')"
              class="bottom-dialog-infrastructure-item"
              :class="{ active: infrastructureTabValue == 'foreignDB' }"
            >
              非国产化
            </div>
          </div>
          <div class="bottom-dialog-search">
            <el-select
              v-model="cloudValue"
              @change="changeCloudTypeFunc"
              clearable
              placeholder="请选择"
              :suffix-icon="CaretBottom"
              style="width: 100px"
            >
              <el-option
                v-for="item in cloudOption"
                :key="item.cloudName"
                :label="item.cloudName"
                :value="item.cloudName"
              />
            </el-select>
            <el-select
              v-model="platformTypeName"
              @change="changeCloudPlatformTypeNameFunc"
              placeholder="请选择云平台"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 140px"
            >
              <el-option
                v-for="item in platformTypeNamePoolList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
            <el-select
              v-model="resourcePoolValue"
              @change="changeResourcePooolFunc"
              clearable
              placeholder="请选择资源池"
              :suffix-icon="CaretBottom"
              style="width: 160px"
            >
              <el-option
                v-for="item in resourcePoolList"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
            <el-input
              v-model="searchForm.databaseName"
              placeholder="请输入名称"
              clearable
              style="width: 160px"
            ></el-input>
            <el-input
              v-model="searchForm.databaseIp"
              placeholder="请输入IP"
              clearable
              style="width: 160px"
            ></el-input>
            <el-select
              v-model="searchForm.databaseType"
              clearable
              placeholder="请选择类型"
              :suffix-icon="CaretBottom"
              style="width: 160px"
            >
              <el-option key="DM" label="DM" value="DM" />
              <el-option key="GoldenDB" label="GoldenDB" value="GoldenDB" />
              <el-option key="GaussDB" label="GaussDB" value="GaussDB" />
              <el-option key="Oracle" label="Oracle" value="Oracle" />
              <el-option key="MySQL" label="MySQL" value="MySQL" />
              <el-option key="Informix" label="Informix" value="Informix" />
            </el-select>
            <el-select
              v-model="searchForm.bsName"
              clearable
              filterable
              placeholder="请选择业务系统"
              :suffix-icon="CaretBottom"
              style="width: 160px"
            >
              <el-option v-for="item in bsNameList" :key="item" :label="item" :value="item" />
            </el-select>

            <el-button class="searchBtn" :icon="Search" @click="searchFunc"> 搜索 </el-button>
            <el-button class="exportBtn" @click="exportFile">
              <img src="/images/computingPower/comPowerIconExport.png" alt="" />
              导出
            </el-button>
          </div>
        </div>
        <div class="bottom-dialog-table">
          <el-auto-resizer>
            <template #default="{ height }">
              <el-table
                ref="tableRef"
                class="comPowerTable"
                :data="tableData"
                :height="height"
                style="width: 100%"
              >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column
                  :key="item.prop"
                  v-for="item in columns"
                  show-overflow-tooltip
                  :prop="item.prop"
                  :label="item.label"
                >
                  <template #default="scope">
                    <span
                      v-if="item.prop == 'relatedPool' || item.prop == 'bsName'"
                      class="c-comPower-table-cell-blue-theme"
                      >{{ scope.row[item.prop] || '--' }}</span>
                    <span v-else>{{ scope.row[item.prop] || '--' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-auto-resizer>
        </div>
        <div style="margin-top: 10px">
          <Pagination
            :pageable="pageable"
            :handle-size-change="handleSizeChange"
            :handle-current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CaretBottom, Search } from '@element-plus/icons-vue'

import { onMounted, reactive, type Ref, ref, watch } from 'vue'
import { ElTable } from 'element-plus'
import Pagination from './pagination.vue'
import { useDownload } from '@/hooks/useDownload'
import {
  getComPowerMapPageDataBaseService,
  getComPowerMapPageBaseDeviceHandResourcePool,
  downloadComPowerMapPageDataBaseService,
} from '@/api/modules/comPowerCenter'

import { getComputerPowerMapListBusinessApi } from '@/api/modules/computingPowerMap'
// 定义需要发出的事件类型
// const emit = defineEmits(['close'])
// table 实例
const tableRef = ref<InstanceType<typeof ElTable>>()

// 定义props接收父组件传递的参数
const props = defineProps({
  cloudListArr: {
    type: Array,
    required: false,
    default: () => [],
  },
  baseDeviceHandResourcePoolList: {
    type: Array,
    required: false,
    default: () => [],
  },
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

//业务系统列表
const bsNameList: any = ref([])
const searchForm = reactive({
  databaseName: '',
  databaseIp: '',
  databaseType: '',
  bsName: '',
})
// 定义类型/接口
interface columnsItem {
  prop: string
  label: string
}
//
const columns: Ref<columnsItem[]> = ref([
  {
    prop: 'databaseName',
    label: '名称',
  },
  {
    prop: 'databaseIp',
    label: '数据库IP',
  },
  {
    prop: 'listenerPort',
    label: '端口',
  },
  {
    prop: 'databaseType',
    label: '数据库类型',
  },
  {
    prop: 'platformTypeName',
    label: '所属云平台',
  },
  {
    prop: 'relatedPool',
    label: '所属物理资源池',
  },
  {
    prop: 'bsName',
    label: '关联业务系统',
  },
])
// 定义类型/接口
interface OptionItem {
  name: string
  instanceId: string
}
const getBsNameList = (type: string) => {
  // 获取业务系统
  getComputerPowerMapListBusinessApi({
    type: 'database_service',
    assetType: type,
  }).then((res: any) => {
    bsNameList.value = res.entity
  })
}
const resourcePoolList: Ref<OptionItem[]> = ref([])
onMounted(() => {
  // console.log(props)
  cloudValue.value = props.requestParams.cloudName
  platformTypeName.value = props.requestParams.platformTypeName

  cloudOption.value = props.cloudListArr
  cloudOption.value.forEach((item: any) => {
    if (item.cloudName == cloudValue.value) {
      platformTypeNamePoolList.value =
        (item.platformTypes && item.platformTypes.map((item: any) => item.platformTypeName)) || []
    }
  })
  getBsNameList('domesticDB')
  getBaseDeviceHandResourcePool()
  searchFunc()
  // getDataInfo()
})
// 导出文件
const exportFile = () => {
  let params: any = {
    cloudName: queryParams.cloudName,
    platformTypeName: queryParams.platformTypeName,
    relatedPool: queryParams.relatedPool,
    cityCode: props.requestParams.cityCode,
    dbCategory: infrastructureTabValue.value,
    databaseName: queryParams.databaseName,
    databaseIp: queryParams.databaseIp,
    databaseType: queryParams.databaseType,
    bsName: queryParams.bsName,
  }
  let temName = '国产化.xlsx'
  if (infrastructureTabValue.value == 'foreignDB') {
    temName = '非国产化.xlsx'
  }
  useDownload(downloadComPowerMapPageDataBaseService, temName, params)
}
const queryParams = reactive({
  cloudName: '',
  platformTypeName: '',
  relatedPool: '',
  databaseName: '',
  databaseIp: '',
  databaseType: '',
  bsName: '',
})
const searchFunc = () => {
  pageable.pageNum = 1
  queryParams.cloudName = cloudValue.value
  queryParams.platformTypeName = platformTypeName.value
  queryParams.relatedPool = resourcePoolValue.value
  queryParams.databaseName = searchForm.databaseName
  queryParams.databaseIp = searchForm.databaseIp
  queryParams.databaseType = searchForm.databaseType
  queryParams.bsName = searchForm.bsName
  getDataInfo()
}
const getDataInfo = () => {
  let params: any = {
    cloudName: queryParams.cloudName,
    platformTypeName: queryParams.platformTypeName,
    relatedPool: queryParams.relatedPool,
    cityCode: props.requestParams.cityCode,
    dbCategory: infrastructureTabValue.value,
    databaseName: queryParams.databaseName,
    databaseIp: queryParams.databaseIp,
    databaseType: queryParams.databaseType,
    bsName: queryParams.bsName,
    pageNum: pageable.pageNum,
    pageSize: pageable.pageSize,
  }
  getComPowerMapPageDataBaseService(params).then((res: any) => {
    if (res.code == 200) {
      tableData.value = res.entity.records || []
      pageable.total = res.entity.total || 0
    }
  })
}

//云平台
const platformTypeName = ref('')

const platformTypeNamePoolList = ref([])
// 切换云类型查询
const changeCloudTypeFunc = () => {
  // pageable.pageNum = 1
  platformTypeName.value = ''
  platformTypeNamePoolList.value = []
  cloudOption.value.forEach((item: any) => {
    if (item.cloudName == cloudValue.value) {
      platformTypeNamePoolList.value =
        (item.platformTypes && item.platformTypes.map((item: any) => item.platformTypeName)) || []
    }
  })
  //清空掉资源池信息
  resourcePoolValue.value = ''
  resourcePoolList.value = []
  getBaseDeviceHandResourcePool()
  // getDataInfo()
}
//切换云平台
const changeCloudPlatformTypeNameFunc = () => {
  // pageable.pageNum = 1
  resourcePoolValue.value = ''
  resourcePoolList.value = []
  getBaseDeviceHandResourcePool()
  // getDataInfo()
}
//切换资源池
const changeResourcePooolFunc = () => {
  // pageable.pageNum = 1
  // getDataInfo()
}
//查询物理资源池
const getBaseDeviceHandResourcePool = () => {
  let params = {
    cloudName: cloudValue.value,
    platformTypeName: platformTypeName.value,
    cityCode: props.requestParams.cityCode,
    pageNum: 1,
    pageSize: 9999,
  }
  getComPowerMapPageBaseDeviceHandResourcePool(params).then((res: any) => {
    if (res.code == 200) {
      resourcePoolList.value = res.entity.records || []
    }
  })
}
const pageable = reactive({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: 20,
  // 总条数
  total: 0,
})

const handleSizeChange = (val: number) => {
  pageable.pageNum = 1
  pageable.pageSize = val
  getDataInfo()
}
/**
 * @description 当前页改变
 * @param {Number} val 当前页
 * @return void
 * */
const handleCurrentChange = (val: number) => {
  pageable.pageNum = val
  getDataInfo()
}

const cloudValue = ref('')
const cloudOption: any = ref([])
const resourcePoolValue = ref('')
const tableData = ref([])

const infrastructureTabValue = ref('domesticDB')
const changeInfrastructureTabValue = (val: string) => {
  infrastructureTabValue.value = val
  cloudValue.value = ''
  platformTypeName.value = ''
  platformTypeNamePoolList.value = []
  resourcePoolValue.value = ''
  resourcePoolList.value = []
  searchForm.databaseName = ''
  searchForm.databaseIp = ''
  searchForm.databaseType = ''
  searchForm.bsName = ''

  getBsNameList(val)
  searchFunc()
  // getDataInfo()
}

// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)
// 关闭当前弹窗
// const closeBottomDialog = () => {
//   emit('close')
// }
</script>
<style scoped lang="scss">
.bottom-dialog-container {
  height: 100vh;
  // position: fixed;
  // right: 10px;
  // bottom: 10px;
  // width: calc(100% - 494px);
  // height: 436px;
  // z-index: 20;
  box-sizing: border-box;
  //height: 367px;
  & > img {
    position: absolute;
    top: 0;
    right: 10px;
    cursor: pointer;
  }
  .bottom-dialog-content {
    // position: absolute;
    // left: 0;
    // bottom: 0;
    // width: 100%;
    // height: 400px;
    height: 100%;
    padding: 12px 9px 11px 10px;
    box-shadow: 0px 0px 5px 0px #09155c;
    border-radius: 1px;
    box-sizing: border-box;
    background-color: rgba(170, 198, 245, 0.3);
    .bottom-dialog-main {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      padding: 8px 15px;
      border-radius: 1px;
      box-sizing: border-box;
      background: linear-gradient(0deg, rgba(124, 166, 238, 0.92), rgba(10, 52, 103, 0.92));
    }
    //opacity: 0.92;
    .bottom-dialog-title {
      //display: flex;
      div {
        display: inline-block;
        font-size: 18px;
        color: #ffffff;
        width: calc(100% - 100px);
        vertical-align: top;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei';
        img {
          width: 100%;
        }
      }
      & > div:nth-child(1) {
        width: 70px;
        margin-right: 12px;
      }
    }
    .bottom-dialog-tooltip {
      box-sizing: border-box;
      margin-bottom: 10px;
      border-bottom: 2px solid #3161b4;
      padding-top: 4px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      align-content: stretch;
      flex-direction: column;
      flex-wrap: nowrap;
      .bottom-dialog-infrastructure {
        //display: inline-block;
        //margin-right: 139px;
        vertical-align: bottom;
        .bottom-dialog-infrastructure-item {
          width: 120px;
          height: 25px;
          line-height: 25px;
          font-size: 16px;
          color: #7f91a7;
          display: inline-block;
          margin-right: 21px;
          background: url('/images/computingPower/comPowerInfrastructureTabBg.png') no-repeat 0 0;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &.active {
            color: #ffffff;
            background: url('/images/computingPower/comPowerInfrastructureTabActiveBg.png')
              no-repeat 0 0;
            background-size: 100% 100%;
          }
        }
      }
      .bottom-dialog-search {
        //display: inline-block;
        width: 100%;
        text-align: right;
        vertical-align: top;
        margin: 20px 40px 20px 0;
        //position: absolute;
        //right: 0;
        //top: 0;
        .el-select {
          background: transparent;
          font-size: 15px;
          color: #ffffff;
          margin-right: 13px;
        }
        .el-input {
          background: transparent;
          font-size: 14px;
          color: #ffffff;
          margin-right: 13px;
          height: 32px;
        }
        .searchBtn {
          min-width: 80px;
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          img {
            margin-right: 8px;
          }
        }
        .exportBtn {
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          img {
            margin-right: 8px;
          }
        }
      }
    }
    .bottom-dialog-infrastructure-sub-tab {
      text-align: center;
      .bottom-dialog-infrastructure-sub-item {
        display: inline-block;
        margin: 10px 23px;
        background: #3c5784;
        border-radius: 2px;
        font-size: 16px;
        color: #ffffff;
        padding: 3px 16px;
        cursor: pointer;
        &.active {
          background: linear-gradient(-90deg, #2d78d3, #0f87a3, #2064b7);
        }
      }
    }
    .bottom-dialog-table {
      flex: 1;
      overflow: hidden;
      .el-table {
        background: transparent;
        th.el-table__cell {
          background: transparent;
        }
      }
    }
  }
}
</style>
<style>
.bottom-dialog-search .el-input .el-input__wrapper {
  background: transparent;
  box-shadow: none;
  border-radius: 2px;
  border: 1px solid #3069b0;
  font-size: 14px;
  color: #ffffff;
}
.bottom-dialog-search .el-input .el-input__wrapper .el-input__inner {
  color: #ffffff;
}
.bottom-dialog-search .el-input .el-input__wrapper .el-input__inner::placeholder {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}
</style>
