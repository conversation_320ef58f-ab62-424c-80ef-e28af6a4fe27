import { CHANGE_EVENT } from 'element-plus'
import { buildProps, isNumber, isString } from '@/utils/index'
import type Steps from './steps.vue'
import type { ExtractPropTypes } from 'vue'

export const stepsProps = buildProps({
  /**
   * @description the spacing of each step, will be responsive if omitted. Supports percentage.
   */
  space: {
    type: [Number, String],
    default: '',
  },
  isBranch: {
    type: Boolean,
    default: false,
  },
  toBeRender: {
    type: Boolean,
    default: true,
  },
  /**
   * @description current activation step
   */
  active: {
    type: [Number, String],
    default: 0,
  },
  /**
   * @description display direction
   */
  direction: {
    type: String,
    default: 'horizontal',
    values: ['horizontal', 'vertical'],
  },
  /**
   * @description center title and description
   */
  alignCenter: {
    type: Boolean,
  },
  /**
   * @description whether to apply simple theme
   */
  simple: {
    type: Boolean,
  },
  /**
   * @description status of end step
   */
  finishStatus: {
    type: String,
    values: ['wait', 'process', 'finish', 'error', 'success'],
    default: 'finish',
  },
  /**
   * @description status of current step
   */
  processStatus: {
    type: String,
    values: ['wait', 'process', 'finish', 'error', 'success'],
    default: 'process',
  },
  /**
   * @description whether to apply gray mode to all nodes
   */
  grayMode: {
    type: Boolean,
    default: false,
  },
} as const)
export type StepsProps = ExtractPropTypes<typeof stepsProps>

export const stepsEmits = {
  [CHANGE_EVENT]: (newVal: number | string, oldVal: number | string) =>
    [newVal, oldVal].every((e) => isNumber(e) || isString(e)),
}
export type StepsEmits = typeof stepsEmits

export type StepsInstance = InstanceType<typeof Steps>
