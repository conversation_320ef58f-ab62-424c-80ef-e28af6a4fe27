import { reactive, type Ref } from 'vue'

export interface IEcsModel {
  // 主机名称
  instanceName: string
  // 云类型
  catalogueDomainCode: string
  catalogueDomainName: string
  // 云平台
  domainCode: string
  domainName: string
  // 资源池
  resourcePoolId: string
  resourcePoolName: string
  regionCode: string
  // 可用区
  azCode: string
  azName: string
  azId: string
  /** 实例规格 */
  ecs: any[] | string
  ecsLabel: string
  /** 系统 */
  imageOs: any[]
  imageOsLabel: string
  /** 系统盘 */
  sysDisk: any[]
  sysDiskLabel: string
  /** 是否挂载数据盘 */
  isMountEvs: '0' | '1'
  /** 数据盘 */
  evs: any[][]
  evsLabel: string
  /** 是否绑定公网IP */
  isBindPublicNetworkIp: '0' | '1'
  /** 带宽 */
  eipAttrId: number
  eipValue: number
  /** 开通数量 */
  numbers: number
  /** 申请时长 */
  time: number
  timeLabel: string
  /** 产品类型 */
  productType: 'ecs'
  /** 产品名称 */
  goodsName: '云主机'
  goodsId: string
  /** 表单实例 提交需删 */
  ref?: Ref<any>
  /** 开通方式 */
  openType: 'online' | 'offline'
}

export interface IGcsModel {
  // 主机名称
  instanceName: string
  // 云类型
  catalogueDomainCode: string
  catalogueDomainName: string
  // 云平台
  domainCode: string
  domainName: string
  // 资源池
  resourcePoolId: string
  resourcePoolName: string
  regionCode: string
  // 可用区
  azCode: string
  azName: string
  azId: string
  /** 实例规格 */
  gcs: any[] | string
  gcsLabel: string
  /** 系统 */
  imageOs: any[]
  imageOsLabel: string
  /** 系统盘 */
  sysDisk: any[]
  sysDiskLabel: string
  /** 是否挂载数据盘 */
  isMountEvs: '0' | '1'
  /** 数据盘 */
  evs: any[][]
  evsLabel: string
  /** 是否绑定公网IP */
  isBindPublicNetworkIp: '0' | '1'
  /** 带宽 */
  eipAttrId: number
  eipValue: number
  /** 开通数量 */
  numbers: number
  /** 申请时长 */
  time: number
  timeLabel: string
  /** 产品类型 */
  productType: 'gcs'
  /** 产品名称 */
  goodsName: 'GPU云主机'
  goodsId: string
  /** 表单实例 提交需删 */
  ref?: Ref<any>
  /** 开通方式 */
  openType: 'online' | 'offline'
}
export interface IBmsModel {
  // 主机名称
  instanceName: string
  // 资源池
  resourcePoolId: string
  resourcePoolName: string
  regionCode: string
  /** 实例规格 */
  bms: any[]
  bmsLabel: string
  /** 系统 */
  imageOs: any[]
  imageOsLabel: string
  /** 系统盘 */
  sysDisk: any[]
  sysDiskLabel: string
  /** 是否挂载数据盘 */
  isMountEvs: '0' | '1'
  /** 数据盘 */
  evs: any[][]
  evsLabel: string
  /** 是否绑定公网IP */
  isBindPublicNetworkIp: '0' | '1'
  /** 带宽 */
  eipAttrId: number
  eipValue: number
  /** 开通数量 */
  numbers: number
  /** 申请时长 */
  time: number
  timeLabel: string
  /** 产品类型 */
  productType: 'bms'
  /** 产品名称 */
  goodsName: '裸金属'
  goodsId: string
  /** 表单实例 提交需删 */
  ref?: Ref<any>
  /** 开通方式 */
  openType: 'offline'
}
export interface IGmsModel {
  // 主机名称
  instanceName: string
  // 资源池
  resourcePoolId: string
  resourcePoolName: string
  regionCode: string
  /** 实例规格 */
  gms: any[]
  gmsLabel: string
  /** 系统 */
  imageOs: any[]
  imageOsLabel: string
  /** 系统盘 */
  sysDisk: any[]
  sysDiskLabel: string
  /** 是否挂载数据盘 */
  isMountEvs: '0' | '1'
  /** 数据盘 */
  evs: any[][]
  evsLabel: string
  /** 是否绑定公网IP */
  isBindPublicNetworkIp: '0' | '1'
  /** 带宽 */
  eipAttrId: number
  eipValue: number
  /** 开通数量 */
  numbers: number
  /** 申请时长 */
  time: number
  timeLabel: string
  /** 产品类型 */
  productType: 'gms'
  /** 产品名称 */
  goodsName: 'GPU裸金属'
  goodsId: string
  /** 表单实例 提交需删 */
  ref?: Ref<any>
  /** 开通方式 */
  openType: 'offline'
}
export interface IMysqlModel {
  /// 主机名称
  instanceName: string
  // 资源池
  resourcePoolId: string
  resourcePoolName: string
  regionCode: string
  /** 实例规格 */
  mysql: any[]
  mysqlLabel: string
  /** 系统 */
  imageOs: any[]
  imageOsLabel: string
  /** 系统盘 */
  sysDisk: any[]
  sysDiskLabel: string
  /** 是否挂载数据盘 */
  isMountEvs: '0' | '1'
  /** 数据盘 */
  evs: any[][]
  evsLabel: string
  /** 是否绑定公网IP */
  isBindPublicNetworkIp: '0' | '1'
  /** 带宽 */
  eipAttrId: number
  eipValue: number
  /** 开通数量 */
  numbers: number
  /** 申请时长 */
  time: number
  timeLabel: string
  /** 产品类型 */
  productType: 'mysql'
  /** 产品名称 */
  goodsName: 'mysql'
  goodsId: string
  /** 表单实例 提交需删 */
  ref?: Ref<any>
  /** 开通方式 */
  openType: 'offline'
  version: string
  /** 数据库类型 */
  mysqlType: string
}
export interface IRedisModel {
  // 主机名称
  instanceName: string
  // 资源池
  resourcePoolId: string
  resourcePoolName: string
  regionCode: string
  /** 实例规格 */
  redis: any[]
  redisLabel: string
  /** 系统 */
  imageOs: any[]
  imageOsLabel: string
  /** 系统盘 */
  sysDisk: any[]
  sysDiskLabel: string
  /** 是否挂载数据盘 */
  isMountEvs: '0' | '1'
  /** 数据盘 */
  evs: any[][]
  evsLabel: string
  /** 是否绑定公网IP */
  isBindPublicNetworkIp: '0' | '1'
  /** 带宽 */
  eipAttrId: number
  eipValue: number
  /** 开通数量 */
  numbers: number
  /** 申请时长 */
  time: number
  timeLabel: string
  /** 产品类型 */
  productType: 'redis'
  /** 产品名称 */
  goodsName: 'redis'
  goodsId: string
  /** 表单实例 提交需删 */
  ref?: Ref<any>
  /** 开通方式 */
  openType: 'offline'
  version: string
  redisType: string
}
export interface IPostgresModel {
  // 主机名称
  instanceName: string
  // 资源池
  resourcePoolId: string
  resourcePoolName: string
  regionCode: string
  /** 实例规格 */
  postgreSql: any[]
  postgreSqlLabel: string
  /** 系统 */
  imageOs: any[]
  imageOsLabel: string
  /** 系统盘 */
  sysDisk: any[]
  sysDiskLabel: string
  /** 是否挂载数据盘 */
  isMountEvs: '0' | '1'
  /** 数据盘 */
  evs: any[][]
  evsLabel: string
  /** 是否绑定公网IP */
  isBindPublicNetworkIp: '0' | '1'
  /** 带宽 */
  eipAttrId: number
  eipValue: number
  /** 开通数量 */
  numbers: number
  /** 申请时长 */
  time: number
  timeLabel: string
  /** 产品类型 */
  productType: 'postgreSql'
  /** 产品名称 */
  goodsName: 'postgreSql'
  goodsId: string
  /** 表单实例 提交需删 */
  ref?: Ref<any>
  /** 开通方式 */
  openType: 'offline'
  version: string
  /** 数据库类型 */
  postgreSqlType: string
}

export interface IModel {
  basicModel: {
    billNo: string
    customerName: string
    contactPerson: string
    customerManager: string
    customerCode: string
    contactPhone: string
    managerPhone: string
    workOrderTitle: string
    orderDesc: string
    files: string[]
    contractCost: number
    branchLeader: string
    branchLeaderName: string
    customerId: string
    ref?: Ref<any>
  }
  goodsListModel: (
    | IEcsModel
    | IGcsModel
    | IBmsModel
    | IGmsModel
    | IMysqlModel
    | IRedisModel
    | IPostgresModel
  )[]
}

export function initModel(
  type: 'ecs' | 'gcs' | 'bms' | 'gms' | 'mysql' | 'redis' | 'postgreSql',
  openType: 'online' | 'offline' = 'online',
): IEcsModel | IGcsModel | IBmsModel | IGmsModel | IMysqlModel | IRedisModel | IPostgresModel {
  switch (type) {
    case 'ecs':
      return {
        instanceName: '',
        catalogueDomainCode: '',
        catalogueDomainName: '',
        domainCode: '',
        domainName: '',
        resourcePoolId: '',
        resourcePoolName: '',
        regionCode: '',
        azCode: '',
        azName: '',
        azId: '',
        ecs: '',
        ecsLabel: '',
        imageOs: [],
        imageOsLabel: '',
        sysDisk: [],
        sysDiskLabel: '',
        isMountEvs: '0',
        evs: [[]],
        evsLabel: '',
        isBindPublicNetworkIp: '0',
        eipAttrId: 0,
        eipValue: 0,
        numbers: 0,
        time: 0,
        timeLabel: '',
        productType: 'ecs',
        goodsName: '云主机',
        goodsId: '',
        openType: openType,
      }
    case 'gcs':
      return {
        instanceName: '',
        catalogueDomainCode: '',
        catalogueDomainName: '',
        domainCode: '',
        domainName: '',
        resourcePoolId: '',
        resourcePoolName: '',
        regionCode: '',
        azCode: '',
        azName: '',
        azId: '',
        gcs: '',
        gcsLabel: '',
        imageOs: [],
        imageOsLabel: '',
        sysDisk: [],
        sysDiskLabel: '',
        isMountEvs: '0',
        evs: [[]],
        evsLabel: '',
        isBindPublicNetworkIp: '0',
        eipAttrId: 0,
        eipValue: 0,
        numbers: 0,
        time: 0,
        timeLabel: '',
        productType: 'gcs',
        goodsName: 'GPU云主机',
        goodsId: '',
        openType: openType,
      }
    case 'bms':
      return {
        instanceName: '',
        resourcePoolId: '',
        resourcePoolName: '',
        regionCode: '',
        bms: [0, 0],
        bmsLabel: '',
        imageOs: [],
        imageOsLabel: '',
        sysDisk: [],
        sysDiskLabel: '',
        isMountEvs: '0',
        evs: [[]],
        evsLabel: '',
        isBindPublicNetworkIp: '0',
        eipAttrId: 0,
        eipValue: 0,
        numbers: 0,
        time: 0,
        timeLabel: '',
        productType: 'bms',
        goodsName: '裸金属',
        goodsId: '',
        openType: 'offline',
      }
    case 'gms':
      return {
        instanceName: '',
        resourcePoolId: '',
        resourcePoolName: '',
        regionCode: '',
        gms: [0, 0, ''],
        gmsLabel: '',
        imageOs: [],
        imageOsLabel: '',
        sysDisk: [],
        sysDiskLabel: '',
        isMountEvs: '0',
        evs: [[]],
        evsLabel: '',
        isBindPublicNetworkIp: '0',
        eipAttrId: 0,
        eipValue: 0,
        numbers: 0,
        time: 0,
        timeLabel: '',
        productType: 'gms',
        goodsName: 'GPU裸金属',
        goodsId: '',
        openType: 'offline',
      }
    case 'mysql':
      return {
        instanceName: '',
        resourcePoolId: '',
        resourcePoolName: '',
        regionCode: '',
        mysql: [0, 0],
        mysqlLabel: '',
        imageOs: [],
        imageOsLabel: '',
        sysDisk: [],
        sysDiskLabel: '',
        isMountEvs: '0',
        evs: [[]],
        evsLabel: '',
        isBindPublicNetworkIp: '0',
        eipAttrId: 0,
        eipValue: 0,
        numbers: 0,
        time: 0,
        timeLabel: '',
        productType: 'mysql',
        goodsName: 'mysql',
        goodsId: '',
        openType: 'offline',
        version: '',
        mysqlType: '标准型',
      }
    case 'redis':
      return {
        instanceName: '',
        resourcePoolId: '',
        resourcePoolName: '',
        regionCode: '',
        redis: [0, 0],
        redisLabel: '',
        imageOs: [],
        imageOsLabel: '',
        sysDisk: [],
        sysDiskLabel: '',
        isMountEvs: '0',
        evs: [[]],
        evsLabel: '',
        isBindPublicNetworkIp: '0',
        eipAttrId: 0,
        eipValue: 0,
        numbers: 0,
        time: 0,
        timeLabel: '',
        productType: 'redis',
        goodsName: 'redis',
        goodsId: '',
        openType: 'offline',
        version: '',
        redisType: '标准型',
      }
    case 'postgreSql':
      return {
        instanceName: '',
        resourcePoolId: '',
        resourcePoolName: '',
        regionCode: '',
        postgreSql: [0, 0],
        postgreSqlLabel: '',
        imageOs: [],
        imageOsLabel: '',
        sysDisk: [],
        sysDiskLabel: '',
        isMountEvs: '0',
        evs: [[]],
        evsLabel: '',
        isBindPublicNetworkIp: '0',
        eipAttrId: 0,
        eipValue: 0,
        numbers: 0,
        time: 0,
        timeLabel: '',
        productType: 'postgreSql',
        goodsName: 'postgreSql',
        goodsId: '',
        openType: 'offline',
        version: '',
        postgreSqlType: '标准型',
      }
  }
}

export const useBasicModel = () => {
  return reactive<IModel>({
    basicModel: {
      billNo: '',
      customerName: '',
      contactPerson: '',
      customerManager: '',
      customerCode: '',
      contactPhone: '',
      managerPhone: '',
      workOrderTitle: '',
      orderDesc: '',
      files: [],
      contractCost: 0,
      branchLeader: '',
      branchLeaderName: '',
      customerId: '',
    },
    goodsListModel: [],
  })
}
