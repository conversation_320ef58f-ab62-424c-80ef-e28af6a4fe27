<template>
  <Container no-scale>
    <div class="page-content">
      <div class="header">
        <Breadcrumb />
      </div>
      <div class="top-section">
        <div class="left-panel">
          <AccountDetailCard :data="customData" />
          <AccountStatsCards :data="customData" />
        </div>
        <div class="right-panel">
          <AccountTop5Cards />
        </div>
      </div>
      <div class="product-list">
        <ProductList />
      </div>
    </div>
  </Container>
</template>

<script setup lang="ts">
import Breadcrumb from './components/Breadcrumb.vue'
import Container from './components/Container.vue'
import AccountDetailCard from './components/AccountDetailCard.vue'
import AccountStatsCards from './components/AccountStatsCards.vue'
import AccountTop5Cards from './components/AccountTop5Cards.vue'
import ProductList from './components/ProductList.vue'
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getCustomDetailApi } from '@/api/modules/computingPowerMap'

const route = useRoute()
const customId = (route.query.customId as string) || ''
const customData = ref<any>(null)

const fetchCustomData = async () => {
  if (!customId) return
  const res = await getCustomDetailApi({ customId })
  customData.value = res.entity
}
onMounted(fetchCustomData)
</script>

<style scoped>
.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.header {
  margin-bottom: 16px;
}
.top-section {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}
.left-panel {
  flex: 1;
}
.right-panel {
  flex: 1;
  /* display: flex;
  flex-direction: column;
  gap: 18px; */
}
.product-section {
  margin-top: 8px;
}
.product-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}
:deep(.el-breadcrumb) {
  font-size: 14px;
}
</style>
