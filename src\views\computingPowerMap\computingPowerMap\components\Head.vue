<template>
  <header class="dashboard-header">
    <div class="dashboard-header-left">
      <img src="/images/computingPower/comPowerLocationIcon.png" alt="" />
      <span>{{ cityName }}</span>
      <span> {{ year }}.{{ month }}.{{ day }} {{ hours }}:{{ minutes }} </span>
    </div>
    <div class="dashboard-title-area">
      <div class="dashboard-title">
        <span style="margin-left: 6px"></span>
      </div>
    </div>

    <div class="dashboard-header-right" @click="changeOperaOverview">
      <img src="/images/computingPower/changeIcon.png" alt="" />
      <span>{{ props.name }}</span>
    </div>
  </header>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

// 定义props
interface Props {
  city?: string
  path?: string
  name?: string
}

const props = withDefaults(defineProps<Props>(), {
  city: 'zhejiang',
  path: '/computingPowerMapDetail',
  name: '算力地图切换',
})

const changeOperaOverview = () => {
  router.push({
    path: props.path,
    query: { city: props.city },
  })
  // 判断当前是省市地图，还是区县地图，
}
// 当前时间
const currentTime = ref(new Date())
const cityName = ref('杭州')
// 时间各部分
const year = computed(() => currentTime.value.getFullYear())
const month = computed(() => (currentTime.value.getMonth() + 1).toString().padStart(2, '0'))
const day = computed(() => currentTime.value.getDate().toString().padStart(2, '0'))
const hours = computed(() => currentTime.value.getHours().toString().padStart(2, '0'))
const minutes = computed(() => currentTime.value.getMinutes().toString().padStart(2, '0'))

// 设置定时器更新时间
let timer: any = null
// 浙江省城市拼音到中文的映射
const cityMap: any = ref({
  hangzhou: '杭州',
  ningbo: '宁波',
  wenzhou: '温州',
  jiaxing: '嘉兴',
  huzhou: '湖州',
  shaoxing: '绍兴',
  jinhua: '金华',
  quzhou: '衢州',
  zhoushan: '舟山',
  taizhou: '台州',
  lishui: '丽水',
})
onMounted(() => {
  fetch('https://ipapi.co/json/')
    .then((response) => response.json())
    .then((data) => {
      setTimeout(() => {
        const pinyin: any = data.city.trim().toLowerCase()

        // 查找对应的中文名称
        if (cityMap.value[pinyin]) {
          cityName.value = cityMap.value[pinyin]
        } else {
          cityName.value = '浙江'
        }
      }, 1000)
    })
    .catch((error: any) => {
      console.log(error)
    })

  timer = setInterval(() => {
    currentTime.value = new Date()
  }, 1000)
})

onBeforeUnmount(() => {
  if (timer) clearInterval(timer)
})
</script>
<style scoped>
@font-face {
  font-family: 'PangMenZhengDao';
  src: url('/fonts/PangMenZhengDao.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.dashboard-header {
  display: flex;
  align-items: baseline;
  padding: 0 40px;
  z-index: 2;
  background: url('/images/computingPower/comPowerMapHeader.png') no-repeat center center;
  background-size: cover;
  justify-content: space-between;
}
.dashboard-header-left img {
  display: inline-block;
  vertical-align: middle;
}
.dashboard-header-left span {
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
  font-size: 14px;
  color: #033176;
}
.dashboard-header-right {
  cursor: pointer;
}
.dashboard-header-right img {
  width: 26px;
  display: inline-block;
  vertical-align: middle;
}
.dashboard-header-right span {
  display: inline-block;
  vertical-align: middle;
  margin-left: 8px;
  font-size: 14px;
  color: #033176;
}
.dashboard-title-area {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.dashboard-title {
  font-size: 42px;
  color: #fff;
  font-weight: 100;
  letter-spacing: 12px;
  text-shadow: 0 2px 8px rgba(42, 107, 255, 0.1);
  height: 120px;
  line-height: 1;
  font-family: 'PangMenZhengDao', 'Microsoft YaHei', Arial, sans-serif;
}
</style>
