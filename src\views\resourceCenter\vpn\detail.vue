<template>
  <div id="VpnDetail" class="table-box">
    <sl-page-header
      title="VPN详情"
      :icon="{
        class: 'page_VPN',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="isPublic ? publicFormOptions : formOptions"
        >
        </sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive, computed } from 'vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getResourceDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')
const isPublic = computed(() => route.query.sourceType === 'DG')

const detailData = reactive<any>({
  deviceName: '',
  businessSysName: '',
  catalogueDomainName: '',
  domainName: '',
  resourcePoolName: '',
  azName: '',
  vpcName: '',
  subnetName: '',
  eip: '',
  bandWidth: '',
  spec: '',
  tenantName: '',
  orderCode: '',
  effectiveTime: '',
  expireTime: '',
  deviceStatusCn: '',
  applyUserName: '',
  applyTime: '',
  billId: '',
  deviceStatus: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: 'VPN名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
        hidden: computed(() => route.query.sourceType === 'DG'),
      },
      {
        label: '云平台',
        type: 'text',
        key: 'domainName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: 'VPC',
        type: 'text',
        key: 'vpcName',
        span: 8,
      },
      {
        label: '子网',
        type: 'text',
        key: 'subnetName',
        span: 8,
      },
      {
        label: '公网IP',
        type: 'text',
        key: 'eip',
        span: 8,
      },
      {
        label: '带宽',
        type: 'text',
        key: 'bandWidth',
        span: 8,
      },
      {
        label: '最大客户端数',
        type: 'text',
        key: 'spec',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'effectiveTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
    ],
  },
])

const publicFormOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: 'VPN名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '云平台',
        type: 'text',
        key: 'domainName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: 'VPC',
        type: 'text',
        key: 'vpcName',
        span: 8,
      },
      {
        label: '子网',
        type: 'text',
        key: 'subnetName',
        span: 8,
      },
      {
        label: '公网IP',
        type: 'text',
        key: 'eip',
        span: 8,
      },
      {
        label: '带宽',
        type: 'text',
        key: 'bandWidth',
        span: 8,
      },
      {
        label: '最大客户端数',
        type: 'text',
        key: 'spec',
        span: 8,
      },
      {
        label: '订单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '订购时间',
        type: 'text',
        key: 'effectiveTime',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '订购人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getResourceDetail({
    id: resourceId.value,
    type: 'vpn',
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/vpnList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}
</style>
