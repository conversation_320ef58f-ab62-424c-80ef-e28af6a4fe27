<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getCorporateResourceList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="goodsOrderId"
  >
  </SlProTable>
  <SlDialog
    v-model="updatePasswordDialogVisible"
    title="修改密码"
    width="600px"
    destroy-on-close
    confirm-text="提交"
    @close="handleUpdatePasswordClose"
    @confirm="handleUpdatePasswordConfirm"
  >
    <sl-form
      ref="updatePasswordFormRef"
      :options="updatePasswordFormOptions"
      v-model="updatePasswordFormModel"
    >
    </sl-form>
    <div class="warning">
      提醒：密码格式必须包含字母和数字，且长度在8到26个字符之间，只可包含特殊字符()~!@#$%^&*-+=|{}[]∶;,.?/
    </div>
  </SlDialog>
  <SlDialog
    v-model="subnetDialogVisible"
    title="子网信息"
    width="1000px"
    :show-cancel="false"
    @confirm="subnetDialogVisible = false"
    destroy-on-close
  >
    <SubnetList :subnet-list="currentSubnetList" :hide-network-plane="true" />
  </SlDialog>

  <!-- 资源变更弹窗 -->
  <ResourceChangeDialog
    v-model:visible="changeDialogVisible"
    resource-type="gcs"
    :selected-resources="selectedResources"
    :allowed-change-types="allowedChangeTypes"
    @confirm="handleConfirm"
    source-type="DG"
  />

  <!-- 资源延期弹窗 -->
  <ResourceChangeDialog
    v-model:visible="delayDialogVisible"
    resource-type="gcs"
    :selected-resources="selectedResources"
    :is-delay="true"
    @confirm="handleConfirm"
  />

  <!-- 安全组弹窗 -->
  <SlDialog
    v-model="securityGroupDialog"
    title="选择安全组"
    width="1000px"
    @close="securityGroupDialog = false"
    :show-confirm="false"
    cancel-text="关闭"
    destroy-on-close
  >
    <PublicDataList
      ref="securityGroupListRef"
      :query-params="securityGroupQueryParams"
      :is-select-mode="true"
      @selected="handleSecurityGroupSelected"
    />
  </SlDialog>
  <!-- 虚拟网卡弹窗 -->
  <SlDialog
    v-model="virtualNicDialogVisible"
    title="选择虚拟网卡"
    width="1200px"
    @close="virtualNicDialogVisible = false"
    :show-confirm="false"
    cancel-text="关闭"
    destroy-on-close
  >
    <VirtualNicPublicDataList
      ref="virtualNicListRef"
      :query-params="virtualNicQueryParams"
      :is-select-mode="true"
      @selected="handleVirtualNicSelected"
    />
  </SlDialog>
  <ChangeImageDialog
    v-model:visible="changeImageDialogVisible"
    :resource-data="currentImageChangeRow"
    @success="handleChangeImageSuccess"
  />
  <corporatePropertyChange
    v-if="propertyChangeDrawer"
    :goods-list="goodsList"
    type="gcs"
    v-model="propertyChangeDrawer"
    @confirm="handlePropertyChangeConfirm"
  />
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import {
  getCorporateResourceList,
  dgRecoveryWorkOrderCreate,
  getSubnetList,
  securityGroupOperate,
  virtualNicOperate,
} from '@/api/modules/resourecenter'
import {
  useResourceOperationFormModel,
  useUpdatePasswordFormOptions,
  executeResourceOperationConfirm,
  useResourceOperationFormClear,
  type ResourceOperationModel,
} from '../hooks/useResourceOperationModels'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import SlMessage from '@/components/base/SlMessage'
import SubnetList from './components/subnetList.vue'
import type { ResourceChangeType } from '../types'
import { ElMessage, ElMessageBox, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import { useVerifyThePromptBox } from '@/hooks/useVerifyThePromptBox'
import PublicDataList from '../securityGroup/components/PublicDataList.vue'
import VirtualNicPublicDataList from '../virtualNic/components/PublicDataList.vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { useConsole } from '../hooks/useConsole'
import ChangeImageDialog from '../ecs/components/ChangeImageDialog.vue'
import { useChangeImage } from '../hooks/useChangeImage'

import corporatePropertyChange from '@/views/corporatePropertyChange/index.vue'
import { transformData } from '@/views/corporatePropertyChange/propertyChange'
import type { ICartsModel } from '@/views/corporatePropertyChange/propertyChange'

const { queryParams, isEvsSelect, isBindDialog, isHiddenSelection, hideOperations } = defineProps<{
  queryParams: any
  isEvsSelect?: boolean
  isBindDialog?: boolean
  isHiddenSelection?: boolean
  hideOperations?: boolean
}>()

const { validateResources } = useResourceChange()

const emit = defineEmits(['currentChange', 'selectDevice'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

const opColumn: [ColumnProps<any>] | [] =
  isEvsSelect || hideOperations
    ? []
    : isHiddenSelection
      ? []
      : isBindDialog
        ? [
            {
              prop: 'operation',
              label: '操作',
              width: 100,
              fixed: 'right',
              render: bindDialogOperationRender,
            },
          ]
        : [
            {
              prop: 'operation',
              label: '操作',
              width: 200,
              fixed: 'right',
              className: 'operation-column',
              render: operationRender,
            },
          ]

// 格式化计费类型
const formatBillType = (type: string): string => {
  const billTypeMap: Record<string, string> = {
    day: '按天计费',
    month: '按月计费',
    year: '按年计费',
  }
  return billTypeMap[type] || type || '--'
}

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  queryParams?.hideSelection
    ? { type: 'index', label: '序号', width: 55 }
    : { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'deviceName',
    label: '主机名称',
    width: 150,
    fixed: 'left',
    render: ({ row }: any): VNode => {
      return (
        <router-link
          to={{
            path: '/gcsDetail',
            query: {
              id: row.id,
              deviceId: row.deviceId,
              sourceType: 'DG',
            },
          }}
          style="color: #0052D9; text-decoration: none;"
        >
          {row.deviceName}
        </router-link>
      )
    },
  },
  { prop: 'deviceId', label: '资源ID', width: 200 },
  {
    prop: 'billType',
    label: '计费类型',
    width: 100,
    render: ({ row }) => formatBillType(row.billType),
  },
  { prop: 'osVersion', label: '系统版本', width: 120 },
  { prop: 'spec', label: '实例规格', width: 120 },
  { prop: 'sysDisk', label: '系统盘', width: 150 },
  { prop: 'dataDisk', label: '数据盘', width: 150 },
  { prop: 'ip', label: 'IP', width: 150 },
  { prop: 'eip', label: '弹性公网IP', width: 150 }, // 显示绑定的公网IP或占位符
  { prop: 'bandWidth', label: '带宽', width: 100 }, // 显示带宽或占位符
  { prop: 'subnet', label: '子网信息', width: 100, render: subnetRender },
  { prop: 'tenantName', label: '租户', width: 100 },
  { prop: 'cloudPlatform', label: '所属云', width: 120 }, // 支持筛选过滤
  { prop: 'resourcePoolName', label: '资源池', width: 120 }, // 支持筛选过滤
  { prop: 'azName', label: '可用区', width: 120 }, // 支持筛选过滤
  { prop: 'orderCode', label: '订单编号', width: 150 },
  { prop: 'securityGroupName', label: '安全组', width: 150 },
  { prop: 'vnicName', label: '虚拟网卡', width: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 }, // 运行中，已关机，异常
  { prop: 'createTime', label: '订购时间', width: 150 },
  { prop: 'applyUserName', label: '订购人', width: 100 },
  { prop: 'changeStatusCn', label: '变更状态', width: 100 },
  ...opColumn,
])

// 使用控制台hooks
const { supportsConsole, handleOpenConsole } = useConsole()

// 使用更换镜像hooks
const {
  changeImageDialogVisible,
  currentImageChangeRow,
  supportsChangeImage,
  handleChangeImage,
  handleChangeImageSuccess: onChangeImageSuccess,
} = useChangeImage()

const handleCommand = (command: string | number, row: any) => {
  switch (command) {
    case 'resetPwd':
      handleUpdatePassword(row)
      break
    case 'bindOrUnbindSecurityGroup':
      handleBindSecurityGroups(row)
      break
    case 'bindVirtualNic':
      handleBindVirtualNic(row, 'BIND')
      break
    case 'unbindVirtualNic':
      handleBindVirtualNic(row, 'UNBIND')
      break
    case 'console':
      handleOpenConsole(row)
      break
    case 'changeImage':
      handleChangeImage(row)
      break
  }
}

function operationRender({ row }: any): VNode {
  return (
    <>
      {['RUNING', 'STOPED'].includes(row.deviceStatus) && (
        <el-button onClick={() => handleSwitchMachine(row)} type="primary" link>
          {row.deviceStatus == 'RUNING' ? '关机' : '开机'}
        </el-button>
      )}
      <el-button
        onClick={() => handleRestart(row)}
        type="primary"
        disabled={row.deviceStatus != 'RUNING'}
        link
      >
        重启
      </el-button>
      <ElDropdown
        onCommand={(command) => handleCommand(command, row)}
        v-slots={{
          dropdown: () => (
            <ElDropdownMenu>
              <ElDropdownItem command="resetPwd" disabled={row.resetPwd !== 1}>
                修改密码
              </ElDropdownItem>
              <ElDropdownItem command="bindOrUnbindSecurityGroup">
                {row.securityGroupName ? '解绑安全组' : '绑定安全组'}
              </ElDropdownItem>
              <ElDropdownItem command="bindVirtualNic">绑定虚拟网卡</ElDropdownItem>
              {row.vnicId ? (
                <ElDropdownItem command="unbindVirtualNic">解绑虚拟网卡</ElDropdownItem>
              ) : null}
              {supportsConsole(row) ? (
                <ElDropdownItem command="console">控制台</ElDropdownItem>
              ) : null}
              {supportsChangeImage(row) ? (
                <ElDropdownItem command="changeImage" disabled={row.deviceStatus !== 'STOPED'}>
                  更换镜像
                </ElDropdownItem>
              ) : null}
            </ElDropdownMenu>
          ),
        }}
        style={{ marginLeft: '8px' }}
      >
        <el-button type="primary" link>
          更多操作
          <el-icon class="el-icon--right">
            <ArrowDown />
          </el-icon>
        </el-button>
      </ElDropdown>
    </>
  )
}

function subnetRender({ row }: any): VNode {
  return (
    <el-button onClick={() => handleViewSubnet(row)} type="primary" link>
      查看子网
    </el-button>
  )
}

const proTable = ref<ProTableInstance>()

// 使用回收校验钩子函数
const { validateUnsubscribe, validateChangeDg, getDgFormData } = useRecycleValidation()

const handleCreateUnsubscribe = async (goodsItems: any[]) => {
  const res = await dgRecoveryWorkOrderCreate(getDgFormData('gcs', goodsItems))
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  SlMessage.success('已提交退订')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}

// 批量回收功能
const handleBatchUnsubscribe = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateUnsubscribe(selectedList, 'gcs')) {
    const arr = await useVerifyThePromptBox(selectedList, [], true)
    if (!arr || !arr.length) return
    handleCreateUnsubscribe(arr)
  }
}

// 开关机功能
const handleSwitchMachine = async (row: any) => {
  const msgStr = {
    RUNING: '选中信息确认关机吗？',
    STOPED: '选中信息确认开机吗？',
  }
  const deviceStatus = row.deviceStatus?.toUpperCase() || 'UNKNOWN'
  const msg = msgStr[deviceStatus as keyof typeof msgStr] || '未知状态，请确认操作'
  await ElMessageBox.confirm(msg, '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await executeResourceOperationConfirm({
        orderId: row.orderId,
        goodsOrderId: row.goodsOrderId,
        operationType: deviceStatus === 'RUNING' ? 'STOP' : 'START',
      })
      proTable.value?.getTableList()
    })
    .catch(() => {
      console.log(`操作取消`)
    })
}
// 重启功能
const handleRestart = async (row: any) => {
  if (row.deviceStatus != 'RUNING') {
    return ElMessage.warning('当前主机状态为非运行中，无法重启')
  }
  await ElMessageBox.confirm('选中信息确认重启吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await executeResourceOperationConfirm({
        orderId: row.orderId,
        goodsOrderId: row.goodsOrderId,
        operationType: 'RESTART',
      })
      proTable.value?.getTableList()
    })
    .catch(() => {
      console.log(`操作取消`)
    })
}

// 绑定安全组功能
const securityGroupDialog = ref<boolean>(false)
const securityGroupListRef = ref<InstanceType<typeof PublicDataList>>()
const currentRow = ref<any>({})
const securityGroupQueryParams = ref<Record<string, any>>({ type: 'securityGroup' })

async function handleBindSecurityGroups(row: any) {
  if (row.securityGroupName) {
    // 如果已经绑定了安全组，则提示是否解绑
    ElMessageBox.confirm(`确定要解绑安全组 "${row.securityGroupName}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        try {
          const res = await securityGroupOperate({
            operateType: 'UNBIND_SG',
            regionCode: row.resourcePoolCode,
            businessSystemId: row.businessSysId,
            vmId: row.deviceId,
            securityGroupIds: row.securityGroupIds,
          })
          if (res.code == 200) {
            ElMessage.success('已提交解绑安全组请求')
            proTable.value?.getTableList()
          }
        } catch (error) {
          console.error(error)
        }
      })
      .catch(() => {
        // 用户取消操作
      })
  } else {
    // 打开安全组选择弹窗，并根据vpcName进行过滤
    currentRow.value = row
    // 更新安全组查询参数，添加vpcName过滤条件
    securityGroupQueryParams.value = {
      type: 'securityGroup',
      vpcName: row.vpcName,
      sourceType: 'DG',
    }
    securityGroupDialog.value = true
  }
}

function handleSecurityGroupSelected(row: any) {
  if (!row.id) {
    ElMessage.warning('请选择要绑定的安全组')
    return
  }

  try {
    securityGroupOperate({
      operateType: 'BIND_SG',
      regionCode: currentRow.value.resourcePoolCode,
      businessSystemId: currentRow.value.businessSysId,
      vmId: currentRow.value.deviceId,
      securityGroupIds: row.id,
    }).then((res) => {
      if (res.code == 200) {
        ElMessage.success('已提交绑定安全组')
        securityGroupDialog.value = false
        proTable.value?.getTableList()
      }
    })
  } catch (error) {
    console.error(error)
  }
}

// 修改密码功能
const updatePasswordDialogVisible = ref(false)
const updatePasswordFormModel = reactive<ResourceOperationModel>(useResourceOperationFormModel())
const updatePasswordFormOptions = reactive(useUpdatePasswordFormOptions(updatePasswordFormModel))

const handleUpdatePassword = async (row: any) => {
  updatePasswordDialogVisible.value = true
  updatePasswordFormModel.orderId = row.orderId
  updatePasswordFormModel.goodsOrderId = row.goodsOrderId
  updatePasswordFormModel.deviceStatus = row.deviceStatus
}

const handleUpdatePasswordClose = () => {
  updatePasswordDialogVisible.value = false
  useResourceOperationFormClear(updatePasswordFormModel)
}

const updatePasswordFormRef = ref()
const handleUpdatePasswordConfirm = async () => {
  if (!(await updatePasswordFormRef.value?.validate(() => true))) return
  await executeResourceOperationConfirm({ ...updatePasswordFormModel, operationType: 'RESETPWD' })
  handleUpdatePasswordClose()
  proTable.value?.getTableList()
}

// 子网弹窗相关
const subnetDialogVisible = ref(false)
const currentSubnetList = ref<any[]>([])

// 查看子网信息
const handleViewSubnet = async (row: any) => {
  // TODO: 这里需要调用获取子网信息的接口
  const res = await getSubnetList({ id: row.id })
  currentSubnetList.value = res.entity
  subnetDialogVisible.value = true
}

// 优化多选数据处理
const multipleSelection = ref<any[]>([])
const changeDialogVisible = ref(false)
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])
// 使用计算属性缓存处理过的资源列表以提高性能
const processedResources = computed(() => {
  if (multipleSelection.value.length === 0) return []
  return multipleSelection.value.map((resource) => ({
    ...resource,
    _resourceId: resource.id, // 添加一个唯一标识，用于跟踪变化
  }))
})

// 使用计算属性判断是否禁用变更和延期按钮
const isChangeDisabled = computed(() => multipleSelection.value.length === 0)

// GCS允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = [
  'instance_spec_change',
  'storage_expand',
  'bandwidth_expand',
]

// 重构资源变更处理方法
const handleResourceChange = async (dg = false) => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要变更的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChangeDg(selectedResources.value, 'gcs')) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value, dg)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

const handleResourceDelay = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要延期的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChangeDg(selectedResources.value, 'gcs')) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

const goodsList = ref<ICartsModel<'gcs'>>([])
const propertyChangeDrawer = ref(false)

function handleOpenPropertyChangeDrawer(list: any) {
  if (!list) return ElMessage.warning('请选择要变更的资源')
  propertyChangeDrawer.value = true
  goodsList.value = transformData(list)
}
// 处理确认
const handleConfirm = (data: any) => {
  changeDialogVisible.value = false
  handleOpenPropertyChangeDrawer(data)
}
const handlePropertyChangeConfirm = () => {
  propertyChangeDrawer.value = false
  proTable.value?.getTableList()
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 绑定弹窗中的操作列渲染函数
function bindDialogOperationRender({ row }: any): VNode {
  return (
    <>
      <el-button
        onClick={() => handleSelectDevice(row)}
        type="primary"
        disabled={row.recoveryStatus !== 0 || row.changeStatusCn !== '未变更'}
        link
      >
        绑定
      </el-button>
    </>
  )
}

// 虚拟网卡相关功能
const virtualNicDialogVisible = ref(false)
const virtualNicListRef = ref<InstanceType<typeof VirtualNicPublicDataList>>()
const virtualNicQueryParams = ref<Record<string, any>>({ type: 'virtualNic' })
const virtualNicSafeLock = ref(false)

async function handleBindVirtualNic(row: any, operateType: string) {
  currentRow.value = row
  virtualNicDialogVisible.value = true
  virtualNicQueryParams.value = {
    type: 'virtualNic',
    vnicId: row.vnicId,
    vmId: operateType == 'UNBIND' ? row.deviceId : undefined,
    operateType,
    sourceType: 'DG',
  }
}

async function handleVirtualNicSelected(row: any, operateType: string) {
  if (!row.id) {
    ElMessage.warning('请选择要操作的虚拟网卡')
    return
  }
  if (virtualNicSafeLock.value) return
  try {
    virtualNicSafeLock.value = true
    const res = await virtualNicOperate({
      operateType,
      deviceId: currentRow.value.deviceId,
      vnicOrderId: row.id,
    })
    if (res.code == 200) {
      ElMessage.success(`已提交${operateType == 'bind' ? '绑定' : '解绑'}虚拟网卡请求`)
      virtualNicDialogVisible.value = false
      proTable.value?.getTableList()
    }
  } catch (error) {
    console.error(error)
  } finally {
    virtualNicSafeLock.value = false
  }
}

// 选择设备
const handleSelectDevice = (row: any) => {
  emit('selectDevice', row)
}

// 更换镜像成功回调
const handleChangeImageSuccess = () => {
  onChangeImageSuccess(() => proTable.value?.getTableList())
}

defineExpose({
  handleBatchUnsubscribe,
  handleResourceChange,
  handleResourceDelay,
})
</script>
<style lang="scss" scoped>
.warning {
  width: 90%;
  margin: 0 auto;
  margin-top: -10px;
  word-break: break-all;
  color: red;
}
</style>
<style lang="scss">
.operation-column .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
