<template>
  <div class="table-box">
    <sl-page-header
      title="通用Redis"
      title-line="通用Redis(Cloud Middleware)作为内存数据库，提供毫秒级响应与持久化能力，助力用户构建高并发、低延迟的应用服务，全面提升数据缓存与实时处理效率。"
      :icon="{
        class: 'page_yunzhongjianjian',
        color: '#0052D9',
        size: '40px',
      }"
    >
    </sl-page-header>
    <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
      <el-button @click="handleBatchRecycle" type="primary" v-permission="'BatchRecycle'">
        批量回收
      </el-button>
      <el-button @click="handleBatchChange" type="primary" v-permission="'Change'">
        资源变更
      </el-button>
      <el-button @click="handleBatchDelay" type="primary" v-permission="'Delay'">
        资源延期
      </el-button>
    </div>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <dataList
        ref="dataListRef"
        :query-params="queryParams"
        :hide-operations="shouldHideResourceOperations"
      ></dataList>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { Delete, Search, ArrowDown, ArrowUp, Upload } from '@element-plus/icons-vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { useDichooks } from '../hooks/useDichooks'
import { normalizeExportArgs } from '@/views/resourceCenter/utils'
import { useDownload } from '@/hooks/useDownload'
import { resourceExport } from '@/api/modules/resourecenter'
import { useRolePermission } from '../hooks/useRolePermission'

// 获取路由信息
const route = useRoute()

// 从query参数中获取业务系统的初始值
const getBusinessSysIdsFromQuery = () => {
  const businessSysIds = route.query.businessSysIds
  if (businessSysIds && typeof businessSysIds === 'string') {
    // 字符串格式，用逗号分割
    return businessSysIds.split(',').filter((id: string) => id.trim())
  }
  return []
}

const { busiSystemOptions } = useBusiSystemOptions()
const formRef = ref<any>(null)
const formModel = reactive<any>({
  businessSysIds: getBusinessSysIdsFromQuery(),
})

const queryParams = ref<any>({ type: 'redis', ...formModel })
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}
function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

const { resourcePoolsDic } = useDichooks()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic
// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '通用Redis名称',
        type: 'input',
        key: 'deviceName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSysIds',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
        props: {
          select: {
            multiple: true,
            filterable: true,
            clearable: true,
            collapseTags: true,
          },
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'redis', '通用Redis.xlsx', {
                      ...formModel,
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '资源ID',
        type: 'input',
        key: 'deviceId',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '系统版本',
        type: 'input',
        key: 'osVersion',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '实例规格',
        type: 'input',
        key: 'spec',
        valueField: 'lable',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '系统盘',
        type: 'input',
        key: 'sysDisk',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '数据盘',
        type: 'input',
        key: 'dataDisk',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: 'IP',
        type: 'input',
        key: 'ip',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '弹性公网IP',
        type: 'input',
        key: 'eip',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'applyTime',
        options: getDic('time'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '工单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '开通时间',
        type: 'date',
        key: 'effectiveTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        label: '到期时间',
        type: 'date',
        key: 'expireTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        label: '状态',
        type: 'select',
        key: 'deviceStatus',
        options: getDic('vmStatus'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '交维状态',
        type: 'select',
        key: 'handoverStatus',
        options: [
          { value: '未交维', label: '未交维' },
          { value: '已交维', label: '已交维' },
        ],
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请人',
        type: 'input',
        key: 'applyUserName',
        span: 8,
        disabled: false,
        hidden: true,
      },
    ],
  },
])

// 批量回收
const dataListRef = ref()
const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}

// 资源变更
const handleBatchChange = () => {
  dataListRef.value?.handleResourceChange()
}

// 资源延期
const handleBatchDelay = () => {
  dataListRef.value?.handleResourceDelay()
}

const { shouldHideResourceOperations } = useRolePermission()
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
