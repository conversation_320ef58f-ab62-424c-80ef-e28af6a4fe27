<template>
  <div id="PerformanceChart">
    <div class="header">
      <div class="header-title">性能数据</div>
      <div class="header-date">
        <el-radio-group v-model="activeDateType" @change="handleDateOrDateTypeChange">
          <el-radio-button :value="item.code" v-for="item in dateTypeList" :key="item.code">
            {{ item.name }}
          </el-radio-button>
        </el-radio-group>
        <el-date-picker
          v-model="date"
          type="date"
          placeholder="请选择日期"
          :disabled-date="disabledDate"
          @change="handleDateOrDateTypeChange"
        />
      </div>
    </div>
    <template v-if="showChart">
      <div ref="chartContainer" class="chart-container"></div>
      <div ref="chartContainerEvs" class="chart-container"></div>
    </template>
    <template v-else>
      <el-empty description="暂无数据" />
    </template>
  </div>
</template>

<script setup lang="ts" name="PerformanceChart">
import { onMounted, onBeforeUnmount, ref, watch, type Ref, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { usePerformanceData, type PerformanceDataItem } from '../hooks/usePerformanceData'

echarts.use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

const props = defineProps({
  deviceId: {
    type: String,
    required: true,
  },
})

// 使用性能数据hooks
const {
  dateTypeList,
  activeDateType,
  date,
  showChart,
  performanceData,
  disabledDate,
  handleDateOrDateTypeChange,
  getPerformanceData,
} = usePerformanceData(props.deviceId)

const chartContainer: Ref<HTMLElement | null> = ref(null)
const chartContainerEvs: Ref<HTMLElement | null> = ref(null)

let chart: echarts.ECharts | null = null
let chartEvs: echarts.ECharts | null = null

const initChart = (
  data: PerformanceDataItem[],
  type: string,
  container: HTMLElement | null,
  chartInstance: echarts.ECharts | null,
) => {
  const _legendData = type === 'cloud' ? ['CPU', '内存'] : ['IO读', 'IO写']
  if (container) {
    // 检查容器是否可见
    const rect = container.getBoundingClientRect()
    if (rect.width === 0) {
      console.log('容器不可见，延迟初始化图表')
      // 如果容器不可见，延迟执行
      setTimeout(() => {
        initChart(data, type, container, chartInstance)
      }, 200)
      return
    }

    const _chartInstance = chartInstance || echarts.init(container)
    const option: echarts.EChartsCoreOption = {
      backgroundColor: '#fff',
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          let tooltip = params[0].name + '<br>'
          params.forEach((param: any) => {
            tooltip += `${param.seriesName}: ${param.value} ${type === 'cloud' ? '%' : '次/秒'}<br>`
          })
          return tooltip
        },
      },
      legend: {
        data: _legendData,
        icon: 'rect',
        itemWidth: 14,
        itemHeight: 14,
        left: 120,
        itemGap: 50,
        textStyle: {
          fontSize: 14,
        },
      },
      color: ['rgba(255, 165, 0, 1)', 'rgba(102, 177, 255, 1)'],
      xAxis: {
        type: 'category',
        data: data.map((item) => item.createTime),
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: type === 'cloud' ? '{value}%' : '{value} 次/秒', // 自定义标签格式
        },
      },
      series: [
        {
          name: _legendData[0],
          type: 'line',
          lineStyle: {
            color: 'rgba(255, 165, 0, 1)', // 设置磁盘的线条颜色
          },
          data: data.map((item) => {
            const value = type === 'cloud' ? item.cpuUtil : item.diskReadIops
            return parseFloat(value.toFixed(2))
          }),
        },
        {
          name: _legendData[1],
          type: 'line',
          lineStyle: {
            color: 'rgba(102, 177, 255, 1)', // 设置CPU的线条颜色
          },
          data: data.map((item) => {
            const value = type === 'cloud' ? item.memUtil : item.diskWriteIops
            return parseFloat(value.toFixed(2))
          }),
        },
      ],
    }
    _chartInstance.setOption(option)

    // 确保图表正确渲染
    setTimeout(() => {
      _chartInstance.resize()
    }, 100)

    if (type === 'cloud') {
      chart = _chartInstance
    } else {
      chartEvs = _chartInstance
    }
  }
}

const resizeObserver = new ResizeObserver((entries) => {
  for (let entry of entries) {
    if (entry.contentBoxSize) {
      const contentBoxSize = Array.isArray(entry.contentBoxSize)
        ? entry.contentBoxSize[0]
        : entry.contentBoxSize
      if (entry.target.id === 'chartContainer') {
        chart?.resize({
          width: contentBoxSize.inlineSize,
          height: contentBoxSize.blockSize,
        })
      } else if (entry.target.id === 'chartContainerEvs') {
        chartEvs?.resize({
          width: contentBoxSize.inlineSize,
          height: contentBoxSize.blockSize,
        })
      }
    } else {
      if (entry.target.id === 'chartContainer') {
        chart?.resize({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        })
      } else if (entry.target.id === 'chartContainerEvs') {
        chartEvs?.resize({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        })
      }
    }
  }
})

// 监听数据变化，确保图表及时更新
watch(
  () => performanceData.value,
  (newData) => {
    console.log('性能数据更新:', newData)
    if (newData && newData.length > 0) {
      // 延迟初始化确保容器尺寸正确
      setTimeout(() => {
        initChart(newData, 'cloud', chartContainer.value, chart)
        initChart(newData, 'evs', chartContainerEvs.value, chartEvs)
      }, 100)
    }
  },
  { deep: true },
)

// 监听容器可见性变化，处理tab切换时的图表重新渲染
const checkVisibilityAndResize = () => {
  const checkContainer = (container: HTMLElement | null, chartInstance: echarts.ECharts | null) => {
    if (container && chartInstance) {
      const rect = container.getBoundingClientRect()
      const isVisible = rect.width > 0 && rect.height > 0

      if (isVisible) {
        // 容器可见且有尺寸，重新调整图表大小
        setTimeout(() => {
          chartInstance.resize()
        }, 100)
      }
    }
  }

  checkContainer(chartContainer.value, chart)
  checkContainer(chartContainerEvs.value, chartEvs)
}

// 添加全局的tab切换监听
const handleTabVisibilityChange = () => {
  // 使用MutationObserver监听tab面板的显示/隐藏
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
        const target = mutation.target as HTMLElement
        if (target.contains(chartContainer.value) || target.contains(chartContainerEvs.value)) {
          // 延迟检查，确保CSS动画完成
          setTimeout(checkVisibilityAndResize, 200)
        }
      }
    })
  })

  // 监听包含图表的父元素
  const parentElement = chartContainer.value?.closest('.el-tab-pane')
  if (parentElement) {
    observer.observe(parentElement, {
      attributes: true,
      attributeFilter: ['style', 'class'],
      subtree: true,
    })
  }

  return observer
}

onMounted(async () => {
  console.log('PerformanceChart组件挂载, deviceId:', props.deviceId)

  window.addEventListener('resize', () => {
    chart?.resize()
    chartEvs?.resize()
  })

  // 等待DOM渲染完成
  await nextTick()

  if (chartContainer.value) {
    chartContainer.value.id = 'chartContainer'
    resizeObserver.observe(chartContainer.value)
  }
  if (chartContainerEvs.value) {
    chartContainerEvs.value.id = 'chartContainerEvs'
    resizeObserver.observe(chartContainerEvs.value)
  }

  // 监听Element Plus的tab切换事件
  const handleTabClick = () => {
    // 延迟检查，确保tab切换动画完成
    setTimeout(() => {
      const containers = [
        { container: chartContainer.value, chart: chart },
        { container: chartContainerEvs.value, chart: chartEvs },
      ]

      containers.forEach(({ container, chart }) => {
        if (container && chart) {
          const rect = container.getBoundingClientRect()
          if (rect.width > 0 && rect.height > 0) {
            chart.resize()
            console.log('Tab切换后重新调整图表大小')
          }
        }
      })
    }, 300) // 给足够时间让tab切换动画完成
  }

  // 查找父级的el-tabs组件并监听tab-click事件
  const findTabsElement = (element: HTMLElement | null): HTMLElement | null => {
    while (element && element !== document.body) {
      if (element.classList.contains('el-tabs')) {
        return element
      }
      element = element.parentElement
    }
    return null
  }

  const tabsElement = findTabsElement(chartContainer.value)
  if (tabsElement) {
    tabsElement.addEventListener('click', handleTabClick)
  }

  // 添加全局的tab切换监听
  const observer = handleTabVisibilityChange()

  // 使用nextTick确保DOM完全渲染后再初始化图表
  if (props.deviceId) {
    console.log('开始获取性能数据...')
    getPerformanceData().then(() => {
      console.log('性能数据获取完成:', performanceData.value)
      if (performanceData.value && performanceData.value.length > 0) {
        // 延迟初始化确保容器尺寸正确
        setTimeout(() => {
          initChart(performanceData.value, 'cloud', chartContainer.value, chart)
          initChart(performanceData.value, 'evs', chartContainerEvs.value, chartEvs)
        }, 200)
      }
    })
  }

  // 清理资源
  onBeforeUnmount(() => {
    observer.disconnect()
    resizeObserver.disconnect()
    if (tabsElement) {
      tabsElement.removeEventListener('click', handleTabClick)
    }
  })
})
</script>

<style lang="scss" scoped>
#PerformanceChart {
  width: 100%;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .header-title {
      font-size: 16px;
      font-weight: bold;
    }
    .header-date {
      display: flex;
      align-items: center;
      .el-radio-group {
        margin-right: 10px;
      }
    }
  }
  .chart-container {
    width: 100%;
    height: 400px;
    min-width: 0; // 确保flex容器中的宽度正确
    margin-bottom: 20px;
  }
}
</style>
