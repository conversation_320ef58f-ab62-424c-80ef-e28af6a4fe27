<template>
  <div class="table-box">
    <sl-page-header
      :icon="{
        class: 'page_apiGetway',
        color: '#0052D9',
        size: '40px',
      }"
      title="API管理"
      title-line="API管理通过接口对云资源进行全生命周期管理，涵盖开通、回收等操作及权限控制，实现统一管控，保障安全高效，提高协作效率。"
    ></sl-page-header>
    <div class="btn op" style="margin-top: 8px">
      <el-button @click="openDialog" :icon="Plus" type="primary"> 添加API </el-button>
    </div>
    <div class="filter-form-con">
      <sl-form ref="formRef" v-model="formModel" :options="formOptions" class="filter-form">
      </sl-form>
    </div>
    <div class="sl-page-content table-main">
      <SlProTable
        ref="proTable"
        :columns="columns"
        :init-param="queryParams"
        :request-api="selectAPiDetailApi"
      >
        <template #detailList="scope">
          <span v-for="item in scope.row.detailList" :key="item.id" class="operation-btn">{{
            item.urlNote
          }}</span>
        </template>
        <template #operation="scope">
          <el-button :icon="EditPen" link type="primary" @click="openDialog(scope.row)">
            编辑
          </el-button>
          <el-button :icon="Delete" link type="danger" @click="deletePermission(scope.row)">
            删除
          </el-button>
        </template>
      </SlProTable>
    </div>
    <!-- 创建权限 -->
    <el-drawer
      v-model="showPermissionAdd"
      :before-close="handlePermissionAddClose"
      :close-on-press-escape="false"
      :title="currentPermission?.id ? '编辑API' : '添加API'"
      destroy-on-close
      size="800"
    >
      <PermissionAdd
        :current-permission="currentPermission"
        @close="handlePermissionAddClose"
        @submit="handlePermissionAddSubmit"
      ></PermissionAdd>
    </el-drawer>
  </div>
</template>

<script lang="tsx" name="tenantManagement" setup>
import {
  deleteApiByIdApi,
  selectAPiDetailApi,
  getApiPlatformListApi,
  getApiResTypeListApi,
} from '@/api/modules/managementCenter'
import { Delete, EditPen, Plus, Search } from '@element-plus/icons-vue'
import { reactive, ref, onMounted } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import type { PermissionType } from './interface/type'
import PermissionAdd from './components/PermissionAdd.vue'
import SlMessage from '@/components/base/SlMessage'
// -----------------------用户列表------------------------------
const proTable = ref<ProTableInstance>()

const columns = reactive<ColumnProps<PermissionType>[]>([
  { type: 'index', fixed: 'left', label: '序号', width: 55 },
  {
    prop: 'domainName',
    label: '云平台',
    width: 200,
    // search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'resTypeName',
    label: '资源类型',
    width: 200,
    // search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'detailList',
    label: '操作权限',
    align: 'left',
    // search: { el: 'input', checked: true, defaultDisabled: true }
  },
  { prop: 'operation', label: '操作', fixed: 'right', width: 250 },
])

// 新增表单
const formRef = ref<any>(null)
// 上传文件
const queryParams = ref<any>(null)

const formModel = reactive({})

// 重置表单内容
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}

function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

// 定义类型/接口
interface Option {
  label: string
  value: string
}

const categoryCodeList = ref<Option[]>([])
const dominCodeList: any = ref([])
// 初始化表格数据
onMounted(() => {
  init()
})

async function init() {
  const res1: any = await getApiPlatformListApi({}) //查询domincode
  dominCodeList.value = res1.entity

  const res: any = await getApiResTypeListApi({})

  res.entity.forEach((item: any) => {
    categoryCodeList.value.push({
      label: item.configName,
      // label: item.resType,
      value: item.configName,
    })
  })
}

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        labelField: 'domainName',
        valueField: 'domainCode',
        options: dominCodeList,
        span: 6,
        labelWidth: '63',
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '资源类型',
        type: 'select',
        key: 'resTypeName',
        options: categoryCodeList,
        span: 6,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },

      {
        label: '操作权限',
        type: 'input',
        key: 'urlName',
        span: 6,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 6,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button onClick={reset} icon={<Delete />}>
                重置{' '}
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                {' '}
                搜索{' '}
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])
const showPermissionAdd = ref(false)
const currentPermission = ref()
const openDialog = (row?: any) => {
  showPermissionAdd.value = true
  currentPermission.value = {}
  if (!row) return
  currentPermission.value = row
}

const handlePermissionAddClose = () => {
  showPermissionAdd.value = false
}

const handlePermissionAddSubmit = () => {
  proTable.value?.getTableList()
  showPermissionAdd.value = false
}

/**
 * 删除
 * @param id
 */
const deletePermission = async (row: any) => {
  // 假设 row.detailList 是对象数组，每个对象有 id 属性
  const idArray = row.detailList.map((item: any) => item.id)
  await ElMessageBox.confirm('确认删除该权限吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await deleteApiByIdApi(idArray)
  // await deleteApiByIdApi({
  //   menuId: id
  // })
  SlMessage.success('删除成功')
  proTable.value?.getTableList()
  init()
}
</script>

<style lang="scss" scoped>
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow: hidden;
}

.operation-btn {
  margin-right: 10px;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
</style>
