import type { RouteRecordRaw } from 'vue-router'

const resourceCenterRouter: RouteRecordRaw[] = [
  {
    path: '/ecsList',
    component: () => import('@/views/resourceCenter/ecs/list.vue'),
    name: 'ecsList',
    meta: {
      title: '云主机列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/ecsDetail',
    component: () => import('@/views/resourceCenter/ecs/detail.vue'),
    name: 'ecsDetail',
    meta: {
      title: '云主机详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/gcsList',
    component: () => import('@/views/resourceCenter/gcs/list.vue'),
    name: 'gcsList',
    meta: {
      title: 'GPU云主机列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/gcsDetail',
    component: () => import('@/views/resourceCenter/gcs/detail.vue'),
    name: 'gcsDetail',
    meta: {
      title: '云主机详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/obsList',
    component: () => import('@/views/resourceCenter/obs/list.vue'),
    name: 'obsList',
    meta: {
      title: '对象存储列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/obsDetail',
    component: () => import('@/views/resourceCenter/obs/detail.vue'),
    name: 'obsDetail',
    meta: {
      title: '对象存储详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/vpcList',
    component: () => import('@/views/resourceCenter/vpc/list.vue'),
    name: 'vpcList',
    meta: {
      title: 'VPC列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/vpcForm',
    component: () => import('@/views/resourceCenter/vpc/form.vue'),
    name: 'vpcForm',
    meta: {
      title: 'VPC资源开通',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/networkList',
    component: () => import('@/views/resourceCenter/network/list.vue'),
    name: 'networkList',
    meta: {
      title: '网络列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/networkForm',
    component: () => import('@/views/resourceCenter/network/form.vue'),
    name: 'networkForm',
    meta: {
      title: '网络资源开通',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/slbList',
    component: () => import('@/views/resourceCenter/slb/list.vue'),
    name: 'slbList',
    meta: {
      title: '负载均衡列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/slbDetail',
    component: () => import('@/views/resourceCenter/slb/detail.vue'),
    name: 'slbDetail',
    meta: {
      title: '负载均衡详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/slbListenerForm',
    component: () => import('@/views/resourceCenter/slb/listenerForm.vue'),
    name: 'slbListenerForm',
    meta: {
      title: '负载均衡监听',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/evsList',
    component: () => import('@/views/resourceCenter/evs/list.vue'),
    name: 'evsList',
    meta: {
      title: '云硬盘列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/evsDetail',
    component: () => import('@/views/resourceCenter/evs/detail.vue'),
    name: 'evsDetail',
    meta: {
      title: '云硬盘详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/shareEvsList',
    component: () => import('@/views/resourceCenter/shareEvs/list.vue'),
    name: 'shareEvsList',
    meta: {
      title: '云有共享盘列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/shareEvsDetail',
    component: () => import('@/views/resourceCenter/shareEvs/detail.vue'),
    name: 'shareEvsDetail',
    meta: {
      title: '云有共享盘详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/natList',
    component: () => import('@/views/resourceCenter/nat/list.vue'),
    name: 'natList',
    meta: {
      title: 'NAT网关列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/natDetail',
    component: () => import('@/views/resourceCenter/nat/detail.vue'),
    name: 'natDetail',
    meta: {
      title: 'NAT网关详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/vpnList',
    component: () => import('@/views/resourceCenter/vpn/list.vue'),
    name: 'vpnList',
    meta: {
      title: 'VPN网关列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/vpnDetail',
    component: () => import('@/views/resourceCenter/vpn/detail.vue'),
    name: 'vpnDetail',
    meta: {
      title: 'VPN网关详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/nasList',
    component: () => import('@/views/resourceCenter/nas/list.vue'),
    name: 'nasList',
    meta: {
      title: 'NAS存储列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/nasDetail',
    component: () => import('@/views/resourceCenter/nas/detail.vue'),
    name: 'nasDetail',
    meta: {
      title: 'NAS详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/shoppingCarts',
    component: () => import('@/views/shoppingCarts/index.vue'),
    name: 'shoppingCarts',
    meta: {
      title: '购物车',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/recycleBin',
    component: () => import('@/views/recycleBin/index.vue'),
    name: 'recycleBin',
    meta: {
      title: '回收站',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/orderEdit',
    component: () => import('@/views/shoppingCarts/index.vue'),
    name: 'orderEdit',
    meta: {
      title: '工单编辑',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/eipList',
    component: () => import('@/views/resourceCenter/eip/list.vue'),
    name: 'eipList',
    meta: {
      title: '弹性公网列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/eipDetail',
    component: () => import('@/views/resourceCenter/eip/detail.vue'),
    name: 'eipDetail',
    meta: {
      title: '弹性公网详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/propertyChange',
    component: () => import('@/views/propertyChange/index.vue'),
    name: 'propertyChange',
    meta: {
      title: '资源变更',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/imagesList',
    component: () => import('@/views/resourceCenter/images/list.vue'),
    name: 'imagesList',
    meta: {
      title: '镜像列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/securityGroupList',
    component: () => import('@/views/resourceCenter/securityGroup/list.vue'),
    name: 'securityGroupList',
    meta: {
      title: '安全组列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/securityGroupCreate',
    component: () => import('@/views/resourceCenter/securityGroup/create.vue'),
    name: 'securityGroupCreate',
    meta: {
      title: '创建安全组',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/securityGroupDetail',
    component: () => import('@/views/resourceCenter/securityGroup/detail.vue'),
    name: 'securityGroupDetail',
    meta: {
      title: '安全组详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/virtualNicList',
    component: () => import('@/views/resourceCenter/virtualNic/list.vue'),
    name: 'virtualNicList',
    meta: {
      title: '虚拟网卡列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/virtualNicCreate',
    component: () => import('@/views/resourceCenter/virtualNic/create.vue'),
    name: 'virtualNicCreate',
    meta: {
      title: '创建虚拟网卡',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/virtualNicDetail',
    component: () => import('@/views/resourceCenter/virtualNic/detail.vue'),
    name: 'virtualNicDetail',
    meta: {
      title: '虚拟网卡详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/virtualIpList',
    component: () => import('@/views/resourceCenter/virtualIp/list.vue'),
    name: 'virtualIpList',
    meta: {
      title: '虚拟IP列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/certificateList',
    component: () => import('@/views/resourceCenter/certificate/list.vue'),
    name: 'certificateList',
    meta: {
      title: '证书管理',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/certificateDetail',
    component: () => import('@/views/resourceCenter/certificate/detail.vue'),
    name: 'certificateDetail',
    meta: {
      title: '证书详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/virtualIpCreate',
    component: () => import('@/views/resourceCenter/virtualIp/create.vue'),
    name: 'virtualIpCreate',
    meta: {
      title: '创建虚拟IP',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/virtualIpDetail',
    component: () => import('@/views/resourceCenter/virtualIp/detail.vue'),
    name: 'virtualIpDetail',
    meta: {
      title: '虚拟IP详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/cqList',
    component: () => import('@/views/resourceCenter/cq/list.vue'),
    name: 'cqList',
    meta: {
      title: '容器配额',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/containerQuotaDetail',
    component: () => import('@/views/resourceCenter/cq/detail.vue'),
    name: 'containerQuotaDetail',
    meta: {
      title: '容器配额详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/mysqlList',
    component: () => import('@/views/resourceCenter/mysql/list.vue'),
    name: 'mysqlList',
    meta: {
      title: 'MySQL云数据库列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/mysqlDetail',
    component: () => import('@/views/resourceCenter/mysql/detail.vue'),
    name: 'mysqlDetail',
    meta: {
      title: 'MySQL云数据库详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/postgreSqlList',
    component: () => import('@/views/resourceCenter/postgreSql/list.vue'),
    name: 'postgreSqlList',
    meta: {
      title: 'PostgreSQL云数据库列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/postgreSqlDetail',
    component: () => import('@/views/resourceCenter/postgreSql/detail.vue'),
    name: 'postgreSqlDetail',
    meta: {
      title: 'PostgreSQL云数据库详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/redisList',
    component: () => import('@/views/resourceCenter/redis/list.vue'),
    name: 'redisList',
    meta: {
      title: '通用Redis列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/redisDetail',
    component: () => import('@/views/resourceCenter/redis/detail.vue'),
    name: 'redisDetail',
    meta: {
      title: '通用Redis详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/cloudPortList',
    component: () => import('@/views/resourceCenter/cloudPort/list.vue'),
    name: 'cloudPortList',
    meta: {
      title: '云端口列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/cloudPortCreate',
    component: () => import('@/views/resourceCenter/cloudPort/create.vue'),
    name: 'cloudPortCreate',
    meta: {
      title: '创建云端口',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/cloudPortDetail',
    component: () => import('@/views/resourceCenter/cloudPort/detail.vue'),
    name: 'cloudPortDetail',
    meta: {
      title: '云端口详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/backupList',
    component: () => import('@/views/resourceCenter/backup/list.vue'),
    name: 'backupList',
    meta: {
      title: '云灾备列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/backupDetail',
    component: () => import('@/views/resourceCenter/backup/detail.vue'),
    name: 'backupDetail',
    meta: {
      title: '云灾备详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/ecs',
    component: () => import('@/views/corporateProducts/ecs.vue'),
    name: 'corporateEcs',
    meta: {
      title: 'ECS',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/gcs',
    component: () => import('@/views/corporateProducts/gcs.vue'),
    name: 'corporateGcs',
    meta: {
      title: 'GCS',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/evs',
    component: () => import('@/views/corporateProducts/evs.vue'),
    name: 'corporateEvs',
    meta: {
      title: 'EVS',
      icon: 'icon-gongdan',
    },
  },

  {
    path: '/corporate/obs',
    component: () => import('@/views/corporateProducts/obs.vue'),
    name: 'corporateObs',
    meta: {
      title: 'OBS',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/slb',
    component: () => import('@/views/corporateProducts/slb.vue'),
    name: 'corporateSlb',
    meta: {
      title: 'SLB',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/nat',
    component: () => import('@/views/corporateProducts/nat.vue'),
    name: 'corporateNat',
    meta: {
      title: 'NAT',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/eip',
    component: () => import('@/views/corporateProducts/eip.vue'),
    name: 'corporateEip',
    meta: {
      title: 'Eip',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/vpn',
    component: () => import('@/views/corporateProducts/vpn.vue'),
    name: 'corporateVpn',
    meta: {
      title: 'VPN',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/rdsMysql',
    component: () => import('@/views/corporateProducts/rdsMysql.vue'),
    name: 'corporateRdsMysql',
    meta: {
      title: 'MySQL云数据库',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/vpc',
    component: () => import('@/views/corporateProducts/vpc.vue'),
    name: 'corporateVpc',
    meta: {
      title: 'VPC',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/vpcView',
    component: () => import('@/views/corporateProducts/vpcView.vue'),
    name: 'corporateVpcView',
    meta: {
      title: 'VPC详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/backup',
    component: () => import('@/views/corporateProducts/backup.vue'),
    name: 'corporateBackup',
    meta: {
      title: '云灾备',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporate/shoppingList',
    component: () => import('@/views/corporateProducts/shoppingList.vue'),
    name: 'corporateShoppingList',
    meta: {
      title: '购物清单',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/vmwareConsole',
    component: () => import('@/views/resourceCenter/components/VmwareConsolePage.vue'),
    name: 'vmwareConsole',
    meta: {
      title: 'VMware控制台',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/kafkaList',
    component: () => import('@/views/resourceCenter/kafka/list.vue'),
    name: 'kafkaList',
    meta: {
      title: 'Kafka列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/kafkaDetail',
    component: () => import('@/views/resourceCenter/kafka/detail.vue'),
    name: 'kafkaDetail',
    meta: {
      title: 'Kafka详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/esList',
    component: () => import('@/views/resourceCenter/es/list.vue'),
    name: 'esList',
    meta: {
      title: 'ElasticSearch列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/esDetail',
    component: () => import('@/views/resourceCenter/es/detail.vue'),
    name: 'esDetail',
    meta: {
      title: 'ElasticSearch详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/flinkList',
    component: () => import('@/views/resourceCenter/flink/list.vue'),
    name: 'flinkList',
    meta: {
      title: 'Flink列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/flinkDetail',
    component: () => import('@/views/resourceCenter/flink/detail.vue'),
    name: 'flinkDetail',
    meta: {
      title: 'Flink详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/pmList',
    component: () => import('@/views/resourceCenter/pm/list.vue'),
    name: 'pmList',
    meta: {
      title: '裸金属列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/pmDetail',
    component: () => import('@/views/resourceCenter/pm/detail.vue'),
    name: 'pmDetail',
    meta: {
      title: '裸金属详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/bldRedisList',
    component: () => import('@/views/resourceCenter/bldRedis/list.vue'),
    name: 'bldRedisList',
    meta: {
      title: '国产Redis列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/bldRedisDetail',
    component: () => import('@/views/resourceCenter/bldRedis/detail.vue'),
    name: 'bldRedisDetail',
    meta: {
      title: '国产Redis详情',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/chatDemo',
    component: () => import('@/views/chatDemo/index.vue'),
    name: 'chatDemo',
    meta: {
      title: 'ChatDemo',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/chat',
    component: () => import('@/views/chatDemo/chat.vue'),
    name: 'chat',
    meta: {
      title: 'Chat',
      icon: 'icon-gongdan',
    },
  },
]

export default resourceCenterRouter
