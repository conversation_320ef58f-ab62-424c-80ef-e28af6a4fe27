<template>
  <div class="resourcerequest">
    <sl-page-header
      :title="pageTitle"
      :icon="{
        class: 'page_yunduankou',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: goBack,
      }"
    >
    </sl-page-header>
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <el-scrollbar class="scroll-view">
            <sl-form
              ref="formRef"
              v-model="formModel"
              show-block-title
              :options="formOptions"
              label-width="120px"
            ></sl-form>
          </el-scrollbar>
          <!-- 底部按钮 -->
          <div class="onefooter">
            <el-button @click="goBack">取消</el-button>
            <el-button type="primary" @click="submit" :loading="loading">提交</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlForm from '@/components/form/SlForm.vue'
import { getCloudPlatformDic, getResourcePoolsDic, getAzListDic } from '@/api/modules/dic'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createCloudPort, getVirtualNicDetail, vpcList } from '@/api/modules/resourecenter'
import { useSelectLabel } from '@/hooks/useSelectLabel'

const router = useRouter()
const route = useRoute()
const formRef = ref()
const loading = ref(false)

// 判断是创建模式还是编辑模式
const isEditMode = computed(() => route.query.id !== undefined)
const virtualNicId = computed(() => (route.query.id as string) || '')

// 判断是否为对公云端口创建
const isPublicCreate = computed(() => route.query.sourceType === 'DG')

// 标题文本
const pageTitle = computed(() => (isEditMode.value ? '编辑云端口' : '创建云端口'))

// 字典数据
const cloudPlatformOptions = ref<any[]>([])
const regionOptions = ref<any[]>([])
const vpcOrNetworkOptions = ref<any[]>([])
const azOptions = ref<any[]>([])
// 表单数据
const formModel = reactive({
  id: '', // ID
  cloudPortName: '', // 云端口名称
  regionCode: '', // 资源池
  regionName: '', // 资源池名称
  vlanId: '', // VLAN ID
  peerPassword: '', // BGP密钥
  catalogueDomainCode: 'cloudst_group_moc', // 云类型
  catalogueDomainName: '移动云', // 云类型名称
  platformCode: '', // 云平台
  platformName: '', // 云平台名称
  azCode: '', // 可用区
  azName: '', // 可用区名称
  srcIp: '', // 本端地址
  vpcId: '', // VPC ID
  vpcName: '', // VPC名称
  peerIp: '', // CM2端地址
})

useSelectLabel(
  () => cloudPlatformOptions,
  () => formModel.platformCode,
  (option: any) => {
    formModel.platformName = option.label
  },
)
useSelectLabel(
  () => regionOptions,
  () => formModel.regionCode,
  (option) => {
    formModel.regionName = option.label
    getAzOptions(option.id)
  },
)
useSelectLabel(
  () => vpcOrNetworkOptions,
  () => formModel.vpcId,
  (option) => {
    formModel.vpcName = option.label
  },
)
useSelectLabel(
  () => azOptions,
  () => formModel.azCode,
  (option) => {
    formModel.azName = option.label
  },
)

// 表单配置
const formOptions = reactive([
  {
    style: 'margin:0;border-radius:0;',
    groupName: '基本信息',
    gutter: 20,
    groupItems: [
      {
        label: '云端口名称',
        type: 'input',
        key: 'cloudPortName',
        span: 8,
        rules: [{ required: true, message: '请输入云端口名称', trigger: 'blur' }],
        props: {
          maxlength: 50,
          showWordLimit: true,
          placeholder: '请输入',
        },
      },
      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        options: [
          {
            value: 'cloudst_group_moc',
            label: '移动云',
          },
        ],
        span: 8,
        disabled: true,
        props: {
          placeholder: '移动云',
          select: {
            disabled: true,
          },
        },
      },
      {
        label: '云平台',
        type: 'select',
        key: 'platformCode',
        options: cloudPlatformOptions,
        onChange: () => {
          if (formModel.platformCode) {
            getResourcePools(formModel.platformCode)
          }
        },
        span: 8,
        props: {
          placeholder: '请选择',
        },
        rules: [{ required: true, message: '请选择云平台', trigger: 'change' }],
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionCode',
        options: regionOptions,
        onChange: () => {
          getVpcList()
        },
        span: 8,
        props: {
          placeholder: '请选择',
        },
        rules: [{ required: true, message: '请选择资源池', trigger: 'change' }],
      },
      {
        label: '可用区',
        type: 'select',
        key: 'azCode',
        options: azOptions,
        span: 8,
        props: {
          placeholder: '请选择',
        },
        rules: [{ required: true, message: '请选择可用区', trigger: 'change' }],
      },
      {
        label: 'VPC名称',
        type: 'select',
        key: 'vpcId',
        span: 8,
        options: vpcOrNetworkOptions,
        props: {
          placeholder: '请选择',
        },
        rules: [{ required: true, message: '请选择VPC名称', trigger: 'change' }],
      },
      {
        label: 'VLAN ID',
        type: 'input',
        key: 'vlanId',
        span: 8,
        props: {
          placeholder: '请输入',
        },
        rules: [{ required: true, message: '请输入VLAN ID', trigger: 'blur' }],
      },
      {
        label: '本端地址',
        type: 'input',
        key: 'srcIp',
        span: 8,
        props: {
          placeholder: '示例:********/30,********/30',
        },
        rules: [{ required: true, message: '请输入本端地址', trigger: 'blur' }],
      },
      {
        label: 'CM2端地址',
        type: 'input',
        key: 'peerIp',
        span: 8,
        props: {
          placeholder: '示例:********/30,********/30',
        },
        rules: [{ required: true, message: '请输入CM2端地址', trigger: 'blur' }],
      },
      {
        label: 'BGP密钥',
        type: 'input',
        key: 'peerPassword',
        span: 8,
        props: {
          placeholder: '请输入',
        },
        rules: [{ required: true, message: '请输入BGP密钥', trigger: 'blur' }],
      },
    ],
  },
])

// 获取云平台列表
const getCloudPlatformList = async () => {
  try {
    const params: any = {
      parentCode: 'cloudst_group_moc',
      businessCode: 'cloudPort',
    }

    const { entity } = await getCloudPlatformDic(params)
    cloudPlatformOptions.value = entity.map((e: any) => ({
      value: e.code,
      label: e.name,
    }))
  } catch (error) {
    console.error('获取云平台失败', error)
  }
}

// 获取资源池列表
const getResourcePools = async (domainCode: string) => {
  try {
    const { entity } = await getResourcePoolsDic({ domainCode })
    regionOptions.value = entity.map((e: any) => ({
      value: e.code,
      label: e.name,
      id: e.id,
    }))
  } catch (error) {
    console.error('获取资源池失败', error)
  }
}

async function getAzOptions(regionId: string) {
  const { entity } = await getAzListDic({ regionId: regionId })
  if (entity) {
    azOptions.value = entity.map((e: any) => ({
      value: e.code,
      label: e.name,
    }))
  }
}

// 获取VPC列表
const getVpcList = async () => {
  try {
    const params: any = {
      pageNum: 1,
      pageSize: 9999,
      regionCode: formModel.regionCode,
    }

    // 如果是对公云端口创建，添加sourceType参数
    if (isPublicCreate.value) {
      params.sourceType = 'DG'
    }

    const { entity } = await vpcList(params)
    vpcOrNetworkOptions.value = entity.records.map((e: any) => ({
      value: e.id,
      label: e.vpcName,
    }))
  } catch (error) {
    console.error('获取VPC列表失败', error)
  }
}

// 加载详情
const loadVirtualNicDetail = async () => {
  try {
    loading.value = true
    const res = await getVirtualNicDetail({ id: virtualNicId.value })

    if (res.code === 200 && res.entity) {
      const detail = res.entity

      // 设置基本信息
      formModel.id = detail.id || ''
      formModel.cloudPortName = detail.cloudPortName || ''
      formModel.regionCode = detail.regionCode || ''
      formModel.vlanId = detail.vlanId || ''
      formModel.peerPassword = detail.peerPassword || ''
      formModel.catalogueDomainCode = detail.catalogueDomainCode || '移动云'
      formModel.azCode = detail.azCode || ''
      formModel.srcIp = detail.srcIp || ''
      formModel.vpcName = detail.vpcName || ''
      formModel.peerIp = detail.peerIp || ''

      // 加载关联数据
      if (formModel.regionCode) {
        await getVpcList()
      }
    } else {
      ElMessage.error(res.message || '获取云端口详情失败')
    }
  } catch (error) {
    console.error('获取云端口详情失败', error)
    ElMessage.error('获取云端口详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 返回列表页
const goBack = () => {
  if (!isEditMode.value && formModel.cloudPortName) {
    ElMessageBox.confirm('确定要离开吗？未保存的数据将会丢失', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        router.push('cloudPortList')
      })
      .catch(() => {
        // 用户取消操作，不执行任何动作
      })
  } else {
    router.push('cloudPortList')
  }
}

// 表单提交
const submit = async () => {
  try {
    loading.value = true
    const valid = await formRef.value.validate()
    if (!valid) {
      loading.value = false
      return
    }
    const params = {
      cloudPortName: formModel.cloudPortName, // 云端口名称
      regionCode: formModel.regionCode, // 资源池
      regionName: formModel.regionName, // 资源池名称
      vlanId: formModel.vlanId, // VLAN ID
      peerPassword: formModel.peerPassword, // BGP密钥
      catalogueDomainCode: formModel.catalogueDomainCode, // 云类型
      catalogueDomainName: formModel.catalogueDomainName, // 云类型名称
      platformCode: formModel.platformCode, // 云平台
      platformName: formModel.platformName, // 云平台名称
      azCode: formModel.azCode, // 可用区
      azName: formModel.azName, // 可用区名称
      srcIp: formModel.srcIp, // 本端地址
      vpcId: formModel.vpcId, // VPC ID
      vpcName: formModel.vpcName, // VPC名称
      peerIp: formModel.peerIp, // CM2端地址
    }

    await createCloudPort(params)
    router.push('cloudPortList')
    ElMessage.success('创建云端口成功')
  } finally {
    loading.value = false
  }
}

// 初始化数据
onMounted(async () => {
  // 获取云平台数据
  await getCloudPlatformList()
  // 如果是编辑模式，加载详情
  if (isEditMode.value) {
    await loadVirtualNicDetail()
  }
})
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;

      .oneboxleft {
        flex-grow: 3;
        margin: 1px 0px 8px 0;
      }

      .oneboxright {
        flex-grow: 1;
        flex-basis: 0;
        background: #ffffff;
        margin: 8px 8px 8px 0;
        border-radius: 6px;
        min-width: 300px;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}
</style>
