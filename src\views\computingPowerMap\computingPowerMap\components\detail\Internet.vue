<template>
  <div class="internet-container">
    <div class="network-section" @click="handleClick('dcn')">
      <div class="network-header">
        <div class="network-total">{{ internetData.dcn.total }}</div>
        <div class="network-label">DCN地址总数</div>
      </div>

      <div class="network-details">
        <div class="detail-item opened">
          <div class="detail-label">地址已开通数</div>
          <div class="detail-value">{{ internetData.dcn.used }}个</div>
        </div>
        <div class="detail-item remaining">
          <div class="detail-label">地址剩余量</div>
          <div class="detail-value">{{ internetData.dcn.remaining }}个</div>
        </div>
      </div>
    </div>

    <div class="network-section" @click="handleClick('public')">
      <div class="network-header">
        <div class="network-total">{{ internetData.publicIp.total }}</div>
        <div class="network-label">公网IP总数</div>
      </div>

      <div class="network-details">
        <div class="detail-item opened">
          <div class="detail-label">IP已开通数</div>
          <div class="detail-value">{{ internetData.publicIp.used }}个</div>
        </div>
        <div class="detail-item remaining">
          <div class="detail-label">IP剩余量</div>
          <div class="detail-value">{{ internetData.publicIp.remaining }}个</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义 props
const props = defineProps<{
  internetData?: {
    dcn: {
      total: number
      used: number
      remaining: number
    }
    publicIp: {
      total: number
      used: number
      remaining: number
    }
  }
}>()

// 定义事件
const emit = defineEmits<{
  click: [type: string]
}>()

// 网络数据，使用 props 或默认值
const internetData = computed(
  () =>
    props.internetData || {
      dcn: {
        total: 0,
        used: 0,
        remaining: 0,
      },
      publicIp: {
        total: 0,
        used: 0,
        remaining: 0,
      },
    },
)

// 点击事件处理
const handleClick = (type: string) => {
  emit('click', type)
}
</script>

<style lang="scss" scoped>
.internet-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.network-section {
  flex: 1;
  display: flex;
  justify-content: space-between;

  &:first-child {
    border-bottom: 10px solid transparent;
    border-image: url('/images/computingPower/comPowerSplitIconRevolve.png') 10;
  }

  .network-header {
    width: 30%;
    font-size: 18px;
    color: #000;
    text-align: center;
    margin-right: 30px;

    .network-total {
      margin-top: 20px;
      font-size: 32px;
      color: #004fb1;
      font-weight: 700;
    }
  }

  .network-details {
    flex: 1;
    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 13px;
      font-size: 16px;
      padding: 8px 16px;
      border-radius: 20px;
      transition: all 0.3s ease;
    }
    .opened {
      background: linear-gradient(90deg, rgba(76, 175, 80, 0.2) 0%, rgba(76, 175, 80, 0.1) 100%);
      border: 1px solid rgba(76, 175, 80, 0.3);
      color: #7fa57d;
      margin-top: 15px;
      margin-bottom: 10px;
    }

    .remaining {
      background: linear-gradient(90deg, rgba(33, 150, 243, 0.2) 0%, rgba(33, 150, 243, 0.1) 100%);
      border: 1px solid rgba(33, 150, 243, 0.3);
      color: #004fb1;
    }
  }
}
</style>
