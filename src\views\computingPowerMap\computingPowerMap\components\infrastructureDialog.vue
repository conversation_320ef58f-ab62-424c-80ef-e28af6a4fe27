<template>
  <div class="bottom-dialog-container">
    <img @click="closeBottomDialog" src="/images/computingPower/comPowerIconClose.png" alt="" />
    <div class="bottom-dialog-content">
      <div class="bottom-dialog-main">
        <div class="bottom-dialog-title">
          <div>基础设施</div>
          <div>
            <img src="/images/computingPower/comPowerDialogTitleBg.png" alt="" />
          </div>
        </div>
        <div class="bottom-dialog-tooltip">
          <div class="bottom-dialog-infrastructure">
            <div
              @click="changeInfrastructureTabValue('工程')"
              class="bottom-dialog-infrastructure-item"
              :class="{ active: infrastructureTabValue == '工程' }"
            >
              工程态
            </div>
            <div
              @click="changeInfrastructureTabValue('在网')"
              class="bottom-dialog-infrastructure-item"
              :class="{ active: infrastructureTabValue == '在网' }"
            >
              在网态
            </div>
          </div>
          <div class="bottom-dialog-search">
            <el-select
              v-model="cloudValue"
              @change="changeCloudTypeFunc"
              clearable
              placeholder="请选择"
              :suffix-icon="CaretBottom"
              style="width: 100px"
            >
              <el-option
                v-for="item in cloudOption"
                :key="item.cloudName"
                :label="item.cloudName"
                :value="item.cloudName"
              />
            </el-select>
            <el-select
              v-model="platformTypeName"
              @change="changeCloudPlatformTypeNameFunc"
              placeholder="请选择云平台"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 140px"
            >
              <el-option
                v-for="item in platformTypeNamePoolList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
            <el-select
              v-model="resourcePoolValue"
              @change="changeResourcePooolFunc"
              placeholder="请选择资源池"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 160px"
            >
              <el-option
                v-for="item in resourcePoolList"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
            <el-button class="exportBtn" @click="exportFile">
              <img src="/images/computingPower/comPowerIconExport.png" alt="" />
              导出
            </el-button>
          </div>
        </div>
        <div class="bottom-dialog-infrastructure-sub-tab">
          <div
            @click="changeInfrastructureSubTabValue(item.value, item)"
            class="bottom-dialog-infrastructure-sub-item"
            :class="{ active: infrastructureSubTabValue == item.value }"
            :key="'infrastructureSubTabValue' + index"
            v-for="(item, index) in infrastructureSubTabList"
          >
            {{ item.label }}
          </div>
        </div>
        <div class="bottom-dialog-table">
          <el-table
            ref="tableRef"
            class="comPowerTable"
            :data="tableData"
            style="width: 100%"
            height="250"
          >
            <el-table-column type="index" label="序号" />
            <el-table-column
              :key="item.prop"
              v-for="item in columns"
              show-overflow-tooltip
              :prop="item.prop"
              :label="item.label"
            >
              <template #default="scope">
                <span
                  v-if="item.prop == 'relatedPool' || item.prop == 'bsName'"
                  class="c-comPower-table-cell-blue-theme"
                  >{{ scope.row[item.prop] || '--' }}</span>
                <span v-else>{{ scope.row[item.prop] || '--' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 10px">
            <Pagination
              :pageable="pageable"
              :handle-size-change="handleSizeChange"
              :handle-current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CaretBottom } from '@element-plus/icons-vue'
import { onMounted, reactive, type Ref, ref, watch } from 'vue'
import { ElTable } from 'element-plus'
import Pagination from './pagination.vue'
import { useDownload } from '@/hooks/useDownload'
import {
  getComPowerMapPageBaseDevice,
  getComPowerMapPageBaseDeviceHandResourcePool,
  downloadComPowerMapPageBaseDevice,
} from '@/api/modules/comPowerCenter'

// 定义类型/接口
interface OptionItem {
  name: string
  instanceId: string
}
// 定义类型/接口
interface columnsItem {
  prop: string
  label: string
}
// 关
const columns: Ref<columnsItem[]> = ref([])
// 定义需要发出的事件类型
const emit = defineEmits(['close'])
// table 实例
const tableRef = ref<InstanceType<typeof ElTable>>()

// 定义props接收父组件传递的参数
const props = defineProps({
  cloudListArr: {
    type: Array,
    required: false,
    default: () => [],
  },
  baseDeviceHandResourcePoolList: {
    type: Array,
    required: false,
    default: () => [],
  },
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)

//云类型
const cloudValue = ref('')

const cloudOption: any = ref([])
//云平台
const platformTypeName = ref('')
const platformTypeNamePoolList = ref([])
// 资源池信息
const resourcePoolValue = ref('')
const resourcePoolList = ref<OptionItem[]>([])
//表格数据
const tableData = ref([])
//上方tab切换
const infrastructureSubTabValue = ref('BLAD_BLOCK')
const infrastructureTabValue = ref('工程')

const pageable = reactive({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: 10,
  // 总条数
  total: 0,
})
//切换云类型信息
const changeCloudTypeFunc = () => {
  pageable.pageNum = 1
  platformTypeName.value = ''
  resourcePoolValue.value = ''
  cloudOption.value.forEach((item: any) => {
    if (item.cloudName == cloudValue.value) {
      platformTypeNamePoolList.value = item.platformTypeNames
    }
  })
  getDataInfo()
}
//切换云平台
const changeCloudPlatformTypeNameFunc = () => {
  pageable.pageNum = 1
  resourcePoolValue.value = ''
  getBaseDeviceHandResourcePool()
  getDataInfo()
}
//切换资源池
const changeResourcePooolFunc = () => {
  pageable.pageNum = 1
  getDataInfo()
}

// 导出文件
const exportFile = () => {
  let params: any = {
    cloudName: cloudValue.value,
    platformTypeName: platformTypeName.value,
    relatedPool: resourcePoolValue.value,
    cityCode: props.requestParams.cityCode,
    type: infrastructureSubTabValue.value,
    lifeCycleState: infrastructureTabValue.value,
  }
  let temName = '刀框数据.xlsx'
  if (infrastructureSubTabValue.value == 'BLADE_SERVER') {
    temName = '刀片服务器数据.xlsx'
  } else if (infrastructureSubTabValue.value == 'PHYSICAL_MACHINE') {
    temName = '物理服务器数据.xlsx'
  } else if (infrastructureSubTabValue.value == 'SWITCH') {
    temName = '交换机数据.xlsx'
  } else if (infrastructureSubTabValue.value == 'ROUTER') {
    temName = '路由器数据.xlsx'
  } else if (infrastructureSubTabValue.value == 'FIREWALL') {
    temName = '防火墙数据.xlsx'
  } else if (infrastructureSubTabValue.value == 'LOAD_BALANCER') {
    temName = '负载均衡数据.xlsx'
  }
  useDownload(downloadComPowerMapPageBaseDevice, temName, params)
}
const getDataInfo = () => {
  let parms: any = {
    cloudName: cloudValue.value,
    platformTypeName: platformTypeName.value,
    relatedPool: resourcePoolValue.value,
    cityCode: props.requestParams.cityCode,
    type: infrastructureSubTabValue.value,
    lifeCycleState: infrastructureTabValue.value,
    pageNum: pageable.pageNum,
    pageSize: pageable.pageSize,
  }
  getComPowerMapPageBaseDevice(parms).then((res: any) => {
    if (res.code == 200) {
      tableData.value = res.entity.records || []
      pageable.total = res.entity.total || 0
    }
  })
}

const handleSizeChange = (val: number) => {
  pageable.pageNum = 1
  pageable.pageSize = val
  getDataInfo()
}
/**
 * @description 当前页改变
 * @param {Number} val 当前页
 * @return void
 * */
const handleCurrentChange = (val: number) => {
  pageable.pageNum = val
  getDataInfo()
}

const changeInfrastructureTabValue = (val: string) => {
  infrastructureTabValue.value = val
  getDataInfo()
}
const changeInfrastructureSubTabValue = (val: string, item: any) => {
  infrastructureSubTabValue.value = val
  pageable.pageNum = 1
  columns.value = item.columns
  getDataInfo()
}
//闭当前弹窗
const closeBottomDialog = () => {
  emit('close')
}

const daokuangColumn = ref([
  {
    label: '带外管理地址',
    prop: 'bmcIp',
  },
  {
    label: '所属机房',
    prop: 'idcRoom',
  },
  {
    label: '所属机架',
    prop: 'idcRack',
  },
  {
    label: '所属云平台',
    prop: 'platformTypeName',
  },
  {
    label: '所属硬件资源池',
    prop: 'relatedPool',
  },
  {
    label: '设备型号',
    prop: 'model',
  },
])
const daopianServerOrJiaohuanOrLuyouColumn = ref([
  {
    label: '设备名称',
    prop: 'name',
  },
  {
    label: '所属机房',
    prop: 'idcRoom',
  },
  {
    label: '所属机架',
    prop: 'idcRack',
  },
  {
    label: '所属云平台',
    prop: 'platformTypeName',
  },
  {
    label: '所属硬件资源池',
    prop: 'relatedPool',
  },
  {
    label: '设备型号',
    prop: 'model',
  },
  {
    label: '管理网IPV4地址',
    prop: 'mgmtIpv4',
  },
  {
    label: '管理网IPV6地址',
    prop: 'mgmtIpv6',
  },
])
const wuliServerColumn = ref([
  {
    label: '设备名称',
    prop: 'name',
  },
  {
    label: '操作系统版本',
    prop: 'idcRoom',
  },
  {
    label: '设备型号',
    prop: 'idcRoom',
  },
  {
    label: '所属机房',
    prop: 'idcRoom',
  },
  {
    label: '所属机架',
    prop: 'idcRack',
  },
  {
    label: '所属云平台',
    prop: 'platformTypeName',
  },
  {
    label: '所属硬件资源池',
    prop: 'relatedPool',
  },
  {
    label: '管理网IPV4地址',
    prop: 'mgmtIpv4',
  },
  {
    label: '管理网IPV6地址',
    prop: 'mgmtIpv6',
  },
])
const fanghuoqiangOrFuzaiColumn = ref([
  {
    label: '设备名称',
    prop: 'name',
  },
  {
    label: '所属机房',
    prop: 'idcRoom',
  },
  {
    label: '所属机架',
    prop: 'idcRack',
  },
  {
    label: '所属云平台',
    prop: 'platformTypeName',
  },
  {
    label: '所属硬件资源池',
    prop: 'relatedPool',
  },
  {
    label: '所属硬件分区',
    prop: 'hardwarePartition',
  },
  {
    label: '所属虚拟资源池',
    prop: 'virtualResourcePool',
  },
  {
    label: '设备型号',
    prop: 'model',
  },
  {
    label: '管理网IPV4地址',
    prop: 'mgmtIpv4',
  },
  {
    label: '管理网IPV6地址',
    prop: 'mgmtIpv6',
  },
])

const infrastructureSubTabList = ref([
  {
    label: '刀框',
    value: 'BLAD_BLOCK',
    columns: daokuangColumn.value,
  },
  {
    label: '刀片服务器',
    value: 'BLADE_SERVER',
    columns: daopianServerOrJiaohuanOrLuyouColumn.value,
  },
  {
    label: '物理服务器',
    value: 'PHYSICAL_MACHINE',
    columns: wuliServerColumn.value,
  },
  {
    label: '交换机',
    value: 'SWITCH',
    columns: daopianServerOrJiaohuanOrLuyouColumn.value,
  },
  {
    label: '路由器',
    value: 'ROUTER',
    columns: daopianServerOrJiaohuanOrLuyouColumn.value,
  },
  {
    label: '防火墙',
    value: 'FIREWALL',
    columns: fanghuoqiangOrFuzaiColumn.value,
  },
  {
    label: '负载均衡',
    value: 'LOAD_BALANCER',
    columns: fanghuoqiangOrFuzaiColumn.value,
  },
])
//查询物理资源池
const getBaseDeviceHandResourcePool = () => {
  let params = {
    cloudName: cloudValue.value,
    platformTypeName: platformTypeName.value,
    cityCode: props.requestParams.cityCode,
    pageNum: 1,
    pageSize: 9999,
  }
  getComPowerMapPageBaseDeviceHandResourcePool(params).then((res: any) => {
    if (res.code == 200) {
      resourcePoolList.value = res.entity.records || []
    }
  })
}
onMounted(() => {
  cloudValue.value = props.requestParams.cloudName
  platformTypeName.value = props.requestParams.platformTypeName
  cloudOption.value = props.cloudListArr
  cloudOption.value.forEach((item: any) => {
    if (item.cloudName == cloudValue.value) {
      platformTypeNamePoolList.value = item.platformTypeNames
    }
  })
  getBaseDeviceHandResourcePool()
  columns.value = daokuangColumn.value
  getDataInfo()
})
</script>
<style scoped lang="scss">
.bottom-dialog-container {
  position: fixed;
  left: 10px;
  bottom: 10px;
  width: calc(100% - 494px);
  height: 466px;
  z-index: 20;
  box-sizing: border-box;
  //height: 367px;
  & > img {
    position: absolute;
    top: 0;
    right: 10px;
    cursor: pointer;
  }
  .bottom-dialog-content {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 430px;
    padding: 12px 9px 11px 10px;
    box-shadow: 0px 0px 5px 0px #09155c;
    border-radius: 1px;
    box-sizing: border-box;
    background-color: rgba(170, 198, 245, 0.3);
    .bottom-dialog-main {
      width: 100%;
      height: 100%;
      padding: 8px 15px;
      border-radius: 2px;
      box-sizing: border-box;
      background: linear-gradient(0deg, rgba(124, 166, 238, 0.92), rgba(10, 52, 103, 0.92));
    }
    //opacity: 0.92;
    .bottom-dialog-title {
      //display: flex;
      div {
        display: inline-block;
        font-size: 18px;
        color: #ffffff;
        width: calc(100% - 120px);
        vertical-align: top;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei';
        img {
          width: 100%;
        }
      }
      & > div:nth-child(1) {
        width: 80px;
        margin-right: 30px;
      }
    }
    .bottom-dialog-tooltip {
      height: 39px;
      box-sizing: border-box;
      margin-bottom: 10px;
      text-align: right;
      border-bottom: 2px solid #3161b4;
      padding-top: 4px;
      position: relative;
      display: flex;
      align-items: end;
      justify-content: center;
      .bottom-dialog-infrastructure {
        display: inline-block;
        margin-right: 139px;
        vertical-align: bottom;
        .bottom-dialog-infrastructure-item {
          width: 120px;
          height: 25px;
          line-height: 25px;
          font-size: 16px;
          color: #7f91a7;
          display: inline-block;
          margin-right: 21px;
          background: url('/images/computingPower/comPowerInfrastructureTabBg.png') no-repeat 0 0;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &.active {
            color: #ffffff;
            background: url('/images/computingPower/comPowerInfrastructureTabActiveBg.png')
              no-repeat 0 0;
            background-size: 100% 100%;
          }
        }
      }
      .bottom-dialog-search {
        display: inline-block;
        vertical-align: top;
        margin-right: 60px;
        position: absolute;
        right: 0;
        top: 0;
        .el-select {
          background: transparent;
          font-size: 15px;
          color: #ffffff;
          margin-right: 13px;
        }
        .exportBtn {
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          img {
            margin-right: 8px;
          }
        }
      }
    }
    .bottom-dialog-infrastructure-sub-tab {
      text-align: center;
      .bottom-dialog-infrastructure-sub-item {
        display: inline-block;
        margin: 4px 23px;
        background: #3c5784;
        border-radius: 2px;
        font-size: 16px;
        color: #ffffff;
        padding: 3px 16px;
        cursor: pointer;
        &.active {
          background: linear-gradient(-90deg, #2d78d3, #0f87a3, #2064b7);
        }
      }
    }
    .bottom-dialog-table {
      .el-table {
        background: transparent;
        th.el-table__cell {
          background: transparent;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.comPowerPage.el-pagination {
  .el-select__wrapper.el-tooltip__trigger {
    background: #3f6eb8;
    color: #fff;
    box-shadow: none;
    border-radius: 1px;
    border: 1px solid #42659e;
    .el-select__input.is-default,
    .el-select__selected-item.el-select__placeholder {
      color: #fff;
    }
  }
  .el-pagination__total.is-first {
    color: #ffffff;
    font-size: 15px;
  }
  .el-pagination__goto {
    color: #ffffff;
  }
  .el-input__wrapper {
    background: #3f6eb8;
    border: none;
    box-shadow: none;
    .el-input__inner {
      color: #ffffff;
    }
  }
  .el-pagination__classifier {
    color: #ffffff;
  }
}
.comPowerPage.el-pagination.is-background .btn-next:disabled,
.comPowerPage.el-pagination.is-background .btn-prev:disabled {
  background: transparent;
  color: #3f5d78;
}
.comPowerPage.el-pagination.is-background .btn-next,
.comPowerPage.el-pagination.is-background .btn-prev {
  background: transparent;
  color: #0787de;
}
.comPowerPage.el-pagination.is-background .el-pager li {
  background: #3f6eb8;
  padding: 2px 16px;
}
.comPowerPage.el-pagination.is-background .el-pager li.is-active {
  background: #317ced;
}
.comPowerPage.el-pagination .btn-next .el-icon,
.comPowerPage.el-pagination .btn-prev .el-icon {
  font-size: 24px;
}
</style>
