<template>
  <div id="EvsDetail" class="table-box">
    <sl-page-header
      title="云硬盘详情"
      :icon="{
        class: 'pege_yunyingpan',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <div class="operation-buttons" v-if="!shouldHideResourceOperations">
          <!-- 绑定/解绑设备按钮 -->
          <el-button
            v-if="!detailData.mountOrNot || detailData.mountOrNot === '否'"
            type="primary"
            :disabled="detailData.recoveryStatus != 0 || detailData.changeStatusCn !== '未变更'"
            @click="handleBind"
          >
            绑定云主机
          </el-button>
          <el-button
            v-else
            type="primary"
            :disabled="detailData.recoveryStatus != 0 || detailData.changeStatusCn !== '未变更'"
            @click="handleUnbind"
          >
            解绑云主机
          </el-button>
        </div>
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="isPublic ? publicFormOptions : formOptions"
        >
        </sl-form>
      </div>
    </el-scrollbar>

    <!-- 绑定设备弹窗 -->
    <DeviceBindDialog
      v-model:visible="bindDialogVisible"
      v-model:business-sys-id="detailData.businessSysId"
      @selectDevice="handleDeviceSelect"
      :source-type="isPublic ? 'DG' : ''"
    />
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive, computed } from 'vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getResourceDetail, evsBind, evsUnbind } from '@/api/modules/resourecenter'
import { ElMessage, ElMessageBox } from 'element-plus'
import DeviceBindDialog from './components/DeviceBindDialog.vue'
import { useRolePermission } from '../hooks/useRolePermission'

const router = useRouter()
const route = useRoute()

const { shouldHideResourceOperations } = useRolePermission()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')
// 判断是否为对公
const isPublic = computed(() => route.query.sourceType === 'DG')

const detailData = reactive<any>({
  dataDisk: '',
  deviceId: '',
  mountOrNot: '',
  ecsName: '',
  applyTime: '',
  tenantName: '',
  businessSysName: '',
  cloudPlatform: '',
  resourcePoolName: '',
  orderCode: '',
  projectName: '',
  createTime: '',
  expireTime: '',
  billId: '',
  deviceStatusCn: '',
  deviceStatus: '',
  recoveryStatus: '',
  recoveryStatusCn: '',
  changeStatusCn: '',
  applyUserName: '',
  orderId: 0,
  goodsOrderId: '',
  businessSysId: '',
  resourcePoolCode: '',
  id: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '数据盘',
        type: 'text',
        key: 'dataDisk',
        span: 8,
      },
      {
        label: '资源ID',
        type: 'text',
        key: 'deviceId',
        span: 8,
      },
      {
        label: '是否挂载云主机',
        type: 'text',
        key: 'mountOrNot',
        span: 8,
      },
      {
        label: '云主机',
        type: 'text',
        key: 'ecsName',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '回收状态',
        type: 'text',
        key: 'recoveryStatusCn',
        span: 8,
      },
      {
        label: '变更状态',
        type: 'text',
        key: 'changeStatusCn',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

const publicFormOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '数据盘',
        type: 'text',
        key: 'dataDisk',
        span: 8,
      },
      {
        label: '资源ID',
        type: 'text',
        key: 'deviceId',
        span: 8,
      },
      {
        label: '是否挂载云主机',
        type: 'text',
        key: 'mountOrNot',
        span: 8,
      },
      {
        label: '云主机',
        type: 'text',
        key: 'ecsName',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '订单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '订购时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '订购人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

const fetchResouceDetail = async () => {
  const res = await getResourceDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.go(-1)
}

// 绑定设备相关变量
const bindDialogVisible = ref(false)
const tableLoading = ref(false)

// 处理绑定设备
const handleBind = () => {
  if (detailData.recoveryStatus != 0 || detailData.changeStatusCn !== '未变更') {
    return ElMessage.warning('当前资源状态不允许绑定操作')
  }
  bindDialogVisible.value = true
}

// 处理设备选择
const handleDeviceSelect = async (device: any) => {
  if (!detailData.id) return

  try {
    tableLoading.value = true
    await evsBind({
      volumeResourceDetailId: detailData.id,
      vmResourceDetailId: device.id,
    })

    ElMessage.success('绑定设备成功')
    bindDialogVisible.value = false
    await fetchResouceDetail() // 刷新详情数据
  } catch (error) {
    console.error(error)
  } finally {
    tableLoading.value = false
  }
}

// 处理解绑设备
const handleUnbind = async () => {
  if (detailData.recoveryStatus != 0 || detailData.changeStatusCn !== '未变更') {
    return ElMessage.warning('当前资源状态不允许解绑操作')
  }

  try {
    await ElMessageBox.confirm('确定要解绑该设备吗？', '解绑确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    tableLoading.value = true
    const res = await evsUnbind({
      volumeResourceDetailId: detailData.id,
    })

    if (res.code !== 200) {
      return
    }

    ElMessage.success('解绑设备成功')
    await fetchResouceDetail() // 刷新详情数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  } finally {
    tableLoading.value = false
  }
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResouceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}

.tab-content {
  margin-top: 16px;
}

.operation-buttons {
  background-color: white;
  margin: 8px 8px -8px 8px;
  padding: 8px;
  display: flex;
  justify-content: flex-end;
  border-bottom: 1px solid #e0e0e0;
}

.tab-content {
  background-color: white;
  margin: -8px 8px 8px 8px;
  padding: 8px 24px;
  :deep(.el-tabs__item) {
    min-width: 80px;
  }
}

.resource-item {
  height: 30px;
  padding: 0 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #3d3d3d;
  background-color: rgba(216, 216, 216, 0.6);
  opacity: 0.65;
  border-radius: 8px;
  font-size: 14px;
}
</style>
