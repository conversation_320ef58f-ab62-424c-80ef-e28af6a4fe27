import { ref, reactive } from 'vue'
import { getEcsDetailUsage } from '@/api/modules/resourecenter'
import { getDateRange, getDateFromToday } from '@/utils/dateFormat'

export interface PerformanceDataItem {
  createTime: string
  cpuUtil: number
  memUtil: number
  diskReadIops: number
  diskWriteIops: number
}

export const usePerformanceData = (resourceId: string) => {
  // 日期类型选项
  const dateTypeList = ref([
    { code: 'weekly', name: '最近一周', days: 7 },
    { code: 'monthly', name: '最近30天', days: 30 },
  ])

  const activeDateType = ref('weekly') // 初始化日期类型选项为最近一周
  const date = ref(getDateFromToday({ days: -1 })) // 初始化日期为昨天
  let dateRange = reactive(getDateRange({ endDate: date.value, days: 7 }))

  const showChart = ref(false)
  const performanceData = ref<PerformanceDataItem[]>([])

  // 禁用日期函数
  const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
  }

  // 处理日期或日期类型变化
  const handleDateOrDateTypeChange = () => {
    const _dateType = dateTypeList.value.find((i) => i.code === activeDateType.value)
    if (_dateType) {
      dateRange = { ...getDateRange({ endDate: date.value, days: _dateType.days }) }
    } else {
      console.error(`Invalid date type: ${activeDateType.value}`)
    }
    // 获取数据
    getPerformanceData()
  }

  // 获取性能数据
  const getPerformanceData = async () => {
    if (!resourceId) {
      console.log('resourceId为空，无法获取性能数据')
      performanceData.value = []
      showChart.value = false
      return
    }

    try {
      const params = {
        startTime: dateRange.startDate,
        endTime: dateRange.endDate,
      }
      console.log('调用getEcsDetailUsage API:', { resourceId, params })
      const res = await getEcsDetailUsage(resourceId, params)
      console.log('API响应:', res)
      performanceData.value = res.entity || []
      showChart.value = res.entity && res.entity.length > 0
      console.log('设置performanceData:', performanceData.value, 'showChart:', showChart.value)
    } catch (error) {
      console.error('获取性能数据失败:', error)
      performanceData.value = []
      showChart.value = false
    }
  }

  return {
    dateTypeList,
    activeDateType,
    date,
    dateRange,
    showChart,
    performanceData,
    disabledDate,
    handleDateOrDateTypeChange,
    getPerformanceData,
  }
}
