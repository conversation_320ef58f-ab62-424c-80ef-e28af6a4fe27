<template>
  <div class="gpu-bar-chart">
    <!-- 上面 -->
    <div class="chart-header">
      <span class="total-label">物理卡总数：</span>
      <span class="total-value">{{ totalCards }}</span>
    </div>
    <div class="legend">
      <div class="legend-item" v-for="item in gpuData" :key="item.label">
        <span class="dot" :style="{ backgroundColor: item.color }"></span>
        <span class="text">{{ item.label }}:{{ item.value }}</span>
      </div>
    </div>
    <div
      v-show="chartVisible"
      ref="chartRef"
      style="width: 100%; height: 30px; padding-left: 5px"
    ></div>
    <div
      v-show="!chartVisible"
      style="
        width: 100%;
        height: 30px;
        padding-left: 5px;
        text-align: center;
        color: #999;
        font-size: 12px;
      "
    >
      暂无数据
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

export interface GpuDataItem {
  label: string
  value: number
  color: string
}

const chartRef = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null

// GPU数据配置
const gpuData = ref<GpuDataItem[]>([
  { label: 'T4', value: 840, color: '#4D9BF9' },
  { label: 'A10', value: 188, color: '#E91E63' },
  { label: 'V100', value: 56, color: '#FF9800' },
  { label: 'A40', value: 40, color: '#4CAF50' },
])

// 计算总数
const totalCards = ref(0)

const chartVisible = ref(true)

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value, null, { renderer: 'svg' })

  const seriesData = gpuData.value.map((item) => {
    const percentage = (item.value / totalCards.value) * 100
    return {
      name: item.label,
      type: 'bar' as const,
      stack: 'total',
      data: [percentage],
      barWidth: '50%',
      itemStyle: {
        color: item.color,
        borderRadius: 0,
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    }
  })

  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const gpuItem = gpuData.value.find((item) => item.label === params.seriesName)
        return `${params.seriesName}: ${gpuItem?.value || 0}`
      },
    },
    grid: {
      left: 0,
      right: 0,
      top: 10,
      bottom: 10,
      containLabel: false,
    },
    xAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 100,
    },
    yAxis: {
      type: 'category',
      data: [''],
      show: false,
    },
    series: seriesData,
  }

  chartInstance.setOption(option)
}

const initData = (data: GpuDataItem[]) => {
  gpuData.value = data
  totalCards.value = gpuData.value.reduce((sum, item) => sum + item.value, 0)
  chartVisible.value = totalCards.value > 0

  if (chartInstance) {
    const seriesData = gpuData.value.map((item) => {
      const percentage = (item.value / totalCards.value) * 100
      return {
        name: item.label,
        type: 'bar' as const,
        stack: 'total',
        data: [percentage],
        barWidth: '50%',
        itemStyle: {
          color: item.color,
          borderRadius: 0,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      }
    })
    chartInstance.setOption(
      {
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            const gpuItem = gpuData.value.find((item) => item.label === params.seriesName)
            return `${params.seriesName}: ${gpuItem?.value || 0}`
          },
        },
        grid: {
          left: 0,
          right: 0,
          top: 10,
          bottom: 10,
          containLabel: false,
        },
        xAxis: {
          type: 'value',
          show: false,
          min: 0,
          max: 100,
        },
        yAxis: {
          type: 'category',
          data: [''],
          show: false,
        },
        series: seriesData,
      },
      true,
    )
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  setTimeout(() => {
    initChart()
  }, 500)
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  initData,
})
</script>

<style lang="scss" scoped>
.gpu-bar-chart {
  width: 100%;
  min-height: 95px;
  padding-bottom: 1px;
  .chart-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    font-size: 12px;
    color: #333;
    font-weight: bold;

    .total-label {
      margin-right: 8px;
    }
  }

  .legend {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    min-height: 30px;
    .legend-item {
      display: flex;
      align-items: center;
      font-size: 10px;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
      }

      .text {
        color: #666;
      }
    }
  }
}
</style>
