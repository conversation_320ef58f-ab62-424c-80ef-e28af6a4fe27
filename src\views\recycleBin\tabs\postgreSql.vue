<template>
  <div class="form-container">
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :data="goodsList"
      hidden-table-header
      :pagination="false"
      row-key="goodsOrderId"
    >
    </SlProTable>
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { reactive } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { useOp } from './useOp'

const { operationRender } = useOp()
const { goodsList = [] } = defineProps<{
  goodsList?: any[]
}>()
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55 },
  { prop: 'deviceName', label: '云数据库名称', width: 150 },
  { prop: 'resourceId', label: '资源ID', width: 200 },
  { prop: 'osVersion', label: '版本', width: 120 },
  {
    prop: 'syncRecovery ',
    label: '是否组合回收',
    width: 120,
    render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
  },
  { prop: 'spec', label: '实例规格', width: 120 },
  { prop: 'sysDisk', label: '系统盘', width: 150 },
  { prop: 'dataDisk', label: '数据盘', width: 150 },
  { prop: 'ip', label: 'IP', width: 150 },
  { prop: 'eip', label: '弹性公网IP', width: 150 }, // 显示绑定的公网IP或占位符
  { prop: 'bandWidth', label: '带宽', width: 100 }, // 显示带宽或占位符
  { prop: 'tenantName', label: '租户', width: 100 },
  { prop: 'businessSysName', label: '业务系统', width: 150 }, // 支持筛选过滤
  { prop: 'domainName', label: '所属云', width: 120 }, // 支持筛选过滤
  { prop: 'resourcePoolName', label: '资源池', width: 120 }, // 支持筛选过滤
  { prop: 'orderCode', label: '工单编号', width: 150 },
  { prop: 'projectName', label: '项目名称', width: 150 },
  { prop: 'createTime', label: '开通时间', width: 150 },
  { prop: 'expireTime', label: '到期时间', width: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'instanceUuid', label: 'UUID', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 }, // 运行中，已关机，异常
  { prop: 'applyUserName', label: '申请人', width: 100 },
  { prop: 'operation', label: '操作', width: 100, fixed: 'right', render: operationRender },
])
</script>
<style lang="scss" scoped>
.form-container {
  margin: 8px;
}
</style>
