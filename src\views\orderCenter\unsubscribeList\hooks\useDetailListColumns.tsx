import type { ColumnProps } from '@/components/SlProTable/interface'
import { h } from 'vue'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { cancelUnsubscribeOrder } from '@/api/modules/orderCenter'

/**
 * 退订详情各资源类型的表格列配置
 */
export function useDetailListColumns({
  allowDelete = true,
}: {
  allowDelete?: boolean
} = {}) {
  // 格式化退订状态
  const formatRecoveryStatus = (status: number): string => {
    const statusMap: Record<number, string> = {
      0: '未发起回收',
      1: '资源待退订',
      2: '退订中',
      3: '退订完成',
      4: '退订失败',
      5: '网络退订单独参数',
      6: '取消',
    }
    return statusMap[status] || '--'
  }

  // 取消退订操作列渲染函数
  const renderCancelOperation = (row: any, onCancelUnsubscribe: (row: any) => void) => {
    return h(
      ElButton,
      {
        type: 'primary',
        link: true,
        disabled: row.recoveryStatus != 1, // 只有当退订状态为1时才允许取消退订
        onClick: () => onCancelUnsubscribe(row),
      },
      { default: () => '取消退订' },
    )
  }

  // 格式化计费类型
  const formatBillType = (type: string): string => {
    const billTypeMap: Record<string, string> = {
      day: '按天计费',
      month: '按月计费',
      year: '按年计费',
    }
    return billTypeMap[type] || type || '--'
  }

  // 格式化计费方式
  const formatChargeType = (type: string): string => {
    const chargeTypeMap: Record<string, string> = {
      quant: '按量计费',
      require: '按需计费',
    }
    return chargeTypeMap[type] || type || '--'
  }

  // 格式化是否挂载云主机
  const formatIsMounted = (value: any): string => {
    return value ? '是' : '否'
  }

  // 格式化系列
  const formatSeries = (value: string): string => {
    const seriesMap: Record<string, string> = {
      ALONE: '单机版',
      COLONY: '主备版本',
    }
    return seriesMap[value] || value || '--'
  }

  // 文本渲染函数
  const renderText = (text: string) => {
    return h('div', {}, text)
  }

  // 取消退订处理函数
  const handleCancelUnsubscribe = async (row: any, onRefresh?: () => void) => {
    try {
      // 显示二次确认框
      await ElMessageBox.confirm('确定要取消退订该资源吗？', '取消退订确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      const res = await cancelUnsubscribeOrder({
        productIds: [row.id],
      })
      if (res.code === 200) {
        ElMessage.success('取消退订成功')
        onRefresh?.()
      } else {
        ElMessage.error(res.message || '取消退订失败')
      }
    } catch (error) {
      // 如果是用户取消操作，不显示错误信息
      if (error === 'cancel') {
        return
      }
      console.error('取消退订失败:', error)
      ElMessage.error('取消退订失败')
    }
  }

  // ECS资源的列配置
  const getEcsColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'vmName', label: '主机名称', width: 150, fixed: 'left' },
      { prop: 'deviceId', label: '资源ID', width: 200 },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      { prop: 'osVersion', label: '系统版本', width: 120 },
      { prop: 'spec', label: '实例规格', width: 120 },
      {
        prop: 'syncRecovery ',
        label: '是否组合回收',
        width: 120,
        render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
      },
      { prop: 'sysDisk', label: '系统盘', width: 150 },
      {
        prop: 'evsInfo',
        label: '数据盘',
        minWidth: '150px',
        render(scope) {
          return (
            <div>
              {scope.row.evsModelList?.length
                ? (scope.row.evsModelList?.map((item: any) => item.dataDisk).join(',') ?? '--')
                : '--'}
            </div>
          )
        },
      },
      { prop: 'ip', label: 'IP', width: 150 },
      { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
      {
        label: '带宽',
        prop: 'eipModel.bandwidth',
        minWidth: 100,
      },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // GCS资源的列配置
  const getGcsColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'vmName', label: '主机名称', width: 150, fixed: 'left' },
      { prop: 'deviceId', label: '资源ID', width: 200 },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      { prop: 'osVersion', label: '系统版本', width: 120 },
      { prop: 'spec', label: '实例规格', width: 120 },
      {
        prop: 'syncRecovery ',
        label: '是否组合回收',
        width: 120,
        render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
      },
      { prop: 'sysDisk', label: '系统盘', width: 150 },
      {
        prop: 'evsInfo',
        label: '数据盘',
        minWidth: '150px',
        render(scope) {
          return (
            <div>
              {scope.row.evsModelList?.length
                ? (scope.row.evsModelList?.map((item: any) => item.dataDisk).join(',') ?? '--')
                : '--'}
            </div>
          )
        },
      },
      { prop: 'ip', label: 'IP', width: 150 },
      { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
      {
        label: '带宽',
        prop: 'eipModel.bandwidth',
        minWidth: 100,
      },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // EVS资源的列配置
  const getEvsColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'dataDisk', label: '数据盘', minWidth: 150, fixed: 'left' },
      { prop: 'deviceId', label: '资源ID', width: 200 },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      {
        label: '是否挂载云主机',
        prop: 'mountDataDisk',
        minWidth: 130,
        render(scope) {
          return <div>{scope.row.vmName ? '是' : '否'}</div>
        },
      },
      { prop: 'vmName', label: '云主机', minWidth: 150 },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // OBS资源的列配置
  const getObsColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'obsName', label: '对象存储名称', width: 200, fixed: 'left' },
      { prop: 'deviceId', label: '资源ID', width: 200 },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      {
        prop: 'chargeType',
        label: '计费方式',
        width: 100,
        render: ({ row }) => renderText(formatChargeType(row.chargeType)),
      },
      { prop: 'storeType', label: '存储类型', width: 120 },
      { prop: 'capacity', label: '容量', width: 120 },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // SLB资源的列配置
  const getSlbColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'slbName', label: '负载均衡名称', width: 200, fixed: 'left' },
      { prop: 'deviceId', label: '资源ID', width: 200 },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      {
        prop: 'syncRecovery ',
        label: '是否组合回收',
        width: 120,
        render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
      },
      { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
      {
        prop: 'eipModel.bandwidth',
        label: '带宽大小',
        width: 100,
      },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // NAT资源的列配置
  const getNatColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'natName', label: '网关名称', width: 200, fixed: 'left' },
      { prop: 'deviceId', label: '资源ID', width: 200 },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      { prop: 'spec', label: '实例规格', width: 200 },
      {
        prop: 'syncRecovery ',
        label: '是否组合回收',
        width: 120,
        render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
      },
      { prop: 'vpcName', label: 'VPC', minWidth: 150 },
      {
        prop: 'subnetName',
        label: '子网',
        minWidth: 220,
        render(scope) {
          return (
            <div>
              {scope.row.planeNetworkModel?.subnets?.length
                ? (scope.row.planeNetworkModel?.subnets
                    ?.map((item: any) => item.subnetName)
                    .join(',') ?? '--')
                : '--'}
            </div>
          )
        },
      },
      { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
      { prop: 'eipModel.bandwidth', label: '带宽大小', width: 100 },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // VPN资源的列配置
  const getVpnColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'name', label: 'VPN名称', width: 200, fixed: 'left' },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      { prop: 'maxConnection', label: '最大客户端数', minWidth: 120 },
      {
        label: '带宽',
        prop: 'bandwidth',
        minWidth: 120,
        render(scope) {
          return <div>{scope.row.bandwidth ? scope.row.bandwidth + 'M' : '--'} </div>
        },
      },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // EIP资源的列配置
  const getEipColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'eipName', label: '弹性公网名称', minWidth: 200, fixed: 'left' },
      { prop: 'ipVersion', label: '弹性IP类型', minWidth: 200, fixed: 'left' },
      { prop: 'deviceId', label: '资源ID', width: 200 },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      { prop: 'eip', label: '弹性公网地址', minWidth: 150 },
      { label: '带宽大小', prop: 'bandwidth', minWidth: 100 },
      { prop: 'relatedDeviceType', label: '绑定设备类型', minWidth: 150 },
      { prop: 'relatedDeviceName', label: '绑定设备名称', minWidth: 150 },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // 云数据库资源的列配置
  const getRdsMysqlColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 60, fixed: 'left' },
      { prop: 'mysqlName', label: '云数据库名称', width: 150, fixed: 'left' },
      { prop: 'deviceId', label: '资源ID', width: 180 },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      { prop: 'osVersion', label: '数据库版本', width: 120 },
      {
        prop: 'mountOrNot',
        label: '系列',
        width: 100,
        render: ({ row }) => renderText(formatSeries(row.mountOrNot)),
      },
      {
        prop: 'syncRecovery ',
        label: '是否组合回收',
        width: 120,
        render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
      },
      { prop: 'spec', label: '实例规格', width: 120 },
      { prop: 'sysDisk', label: '存储大小', width: 100 },
      { prop: 'ip', label: 'IP', width: 120 },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      {
        prop: 'message',
        label: '失败原因',
        width: 150,
        render: ({ row }) => renderText(row.message || '--'),
      },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // 云灾备资源的列配置
  const getBackupColumns = (onCancelUnsubscribe: (row: any) => void): ColumnProps<any>[] => {
    // 格式化备份类型
    const formatBackupType = (value: string): string => {
      const backupTypeMap: Record<string, string> = {
        ECS: '云主机备份',
        EVS: '块存储备份',
      }
      return backupTypeMap[value] || value || '--'
    }

    // 格式化备份频率
    const formatFrequency = (value: string): string => {
      const frequencyMap: Record<string, string> = {
        days: '每天',
        weeks: '每周',
      }
      return frequencyMap[value] || value || '--'
    }

    const columns: ColumnProps<any>[] = [
      { type: 'index', label: '序号', width: 55, fixed: 'left' },
      { prop: 'jobName', label: '云灾备名称', width: 150, fixed: 'left' },
      {
        prop: 'billType',
        label: '计费类型',
        width: 100,
        render: ({ row }) => renderText(formatBillType(row.billType)),
      },
      {
        prop: 'chargeType',
        label: '计费方式',
        width: 100,
        render: ({ row }) => renderText(formatChargeType(row.chargeType)),
      },
      {
        prop: 'backupType',
        label: '备份类型',
        width: 120,
        render: ({ row }) => renderText(formatBackupType(row.backupType)),
      },
      { prop: 'tenantName', label: '租户', width: 100 },
      { prop: 'domainName', label: '所属云', width: 120 },
      { prop: 'regionName', label: '资源池', width: 120 },
      { prop: 'azName', label: '可用区', width: 120 },
      {
        prop: 'frequency',
        label: '备份频率',
        width: 100,
        render: ({ row }) => renderText(formatFrequency(row.frequency)),
      },
      { prop: 'daysOfWeek', label: '星期', width: 100 },
      { prop: 'billId', label: '计费号', width: 150 },
      {
        prop: 'recoveryStatus',
        label: '退订状态',
        width: 120,
        render: ({ row }) => renderText(formatRecoveryStatus(row.recoveryStatus)),
      },
      { prop: 'message', label: '失败原因', width: 150 },
      { prop: 'cancelStatus', label: '取消退订状态', width: 150 },
    ]

    // 只有当允许删除时才添加操作列
    if (allowDelete) {
      columns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: ({ row }) => renderCancelOperation(row, onCancelUnsubscribe),
      })
    }

    return columns
  }

  // 根据资源类型获取对应的列配置
  const getColumnsByResourceType = (
    resourceType: string,
    onCancelUnsubscribe: (row: any) => void,
  ): ColumnProps<any>[] => {
    switch (resourceType) {
      case 'ecs':
        return getEcsColumns(onCancelUnsubscribe)
      case 'gcs':
        return getGcsColumns(onCancelUnsubscribe)
      case 'evs':
        return getEvsColumns(onCancelUnsubscribe)
      case 'eip':
        return getEipColumns(onCancelUnsubscribe)
      case 'obs':
        return getObsColumns(onCancelUnsubscribe)
      case 'slb':
        return getSlbColumns(onCancelUnsubscribe)
      case 'nat':
        return getNatColumns(onCancelUnsubscribe)
      case 'vpn':
        return getVpnColumns(onCancelUnsubscribe)
      case 'rdsMysql':
        return getRdsMysqlColumns(onCancelUnsubscribe)
      case 'backup':
        return getBackupColumns(onCancelUnsubscribe)
      default:
        return []
    }
  }

  return {
    getEcsColumns,
    getGcsColumns,
    getEvsColumns,
    getObsColumns,
    getSlbColumns,
    getNatColumns,
    getVpnColumns,
    getEipColumns,
    getRdsMysqlColumns,
    getBackupColumns,
    getColumnsByResourceType,
    handleCancelUnsubscribe,
    formatIsMounted,
    formatBillType,
    formatChargeType,
    formatSeries,
    formatRecoveryStatus,
  }
}
