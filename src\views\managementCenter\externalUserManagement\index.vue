<template>
  <div class="table-box">
    <sl-page-header
      title="外部用户管理"
      title-line="外部用户管理提供了外部用户新增、用户编辑等功能。"
      :icon="{
        class: 'page_waibuUers',
        color: '#0052D9',
        size: '40px',
      }"
    ></sl-page-header>
    <div class="sl-page-content table-main">
      <SlProTable ref="proTable" :columns="columns" :request-api="withoutListFn">
        <template #search>
          <el-button type="primary" :icon="Plus" @click="() => openDialog()" v-permission="'Add'">
            新建用户
          </el-button>
        </template>
        <template #operation="scope">
          <el-button
            @click="handleFreezingUser(scope.row)"
            type="primary"
            link
            v-permission="'Freeze'"
          >
            {{ scope.row.status == 1 ? '冻结' : '解冻' }}
          </el-button>
          <el-button
            type="primary"
            link
            :icon="EditPen"
            @click="openDialog(scope.row)"
            v-if="scope.row.isDefault != 1"
            v-permission="'Update'"
          >
            编辑
          </el-button>
        </template>
      </SlProTable>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formData.id ? '编辑外部用户' : '新建外部用户'"
      width="640px"
      top="30px"
      :close-on-click-modal="false"
      @close="close"
    >
      <sl-form
        v-if="dialogVisible"
        ref="formRef"
        :show-block-title="false"
        v-model="formData"
        :options="options"
      >
      </sl-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="tenantManagement">
import {
  createWithoutUserApi,
  freezeUserApi,
  withoutListApi,
  unfreezeUserApi,
  updateWithoutUserApi,
} from '@/api/modules/managementCenter'
import { EditPen, Plus } from '@element-plus/icons-vue'
import { computed, reactive, ref } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import type { WithoutUserType } from './interface/type'
import {
  validateAccount,
  validateTypeEmail,
  validatePassword,
  validatePhone,
} from '@/utils/validate'
import SlMessage from '@/components/base/SlMessage'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { changeDateFormat } from '@/utils'
// -----------------------用户列表------------------------------

const globalDic = useGlobalDicStore()
const { getDicNumber } = globalDic

const proTable = ref<ProTableInstance>()

const formData = ref<WithoutUserType>({
  account: '',
  username: '',
  mobilephone: '',
  factoryName: '',
  departmentDn: '',
  description: '',
})

const withoutListFn = async (form: any) => {
  const params = changeDateFormat(form, ['createdTime'])
  return await withoutListApi(params)
}
const columns = reactive<ColumnProps<WithoutUserType>[]>([
  { type: 'index', fixed: 'left', label: '序号', width: 55 },
  {
    prop: 'account',
    label: '用户账号',
    minWidth: 250,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'username',
    label: '用户姓名',
    minWidth: 250,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  { prop: 'email', label: '邮箱', minWidth: 220, search: { el: 'input' } },
  { prop: 'mobilephone', label: '手机号', minWidth: 130, search: { el: 'input' } },
  { prop: 'factoryName', label: '业务厂家', minWidth: 250, search: { el: 'input' } },
  { prop: 'departmentDn', label: '部门', minWidth: 250, search: { el: 'input' } },
  { prop: 'description', label: '描述', minWidth: 350, search: { el: 'input' } },
  {
    prop: 'status',
    label: '状态',
    width: 80,
    enum: getDicNumber('userStatus'),
    search: { el: 'select' },
  },
  { prop: 'createdUserName', label: '创建人', width: 150, search: { el: 'input' } },
  {
    prop: 'createdTime',
    label: '创建时间',
    width: 180,
    search: {
      el: 'date-picker',
      span: 2,
      props: {
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '至',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
  },
  { prop: 'operation', label: '操作', fixed: 'right', width: 150 },
])

// -----------------------编辑用户------------------------------
const options = computed(() => [
  {
    groupItems: [
      {
        label: '用户账号',
        type: 'input',
        key: 'account',
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 50,
          showWordLimit: true,
        },
        span: 24,
        rules: [
          { required: true, message: '请输入用户账号', trigger: ['blur', 'change'] },
          { validator: validateAccount, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '用户姓名',
        type: 'input',
        key: 'username',
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 50,
          showWordLimit: true,
        },
        span: 24,
        rules: [{ required: true, message: '请输入用户姓名', trigger: ['blur', 'change'] }],
      },
      {
        label: '密码',
        type: 'input',
        key: 'password',
        props: {
          type: 'password',
          maxlength: 50,
        },
        span: 24,
        rules: [
          {
            required: formData.value.id ? false : true,
            message: '请输入密码',
            trigger: ['blur', 'change'],
          },
          { validator: validatePassword, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '邮箱',
        type: 'input',
        key: 'email',
        props: {
          maxlength: 50,
        },
        span: 24,
        rules: [{ validator: validateTypeEmail, trigger: ['blur', 'change'] }],
      },
      {
        label: '手机号',
        type: 'input',
        key: 'mobilephone',
        props: {},
        span: 24,
        rules: [
          { required: true, message: '请输入手机号', trigger: ['blur', 'change'] },
          { validator: validatePhone, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '业务厂家',
        type: 'input',
        key: 'factoryName',
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 50,
          showWordLimit: true,
        },
        span: 24,
        rules: [{ required: true, message: '请输入业务厂家', trigger: ['blur', 'change'] }],
      },
      {
        label: '部门',
        type: 'input',
        key: 'departmentDn',
        props: {
          type: 'textarea',
          rows: 1,
          maxlength: 50,
          showWordLimit: true,
        },
        rules: [{ required: true, message: '请输入部门', trigger: ['blur', 'change'] }],
        span: 24,
      },
      {
        label: '描述',
        type: 'input',
        key: 'description',
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 200,
          showWordLimit: true,
        },
        span: 24,
      },
    ],
  },
])

const dialogVisible = ref(false)

const openDialog = (row?: any) => {
  if (row) {
    // 获取详情 接口文档不清晰
    Object.keys(formData.value!).forEach((key) => {
      formData.value![key] = row[key]
    })
    formData.value!['id'] = row.id
    row['email'] && (formData.value!['email'] = row['email'])
  }
  dialogVisible.value = true
}

const formRef = ref<any>(null)

//修改添加
const submit = async () => {
  if (!(await formRef.value?.validate(() => true))) return
  const params = { ...formData.value! }
  if (params.password) params.password = btoa(params.password)
  params!.id ? await updateWithoutUserApi(params) : await createWithoutUserApi(params)
  dialogVisible.value = false
  SlMessage.success(params!.id ? '编辑成功' : '创建成功')
  proTable.value?.getTableList()
}

//删除
const handleFreezingUser = async (row: any) => {
  const msg =
    row.status == 1
      ? '是否冻结当前用户？冻结后账号无法通过鉴权调用我方接口。'
      : '是否解冻当前用户？解冻后账号可以通过鉴权调用我方接口。'
  const statusStr = row.status == 1 ? '冻结' : '解冻'
  await ElMessageBox.confirm(msg, '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res =
        row.status == 1
          ? await freezeUserApi({
              id: row.id,
            })
          : await unfreezeUserApi({
              id: row.id,
            })
      if (res.code == 200) {
        SlMessage.success(`${statusStr}成功`)
        proTable.value?.getTableList()
      } else {
        SlMessage.error(res.message || `${statusStr}失败`)
      }
    })
    .catch(() => {
      console.log(`${statusStr}取消`)
    })
}
//关闭弹窗
const close = () => {
  formData.value = {
    account: '',
    username: '',
    mobilephone: '',
    factoryName: '',
    departmentDn: '',
    description: '',
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow: hidden;
}
</style>
