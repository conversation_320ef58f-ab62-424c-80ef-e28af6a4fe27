<template>
  <div class="table-box">
    <sl-page-header
      title="安全组"
      title-line="安全组（Security Group）是指在云计算环境中，由一组预定义的访问控制规则所构成的虚拟防护机制，实现对资源的安全保护和访问管理的系统。"
      :icon="{
        class: 'page_anquanzu',
        color: '#0052D9',
        size: '40px',
      }"
    >
      <template #custom>
        <sl-base-tabs
          :tabs="availableTabs"
          v-model="activeTab"
          v-if="!shouldHideTabs"
        ></sl-base-tabs>
      </template>
    </sl-page-header>
    <div class="resource-tab" v-if="activeTab === 'INNER'">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleToCreate" type="primary"> 创建安全组 </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: collapsed }"
          ref="formRef"
          :options="formOptions"
          v-model="formModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <DataList
          ref="dataListRef"
          :query-params="queryParams"
          :hide-operations="shouldHideResourceOperations"
        ></DataList>
      </div>
    </div>
    <div v-if="activeTab === 'DG'">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleToCreatePublic" type="primary"> 创建安全组 </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: dgCollapsed }"
          ref="dgFormRef"
          :options="DGFormOptions"
          v-model="dgFormModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <PublicDataList
          ref="PubRef"
          :query-params="dgQueryParams"
          :hide-operations="shouldHideResourceOperations"
        ></PublicDataList>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import slForm from '@/components/form/SlForm.vue'
import DataList from './components/DataList.vue'
import PublicDataList from './components/PublicDataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useDichooks } from '../hooks/useDichooks'
import { useRouter } from 'vue-router'
import { useRolePermission } from '../hooks/useRolePermission'
import { useResourceTabs } from '../hooks/useResourceTabs'

const router = useRouter()
const { busiSystemOptions } = useBusiSystemOptions()
const { resourcePoolsDic } = useDichooks()
const { resourcePoolsDic: dgResourcePoolsDic } = useDichooks({
  resourcePools: {
    realmType: 'iaas',
    domainCodes: [
      'plf_prov_moc_zj_vmware',
      'plf_prov_moc_zj_h3c',
      'plf_prov_moc_zj_huawei',
      'plf_prov_moc_zj_inspur',
    ],
  },
})

// 使用统一的tabs管理hook
const { availableTabs, activeTab, shouldHideTabs } = useResourceTabs()

const formRef = ref<any>(null)
const queryParams = ref<any>({ type: 'securityGroup' })

const formModel = reactive({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}
function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '安全组名称',
        type: 'input',
        key: 'name',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSysId',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: 'VPC',
        type: 'input',
        key: 'vpcName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '描述',
        type: 'input',
        key: 'description',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 对公列表筛选条件及配置
const dgFormRef = ref<any>(null)
const dgQueryParams = ref<any>({ type: 'securityGroup', sourceType: 'DG' })

const dgFormModel = reactive<any>({})
function resetDgSearch() {
  dgFormRef.value!.resetFields()
  dgQueryParams.value = { ...dgQueryParams.value, ...dgFormModel }
}
function doDgSearch() {
  dgQueryParams.value = { ...dgQueryParams.value, ...dgFormModel }
}

// 是否默认折叠搜索项
const dgCollapsed = ref(true)

// 对公资源搜索表单配置
const DGFormOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '安全组名称',
        type: 'input',
        key: 'name',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={dgFormModel}
                  resourceList={DGFormOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={resetDgSearch} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={doDgSearch} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (dgCollapsed.value = !dgCollapsed.value)}
              >
                {dgCollapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {dgCollapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '资源ID',
        type: 'input',
        key: 'deviceId',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: dgResourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: 'VPC',
        type: 'input',
        key: 'vpcName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '状态',
        type: 'input',
        key: 'deviceStatus',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订购人',
        type: 'input',
        key: 'applyUserName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订购时间',
        type: 'date',
        key: 'effectiveTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

const { shouldHideResourceOperations } = useRolePermission()

// 跳转到创建安全组页面
const handleToCreate = () => {
  router.push({
    path: '/securityGroupCreate',
  })
}

// 跳转到创建对公安全组页面
const handleToCreatePublic = () => {
  router.push({
    path: '/securityGroupCreate',
    query: {
      sourceType: 'DG',
    },
  })
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
