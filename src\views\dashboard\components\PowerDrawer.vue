<template>
  <el-drawer body-class="drawer-body-dashboard" :with-header="false" size="600px">
    <div>
      <Title title="算力分配状态" />
      <!-- 基本信息区域 -->
      <div class="info-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">所属地区：</span>
            <div class="city-controls">
              <el-select
                v-model="areaCode"
                placeholder="请选择城市"
                @change="handleCityChange"
                clearable
                filterable
              >
                <el-option
                  v-for="item in cityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="info-item">
            <span class="info-label">所属平台：</span>
            <span class="info-value">{{ regionName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">卡类型：</span>
            <div class="card-type-controls">
              <el-select
                v-model="modelName"
                placeholder="请选择卡类型"
                @change="handleModelNameChange"
                clearable
                filterable
              >
                <el-option
                  v-for="item in modelNameOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </div>
        <!-- 关闭按钮 -->
        <el-button
          @click="handleCancel"
          type="primary"
          style="position: absolute; right: 20px; top: 28px; transform: translateY(-50%)"
          plain
        >
          关闭
        </el-button>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <!-- 算力利用率图表 -->
        <div class="chart-container">
          <div class="chart-header">
            <span class="chart-title">
              <span class="chart-icon chart-icon-blue"></span>算力分配业务TOP10
            </span>
          </div>
          <div ref="powerChart" class="chart-content"></div>
        </div>
      </div>
      <div
        class="title-export"
        style="display: flex; justify-content: space-between; align-items: center; margin-top: 12px"
      >
        <Title title="算力分配列表" style="flex: 1" />
        <div class="select-input">
          <el-select
            class="select-item"
            v-model="deptName"
            placeholder="请选择归属部门"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in deptOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
          <el-select
            class="select-item"
            v-model="businessSystemName"
            placeholder="请选择所属业务"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in businessOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
          <el-select
            class="select-item"
            v-model="subModelName"
            placeholder="请选择型号"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in subModelOptions[modelName as keyof typeof subModelOptions]"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            class="select-item"
            v-model="allocationStatus"
            placeholder="请选择分配状态"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in allocationStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            class="select-item"
            v-model="managementStatus"
            placeholder="请选择纳管状态"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in managementStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div style="margin-right: 20px">
          <el-button @click="handleExport" type="primary" size="small"> 导出 </el-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <div class="table-section">
        <el-table
          :data="tableData"
          class="power-table"
          size="small"
          v-loading="loading"
          element-loading-text="加载中..."
          highlight-current-row
          @current-change="handleCurrentChange"
          ref="tableRef"
        >
          <el-table-column prop="deptName" label="归属部门" width="180" />
          <el-table-column show-overflow-tooltip prop="business" label="所属业务" />
          <el-table-column show-overflow-tooltip prop="vmName" label="主机名称" />
          <el-table-column show-overflow-tooltip prop="subModelName" label="型号" width="180" />
          <el-table-column prop="dcnAddress" label="DCN地址" width="260" />
          <el-table-column prop="allocationStatus" label="分配状态" width="180" />
          <el-table-column prop="managementStatus" label="纳管状态" width="180" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            layout="total,prev, pager, next, sizes, jumper"
            :page-sizes="[5, 10, 20, 30]"
            size="small"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import Title from './Title.vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import {
  pageDeviceGpuInfoListAllApi,
  businessSystemTopApi,
  exportDeviceGpuInfoAllApi,
  deviceGpuDicApi,
} from '@/api/modules/zsMap'

import { useDownload } from '@/hooks/useDownload'

const props = defineProps({
  areaCode: {
    type: String,
    default: '',
  },
  modelName: {
    type: String,
    default: '910B',
  },
})
let chartInstance1: ECharts | null = null
const areaCode = ref<string>(props.areaCode)
const regionName = ref<string>('')
const tableRef = ref<any>()

const modelName = ref<string>(props.modelName || '910B')
const cityOptions = ref<any[]>([
  { label: '杭州市', value: '杭州' },
  { label: '宁波市', value: '宁波' },
  { label: '温州市', value: '温州' },
  { label: '嘉兴市', value: '嘉兴' },
  { label: '湖州市', value: '湖州' },
  { label: '绍兴市', value: '绍兴' },
  { label: '金华市', value: '金华' },
  { label: '衢州市', value: '衢州' },
  { label: '舟山市', value: '舟山' },
  { label: '台州市', value: '台州' },
  { label: '丽水市', value: '丽水' },
])
const modelNameOptions = ref<any[]>([
  { label: '910B', value: '910B' },
  { label: '300I', value: '300I' },
  { label: 'T4', value: 'T4' },
  { label: 'A10', value: 'A10' },
  { label: 'V100', value: 'V100' },
  { label: 'A40', value: 'A40' },
])

const handleExport = () => {
  const params: any = {
    areaCode: areaCode.value,
    modelName: modelName.value,
    deviceId: deviceId.value,
    businessSystemName: businessSystemName.value,
    regionName: '', // 如果需要可以从数据中获取
    gpuSort: false, // 默认不启用GPU排序
    inUsed: allocationStatus.value,
    inManage: managementStatus.value,
  }
  useDownload(exportDeviceGpuInfoAllApi, `${modelName.value}_设备信息`, params, true, '.xlsx')
}

const deviceId = ref<string>('')

const handleCityChange = () => {
  // 城市变化时重新加载数据
  loadTableData()
  getDevicesMetricPercent()
}

const handleModelNameChange = () => {
  subModelName.value = ''
  // 卡类型变化时重新加载数据
  loadTableData()
  getDevicesMetricPercent()
}

async function getDevicesMetricPercent() {
  const params: any = {
    areaCode: areaCode.value,
    modelName: modelName.value,
  }

  try {
    const { entity }: any = await businessSystemTopApi(params)

    if (!entity || !entity.computeRanking || entity.computeRanking.length === 0) {
      // 没有数据时显示空图表
      if (!powerChart.value) return
      chartInstance1 = initChart(powerChart.value, [], [])
      return
    }

    // 提取业务系统名称作为X轴数据
    const xAxisData = entity.computeRanking.map((item: any) => item.businessSystemName)

    // 提取已分配算力作为Y轴数据
    const yAxisData = entity.computeRanking.map((item: any) => item.allocatedDeviceCount)

    // 构建柱状图数据
    const barData = [
      {
        name: '已分配算力',
        data: yAxisData,
        color: '#5289FF', // 蓝色
      },
    ]

    if (!powerChart.value) return
    chartInstance1 = initChart(powerChart.value, barData, xAxisData)
  } catch (error) {
    console.error('获取业务系统算力排名失败:', error)
    // 错误时显示空图表
    if (!powerChart.value) return
    chartInstance1 = initChart(powerChart.value, [], [])
  }
}
// 定义API请求参数类型
interface DeviceGpuListParams {
  pageSize: number
  pageNum: number
  areaCode?: string
  deviceType?: string
  inUsed?: string
  dncIp?: string
  inManage?: string
  catalogueDomainCode?: string
  sourceType?: string
  domainCode?: string
  regionId?: number
  regionCode?: string
  startTime?: string
  endTime?: string
  businessSystemId?: number
  deviceIds?: string[]
  deviceId?: string
  modelName?: string
  gpuSort?: boolean
  physicalDeviceIdList?: string[]
  sliceStatus?: string
  deptName?: string
  businessSystemName?: string
  subModelName?: string
}

// 定义表格数据类型
interface TableDataItem {
  deptName: string
  business: string
  vmName: string
  subModelName: string
  dcnAddress: string
  allocationStatus: string
  managementStatus: string
  deviceId?: string
  modelName?: string
  areaCode: string
  regionName: string
}

// 基本数据

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)

// 图表实例
const powerChart = ref<HTMLElement>()

// 删除原有的静态数据生成逻辑，替换为API调用
const allTableData = ref<TableDataItem[]>([])

const deptOptions = ref<any[]>([])
const businessOptions = ref<any[]>([])

const businessSystemName = ref<string>('')
const subModelName = ref<string>('')
const allocationStatus = ref<string>('')
const managementStatus = ref<string>('')
const subModelOptions = {
  '910B': [
    { label: '910B2', value: '910B2' },
    { label: '910B4', value: '910B4' },
  ],
  '300I': [{ label: '300I', value: '300I' }],
  T4: [{ label: 'T4', value: 'T4' }],
  A10: [{ label: 'A10', value: 'A10' }],
  V100: [{ label: 'V100', value: 'V100' }],
  A40: [{ label: 'A40', value: 'A40' }],
}
const allocationStatusOptions = ref<any[]>([
  { label: '已分配', value: '1' },
  { label: '未分配', value: '0' },
])
const managementStatusOptions = ref<any[]>([
  { label: '已纳管', value: '1' },
  { label: '未纳管', value: '0' },
])
const deptName = ref<string>('')
//下拉字典
const gitDic = async () => {
  const { entity: dept } = await deviceGpuDicApi({ type: 'dept' })
  const { entity: business } = await deviceGpuDicApi({ type: 'business' })
  deptOptions.value = dept.map((item: string) => ({ name: item, id: name }))
  businessOptions.value = business.map((item: string) => ({ name: item, id: name }))
}
gitDic()
// API调用函数
const loadTableData = async () => {
  try {
    loading.value = true

    // 构建请求参数
    const params: DeviceGpuListParams = {
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      areaCode: areaCode.value,
      modelName: modelName.value,
      businessSystemName: businessSystemName.value ? businessSystemName.value : undefined,
      inUsed: allocationStatus.value ? allocationStatus.value : undefined,
      inManage: managementStatus.value ? managementStatus.value : undefined,
      deptName: deptName.value ? deptName.value : undefined,
      subModelName: subModelName.value ? subModelName.value : undefined,
    }

    const response: any = await pageDeviceGpuInfoListAllApi(params)

    // 处理不同可能的响应结构
    let records: any[] = []
    let totalCount = 0

    if (response && response.entity) {
      // 如果数据在entity字段中
      if (response.entity.records) {
        records = response.entity.records
        totalCount = Number(response.entity.total) || response.entity.records.length
      } else if (Array.isArray(response.entity)) {
        records = response.entity
        totalCount = records.length
      }
    } else if (response && response.data) {
      // 如果数据在data字段中
      if (response.data.records) {
        records = response.data.records
        totalCount = Number(response.data.total) || response.data.records.length
      } else if (Array.isArray(response.data)) {
        records = response.data
        totalCount = records.length
      }
    } else if (Array.isArray(response)) {
      // 如果直接返回数组
      records = response
      totalCount = records.length
    }

    if (records && records.length > 0) {
      // 转换API数据为表格数据格式
      allTableData.value = records.map((item: any) => ({
        deptName: item.deptName || '',
        business: item.businessSystemName || '',
        vmName: item.vmName || '',
        subModelName: item.subModelName || item.modelName,
        dcnAddress: item.dcnNetAddr || '',
        allocationStatus: item.inUsed === '1' ? '已分配' : item.inUsed === '0' ? '未分配' : '',
        managementStatus: item.inManage === '1' ? '已纳管' : item.inManage === '0' ? '未纳管' : '',
        deviceId: item.deviceId,
        modelName: item.modelName,
        regionName: item.regionName,
        areaCode: item.areaCode,
      }))

      total.value = totalCount
      tableRef.value.setCurrentRow(allTableData.value[0])
      if (allTableData.value.length > 0) {
        // areaCode.value = allTableData.value[0].areaCode
        regionName.value = allTableData.value[0].regionName
      }
      console.log('成功加载数据:', allTableData.value.length, '条记录，总计:', totalCount)
    } else {
      allTableData.value = []
      total.value = 0
      // areaCode.value = ''
      regionName.value = ''
      // if (!powerChart.value) return
      // chartInstance1 = initChart(powerChart.value, [], [])
      console.warn('API返回数据为空或格式异常:', response)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 计算当前页显示的数据
const tableData = computed(() => {
  // 由于我们现在直接从API获取分页数据，直接返回所有数据
  return allTableData.value
})

// 处理页码变化
const handleCurrentChange = (row: any) => {
  if (!row?.deviceId) return
  deviceId.value = row?.deviceId
}

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTableData() // 重新加载数据
}

// 处理页面变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTableData()
}

// 图表配置
const initChart = (chartRef: HTMLElement, dataSet?: any[], xAxisData?: any[]) => {
  const chart = echarts.init(chartRef, { renderer: 'svg' })
  if (!dataSet?.length) {
    chart.clear()
    chart.setOption({
      graphic: {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: '暂无数据',
          fontSize: 14,
          fill: '#999',
          textAlign: 'center',
          textVerticalAlign: 'middle',
        },
      },
      xAxis: {
        show: false,
      },
      yAxis: {
        show: false,
      },
      grid: {
        show: false,
      },
    })
    return chart
  }
  chart.clear()

  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 12,
      },
      formatter: function (params: any) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;z-index: 1000;">${params[0].axisValue}</div>`
        // params按照value降序排序
        params.sort((a: any, b: any) => b.value - a.value)
        params.forEach((param: any) => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
            <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${param.color}; margin-right: 8px;"></span>
            <span style="margin-left: auto; font-weight: bold;">${param.value}</span>
          </div>`
        })
        return result
      },
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6',
        },
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        interval: 0, // 强制显示所有标签
        rotate: 0, // 不旋转标签
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
      axisLabel: {
        color: '#666',
        fontSize: 10,
        formatter: '{value}',
      },
    },
    series: dataSet.map((item: any) => ({
      name: item.name,
      data: item.data,
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        color: item.color,
      },
    })),
  }

  chart.setOption(option)
  return chart
}

// 窗口调整大小处理
const handleResize = () => {
  chartInstance1?.resize()
}

const handleCancel = () => {
  emit('update:modelValue', false)
}

const emit = defineEmits(['update:modelValue'])

// 监听props.modelName的变化
watch(
  () => props.modelName,
  (newModelName) => {
    if (newModelName) {
      modelName.value = newModelName
      loadTableData()
      getDevicesMetricPercent()
    }
  },
  { immediate: true },
)

onMounted(() => {
  // 初始加载数据
  loadTableData()
  getDevicesMetricPercent()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance1?.dispose()
})
</script>
<style lang="scss">
.drawer-body-dashboard {
  padding: 0 !important;
  background: #e2edfb;

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
    margin-left: 8px;
    background: #f3f8fd !important;
    .btn-next {
      background: #f3f8fd !important;
      color: #333;
    }
    .btn-prev {
      background: #f3f8fd !important;
      color: #333;
    }
    .el-pager {
      background: #f3f8fd !important;
      .number {
        background: #f3f8fd !important;
        border: 1px solid #e6ebf5;
        color: #333;
      }
      .more {
        background: #f3f8fd !important;
        color: #333;
      }
    }
  }

  .el-table__header {
    background-color: #cbe2ff;
  }
  .el-table__row.current-row td {
    background: #cbe2ff !important;
  }
  .el-table th {
    color: #333;
    font-weight: 500;
    border-bottom: 1px solid #e6ebf5;
    background: #cbe2ff !important;
  }
  .el-table__header-wrapper {
    background: #cbe2ff !important;
  }
  .el-table__body-wrapper {
    background: #f3f8fd !important;
  }

  .el-table td {
    padding: 12px 0;
    border-bottom: 1px solid #f0f2f7;
    background-color: #f3f8fd;
  }

  .el-table tr:hover > td {
    background-color: #e8f2ff !important;
  }

  .el-table__body tr:last-child td {
    border-bottom: none;
  }
  .charts-section {
    display: flex;
    gap: 16px;
    margin: 0 16px 0px;

    .chart-container {
      flex: 1;
      border-radius: 8px;
      box-shadow: none;
      border: none;

      .chart-header {
        padding-top: 16px;
        text-align: center;
        background: white;

        .chart-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .chart-icon {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;

            &.chart-icon-blue {
              background-color: #5470c6;
            }

            &.chart-icon-orange {
              background-color: #ff9800;
            }
          }
        }
      }

      .chart-content {
        background: white;
        height: 200px;
        width: 100%;
        cursor: pointer;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.info-section {
  margin: 0 16px 0px;
  border-radius: 8px;
  padding: 16px 0px;
  box-shadow: none;
  border: none;

  .info-row {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;

    .info-item {
      display: flex;
      align-items: center;

      .info-label {
        color: #666;
        font-size: 14px;
        margin-right: 8px;
      }

      .info-value {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }

      .time-controls {
        width: 400px;
        display: flex;
        align-items: center;
        gap: 8px;

        .time-range {
          color: #333;
          font-size: 14px;
          padding: 4px 8px;
          background: #f5f5f5;
          border-radius: 4px;
          border: 1px solid #e0e0e0;
        }
      }
    }
  }
}

.table-section {
  background: #f3f8fd;
  margin: 0 16px;
  border-radius: 8px;
  padding-bottom: 8px;
  box-shadow: none;
  border: none;
  margin-top: 8px;

  .power-table {
    height: calc(100vh - 430px);
    border: none;
    border-radius: 6px;
    overflow: hidden;
  }
}

:deep(.el-date-editor) {
  --el-date-editor-width: 140px;
  height: 28px;

  .el-input__inner {
    font-size: 12px;
    padding: 0 8px;
  }
}

:deep(.el-pagination) {
  .el-pagination__sizes {
    .el-select {
      .el-input {
        width: 90px;
      }
    }
  }
}

.title-export {
  position: relative;
  .select-input {
    position: absolute;
    width: 60%;
    top: 3px;
    left: 120px;
    display: flex;
    gap: 8px;
    padding: 0 16px;
    box-sizing: border-box;
  }
  .select-item {
    width: 20%;
  }
}
</style>
