<template>
  <div class="white-list-container">
    <!-- 搜索表单 -->
    <div class="filter-form-con">
      <sl-form class="filter-form" ref="formRef" :options="formOptions" v-model="formModel">
      </sl-form>
    </div>

    <!-- 白名单列表表格 -->
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="getWhiteListData"
      :init-param="queryParams"
      @selection-change="handleSelectionChange"
      hidden-table-header
      row-key="id"
    />

    <!-- 新增/编辑白名单弹窗 -->
    <SlDialog
      v-model="whiteListDialogVisible"
      :title="isEdit ? '编辑白名单' : '新增白名单'"
      width="500px"
      destroy-on-close
      @close="handleWhiteListDialogClose"
      @confirm="handleWhiteListConfirm"
    >
      <sl-form
        ref="whiteListFormRef"
        :options="whiteListFormOptions"
        v-model="whiteListFormModel"
      ></sl-form>
      <template #footer>
        <el-button @click="handleWhiteListDialogClose">取消</el-button>
        <sl-button type="primary" :api-function="handleWhiteListConfirm">确定</sl-button>
      </template>
    </SlDialog>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Delete } from '@element-plus/icons-vue'
import SlForm from '@/components/form/SlForm.vue'
import SlDialog from '@/components/SlDialog/index.vue'
import SlButton from '@/components/base/SlButton.vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import {
  getRdsWhitePage,
  createRdsWhite,
  updateRdsWhite,
  deleteRdsWhite,
} from '@/api/modules/resourecenter'

// 定义props
const props = defineProps<{
  resourceId: string
  rdsId: string
}>()

// 表单引用
const formRef = ref()
const whiteListFormRef = ref()
const proTable = ref<ProTableInstance>()

// 白名单数据列表
const whiteListData = ref<any[]>([])

// 查询参数
const queryParams = ref<any>({
  resourceId: props.resourceId,
  rdsId: props.rdsId,
})

// 搜索表单
const formModel = reactive({
  whiteName: '',
  ips: '',
})

// 搜索表单配置
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '白名单名称',
        type: 'input',
        key: 'whiteName',
        span: 8,
        props: {
          placeholder: '请输入白名单名称',
          clearable: true,
        },
      },
      {
        label: 'IP',
        type: 'input',
        key: 'ips',
        span: 8,
        props: {
          placeholder: '请输入IP',
          clearable: true,
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={handleAdd}
                icon={<Plus />}
                type="primary"
                disabled={whiteListData.value.length > 0}
              >
                新增白名单
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])

// 表格列配置
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', fixed: 'left', minWidth: 50 },
  { type: 'index', label: '序号', minWidth: 60 },
  { prop: 'whiteName', label: '白名单名称', minWidth: 200 },
  { prop: 'ips', label: 'IP', minWidth: 200 },
  { prop: 'createTime', label: '创建时间', minWidth: 160 },
  {
    prop: 'operation',
    label: '操作',
    width: 200,
    fixed: 'right',
    render: ({ row }) => (
      <div>
        <el-button type="primary" link onClick={() => handleEdit(row)}>
          编辑
        </el-button>
        <el-button type="danger" link onClick={() => handleDelete(row)}>
          删除
        </el-button>
      </div>
    ),
  },
])

// 搜索重置功能
const reset = () => {
  formRef.value!.resetFields()
  queryParams.value = {
    resourceId: props.resourceId,
    rdsId: props.rdsId,
    ...formModel,
  }
}

const search = () => {
  queryParams.value = {
    resourceId: props.resourceId,
    rdsId: props.rdsId,
    ...formModel,
  }
}

// 选择变更处理
const handleSelectionChange = (selection: any[]) => {
  console.log('选择的白名单:', selection)
}

// 白名单管理弹窗
const whiteListDialogVisible = ref(false)
const isEdit = ref(false)
const whiteListFormModel = reactive({
  id: '',
  whiteName: '',
  ips: '',
})

const whiteListFormOptions = reactive([
  {
    groupName: '',
    groupItems: [
      {
        label: '名称',
        type: 'input',
        key: 'whiteName',
        span: 24,
        props: {
          placeholder: '请输入白名单名称',
          disabled: computed(() => isEdit.value),
        },
        rules: [
          { required: true, message: '请输入白名单名称', trigger: 'blur' },
          { min: 2, max: 50, message: '名称长度在2到50个字符', trigger: 'blur' },
        ],
      },
      {
        label: 'IP',
        type: 'input',
        key: 'ips',
        span: 24,
        props: {
          type: 'textarea',
          autosize: { minRows: 4, maxRows: 8 },
          row: 4,
          maxlength: 200,
          showWordLimit: true,
          placeholder: '请输入IP地址',
        },
        rules: [{ required: true, message: '请输入IP地址', trigger: 'blur' }],
      },
    ],
  },
])

const handleAdd = () => {
  isEdit.value = false
  whiteListFormModel.id = ''
  whiteListFormModel.whiteName = ''
  whiteListFormModel.ips = ''
  whiteListDialogVisible.value = true
}

const handleEdit = (row: any) => {
  isEdit.value = true
  whiteListFormModel.id = row.id
  whiteListFormModel.whiteName = row.whiteName
  whiteListFormModel.ips = row.ips
  whiteListDialogVisible.value = true
}

const handleWhiteListDialogClose = () => {
  whiteListDialogVisible.value = false
}

const handleWhiteListConfirm = async () => {
  if (!(await whiteListFormRef.value?.validate(() => true))) return

  try {
    const apiData = {
      resourceId: props.resourceId,
      rdsId: props.rdsId,
      whiteName: whiteListFormModel.whiteName,
      ips: whiteListFormModel.ips,
    }

    let response
    if (isEdit.value) {
      // 编辑白名单（只修改IP）
      response = await updateRdsWhite({
        id: whiteListFormModel.id,
        resourceId: props.resourceId,
        rdsId: props.rdsId,
        whiteName: whiteListFormModel.whiteName,
        ips: whiteListFormModel.ips,
      })
    } else {
      // 新增白名单
      response = await createRdsWhite(apiData)
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
      whiteListDialogVisible.value = false
      proTable.value?.getTableList() // 刷新表格
    } else {
      ElMessage.error(response.message || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  } catch (error) {
    console.error('保存白名单失败:', error)
    ElMessage.error(isEdit.value ? '编辑失败' : '新增失败')
  }

  return Promise.resolve()
}

// 删除白名单
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该白名单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await deleteRdsWhite({
      id: row.id,
      resourceId: props.resourceId,
      rdsId: props.rdsId,
    })

    if (response.code === 200) {
      ElMessage.success('删除成功')
      proTable.value?.getTableList() // 刷新表格
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败', error)
      ElMessage.error('删除失败')
    }
  }
}

// 自定义请求函数，用于获取白名单数据并更新状态
const getWhiteListData = async (params: any) => {
  try {
    const response = await getRdsWhitePage(params)
    if (response.code === 200) {
      whiteListData.value = response.entity?.records || []
      return response
    } else {
      whiteListData.value = []
      return response
    }
  } catch (error) {
    whiteListData.value = []
    throw error
  }
}
</script>

<style lang="scss" scoped>
.white-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .filter-form-con {
    padding: 16px;
  }

  .table-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }
}
</style>
