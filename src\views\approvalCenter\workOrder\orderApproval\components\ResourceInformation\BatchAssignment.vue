<template>
  <div class="batch-assignment">
    <!--
    1.可以选择赋值可用取区 也可以选择赋值网络 单选

    -->
    <el-form :form="form" :inline="true">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="选择要操作的类型:">
            <el-radio-group v-model="form.type" @change="handleTypeChange">
              <el-radio v-for="item in batchOperationType" :key="item.value" :value="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item class="w100" label="可用区:">
            <el-select
              class="w100"
              v-model="form.az"
              placeholder="请选择可用区"
              @change="handleAzChange"
              value-key="id"
            >
              <el-option
                v-for="item in azList[selePool.regionId]"
                :key="item.value"
                :label="item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <template v-if="form.type === 'network'">
          <el-col :span="6">
            <el-form-item class="w100" label="网络:">
              <el-select
                class="w100"
                v-model="form.network"
                placeholder="请选择网络"
                @change="handleNetworkChange"
                value-key="id"
              >
                <el-option
                  v-for="item in networkOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="w100" label="子网:">
              <el-select
                class="w100"
                v-model="form.subnet"
                placeholder="请选择子网"
                multiple
                :multiple-limit="multiple ? 0 : 1"
                value-key="id"
              >
                <el-option
                  v-for="item in privateNetworkDic"
                  :key="item.id"
                  :label="item.subnetName"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </template>

        <el-form-item>
          <el-button type="primary" @click="submit">确 定</el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="tsx" setup>
import { showTips } from '@/utils'
import { computed, inject, ref, toRefs, watch } from 'vue'

type propsType = {
  batchOperationType: any[]
  azList: FormDataType
  selePool: FormDataType
  networkDic: { [key: string]: any[] }
  multiple: boolean
}

const props = withDefaults(defineProps<propsType>(), {
  batchOperationType: () => [],
  azList: () => [],
  selePool: () => ({}),
})

const form = ref<{
  type: string
  az: any
  network: any
  subnet: any
}>({
  type: 'az',
  az: null,
  network: null,
  subnet: null,
})

const { networkDic, selePool } = toRefs(props)

const isInnovationPool = inject('isInnovationPool', ref(false))
const innovationWorkList = inject('innovationWorkList', ref<FormDataType>({})) //网络列表

const networkOptions = computed(() => {
  return isInnovationPool.value
    ? (innovationWorkList.value[selePool.value.regionCode] ?? [])
    : (networkDic.value[`${selePool.value.regionId}-${form.value.az?.id}`] ?? [])
})

const privateNetworkDic = computed(() => {
  return (
    networkOptions.value.find((item: any) => item.id === form.value.network?.id)?.subnetOrderList ??
    []
  )
})

watch(
  () => selePool.value,
  (newVal) => {
    if (newVal.length === 0) {
      form.value.az = null
      form.value.network = null
      form.value.subnet = null
    }
  },
)

const emit = defineEmits(['handleAzChange', 'submit'])

const handleTypeChange = () => {
  form.value.az = null
  form.value.network = null
  form.value.subnet = null
}

const handleAzChange = (value: any) => {
  form.value.network = null
  form.value.subnet = null
  if (value) {
    emit('handleAzChange', value)
  }
}

const handleNetworkChange = () => {
  form.value.subnet = null
}

const submit = () => {
  // 1.校验不为空
  if (!form.value.az) return showTips('请选择可用区')
  if (form.value.type === 'network' && !form.value.network && !form.value.subnet)
    return showTips('请选择网络')
  // 2.处理数据
  let params: any = {
    azId: form.value.az.id,
    azName: form.value.az.name,
    azCode: form.value.az.code,
  }

  if (form.value.type === 'network') {
    let subnets: any = []
    form.value.subnet.forEach((item: any) => {
      subnets.push({
        subnetId: item.id,
        subnetName: item.subnetName,
        subnetIp: item.cidr,
      })
    })
    params = {
      ...params,
      vpcId: form.value.network.id,
      vpcName: form.value.network.name,
      vpcCode: form.value.network.code,
      subnetId: form.value.subnet.map((item: any) => item.id).join(','),
      subnetNames: form.value.subnet.map((item: any) => item.subnetName).join(','),
      subnetIds: form.value.subnet.map((item: any) => item.id),
      subnets,
    }
  } else {
    // 清空子网网络
    params = {
      ...params,
      vpcId: '',
      vpcName: '',
      vpcCode: '',
      subnetId: '',
      subnetNames: '',
      subnetIds: [],
      subnets: [],
    }
  }

  emit('submit', params)
}

const resetForm = () => {
  form.value = {
    type: 'az',
    az: null,
    network: null,
    subnet: null,
  }
}
defineExpose({
  resetForm,
})
</script>

<style lang="scss" scoped>
.batch-assignment {
  width: 100%;
  margin-bottom: 20px;
}
.w100 {
  width: 100%;
}
</style>
