<template>
  <div
    class="no-card"
    :class="{ 'show-checkbox-all': showCheckboxAll() }"
    v-if="goodsType && goodsType !== 'unknown'"
  >
    <div
      v-if="pageStatus && batchOperationType.length"
      class="flx-align-center"
      style="width: 100%; min-width: 900px"
    >
      <SpecialBatchAssignment
        :az-list="azList"
        :batch-operation-type="batchOperationType"
        :sele-pool="selePool"
        @submit="handleBatchSubmit"
        @handleTypeChange="handleTypeChange"
        :order-id="orderId"
        :multiple="isMultiple"
        ref="batchAssignmentRef"
      />
    </div>
    <sl-pro-table
      :columns="tableColumns()"
      :data="goodsAllDetails"
      :pagination="false"
      :is-show-search="false"
      row-key="ids"
      ref="proTable"
    >
    </sl-pro-table>
    <!-- 弹窗 -->
    <el-dialog
      title="资源详情"
      v-model="dialogVisible"
      width="80%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <sl-pro-table
        :columns="tableColumns(false)"
        :data="goodsDetails"
        :pagination="false"
        :is-show-search="false"
      >
        <template #originName="{ row }">
          {{ row.goodsName }}
        </template>
      </sl-pro-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button :disabled="disCancel" @click="dialogVisible = false">
            {{ isCanOperation ? '取 消' : '关 闭' }}
            <el-tooltip
              v-if="disCancel"
              key="disCancel"
              class="box-item"
              effect="dark"
              content="当前有资源状态发生改变，请点击“提交”按钮合并工单。"
              placement="top-start"
            >
              <el-icon class="ml5">
                <el-icon><Warning /></el-icon>
              </el-icon>
            </el-tooltip>
          </el-button>
          <el-button v-if="isCanOperation" type="primary" @click="submit">提 交</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 配置网络 -->
    <ConfigureNetwork @update-networks="updateNetworks" ref="configureNetworkRef" />
  </div>
</template>

<script setup lang="tsx">
import { computed, inject, ref, toRefs, type VNode, nextTick } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { goodsTypeEnum, type goodsTypeCodeEnum } from '../../../interface/type'
import { useVModel } from '@vueuse/core'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import goodsAllColumns from './goodsColumns'
import ConfigureNetwork from './SpecialConfigureNetwork.vue'
import SpecialBatchAssignment from './SpecialBatchAssignment.vue'
import useModelList, { type goodsDetailsType } from './useModelListIp'
import { getListFlavorApi, standardWorkOrderResOpenApi } from '@/api/modules/approvalCenter'
import { showTips, uniqueByKeys } from '@/utils'

// 列表状态  开通
type PropsType = {
  goodsList: FormDataType[]
  resourcePoolsDic: any[] //资源池字典
  goodsType: goodsTypeCodeEnum
  orderId: string
  azList: FormDataType
  orderDesc: FormDataType //工单详情信息
  domainCodeStatus: boolean //某些云平台集合的权限
}
const props = withDefaults(defineProps<PropsType>(), {})
const { goodsType, azList } = toRefs(props)
const pageStatus = inject('pageStatus', ref(true))
const isInnovationPool = inject('isInnovationPool', ref(false))

const isOffline = inject('isOffline', ref(false))

const btnAuth = inject(
  'btnAuth',
  ref({
    resource_creation: false,
    schema_administrator: false,
    autodit_end: false,
  }),
)

// 是否可多选子网
const isMultiple = computed(
  () => isInnovationPool.value && ['ecs', 'mysql', 'redis', 'postgreSql'].includes(goodsType.value),
)

// 是否可操作合并拆分 且 添加开通数据以及分配资源池
const isCanOperation = computed(() => {
  return pageStatus.value && (btnAuth.value.resource_creation || btnAuth.value.schema_administrator)
})

const emit = defineEmits(['update:goodsList'])
const goodsListRef = useVModel(props, 'goodsList', emit)

/**
 * 生成查重的keys
 */
const getKeys = () => {
  const keys: string[] = ['idHash', 'status', 'regionId']
  if (btnAuth.value.resource_creation) keys.push(...goodsTypeEnum[goodsType.value]?.params)
  return keys
}

const {
  insertAtIndexAndClear,
  mergeParent,
  uniqueByPropertiesWithCustomKey,
  createObjectsArray,
  extractIpAddresses,
} = useModelList(getKeys)

const goodsAllDetails = computed({
  get() {
    if (!goodsListRef.value?.length) return []

    const newGoods = JSON.parse(JSON.stringify(goodsListRef.value))
    return uniqueByPropertiesWithCustomKey<goodsDetailsType>(newGoods, getKeys(), 'openNum')
  },
  set(val) {
    const arrall: any[] = []
    val.map((row: any) => {
      arrall.push(...createObjectsArray(row))
    })

    goodsListRef.value = arrall
  },
})

const undateGoodsAllDetails = () => {
  if (!dialogVisible.value) goodsAllDetails.value = mergeParent(goodsAllDetails.value)
}

// -----------------------弹窗数据---------------------

const goodsDetails = ref<goodsDetailsType[]>([])
const goodIndex = ref<number>(-1)
const dialogVisible = ref(false)

// 是否禁用取消
const disCancel = ref<boolean>(false)

const close = () => {
  goodIndex.value = -1
  goodsDetails.value = []
  disCancel.value = false
}

const submit = () => {
  const newGoods = JSON.parse(JSON.stringify(goodsDetails.value))
  const items = uniqueByPropertiesWithCustomKey(newGoods, getKeys(), 'openNum').map((item) => {
    return { ...item }
  })
  // 合并父级
  const goods = JSON.parse(JSON.stringify(goodsAllDetails.value))

  insertAtIndexAndClear(goods, goodIndex.value, items)

  goodsAllDetails.value = mergeParent(goods)
  dialogVisible.value = false
}
// ===============================================获取字典================================================================

// 公网字典
const changeAz = async (value: number, row: goodsDetailsType, falg: boolean = true) => {
  row['azName'] = ''
  row['azCode'] = ''
  row['azId'] = ''
  row['subnetIds'] = []
  row['subnetName'] = ''
  row['vpcName'] = ''
  row['vpcCode'] = ''
  row['vpcId'] = ''
  row['templateCode'] = ''
  const obj = azList.value[row.regionId].find((item: any) => item.id === value)
  if (obj) {
    row['azName'] = obj.name
    row['azCode'] = obj.code
    row['azId'] = obj.id
    let ids = {
      regionId: row.regionId,
      azId: row.azId,
      name: row.flavorName,
    }
    // change 时间获取
    falg && (await getSpecDic(ids, `${row.azId}-${row.flavorName}`))
  }
  falg && undateGoodsAllDetails()
  return {
    ...row,
  }
}

// 模板字典
const specDic = ref<FormDataType>({})
// 获取模板字典
const getSpecDic = async (ids: any, key: string) => {
  if (specDic.value[key]) return
  if (!isInnovationPool.value) return
  if (!ishideColumn('templateCode')) return
  const { entity } = await getListFlavorApi(ids)
  specDic.value = { ...specDic.value, [key]: entity }
}

// =========================================表单操作=============================================================
/**
 * 当维开通中 和 开通成功时 禁用
 * @param status 状态
 */
const rowStatusBtn = (status: string) => {
  return status && ['opening', 'open_success'].includes(status)
}
/**
 * 当为开通中 和 开通成功时 禁用
 * @param status 状态
 */

const rowStatus = (status: string) => {
  return status && ['opening', 'open_success', 'open_fail'].includes(status)
}

// 禁用表单
const disabledNetwork = (row: goodsDetailsType) => {
  if (!pageStatus.value || !btnAuth.value.resource_creation) return true

  if (rowStatus(row.status)) return true

  // 当为云硬盘的时候判断
  if (['evs', 'eip', 'shareEvs'].includes(goodsType.value) && row['vmName']) return true

  return false
}

// 是否显示列
const ishideColumn = (prop: string) => {
  return goodsTypeEnum[goodsType.value]?.params?.some((item) => item === prop)
}

const azListColumn = () => {
  const isShow = ishideColumn('azId')
  return {
    prop: 'azId',
    label: '可用区',
    align: 'center',
    width: '220px',
    isShow,
    render: ({ row }: { row: any }): VNode => {
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.azName ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              v-model={row.azId}
              style="width: 100%"
              placeholder="请选择可用区"
              onChange={(value: number) => {
                changeAz(value, row)
              }}
            >
              {azList.value[row.regionId]?.map((option: any) => (
                <el-option key={option.id} label={option.name} value={option.id} />
              ))}
              {!azList.value[row.regionId]?.length && (
                <el-option label="数据不存在请刷新页面" value="1" disabled={true} />
              )}
            </el-select>
          )}
        </>
      )
    },
  }
}

const templateCodeColumn = () => {
  const isShow = ishideColumn('templateCode') && isInnovationPool.value
  return {
    prop: 'templateCode',
    label: '模板',
    align: 'center',
    width: '250px',
    isShow,
    render: ({ row }: { row: any }): VNode => {
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.templateCode ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              v-model={row.templateCode}
              style="width: 100%"
              placeholder="请选择模板"
            >
              {specDic.value[`${row.azId}-${row.flavorName}`]?.map((option: any) => (
                <el-option key={option.id} label={option.flavorName} value={option.flavorName} />
              ))}
              {!specDic.value[`${row.azId}-${row.flavorName}`]?.length && (
                <el-option label="数据不存在" value="1" disabled={true} />
              )}
            </el-select>
          )}
        </>
      )
    },
  }
}

const ConfigureNetworkColumn = () => {
  return {
    prop: 'planeNetworkModel',
    label: '网络资源',
    align: 'center',
    width: '150px',
    render: ({ row, $index }: { row: any; $index: number }): VNode => {
      return (
        <>
          <el-button type="primary" onClick={() => openNetworkDialog(row, $index)} link>
            配置网络
          </el-button>
        </>
      )
    },
  }
}
// -----------------------表单列表---------------------

const editFn = (row: any, index: number) => {
  goodsDetails.value = createObjectsArray(row)
  goodIndex.value = index
  dialogVisible.value = true
}

//记录已经开通的子网+ip的集合
const openSubnetIpList = ref<any>({})

const setOpenSubnetIpList = (planeNetworkModel: any) => {
  if (planeNetworkModel?.length) {
    planeNetworkModel.forEach((item: any) => {
      item.subnets.forEach((obj: any) => {
        if (obj.ipAddresses?.length) {
          openSubnetIpList.value[obj.subnetId]
            ? openSubnetIpList.value[obj.subnetId].push(...obj.ipAddresses)
            : (openSubnetIpList.value[obj.subnetId] = obj.ipAddresses)
        }
      })
    })
  }
}

// 操作按钮
// 网络开通函数
const networkOpen = async (row: any) => {
  // 1.组合数据 校验
  let obj: any = {
    standardWorkOrderId: props.orderId,
    openResType: goodsType.value,
  }
  // 详情内 id 与外层的id参数不同
  if (dialogVisible.value) obj['openResIds'] = [row.id]
  else {
    obj['openResIds'] = row.ids
  }

  let flag = true
  let goodsParams = goodsTypeEnum[goodsType.value]?.params ?? []

  // 2.1 当不为 网络云-创新资源池 的时候 不校验 模板字段
  if (!isInnovationPool.value) {
    goodsParams = goodsParams.filter((item) => item !== 'templateCode')
  }
  goodsParams.forEach((item) => {
    flag = flag && row[item]
  })
  if (!flag) {
    return showTips('请先选择要提交的数据！')
  }
  const setVlueKey = ['azCode', 'azId', 'azName']
  setVlueKey.map((item) => {
    if (row[item]) obj[item] = row[item]
  })
  row['templateCode'] && (obj['templateCode'] = row['templateCode'])

  // 2.2 处理配置网络信息
  if (!row.planeNetworkModel || !row.planeNetworkModel?.length) return showTips('请配置网络信息！')

  // 外面的时候清洗数据，里面的时候直接用
  if (!dialogVisible.value) {
    obj.planeNetworkModelList = matchIpAddress(row, true)
  } else {
    obj.planeNetworkModelList = row.planeNetworkModel.map((item: any) => {
      return {
        type: item.type,
        id: item.id,
        name: item.name,
        plane: item.plane,
        subnets: item.subnets.map((obj: any) => {
          let NewObj = {
            ...obj,
            ipAddresses: obj.ipAddress,
          }

          delete NewObj.ipAddress
          return {
            ...NewObj,
          }
        }),
        sort: item.sort,
      }
    })
  }
  let flagIp = false
  let subnetsStr = ''
  obj['planeNetworkModelList'] = obj.planeNetworkModelList.map((item: any) => {
    // 2.3 处理ip地址判断在开通中不要有重复的ip地址 否则存起来
    let subnets = JSON.parse(JSON.stringify(item.subnets))
    subnets.forEach((obj: any) => {
      if (obj.ipAddresses?.length && openSubnetIpList.value[obj.subnetId]?.length) {
        // 2. 判断同子网是否存在相同的ip
        let flag = openSubnetIpList.value[obj.subnetId].some((ip: any) => {
          return obj.ipAddresses.some((ip1: any) => ip1 === ip)
        })
        if (flag) {
          flagIp = true
          subnetsStr += obj.subnetName
        }
      }
    })

    //  抛出数据
    return {
      type: item.type,
      id: item.id,
      name: item.name,
      plane: item.plane,
      subnets: item.subnets,
      sort: item.sort,
    }
  })

  if (flagIp) {
    return showTips(subnetsStr + ',子网中存在重复的ip地址，请重新选择！')
  }

  // 3.1 重新开通先做个假的
  if (row.status === 'open_fail' && goodsType.value !== 'cq') {
    row.status = 'opening'
    row.statusCn = '开通中'
    ElMessage.success('已提交开通')
    return
  }

  try {
    await standardWorkOrderResOpenApi(obj)
    row.status = 'opening'
    row.statusCn = '开通中'
    ElMessage.success('已提交开通')
    //  记录提交的ip 地址
    setOpenSubnetIpList(obj.planeNetworkModelList)
  } catch (e) {
    console.error(e)
    disCancel.value = false
  }
}
const operationRender = (
  { row, $index }: { row: any; $index: number },
  falg: boolean = true,
): VNode => {
  return (
    <>
      {btnAuth.value.resource_creation && pageStatus.value && !isOffline.value && (
        <el-button
          type="primary"
          disabled={rowStatusBtn(row.status)}
          onClick={() => networkOpen(row)}
          link
        >
          {row.status === 'open_fail' ? '重新开通' : '开通'}
        </el-button>
      )}
      {falg && (
        <el-button type="primary" onClick={() => editFn(row, $index)} link>
          详情
        </el-button>
      )}
    </>
  )
}

const operationColumns = (falg: boolean = true) => {
  const width = btnAuth.value.resource_creation ? '140px' : '80px'

  return {
    prop: 'operation',
    label: '操作',
    align: 'center',
    fixed: 'right',
    width,
    render: ({ row, $index }: { row: any; $index: number }) =>
      operationRender({ row, $index }, falg),
  }
}

const columns = ref<ColumnProps<goodsDetailsType>[]>([
  {
    prop: 'openNum',
    label: '数量',
    align: 'center',
    width: '100px',
  },
  {
    prop: 'regionName',
    label: '资源池',
    align: 'center',
    width: '220px',
  },
])

const statuses = [
  { value: 'wait_open', label: '待开通' },
  { value: 'opening', label: '开通中' },
  { value: 'open_success', label: '开通成功' },
  { value: 'open_fail', label: '开通失败' },
]

const statusColumn = (falg: boolean): ColumnProps[] => [
  {
    prop: 'message',
    label: '失败原因',
    align: 'center',
    width: '300px',
    isShow: !falg,
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    width: '100px',
    fixed: 'right',

    render: ({ row }: { row: any }): VNode => {
      let obj = {
        wait_open: 'primary',
        opening: 'warning',
        open_success: 'success',
        open_fail: 'danger',
      }
      const type = obj[row.status as keyof typeof obj] ?? 'primary'
      return (
        <el-text type={type} underline={false}>
          {statuses.find((item) => item.value === row.status)?.label}
        </el-text>
      )
    },
  },
]

const tableColumns = computed(() => (falg: boolean = true) => {
  if (!goodsType.value || goodsType.value === 'unknown') return []

  let indexColumn: ColumnProps = { type: 'index', label: '序号', width: 55 }
  const newColumns = [indexColumn, ...goodsAllColumns[goodsType.value], ...columns.value]
  // 添加表单
  if (btnAuth.value.resource_creation || btnAuth.value.autodit_end) {
    newColumns.push(azListColumn())

    newColumns.push(templateCodeColumn())
    newColumns.push(ConfigureNetworkColumn())
  }

  if (btnAuth.value.resource_creation) {
    newColumns.push(...statusColumn(falg))
  }

  if (falg || btnAuth.value.resource_creation) newColumns.push(operationColumns(falg))
  if (
    falg &&
    pageStatus.value &&
    (btnAuth.value.resource_creation || btnAuth.value.schema_administrator)
  )
    newColumns.unshift(selectedColumns())
  return newColumns
})

//===========================================配置网络=================================================

// -----------------配置网络--------------
const configureNetworkRef = ref<InstanceType<typeof ConfigureNetwork>>()

// 匹配ipAddress
function matchIpAddress(row: any, falg = false) {
  const planeNetworkModel = row.planeNetworkModel.map((network: any) => {
    let networkNew = JSON.parse(JSON.stringify(network))
    if (networkNew?.subnets && networkNew.subnets.length) {
      // 遍历每个子网
      networkNew.subnets.forEach((subnet: any) => {
        // 如果子网有IP地址，添加到结果中
        let key = falg ? 'ipAddresses' : 'ipAddress'
        subnet[key] = row.ipAddressMap[row.idHash + subnet.subnetId + subnet.uuidPlane] ?? []
        if (falg) delete subnet.ipAddress
      })
    }
    return networkNew
  })
  return planeNetworkModel
}

const openNetworkDialog = async (row: any, index: number) => {
  // if (!row.azId && !isInnovationPool.value) return showTips('请先选择可用区')

  let params = {
    orderId: props.orderId,
    regionCode: row.regionCode,
    azCode: row.azCode,
  }

  // 外面的时候清洗数据，里面的时候直接用
  if (row.planeNetworkModel && !dialogVisible.value) {
    row.planeNetworkModel = matchIpAddress(row)
  }

  let filterIpsCopy: any = {}
  if (dialogVisible.value) {
    goodsDetails.value.map((good) => {
      if (good.planeNetworkModel && Array.isArray(good.planeNetworkModel)) {
        good.planeNetworkModel.map((planeNetwork: any) => {
          planeNetwork.subnets?.map((subnet: any) => {
            filterIpsCopy[subnet.subnetId]
              ? filterIpsCopy[subnet.subnetId].push(...subnet.ipAddress)
              : (filterIpsCopy[subnet.subnetId] = subnet.ipAddress)
            //去重去空
            filterIpsCopy[subnet.subnetId] = filterIpsCopy[subnet.subnetId].filter(Boolean)
            filterIpsCopy[subnet.subnetId] = [...new Set(filterIpsCopy[subnet.subnetId])]
          })
        })
      }
    })
  }
  await configureNetworkRef.value?.openDialog({
    row,
    index,
    disabled: disabledNetwork(row),
    multiple: isMultiple.value,
    vpc: props.domainCodeStatus,
    params,
    filterIpsCopy,
  })
}

const updateNetworks = (data: any, index: number) => {
  if (dialogVisible.value) {
    goodsDetails.value[index]['planeNetworkModel'] = JSON.parse(JSON.stringify(data))
  } else {
    const { ipAddressMap, planeNetworkModel } = extractIpAddresses(
      data,
      goodsAllDetails.value[index].idHash,
    )

    goodsAllDetails.value[index]['planeNetworkModel'] = JSON.parse(
      JSON.stringify(planeNetworkModel),
    )
    goodsAllDetails.value[index]['ipAddressMap'] = JSON.parse(JSON.stringify(ipAddressMap))

    undateGoodsAllDetails()
  }
}
// -----------------end---------------------

//  ==========================================批量赋值操作- ==========================================
const proTable = ref<ProTableInstance>()
const batchAssignmentRef = ref<InstanceType<typeof SpecialBatchAssignment>>()
const batchType = ref<string[]>(['az'])

// 批量操作类型
const batchOperationType = computed(() => {
  let arr = [
    {
      label: '可用区',
      value: 'az',
    },
    {
      label: '网络',
      value: 'network',
    },
  ]
  return arr
})

const selePool = computed<any>(() => {
  if (!proTable.value?.selectedList.length) return []
  return proTable.value?.selectedList
})

const handleTypeChange = (value: string[]) => {
  batchType.value = value
  proTable.value?.clearSelection()
}

const showCheckboxAll = () => {
  if (!btnAuth.value.resource_creation) return false
  if (!goodsAllDetails.value.length) return false

  const firstValue = goodsAllDetails.value[0]['regionId']

  return !goodsAllDetails.value.every((item: any) => item['regionId'] === firstValue)
}

const selectable = (row: any) => {
  // 当为云硬盘的时候判断
  if (['evs', 'eip', 'shareEvs'].includes(goodsType.value) && row['vmName']) return false

  if (btnAuth.value.resource_creation) {
    if (rowStatus(row.status)) return false

    const pooold = proTable.value?.selectedList.map((item: any) => item.regionId) ?? []
    return !pooold.length || !row.regionId || pooold.includes(row.regionId)
  }

  return true
}
const selectedColumns = (): ColumnProps => {
  return {
    type: 'selection',
    align: 'center',
    fixed: 'left',
    width: '55px',
    selectable: selectable,
  }
}

const chageDetails = async (poolIdList: string[], params: any, newArr: any[]) => {
  let newParams = {
    azId: params.azId,
    azName: params.azName,
    azCode: params.azCode,
    templateCode: '',
  }
  const arr = newArr.map((item: any) => {
    const orderGoodsIdString = item.ids?.join('|')
    if (poolIdList.includes(orderGoodsIdString!)) {
      item = {
        ...item,
        ...newParams,
      }
    }
    return {
      ...item,
    }
  })

  return arr
}

/**
 *
 * @param item row 数据
 * @param params  网络数据
 * @param lastIndex  截取到最后的索引
 * @returns 返回处理后的ipAddressMap 和 planeNetworkModel
 * 获取选中的数据的 以及 openNum
  拆分出 {
     idHash :
     regionId :
     data // params内的planeNetworkModel内 ipAddress 中的根据openNum 数量均分
  }
 */
const assignIp = (item: any, params: any, firstIndex: number, lastIndex: number) => {
  const { planeNetworkModel } = JSON.parse(JSON.stringify(params))
  planeNetworkModel.forEach((item: any) => {
    item.subnets.forEach((element: any) => {
      element['ipAddress'] = element.ipAddress.slice(firstIndex, lastIndex)
    })
  })
  const { ipAddressMap, planeNetworkModel: newPlaneNetworkModel } = extractIpAddresses(
    planeNetworkModel,
    item.idHash,
  )

  return { ipAddressMap, planeNetworkModel: newPlaneNetworkModel }
}

const chageNetDetails = async (poolIdList: string[], params: any, newArr: any[]) => {
  let lastIndex = 0
  const arr = newArr.map((item: any) => {
    const orderGoodsIdString = item.ids?.join('|')
    if (poolIdList.includes(orderGoodsIdString!)) {
      const { ipAddressMap, planeNetworkModel } = assignIp(
        item,
        params,
        lastIndex,
        lastIndex + item.openNum,
      )

      lastIndex += item.openNum
      item = {
        ...item,
        planeNetworkModel,
        ipAddressMap,
      }
    }
    return {
      ...item,
    }
  })
  return arr
}

const handleBatchSubmit = async (params: any) => {
  if (!proTable.value?.isSelected) return showTips('请选择要操作的数据')
  await ElMessageBox.confirm('确定要批量赋值吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })

  const poolIdList = proTable.value?.selectedListIds.map((item: any) => item.join('|'))

  let arr: any[] = JSON.parse(JSON.stringify(goodsAllDetails.value))

  if (batchType.value.includes('network')) {
    arr = await chageNetDetails(poolIdList, params, arr)
  }

  if (batchType.value.includes('az')) {
    arr = await chageDetails(poolIdList, params, arr)
    // 获取模板
    let specParams: any = []

    arr.map((item: any) => {
      specParams.push({
        regionId: item.regionId,
        azId: item.azId,
        name: item.flavorName,
        key: `${item.azId}-${item.flavorName}`,
      })
    })
    specParams = uniqueByKeys(specParams, ['regionId', 'azId', 'name', 'key'])
    await Promise.all(
      specParams.map((item: any) => {
        let key = item.key
        delete item.key
        return getSpecDic(item, key)
      }),
    )
  }

  nextTick(() => {
    goodsAllDetails.value = mergeParent(arr)
    proTable.value?.clearSelection()
    batchAssignmentRef.value?.resetForm()
    batchType.value = ['az']
  })
}
</script>

<style lang="scss" scoped>
.title-input {
  flex: 1;
  padding-top: 20px;
  margin-right: 10px;
}

/* 隐藏全选复选框 */
.show-checkbox-all {
  :deep() {
    .el-table .el-table__header th .el-checkbox {
      display: none;
    }
  }
}
</style>
