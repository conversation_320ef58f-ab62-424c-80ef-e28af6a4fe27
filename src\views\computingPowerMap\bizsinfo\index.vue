<template>
  <div id="bizsTenantView">
    <SlPageHeader
      title="业务详情信息"
      detail="实时计算资源状态"
      :show-back="true"
      :icon="{ hidden: true }"
    >
      <template #header-right-custom>
        <ResourceFilter :resource-list="resourceList"></ResourceFilter>
      </template>
    </SlPageHeader>
    <div style="padding: 0 20px">
      <div style="margin-top: 20px">
        <el-form
          ref="ruleFormRef"
          style="max-width: 600px"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
        >
          <el-form-item label="业务系统" prop="selectedBusiness">
            <el-select
              v-model="ruleForm.selectedBusiness"
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="2"
              multiple
              placeholder="请选择"
              @change="handleSelectionChange"
            >
              <el-option
                v-for="item in businessSystemList"
                :key="item.busiSystemId"
                :label="item.busiSystemName"
                :value="item.busiSystemId"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <ResourceNumberList v-loading="loading" :resource-list="resourceList" />
      <div class="upper" v-loading="loading">
        <SystemUsageChart
          class="upper-left"
          :system-list="businessSystemList"
          :selected-system-ids="selectedSystemIds"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import ResourceNumberList from './components/ResourceNumberList.vue'
import ResourceFilter from './components/ResourceFilter.vue'
import SystemUsageChart from './components/SystemUsageChart.vue'
import { onMounted, ref, reactive } from 'vue'
import { getBusinessSystemListApi, getResourceNumberListApi } from '@/api/modules/computingPowerMap'
import type { BusinessSystemListType } from './interface/type'
import type { FormInstance, FormRules } from 'element-plus'

interface RuleForm {
  selectedBusiness: string[]
}
const loading = ref(false)
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  selectedBusiness: ['all'],
})
const rules = reactive<FormRules<RuleForm>>({
  selectedBusiness: [
    {
      required: true,
      message: '请至少选择一个',
      trigger: 'change',
    },
  ],
})

// 处理选择变化
const handleSelectionChange = (val: any) => {
  console.log(val)
  if (val.length == 0) {
    if (!ruleFormRef.value) return
    ruleFormRef.value.validateField('selectedBusiness')
    return
  }
  // 如果选择了"全部"
  if (val.includes('all')) {
    const isLastItem = 'all' === val[val.length - 1]
    if (isLastItem) {
      ruleForm.selectedBusiness = ['all']
    } else {
      // 自动取消其他选择
      ruleForm.selectedBusiness = val.filter((item: any) => item !== 'all')
    }
  }
  if (
    ruleForm.selectedBusiness.length == 1 &&
    'all' === ruleForm.selectedBusiness[ruleForm.selectedBusiness.length - 1]
  ) {
    selectedSystemIds.value = businessSystemList.value
      .map((item) => item.busiSystemId)
      .filter((item: any) => item !== 'all')
  } else {
    selectedSystemIds.value = ruleForm.selectedBusiness
  }
  getResourceNumberList()
  // 请求数据
}

// 业务系统列表相关逻辑
const businessSystemList = ref<BusinessSystemListType[]>([])

/**
 * 获取业务系统列表及选择列表
 */
const getBusinessSystemList = async () => {
  const { entity } = await getBusinessSystemListApi()
  businessSystemList.value = entity.map((item: any) => {
    return {
      busiSystemCode: item.systemCode,
      busiSystemId: item.systemId,
      busiSystemName: item.systemName,
      tenantId: item.tenantId,
    }
  })
  businessSystemList.value.unshift({
    busiSystemId: 'all',
    busiSystemName: '全部',
  })
}
const selectedSystemIds = ref<string[]>([])

// 资源数量列表关逻辑
const resourceList = ref([
  {
    key: 'ecs',
    name: '云主机',
    show: true,
    data: {},
  },
  {
    key: 'gcs',
    name: 'GPU云主机',
    show: true,
    data: {},
  },
  {
    key: 'evs',
    name: '云硬盘',
    show: true,
    data: {},
  },
  {
    key: 'obs',
    name: '对象存储',
    show: true,
    unit: 'GB',
    data: {},
  },
  {
    key: 'nat',
    name: 'NAT网关',
    show: true,
    data: {},
  },
  {
    key: 'slb',
    name: '负载均衡',
    show: true,
    data: {},
  },
  {
    key: 'eip',
    name: '弹性公网',
    show: true,
    data: {},
  },
  {
    key: 'vpc',
    name: 'VPC',
    show: true,
    data: {},
  },
  {
    key: 'network',
    name: '网络',
    show: true,
    data: {},
  },
  {
    key: 'cq',
    name: '容器配额',
    show: true,
    data: {},
  },
  {
    key: 'mysql',
    name: 'MySQL云数据库',
    show: true,
    data: {},
  },
  {
    key: 'redis',
    name: '通用Redis',
    show: true,
    data: {},
  },
  {
    key: 'cloudport',
    name: '云端口',
    show: true,
    data: {},
  },
  {
    key: 'backup',
    name: '云灾备',
    show: true,
    data: {},
  },
  {
    key: 'vpn',
    name: 'VPN',
    show: true,
    data: {},
  },
  {
    key: 'nas',
    name: 'NAS',
    show: true,
    data: {},
  },
  {
    key: 'pm',
    name: '裸金属',
    show: true,
    data: {},
  },
  {
    key: 'kafka',
    name: 'Kafka',
    show: true,
    data: {},
  },
  {
    key: 'es',
    name: 'ElasticSearch',
    show: true,
    data: {},
  },
  {
    key: 'flink',
    name: 'Flink',
    show: true,
    data: {},
  },
])
const getResourceNumberList = async () => {
  loading.value = true
  const { entity } = await getResourceNumberListApi({
    busiSystemIds: selectedSystemIds.value,
  })
  const initEntityItem = { goodType: '', amount: 0, diffAmount: 0, lastMount: 0, ratio: '0%' }
  resourceList.value.forEach((item: any) => {
    const entityItem = entity.find((entityItem: any) => entityItem.goodType === item.key)
    item.data = entityItem || JSON.parse(JSON.stringify(initEntityItem))
    if (item.key === 'obs') {
      if (item.data.amount > 1024) {
        item.data.amount = (item.data.amount / 1024).toFixed(2)
        item.data.diffAmount = (item.data.diffAmount / 1024).toFixed(2)
        item.data.unit = 'TB'
      }
    }
  })
  let timer = setTimeout(() => {
    loading.value = false
    clearInterval(timer)
  }, 1000)
}

onMounted(() => {
  getBusinessSystemList().then(() => {
    selectedSystemIds.value = businessSystemList.value
      .map((item) => item.busiSystemId)
      .filter((item: any) => item !== 'all')
    getResourceNumberList()
  })
})
</script>
<style scoped lang="scss">
#bizsTenantView {
  //background-color: white;
  padding: 0;

  background-color: #eff5fe;
  width: 100%;
  //height: 100%;
  //padding: 20px;
  box-sizing: border-box;
  background-image: url('../../../assets/images/img/bj.png');
  background-size: 100% 100%;
  background-position: top left;
  background-repeat: no-repeat;
  .upper {
    display: flex;
    border-top: 1px solid #eee;
    padding: 0 0 30px 10px;
    .upper-left {
      width: 100%;
      //padding-right: 20px;
      border-right: 1px solid #eee;
    }
    //.upper-right {
    //  width: 500px;
    //}
  }
}
</style>
<style lang="scss">
#bizsTenantView {
  .el-drawer__body {
    padding: 0;
  }
}
</style>
