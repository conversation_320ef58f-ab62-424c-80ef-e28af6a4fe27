<template>
  <div class="outer-container">
    <div class="dashboard-container">
      <!-- 顶部标题和算力总数 -->
      <Head path="/computingPowerMap" />
      <div class="dashboard-main">
        <!-- 左侧面板 -->
        <aside class="dashboard-side dashboard-side-left" v-loading="loading">
          <PowerBar :request-params="requestParams" />
          <div
            class="dashboard-side-left-bottom"
            :class="{ active: activeComponent === 'internet' }"
          >
            <div class="panel-title">网络</div>
            <div class="panel-content">
              <Internet
                :internet-data="internetData"
                @click="(type: any) => handleComponentClick('internet', type)"
              />
            </div>
          </div>
        </aside>
        <!-- 中间地图和卡片 -->
        <main class="dashboard-center">
          <DashboardCityMap
            :key="cityParam"
            :city="cityParam"
            @selectChange="handleSelectChange"
            @rightClick="handleRightClick"
            @doubleClick="handleDoubleClick"
          />
        </main>

        <aside class="dashboard-side dashboard-side-right">
          <!-- 右侧面板 -->
          <div
            class="dashboard-side-right-top"
            :class="{ active: activeComponent === 'virtualmachine' }"
          >
            <div class="panel-title">虚拟机</div>
            <div class="panel-content">
              <VirtualMachine
                :value="virtualPoolNum"
                @click="handleComponentClick('virtualmachine')"
              ></VirtualMachine>
            </div>
          </div>
          <div class="dashboard-side-right-top2" :class="{ active: activeComponent === 'storage' }">
            <div class="panel-title">存储</div>
            <div class="panel-content">
              <Storage :storage-data="storageData" @click="handleComponentClick('storage')" />
            </div>
          </div>
          <div class="dashboard-side-right-top3" :class="{ active: activeComponent === 'vcpu' }">
            <div class="panel-title">vCpu</div>
            <div class="panel-content">
              <VCpu :vcpu-data="vcpuData" @click="handleComponentClick('vcpu')"></VCpu>
            </div>
          </div>
          <div
            class="dashboard-side-right-bottom"
            :class="{ active: activeComponent === 'memory' }"
          >
            <div class="panel-title">内存</div>
            <div class="panel-content">
              <Memory :memory-data="memoryData" @click="handleComponentClick('memory')" />
            </div>
          </div>
        </aside>
      </div>
      <!-- <StorageDialog v-if="StorageDialogVisible"></StorageDialog> -->
      <!-- 存储弹窗 -->
      <StorageDialog
        v-if="StorageDialogVisible"
        :request-params="requestParams"
        @close="closeDialog"
      ></StorageDialog>
      <!-- 网络弹窗 -->
      <InternetDialog
        v-if="InternetDialogVisible"
        :request-params="requestParams"
        :internet-active-tab="internetActiveTab"
        @close="closeDialog"
      ></InternetDialog>
      <!-- 虚拟机弹窗 -->
      <VirtualMachineDialog
        v-if="VirtualMachineDialogVisible"
        :request-params="requestParams"
        @close="closeDialog"
      ></VirtualMachineDialog>
      <!-- vpc弹窗 ， 内存 弹窗 -->
      <VCpuOrMemoryLine
        v-if="activeComponent === 'vcpu' || activeComponent === 'memory'"
        :type="activeComponent"
        :request-params="requestParams"
        @close="closeDialog"
      ></VCpuOrMemoryLine>
      <!--        左侧弹窗-->
      <leftDetailDialog
        v-if="showComPowerLeftDialog"
        :request-params="requestParams"
        @close="closeDialog"
      ></leftDetailDialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import PowerBar from './components/comPowerTotal.vue'
import DashboardCityMap from './components/detail/DashboardCityMap.vue'
import Head from './components/Head.vue'
import { onMounted, ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import VirtualMachine from './components/detail/VirtualMachine.vue'
import Storage from './components/detail/Storage.vue'
import VCpu from './components/detail/VCpu.vue'
import Memory from './components/detail/Memory.vue'
import Internet from './components/detail/Internet.vue'
import StorageDialog from './components/detail/StorageDialog.vue'
import InternetDialog from './components/detail/InternetDialog.vue'
import VirtualMachineDialog from './components/detail/VirtualMachineDialog.vue'
import VCpuOrMemoryLine from './components/detail/VCpuOrMemoryLine.vue'
import type {
  ResourceUsageData,
  StorageData,
  VCpuData,
  MemoryData,
  InternetData,
} from './interface/type'
import { getComPowerMapStatsResUsageLatest, getvmCountApi } from '@/api/modules/comPowerCenter'
import cityCodeMap from './json/cityCodeMap.json'
import cityNameMap from './json/cityNameMap.json'
import leftDetailDialog from './components/leftDetailDialog.vue'

const route = useRoute()

// 从URL查询参数中获取城市信息，默认为浙江省
const cityParam = computed(() => {
  const city = (route.query.city as string) || 'zhejiang'
  // 验证城市参数是否为支持的城市，如果不是则返回默认值
  const supportedCities = [
    'zhejiang',
    'hangzhou',
    'ningbo',
    'wenzhou',
    'jiaxing',
    'huzhou',
    'shaoxing',
    'jinhua',
    'quzhou',
    'zhoushan',
    'taizhou',
    'lishui',
  ]

  return city && supportedCities.includes(city) ? city : 'zhejiang'
})

const cityCode = ref<string>('')
const areaCode = ref<string>('')
const cloudName = ref<string>('')
const platformTypeName = ref<string>('')
let maxInnerWidth = 1920
let maxInnerHeight = 1080

// 从路由参数中获取城市代码
let city = route.query.city as string
if (city) {
  const cityName = cityNameMap[city as keyof typeof cityNameMap]
  if (cityName && cityName in cityCodeMap) {
    cityCode.value = cityCodeMap[cityName as keyof typeof cityCodeMap].toString()
  }
}

// 定义请求参数
const requestParams = ref({
  cloudName: cloudName.value,
  platformTypeName: platformTypeName.value,
  cityCode: cityCode.value,
  areaCode: areaCode.value,
})

// 加载状态
const loading = ref<boolean>(true)

// 获取资源使用情况数据
const fetchResourceUsageData = async () => {
  try {
    loading.value = true
    const res = await getComPowerMapStatsResUsageLatest(requestParams.value)
    const entity = res.entity as ResourceUsageData

    // 更新虚拟机数量
    virtualPoolNum.value = entity?.vmNum ?? 0

    const units = determineUnit(entity?.storageTotal ?? 0)
    const storageTotal = convertGbWithUnit(entity?.storageTotal ?? 0, units)
    const storageUsed = convertGbWithUnit(entity?.storageUsed ?? 0, units)
    const storageAvi = convertGbWithUnit(entity?.storageAvi ?? 0, units)
    // 更新存储数据
    storageData.value = {
      total: Number(storageTotal),
      used: Number(storageUsed),
      remaining: Number(storageAvi),
      percentage: entity?.storageTotal
        ? Number(((entity?.storageUsed / entity?.storageTotal) * 100).toFixed(2))
        : 0,
      memoryunits: units,
    }

    // 更新 vCPU 数据
    vcpuData.value = {
      total: entity?.vcpuTotal ?? 0,
      used: entity?.vcpuUsed ?? 0,
      remaining: entity?.vcpuAvi ?? 0,
      percentage: entity?.vcpuTotal
        ? Number(((entity?.vcpuUsed / entity.vcpuTotal) * 100).toFixed(2))
        : 0,
    }

    const memoryunits = determineUnit(entity?.memoryTotal ?? 0)
    const memoryTotal = convertGbWithUnit(entity?.memoryTotal ?? 0, memoryunits)
    const memoryUsed = convertGbWithUnit(entity?.memoryUsed ?? 0, memoryunits)
    const memoryAvi = convertGbWithUnit(entity?.memoryAvi ?? 0, memoryunits)
    // 更新内存数据
    memoryData.value = {
      total: Number(memoryTotal), // 转换为 PB
      used: Number(memoryUsed),
      remaining: Number(memoryAvi),
      percentage: entity?.memoryTotal
        ? Number(((entity?.memoryUsed / entity?.memoryTotal) * 100).toFixed(2))
        : 0,
      memoryunits,
    }

    // 更新网络数据
    internetData.value = {
      dcn: {
        total: entity?.dcnTotal ?? 0,
        used: entity?.dcnUsed ?? 0,
        remaining: entity?.dcnAvi ?? 0,
      },
      publicIp: {
        total: entity?.eipTotal ?? 0,
        used: entity?.eipUsed ?? 0,
        remaining: entity?.eipAvi ?? 0,
      },
    }
    const { entity: vmCountEntity } = await getvmCountApi(requestParams.value)
    virtualPoolNum.value = vmCountEntity ?? 0
  } catch (error) {
    console.error('获取资源使用情况数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听城市参数变化，重新获取数据
watch(
  cityParam,
  (newCity) => {
    // 更新城市代码
    const cityName = cityNameMap[newCity as keyof typeof cityNameMap]
    if (cityName && cityName in cityCodeMap) {
      cityCode.value = cityCodeMap[cityName as keyof typeof cityCodeMap].toString()
    } else {
      cityCode.value = ''
    }

    // 重置区域代码
    areaCode.value = ''

    // 更新请求参数
    requestParams.value = {
      cloudName: cloudName.value,
      platformTypeName: platformTypeName.value,
      cityCode: cityCode.value,
      areaCode: areaCode.value,
    }

    // 重新获取数据
    fetchResourceUsageData()
  },
  { immediate: true },
)

// 当前激活的组件
const activeComponent = ref<string>('')

//总览数据

// 虚拟机数量
const virtualPoolNum = ref<number>(0)

// 资源使用情况数据
const storageData = ref<StorageData>({
  total: 0,
  used: 0,
  remaining: 0,
  percentage: 0,
  memoryunits: 'GB',
})

const vcpuData = ref<VCpuData>({
  total: 0,
  used: 0,
  remaining: 0,
  percentage: 0,
})

const memoryData = ref<MemoryData>({
  total: 0,
  used: 0,
  remaining: 0,
  percentage: 0,
  memoryunits: 'GB',
})

const internetData = ref<InternetData>({
  dcn: {
    total: 0,
    used: 0,
    remaining: 0,
  },
  publicIp: {
    total: 0,
    used: 0,
    remaining: 0,
  },
})

let determineUnit = (gb: number) => {
  const gbToTb = 1024 // 1 TB = 1024 GB
  const tbToPb = 1024 // 1 PB = 1024 TB

  if (gb >= gbToTb * tbToPb) {
    return 'PB'
  } else if (gb >= gbToTb) {
    return 'TB'
  } else {
    return 'GB'
  }
}

let convertGbWithUnit = (val: number, unit: string) => {
  let value

  if (unit === 'PB') {
    value = val / (1024 * 1024) // GB to PB
  } else if (unit === 'TB') {
    value = val / 1024 // GB to TB
  } else {
    value = val // 保持为GB
  }

  // 保留两位小数
  value = value.toFixed(2)
  return value
}

//  ===================适配=======================
onMounted(() => {
  const container = document.querySelector('.dashboard-container') as HTMLElement
  const scaleX = window.innerWidth / maxInnerWidth
  const scaleY = window.innerHeight / maxInnerHeight
  const transValueX = (1 - 1 / scaleX) * 50
  const transValueY = (1 - 1 / scaleY) * 50
  container.style.transform = `scale(${scaleX}, ${scaleY}) translate(${transValueX}%, ${transValueY}%)`

  // 获取资源使用情况数据
  fetchResourceUsageData()
})
window.addEventListener('resize', () => {
  const container = document.querySelector('.dashboard-container') as HTMLElement
  const scaleX = window.innerWidth / maxInnerWidth
  const scaleY = window.innerHeight / maxInnerHeight
  const transValueX = (1 - 1 / scaleX) * 50
  const transValueY = (1 - 1 / scaleY) * 50
  container.style.transform = `scale(${scaleX}, ${scaleY}) translate(${transValueX}%, ${transValueY}%)`
})

// =================点击地图==================
const handleSelectChange = (selectedRegion: {
  cityCode: number | null
  areaCode: string
  cloudName: string
  platformTypeName: string
}) => {
  cityCode.value = selectedRegion.cityCode?.toString() || ''
  areaCode.value = selectedRegion.areaCode
  cloudName.value = selectedRegion.cloudName
  platformTypeName.value = selectedRegion.platformTypeName

  // 更新请求参数
  requestParams.value = {
    cloudName: cloudName.value,
    platformTypeName: platformTypeName.value,
    cityCode: cityCode.value,
    areaCode: areaCode.value,
  }

  // 重新获取资源使用情况数据
  fetchResourceUsageData()
}

// =================右键点击地图==================
const handleRightClick = (selectedRegion: {
  regionName: string
  cityCode: number | null
  areaCode: string
  cloudName: string
  platformTypeName: string
}) => {
  cityCode.value = selectedRegion.cityCode?.toString() || ''
  areaCode.value = selectedRegion.areaCode
  cloudName.value = selectedRegion.cloudName
  platformTypeName.value = selectedRegion.platformTypeName

  // 更新请求参数
  requestParams.value = {
    cloudName: cloudName.value,
    platformTypeName: platformTypeName.value,
    cityCode: cityCode.value,
    areaCode: areaCode.value,
  }

  // 重新获取资源使用情况数据
  fetchResourceUsageData()

  // 打开左侧弹窗
  handleComponentClick('leftDetail')
}

// =================双击地图==================
const handleDoubleClick = (selectedRegion: {
  regionName: string
  cityCode: string
  cloudName: string
  platformTypeName: string
}) => {
  console.log('双击切换城市:', selectedRegion.cityCode)

  // 更新云平台和平台类型参数（保持当前选择）
  cloudName.value = selectedRegion.cloudName
  platformTypeName.value = selectedRegion.platformTypeName

  // 更新当前城市参数，触发地图切换
  const newUrl = new URL(window.location.href)
  newUrl.searchParams.set('city', selectedRegion.cityCode)
  window.history.pushState({}, '', newUrl.toString())

  // 强制触发路由更新
  window.dispatchEvent(new PopStateEvent('popstate'))

  // 立即更新请求参数
  requestParams.value = {
    cloudName: cloudName.value,
    platformTypeName: platformTypeName.value,
    cityCode: cityCode.value,
    areaCode: areaCode.value,
  }

  // 触发路由更新，重新加载对应城市的地图和数据
  // 由于我们使用的是 computed 的 cityParam，它会自动响应 URL 参数的变化
  // watch 会自动处理数据重新获取和地图组件更新
}

// 存储弹窗
const StorageDialogVisible = ref(false)
// 网络弹窗
const InternetDialogVisible = ref(false)
// 虚拟机弹窗
const VirtualMachineDialogVisible = ref(false)
// 左侧弹窗
const showComPowerLeftDialog = ref(false)

const internetActiveTab = ref('1')
// =================组件点击事件==================
const handleComponentClick = (componentName: string, type?: string) => {
  // 如果点击的是当前激活的组件，则取消激活
  // if (activeComponent.value === componentName) {
  //   activeComponent.value = ''
  //   closeDialog()
  // } else {
  // 否则激活点击的组件
  activeComponent.value = componentName
  switch (componentName) {
    case 'storage':
      StorageDialogVisible.value = true
      InternetDialogVisible.value = false
      VirtualMachineDialogVisible.value = false
      showComPowerLeftDialog.value = false
      break
    case 'internet':
      InternetDialogVisible.value = true
      StorageDialogVisible.value = false
      VirtualMachineDialogVisible.value = false
      showComPowerLeftDialog.value = false
      if (type) {
        internetActiveTab.value = type == 'dcn' ? '2' : '1'
      }
      break
    case 'virtualmachine':
      VirtualMachineDialogVisible.value = true
      InternetDialogVisible.value = false
      StorageDialogVisible.value = false
      showComPowerLeftDialog.value = false
      break
    case 'leftDetail':
      showComPowerLeftDialog.value = true
      StorageDialogVisible.value = false
      InternetDialogVisible.value = false
      VirtualMachineDialogVisible.value = false
      break
    default:
      StorageDialogVisible.value = false
      InternetDialogVisible.value = false
      VirtualMachineDialogVisible.value = false
      showComPowerLeftDialog.value = false
      break
  }
  // }
}

const closeDialog = () => {
  StorageDialogVisible.value = false
  activeComponent.value = ''
  InternetDialogVisible.value = false
  VirtualMachineDialogVisible.value = false
  showComPowerLeftDialog.value = false
}
</script>

<style lang="scss" scoped>
.outer-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
.dashboard-container {
  width: 1920px;
  height: 1080px;
  background: url('/images/computingPower/bg.png') no-repeat 0 0;
  background-size: contain;
  display: flex;
  flex-direction: column;
}
.dashboard-main {
  flex: 1;
  display: flex;
  padding: 0 17px;
  box-sizing: border-box;
  margin-top: -34px;
}
.dashboard-side {
  width: 462px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.dashboard-side-left {
  margin-right: 6px;
}
.dashboard-side-right {
  margin-left: 6px;
}

.dashboard-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  background: url('/images/computingPower/computingPowerMapBg.png') no-repeat center bottom;
  background-size: contain;
  padding-top: 30px;
}

.panel-title {
  color: #fff;
  height: 40px;
  font-size: 18px;
  font-weight: 700;
  padding-left: 24px;
  line-height: 40px;
}

.dashboard-side-left-bottom,
.dashboard-side-right-top,
.dashboard-side-right-top2,
.dashboard-side-right-top3,
.dashboard-side-right-bottom {
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(42, 107, 255, 0.08);
  padding: 2px;
  position: relative;
  background-size: 100% 100%;
}

.panel-content {
  flex: 1;
  // background-color: pink;
  padding: 10px 30px;
}

.dashboard-side-left-bottom {
  height: 268px;
  background: url('/images/computingPower/comPowerLeft2Bg.png') no-repeat center center;

  &.active {
    background: url('/images/computingPower/comPowerLeft2BgActive.png') no-repeat center center;

    .panel-title {
      color: #000;
    }
  }
}

.dashboard-side-right-top {
  height: 196px;
  background: url('/images/computingPower/comPowerRight4.png') no-repeat center center;

  &.active {
    background: url('/images/computingPower/comPowerRight4Active.png') no-repeat center center;

    .panel-title {
      color: #000;
    }
  }
}

.dashboard-side-right-top2 {
  height: 240px;
  background: url('/images/computingPower/comPowerRight2Bg.png') no-repeat center center;

  .panel-title {
    line-height: 33px;
  }

  &.active {
    background: url('/images/computingPower/comPowerRight2BgActive.png') no-repeat center center;

    .panel-title {
      color: #000;
    }
  }
}

.dashboard-side-right-top3 {
  height: 240px;
  background: url('/images/computingPower/comPowerRight2Bg.png') no-repeat center center;

  .panel-title {
    line-height: 33px;
  }

  &.active {
    background: url('/images/computingPower/comPowerRight2BgActive.png') no-repeat center center;

    .panel-title {
      color: #000;
    }
  }
}

.dashboard-side-right-bottom {
  height: 242px;
  background: url('/images/computingPower/comPowerRight1Bg.png') no-repeat center center;

  .panel-title {
    line-height: 29px;
  }

  &.active {
    background: url('/images/computingPower/comPowerRight1BgActive.png') no-repeat center center;

    .panel-title {
      color: #000;
    }
  }
}
</style>
<style>
.comPowerNoData {
  width: 100%;
  height: 100%;
  display: flex;
  min-height: 100px;
  align-items: center;
  justify-content: center;
}
.bottom-dialog-search .el-select .el-select__wrapper .el-select__placeholder {
  color: #ffffff;
}
.bottom-dialog-search .el-select .el-select__wrapper {
  background: transparent;
  box-shadow: none;
  border-radius: 2px;
  border: 1px solid #3069b0;
}
.bottom-dialog-table {
  .el-table {
    background: transparent;
    tr {
      background: transparent;
    }
    th.el-table__cell {
      background: transparent;
      border-bottom: 1px solid rgba(102, 102, 102, 0.5);
      font-size: 15px !important;
      color: #b7d7ff !important;
    }
    .cell {
      color: #fff;
    }
  }
  .el-table__inner-wrapper:before {
    background-color: rgba(102, 102, 102, 0.5) !important;
  }
}

.el-table.comPowerTable .el-table__empty-block .el-table__empty-text {
  color: #ffffff;
}
</style>
