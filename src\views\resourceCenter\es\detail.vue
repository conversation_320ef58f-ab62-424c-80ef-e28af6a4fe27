<template>
  <div id="KafkaDetail" class="table-box">
    <sl-page-header
      title="ElasticSearch详情"
      :icon="{
        class: 'page_Elasticsearch',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="kafka-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
          <template #networkModelSnapshotSlot>
            <div style="display: flex; justify-content: flex-start">
              <el-button type="primary" link @click="handleViewTemplate"> 查看 </el-button>
            </div>
          </template>
        </sl-form>
      </div>
    </el-scrollbar>

    <!-- 索引模板弹窗 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="索引模板"
      width="60%"
      :before-close="handleTemplateDialogClose"
    >
      <el-input
        v-model="templateContent"
        type="textarea"
        :rows="15"
        readonly
        placeholder="暂无索引模板内容"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templateDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getResourceDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

// 索引模板弹窗相关
const templateDialogVisible = ref(false)
const templateContent = ref('')

const detailData = reactive<any>({
  deviceName: '',
  spec: '',
  deviceStatus: '',
  frequency: '',
  dataStorageTotal: '',
  applyTime: '',
  tenantName: '',
  businessSysName: '',
  cloudPlatform: '',
  resourcePoolName: '',
  orderCode: '',
  effectiveTime: '',
  expireTime: '',
  billId: '',
  deviceStatusCn: '',
  applyUserName: '',
  networkModelSnapshot: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '索引模板名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '索引模板',
        type: 'slot',
        slotName: 'networkModelSnapshotSlot',
        span: 8,
        align: 'left',
      },
      {
        label: '日均增量数据',
        type: 'text',
        key: 'spec',
        span: 8,
      },
      {
        label: '数据保留时间',
        type: 'text',
        key: 'frequency',
        span: 8,
      },
      {
        label: '索引副本',
        type: 'text',
        key: 'deviceStatus',
        span: 8,
      },
      {
        label: '磁盘大小',
        type: 'text',
        key: 'dataStorageTotal',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'effectiveTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getResourceDetail({
    id: resourceId.value,
    type: 'kafka',
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/esList',
  })
}

// 查看索引模板
const handleViewTemplate = () => {
  templateContent.value = detailData.networkModelSnapshot || ''
  templateDialogVisible.value = true
}

// 关闭模板弹窗
const handleTemplateDialogClose = () => {
  templateDialogVisible.value = false
  templateContent.value = ''
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: 0;
}

.kafka-detail-scroll-view {
  height: 100%;
}

.sl-card {
  margin: 8px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}
</style>
