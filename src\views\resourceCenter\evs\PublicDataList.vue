<template>
  <div
    class="table-main"
    v-loading.fullscreen.lock="tableLoading"
    element-loading-text="操作中，请稍候..."
  >
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="getCorporateResourceList"
      :init-param="queryParams"
      :current-change="currentChange"
      @selection-change="handleSelectionChange"
      hidden-table-header
      row-key="goodsOrderId"
    >
    </SlProTable>

    <!-- 资源变更弹窗 -->
    <ResourceChangeDialog
      v-model:visible="changeDialogVisible"
      resource-type="evs"
      :selected-resources="selectedResources"
      :allowed-change-types="allowedChangeTypes"
      @confirm="handleConfirm"
      source-type="DG"
    />

    <!-- 资源延期弹窗 -->
    <ResourceChangeDialog
      v-model:visible="delayDialogVisible"
      resource-type="evs"
      :selected-resources="selectedResources"
      :is-delay="true"
      @confirm="handleConfirm"
    />

    <!-- 绑定设备弹窗 -->
    <DeviceBindDialog
      v-model:visible="bindDialogVisible"
      v-model:business-sys-id="currentEvs.businessSysId"
      @selectDevice="handleDeviceSelect"
      source-type="DG"
    />
    <corporatePropertyChange
      v-if="propertyChangeDrawer"
      :goods-list="goodsList"
      type="evs"
      v-model="propertyChangeDrawer"
      @confirm="handlePropertyChangeConfirm"
    />
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, computed, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import {
  getCorporateResourceList,
  dgRecoveryWorkOrderCreate,
  evsBind,
  evsUnbind,
} from '@/api/modules/resourecenter'
import SlMessage from '@/components/base/SlMessage'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import DeviceBindDialog from './components/DeviceBindDialog.vue'
import type { ResourceChangeType } from '../types'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import { useRouter } from 'vue-router'
import corporatePropertyChange from '@/views/corporatePropertyChange/index.vue'
import { transformData } from '@/views/corporatePropertyChange/propertyChange'
import type { ICartsModel } from '@/views/corporatePropertyChange/propertyChange'

const router = useRouter()
const { queryParams, isHiddenSelection, hideOperations } = defineProps<{
  queryParams: Record<string, any>
  isHiddenSelection?: boolean
  hideOperations?: boolean
}>()

const { validateResources } = useResourceChange()

// 格式化计费类型
const formatBillType = (type: string): string => {
  const billTypeMap: Record<string, string> = {
    day: '按天计费',
    month: '按月计费',
    year: '按年计费',
  }
  return billTypeMap[type] || type || '--'
}

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

const opColumn: [ColumnProps<any>] | [] =
  isHiddenSelection || hideOperations
    ? []
    : [
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          fixed: 'right',
          render: operationRender,
        },
      ]
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'dataDisk',
    label: '数据盘',
    minWidth: 150,
    fixed: 'left',
    render: ({ row }) => (
      <el-button onClick={() => handleViewDetail(row)} type="primary" link>
        {row.dataDisk}
      </el-button>
    ),
  },
  { prop: 'deviceId', label: '资源ID', width: 200 },
  {
    prop: 'billType',
    label: '计费类型',
    width: 100,
    render: ({ row }) => formatBillType(row.billType),
  },
  { prop: 'mountOrNot', label: '是否挂载云主机', minWidth: 150 },
  { prop: 'ecsName', label: '云主机', minWidth: 150 },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'cloudPlatform', label: '所属云', minWidth: 150, filter: true },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 150, filter: true },
  { prop: 'orderCode', label: '订单编号', minWidth: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 },
  { prop: 'effectiveTime', label: '订购时间', minWidth: 150 },
  { prop: 'applyUserName', label: '订购人', minWidth: 100 },
  { prop: 'changeStatusCn', label: '变更状态', width: 100 },
  ...opColumn,
])

function operationRender({ row }: any): VNode {
  return (
    <>
      {row.mountOrNot === '是' ? (
        <el-button
          onClick={() => handleUnbind(row)}
          type="primary"
          disabled={row.recoveryStatus != 0 || row.changeStatusCn !== '未变更'}
          link
        >
          解绑
        </el-button>
      ) : (
        <el-button
          onClick={() => handleBind(row)}
          type="primary"
          disabled={row.recoveryStatus != 0 || row.changeStatusCn !== '未变更'}
          link
        >
          绑定
        </el-button>
      )}
    </>
  )
}

const proTable = ref<ProTableInstance>()
const tableLoading = ref(false)

const currentRecycleIdsList = ref<any[]>([])

// 使用回收校验钩子函数
// 使用回收校验钩子函数
const { validateUnsubscribe, validateChange, getDgFormData } = useRecycleValidation()

const handleCreateUnsubscribe = async (goodsItems: any[]) => {
  const res = await dgRecoveryWorkOrderCreate(getDgFormData('evs', goodsItems))
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  SlMessage.success('已提交退订')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}

// 批量回收功能
const handleBatchUnsubscribe = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateUnsubscribe(selectedList, 'evs')) {
    currentRecycleIdsList.value = selectedList.map((i) => ({ goodsId: i.id.trim() }))
    handleCreateUnsubscribe(currentRecycleIdsList.value)
  }
}

// 多选数据
const multipleSelection = ref<any[]>([])
const changeDialogVisible = ref(false)
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])

// EVS允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = ['storage_expand']

// 使用计算属性缓存处理过的资源列表以提高性能
const processedResources = computed(() => {
  if (multipleSelection.value.length === 0) return []
  return multipleSelection.value.map((resource) => ({
    ...resource,
    _resourceId: resource.id, // 添加一个唯一标识，用于跟踪变化
  }))
})

// 使用计算属性判断是否禁用变更和延期按钮
const isChangeDisabled = computed(() => multipleSelection.value.length === 0)

// 处理资源变更，优化性能
const handleResourceChange = async (dg = false) => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要变更的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value, dg)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

// 处理资源延期，优化性能
const handleResourceDelay = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要延期的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

// 处理确认
const handleConfirm = (data: any) => {
  changeDialogVisible.value = false
  handleOpenPropertyChangeDrawer(data)
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 绑定设备相关变量
const bindDialogVisible = ref(false)
const currentEvs = ref<any>({})
const bindingLoading = ref('')
const unbindingLoading = ref('')

// 处理绑定设备
const handleBind = (row: any) => {
  currentEvs.value = row
  bindDialogVisible.value = true
}

// 处理设备选择
const handleDeviceSelect = async (device: any) => {
  if (bindingLoading.value) return
  if (!currentEvs.value.id) return

  try {
    bindingLoading.value = currentEvs.value.id
    tableLoading.value = true
    await evsBind({
      volumeResourceDetailId: currentEvs.value.id,
      vmResourceDetailId: device.id,
    })

    ElMessage.success('绑定设备成功')
    proTable.value?.getTableList()
    bindDialogVisible.value = false
    currentEvs.value = {}
  } catch (error) {
    console.error(error)
  } finally {
    bindingLoading.value = ''
    tableLoading.value = false
  }
}

// 处理解绑设备
const handleUnbind = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要解绑该设备吗？', '解绑确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    unbindingLoading.value = row.id
    tableLoading.value = true
    const res = await evsUnbind({
      volumeResourceDetailId: row.id,
    })

    if (res.code !== 200) {
      return
    }

    ElMessage.success('解绑设备成功')
    proTable.value?.getTableList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  } finally {
    unbindingLoading.value = ''
    tableLoading.value = false
  }
}

// 处理查看详情
const handleViewDetail = (row: any) => {
  // 跳转到EVS详情页面
  router.push({
    path: '/evsDetail',
    query: {
      id: row.id,
      sourceType: 'DG',
    },
  })
}

// 企业属性变更相关变量
const propertyChangeDrawer = ref(false)
const goodsList = ref<ICartsModel<'evs'>>([])

function handleOpenPropertyChangeDrawer(list: any) {
  if (!list) return ElMessage.warning('请选择要变更的资源')
  propertyChangeDrawer.value = true
  goodsList.value = transformData(list)
}

// 处理企业属性变更
const handlePropertyChangeConfirm = () => {
  propertyChangeDrawer.value = false
  proTable.value?.getTableList()
}

defineExpose({
  handleBatchUnsubscribe,
  handleResourceChange,
  handleResourceDelay,
  selectedList: multipleSelection,
})
</script>
