import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { useUserStore } from './user'

// 定义工单数据接口
interface OrderCountData {
  workOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
  recycleOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
  changeOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
  nonStanderOrder: {
    pendingCount: number
    approvedCount: number
    rejectedCount: number
  }
}

export const useWorkOrderStore = defineStore(
  'workOrder',
  () => {
    const userStore = useUserStore()
    // 保存当前使用的token值
    const currentToken = ref<string>(userStore.token)

    // 工单数量数据
    const orderData = ref<OrderCountData>({
      workOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
      recycleOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
      changeOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
      nonStanderOrder: {
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
      },
    })

    // 保存定时器实例
    let pollingTimer: number | null = null

    /**
     * 重置工单数据
     */
    const resetOrderData = () => {
      orderData.value = {
        workOrder: {
          pendingCount: 0,
          approvedCount: 0,
          rejectedCount: 0,
        },
        recycleOrder: {
          pendingCount: 0,
          approvedCount: 0,
          rejectedCount: 0,
        },
        changeOrder: {
          pendingCount: 0,
          approvedCount: 0,
          rejectedCount: 0,
        },
        nonStanderOrder: {
          pendingCount: 0,
          approvedCount: 0,
          rejectedCount: 0,
        },
      }
    }

    /**
     * 获取工单数量统计
     */
    const fetchOrderCount = async () => {
      try {
        // 检查token是否存在
        if (!userStore.token) {
          console.error('Token is missing')
          return
        }
        // 动态引入 getOrderCountApi 接口
        const { getOrderCountApi } = await import('@/api/modules/approvalCenter')
        const { entity } = await getOrderCountApi({})
        if (entity) {
          orderData.value = entity
        }
      } catch (error) {
        console.error('获取工单数量统计失败:', error)
      }
    }

    /**
     * 初始化轮询获取工单数量
     */
    const initOrderCountPolling = () => {
      // 如果已存在定时器，先清除
      if (pollingTimer) {
        clearInterval(pollingTimer)
        pollingTimer = null
      }

      // 检查token是否存在
      if (!userStore.token) {
        console.error('Token is missing')
        return
      }

      // 更新当前使用的token
      currentToken.value = userStore.token

      // 立即执行一次
      fetchOrderCount()

      // 设置定时器，每3分钟轮询一次
      pollingTimer = setInterval(
        () => {
          fetchOrderCount()
        },
        3 * 60 * 1000,
      ) // 3分钟 = 180000毫秒
    }

    /**
     * 停止轮询
     */
    const stopPolling = () => {
      if (pollingTimer) {
        clearInterval(pollingTimer)
        pollingTimer = null
      }
    }

    // 监听token变化
    watch(
      () => userStore.token,
      (newToken) => {
        // 如果token变化且不为空
        if (newToken !== currentToken.value && newToken) {
          console.log('Token changed, restarting polling...')
          // 停止现有轮询
          stopPolling()
          // 重置数据
          resetOrderData()
          // 使用新token重新初始化轮询
          initOrderCountPolling()
        } else if (!newToken) {
          // token为空（如登出）时，停止轮询并清空数据
          stopPolling()
          resetOrderData()
        }
      },
    )

    return {
      orderData,
      initOrderCountPolling,
      stopPolling,
      resetOrderData,
      fetchOrderCount,
    }
  },
  {
    persist: true,
  },
)
