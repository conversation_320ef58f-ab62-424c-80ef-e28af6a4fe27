<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogType === 'UNBIND' ? '解绑设备' : '绑定设备'"
    width="80%"
    destroy-on-close
    top="10vh"
    :before-close="handleCancel"
    class="device-bind-dialog"
  >
    <div class="filter-form-con" style="margin-bottom: 0">
      <sl-form
        class="filter-form"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      ></sl-form>
    </div>

    <div class="tab-wrapper">
      <div class="tab-content">
        <component
          :is="currentComponent"
          ref="dataListRef"
          :query-params="queryParams"
          :is-bind-dialog="true"
          :is-hidden-selection="true"
          @selectDevice="handleSelectDevice"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">关闭</el-button>
        <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, markRaw, defineAsyncComponent, watch } from 'vue'
import { Delete, Search } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { useDichooks } from '../../hooks/useDichooks'
import { ElMessage } from 'element-plus'

const props = defineProps<{
  visible: boolean
  backupType: string
  businessSysId?: string
  resourcePoolId?: string
  backupId?: string
  dialogType?: 'BIND' | 'UNBIND'
  sourceType?: string
}>()

const emit = defineEmits([
  'update:visible',
  'selectDevice',
  'update:business-sys-id',
  'update:resource-pool-id',
])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

// 当前激活的标签
const activeTab = ref(props.backupType)
const formRef = ref()
const dataListRef = ref()

// 动态导入组件
const componentsMap: Record<string, any> = {
  ecs: markRaw(defineAsyncComponent(() => import('../../ecs/dataList.vue'))),
  evs: markRaw(defineAsyncComponent(() => import('../../evs/dataList.vue'))),
}

const dgComponentsMap: Record<string, any> = {
  ecs: markRaw(defineAsyncComponent(() => import('../../ecs/PublicDataList.vue'))),
  evs: markRaw(defineAsyncComponent(() => import('../../evs/PublicDataList.vue'))),
}

// 获取当前选中标签页对应的组件
// 获取当前选中标签页对应的组件
const currentComponent = computed(() => {
  if (props.sourceType === 'DG') {
    return dgComponentsMap[activeTab.value] || null
  }
  return componentsMap[activeTab.value] || null
})

// 选中的设备
const selectedDevice = ref<any>(null)

// 设备查询条件
const formModel = reactive({
  deviceName: '',
  resourcePoolId: props.dialogType === 'BIND' ? props.resourcePoolId || '' : '',
  businessSysId: props.businessSysId || '',
  type: activeTab.value,
  backupId: props.dialogType === 'UNBIND' ? props.backupId : undefined,
  deviceId: '',
})

const queryParams = ref<any>({
  hideSelection: false,
  sourceType: props.sourceType,
})

// 字典hooks
const { resourcePoolsDic } = useDichooks()
// 表单配置
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '主机名称',
        type: 'input',
        key: 'deviceName',
        span: 8,
        disabled: false,
        hidden: computed(() => props.backupType === 'evs'),
      },
      {
        label: '资源ID',
        type: 'input',
        key: 'deviceId',
        span: 8,
        disabled: false,
        hidden: computed(() => props.backupType === 'ecs'),
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])
function reset() {
  // 重置表单字段，但保持关键参数
  formModel.deviceName = ''
  formModel.resourcePoolId = props.dialogType === 'BIND' ? props.resourcePoolId || '' : ''
  formModel.businessSysId = props.businessSysId || ''
  formModel.type = activeTab.value
  formModel.backupId = props.dialogType === 'UNBIND' ? props.backupId : undefined
  formModel.deviceId = ''

  if (formRef.value) {
    formRef.value.resetFields()
  }
  queryParams.value = { ...queryParams.value, ...formModel }
}
function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}
watch(
  () => activeTab.value,
  () => {
    handleTabClick()
  },
)
// 切换标签页时刷新数据
function handleTabClick() {
  // 重置表单字段，但保持当前选中的标签页类型
  formModel.deviceName = ''
  formModel.resourcePoolId = props.dialogType === 'BIND' ? props.resourcePoolId || '' : ''
  formModel.businessSysId = props.businessSysId || ''
  formModel.type = activeTab.value
  formModel.backupId = props.dialogType === 'UNBIND' ? props.backupId : undefined
  formModel.deviceId = ''

  if (formRef.value) {
    formRef.value.resetFields()
  }
  search()
  selectedDevice.value = null
}

// 处理选择设备
function handleSelectDevice(device: any) {
  selectedDevice.value = {
    ...device,
    deviceType: activeTab.value,
  }
  emit('selectDevice', selectedDevice.value)
}

// 取消按钮
function handleCancel() {
  dialogVisible.value = false
  selectedDevice.value = null
}

// 确认按钮
function handleConfirm() {
  if (dataListRef.value?.selectedList.length === 0) {
    return ElMessage.warning('请选择设备')
  }
  emit('selectDevice', dataListRef.value?.selectedList)
  dialogVisible.value = false
}

watch(
  () => props.businessSysId,
  (newVal) => {
    if (newVal) {
      formModel.businessSysId = newVal
      search()
    }
  },
  { immediate: true },
)

watch(
  () => props.resourcePoolId,
  (newVal) => {
    if (newVal) {
      formModel.resourcePoolId = newVal
      search()
    }
  },
  { immediate: true },
)

watch(
  () => props.backupType,
  (newVal) => {
    activeTab.value = newVal
    search()
  },
)

watch(
  () => props.backupId,
  (newVal) => {
    if (props.dialogType === 'UNBIND' && newVal) {
      formModel.backupId = newVal
      search()
    }
  },
  { immediate: true },
)

// 监听对话框可见性变化
watch(
  () => dialogVisible.value,
  (newVal) => {
    if (newVal) {
      // 弹窗打开时重置表单
      formModel.deviceName = ''
      formModel.resourcePoolId = props.dialogType === 'BIND' ? props.resourcePoolId || '' : ''
      formModel.businessSysId = props.businessSysId || ''
      formModel.type = activeTab.value
      formModel.backupId = props.dialogType === 'UNBIND' ? props.backupId : undefined
      formModel.deviceId = ''

      // 重置后立即执行搜索
      if (formRef.value) {
        formRef.value.resetFields()
      }
      search()
    }
  },
)
</script>

<style lang="scss" scoped>
.device-bind-dialog {
  :deep(.el-dialog__body) {
    height: calc(80vh - 120px);
    overflow-y: auto;
  }
}

.filter-form-con {
  margin-bottom: 16px;
}

.filter-form-btn {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.dialog-footer {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.tab-wrapper {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.orderDetailTab {
  background-color: #f2f3f5;
  position: relative;
  margin-bottom: 0;
  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    left: 5px;
    top: 11px;
    width: 4px;
    z-index: 10;
    background-color: var(--el-color-primary);
    margin-right: 10px;
    height: 0.78em;
    font-size: 18px;
  }
}

.tab-content {
  min-height: 400px;
  height: calc(80vh - 250px);
  overflow-y: auto;
  background-color: #fff;
  padding: 16px;
}
</style>
