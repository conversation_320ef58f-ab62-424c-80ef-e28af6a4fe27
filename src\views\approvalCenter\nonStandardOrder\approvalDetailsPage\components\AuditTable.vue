<template>
  <SlProTable :pagination="false" :columns="columns" :data="approveRecordList">
    <template #tableHeader>
      <sl-block-title>审核日志</sl-block-title>
    </template>
  </SlProTable>
</template>

<script setup lang="ts" name="AuditTable">
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { maskPhoneNumber } from '@/utils'
import { computed, ref } from 'vue'
const props = defineProps({
  orderDesc: {
    type: Object,
    required: true,
  },
})
const approveRecordList = computed(() => props.orderDesc.approveRecordList ?? [])
const columns = ref<ColumnProps[]>([
  {
    type: 'index',
    label: '序号',
    width: 55,
  },
  {
    prop: 'auditNodeName',
    label: '审批节点',
    minWidth: 130,
  },
  {
    prop: 'advice',
    label: '审批建议',
    minWidth: 250,
  },
  {
    prop: 'auditResultDesc',
    label: '审批结果',
    width: 100,
  },

  {
    prop: 'auditNodeRoleNames',
    label: '审批者',
    width: 140,
  },
  {
    prop: 'modifyTime',
    label: '操作时间',
    width: 170,
  },
  {
    prop: 'userName',
    label: '操作人',
    width: 140,
  },
  {
    prop: 'userPhone',
    label: '电话',
    width: 110,
    render: ({ row }) => {
      return row.userPhone ? maskPhoneNumber(row.userPhone) || '-' : '-'
    },
  },
  {
    prop: 'userEmail',
    label: '邮箱',
    width: 200,
  },
])
</script>
<style lang="scss" scoped></style>
