<template>
  <DashboardTitle :total-power="totalPower" />
  <div class="dashboard-cards">
    <Card v-for="item in powerList" :key="item.name" @click="handleOpen(item.name)">
      <div class="card-title">
        <div class="left">{{ item.name }}</div>
        <div class="right">
          <div class="left">
            <span class="top">{{ item.total }}</span>
            <span class="bottom">TFLOPS</span>
          </div>
          <div class="right">
            <el-button type="primary" link size="small" style="text-decoration: underline">
              >>
            </el-button>
          </div>
        </div>
      </div>
      <div class="card-content">
        <div class="card-content-item">
          <span class="card-content-item-title">已分配</span>
          <span class="card-content-item-value">{{ item.allocated }}</span>
        </div>
        <div class="card-content-item">
          <span class="card-content-item-title">剩余量</span>
          <span class="card-content-item-value">{{ item.remaining }}</span>
        </div>
      </div>
    </Card>
  </div>
  <section class="panel dashboard-map">
    <div ref="mapRef" style="width: 700px; height: 450px"></div>
  </section>
  <Drawer
    v-model="drawer"
    title="I am the title"
    :direction="direction"
    :before-close="handleClose"
    size="100%"
    :model-name="modelName"
    v-if="drawer"
    type="physics"
    @update:modelValue="handleClose"
  >
  </Drawer>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts/core'
import { MapChart, ScatterChart } from 'echarts/charts'
import { GeoComponent, TooltipComponent, VisualMapComponent } from 'echarts/components'
import { SVGRenderer } from 'echarts/renderers'
import { type ECharts } from 'echarts/core'
import DashboardTitle from './DashboardTitle.vue'
import Card from './Card.vue'
import { totalComputeApi, devicePhysicalTypeAreaGroupApi } from '@/api/modules/zsMap'
import Drawer from './Drawer.vue'

// 注册必要的组件
echarts.use([
  MapChart,
  ScatterChart,
  GeoComponent,
  TooltipComponent,
  VisualMapComponent,
  SVGRenderer,
])

const drawer = ref(false)
const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('btt')
const modelName = ref<string>('')
const handleClose = () => {
  drawer.value = false
}
const handleOpen = (name: string) => {
  modelName.value = name
  drawer.value = true
}
let chart: ECharts | null = null
const emit = defineEmits(['selectChange'])
const powerList = ref<any>([])
const coords = {
  杭州: [
    [119.5501 - 0.3, 30.1041 + 0.1],
    [119.5501 + 0.3, 30.1041 - 0.1],
    [119.5501 - 0.6, 30.1041 - 0.2],
    [119.5501 + 0.5, 30.1041 + 0.1],
    [119.5501 - 0.35, 30.1041 - 0.2],
    [119.5501, 30.1041],
  ],
  宁波: [
    [121.5503 - 0.24, 29.8746 + 0.2],
    [121.5503 - 0.08, 29.8746 + 0.3],
    [121.5503 - 0.22, 29.8746 - 0.2],
    [121.5503 + 0.1, 29.8746 - 0.4],
    [121.5503 + 0.25, 29.8746],
  ],
  温州: [
    [120.6994 - 0.6, 27.9949 - 0.2],
    [120.6994 - 0.4, 27.9949 - 0.26],
    [120.6994 - 0.25, 27.9949 - 0.4],
    [120.6994, 27.9949 + 0.3],
    [120.6994 + 0.2, 27.9949 + 0.33],
    [120.6994, 27.9949],
  ],
  嘉兴: [
    [120.7555 + 0.15, 30.7461 + 0.15],
    [120.7555 - 0.15, 30.7461 - 0.15],
    [120.7555 - 0.15, 30.7461 + 0.15],
    [120.7555 + 0.15, 30.7461 - 0.15],
    [120.7555 + 0.3, 30.7461 + 0.1],
    [120.7555, 30.7461],
  ],
  湖州: [
    [119.8868 - 0.15, 30.8947 - 0.1],
    [119.8868 - 0.15, 30.8947 + 0.1],
    [119.8868 + 0.15, 30.8947 + 0.1],
    [119.8868 + 0.3, 30.8947 - 0.2],
    [119.8868 + 0.1, 30.8947 - 0.25],
    [119.8868, 30.8947],
  ],
  绍兴: [
    [120.5821 + 0.15, 29.9971 + 0.15],
    [120.5821 - 0.15, 29.9971 - 0.15],
    [120.5821 - 0.15, 29.9971 + 0.15],
    [120.5821 + 0.25, 29.9971 - 0.45],
    [120.5821 + 0.3, 29.9971],
    [120.5821 + 0.1, 29.9971 - 0.1],
  ],
  金华: [
    [119.6474 + 0.6, 29.079 + 0.2],
    [119.6474 + 0.8, 29.079 + 0.15],
    [119.6474 + 0.1, 29.079 - 0.25],
    [119.6474 + 0.35, 29.079 + 0.1],
    [119.6474 + 0.15, 29.079 + 0.35],
    [119.6474, 29.079 + 0.2],
  ],
  衢州: [
    [118.8742, 28.9417],
    [118.9542, 28.8517],
    [119.1742, 28.8217 + 0.2],
    [118.6272, 28.7389],
    [118.5108, 28.9014],
    [118.4158, 29.1378],
  ],
  舟山: [
    [122.2072, 29.9854],
    [122.3072, 29.9654],
    [122.2272, 30.2654],
    [122.4472, 30.7254],
    [122.1072, 29.8854],
    [122.2572, 29.8354],
  ],
  台州: [
    [121.4208, 28.6564],
    [121.2608, 28.6464],
    [121.1439, 28.8528],
    [121.3658, 28.3714],
    [121.0142, 29.1439],
    [120.7308, 28.8472],
  ],
  丽水: [
    [119.9228 - 0.2, 28.4676 - 0.1],
    [119.1342, 27.7422],
    [120.2908 - 0.2, 28.1394 + 0.2],
    [120.0908, 28.6594],
    [119.2756, 28.4928],
    [119.4628, 27.9994],
  ],
}
type City = keyof typeof coords
const getRandomCoord = (city: City) => {
  const res = coords[city][coords[city].length - 1]
  coords[city].pop()
  return res
}

const mapRef = ref<HTMLDivElement | null>(null)
const selectedRegion = ref<string | null>(null)

// 浙江省主要城市及坐标（经纬度，示例数据，可根据需要调整）
const cities = ref<any>([])
const totalPower = ref(0)
const getTotalCompute = async (areaCode: string = '') => {
  const { entity }: any = await totalComputeApi({ areaCode })
  totalPower.value = entity.totalCompute
  // 按照指定顺序排序
  const sortOrder = ['910B', '300I', 'T4', 'A10', 'A40', 'V100']

  const newPowerList: any[] = []
  sortOrder.forEach((type) => {
    const item = entity.modelList.find((item: any) => item.modelName === type)
    if (item) {
      newPowerList.push({
        name: item.modelName,
        total: item.totalCompute,
        allocated: item.allocatedCompute,
        remaining: item.availableCompute,
      })
    } else {
      newPowerList.push({
        name: type,
        total: 0,
        allocated: 0,
        remaining: 0,
      })
    }
  })
  powerList.value = newPowerList
}
const getDevicePhysicalTypeAreaGroup = async () => {
  const { entity }: any = await devicePhysicalTypeAreaGroupApi()
  cities.value = entity
    .map((item: any) =>
      item.modelName.map((ele: string) => ({
        name: ele,
        coord: getRandomCoord(item.areaCode as City),
      })),
    )
    .flat(2)
  setMapOption()
}
getTotalCompute()
getDevicePhysicalTypeAreaGroup()

function setMapOption() {
  if (!chart) return
  chart.setOption({
    geo: {
      map: 'zhejiang',
      roam: false,
      zoom: 1.0,
      label: { show: true, color: '#fff', fontSize: 10, fontWeight: 'bold' },
      itemStyle: {
        areaColor: '#345481', // 设置地图区域为透明
        borderColor: '#fff', // 边框颜色
        borderWidth: 1,
        normal: {
          borderColor: 'rgba(147, 235, 248, 0.6)',
          borderWidth: 0.8,
          areaColor: {
            type: 'linear',
            x: 0,
            y: 300,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: '#2d466b', // 南边深色 // 原方案
                // color: '#3d567b', // 南边深色 // 方案1（稍浅）
                // color: '#4d668b', // 南边深色 // 方案2（中浅）
                // color: '#5d769b', // 南边深色 // 方案3（较浅）
                // color: '#6d86ab', // 南边深色 // 方案4（最浅）
              },
              {
                offset: 0.5,
                color: '#4671af', // 中南部 // 原方案
              },
              {
                offset: 0.8,
                color: '#5d8cc4', // 中北部 // 原方案
              },

              {
                offset: 1,
                color: '#7ba7d9', // 北边浅色 // 原方案
              },
            ],
            global: true, // 基于地图区域坐标
          },
        },
      },
      emphasis: {
        itemStyle: {
          areaColor: '#9fbadd',
        },
        label: {
          color: '#0f203b', // 设置悬停时的字体颜色
        },
      },
      select: {
        itemStyle: {
          areaColor: '#9fbadd', // 选中状态的颜色，与emphasis保持一致
        },
        label: {
          color: '#0f203b', // 选中状态的字体颜色
        },
      },
      zlevel: 2, // 提升 geo 组件的层级
      top: 0,
      left: 25,
      right: 25,
      bottom: 30,
      selectedMode: 'single', // 启用单选模式
    },
    series: [
      // 添加底层阴影层，模拟投影效果
      {
        type: 'map',
        map: 'zhejiang',
        zlevel: 0, // 阴影层在最底层
        itemStyle: {
          areaColor: '#426daf',
          borderWidth: 0,
        },
        silent: true,
        top: 10, // 增加垂直偏移
        left: 26, // 增加水平偏移
        right: 26, // 调整右侧边距以保持阴影效果
        bottom: 20, // 调整底部边距
      },
      {
        type: 'scatter',
        coordinateSystem: 'geo',
        data: cities.value.map((city: any) => ({
          name: city.name,
          value: city.coord,
          symbol: 'image://images/dashboard/scatter.png', // 使用图片作为标记
          symbolSize: [52, 52], // 使用数组形式，分别控制宽度和高度，保持图片比例
          label: {
            show: true,
            formatter: '{cityName|{b}}\n{status|flops}', // 使用富文本格式显示两行
            color: '#fff',
            fontWeight: 'bold',
            fontSize: 14,
            offset: [0, -9],
            textShadowOffsetX: 2,
            textShadowOffsetY: 2,
            rich: {
              cityName: {
                color: '#fff',
                fontSize: 6,
                fontWeight: 'bold',
                lineHeight: 4,
                align: 'center',
              },
              status: {
                color: '#fff',
                fontSize: 6,
                fontWeight: 'normal',
                lineHeight: 4,
                padding: [1, 0, 0, 0],
                align: 'center',
                verticalAlign: 'top',
              },
            },
          },
        })),
        zlevel: 4, // 散点图在最顶层
      },
    ],
  })
}
onMounted(async () => {
  // 地图
  const geojson = await fetch('/geo/zhejiang.json').then((res) => res.json())
  echarts.registerMap('zhejiang', geojson)
  chart = echarts.init(mapRef.value!, 'default', {
    width: 660,
    height: 480,
    renderer: 'svg', // 明确指定使用SVG渲染器
  })
  // 初始化地图
  setMapOption()
  // 添加点击事件监听
  chart.on('click', (params: any) => {
    if (params.componentType === 'geo') {
      const regionName = params.name
      // 如果点击的是当前选中的区域，则取消选中
      if (selectedRegion.value === regionName) {
        selectedRegion.value = null
        chart!.dispatchAction({
          type: 'geoUnSelect',
          name: regionName,
        })
      } else {
        // 选中新区域
        selectedRegion.value = regionName
        chart!.dispatchAction({
          type: 'geoSelect',
          name: regionName,
        })
      }
      getTotalCompute(selectedRegion.value?.replace('市', ''))
      emit('selectChange', selectedRegion.value?.replace('市', ''))
    }
  })
})
</script>

<style scoped>
.card-content {
  padding: 2px 10px 6px 6px;
}
.card-content-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1px;
  .card-content-item-title {
    font-size: 10px;
    color: #858688;
  }
  .card-content-item-value {
    font-size: 10px;
    font-weight: bold;
  }
}
.card-title {
  display: flex;
  width: 200px;
  justify-content: space-between;
  align-items: center;
  background: #e5ebfd;
  border-radius: 8px;
  padding: 2px 6px;
  .left {
    font-size: 12px;
    font-weight: bold;
  }
  .right {
    display: flex;
    align-items: center;
    .left {
      display: flex;
      flex-direction: column;
      .top {
        font-size: 8px;
        font-weight: bold;
        color: #437ae7;
        display: flex;
        justify-content: flex-end;
      }
      .bottom {
        font-size: 6px;
        font-weight: 400;
      }
    }
    .right {
      font-size: 12px;
    }
  }
}
.dashboard-map {
  height: 470px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #b3cfff;
  background: transparent;
}
.dashboard-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
  margin-top: -20px;
  padding: 0 10px;
}
.card {
  flex: 1 1 calc(33.333% - 11px);
  min-width: 200px;
  background: #eaf2ff;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 18px;
  color: #2a6bff;
  box-shadow: 0 1px 4px rgba(42, 107, 255, 0.06);
}
</style>
