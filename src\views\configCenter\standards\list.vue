<template>
  <div class="table-box">
    <sl-page-header
      title="规格"
      title-line="规格(Favor)呈现了多样化资源组合方案、个性化性能调配策略，确保资源适配精准恰当。助力用户搭建适配、高效、经济的资源应用模式，提升整体资源投入产出比。"
      :icon="{
        class: 'page_chanpinguige',
        color: '#0052D9',
        size: '40px',
      }"
    >
    </sl-page-header>

    <div class="btn op" style="margin-top: 8px">
      <el-button @click="openDialog" type="primary"> 新增规格 </el-button>
      <el-button @click="openUploadDialog" type="primary"> 批量导入 </el-button>
      <!--      <el-button @click="handleBatchRecycle" type="primary"> 批量删除 </el-button>-->
    </div>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <el-tabs style="height: 100%" v-model="shares" type="card">
        <el-tab-pane style="height: 100%" label="对公" name="public">
          <dataListPublic ref="dataListRefPublic" :query-params="queryParams"></dataListPublic>
        </el-tab-pane>
        <el-tab-pane style="height: 100%" label="对内" name="private">
          <dataList ref="dataListRef" :query-params="queryParams"></dataList>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 弹窗 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="批量导入"
      width="640px"
      :close-on-click-modal="false"
      @close="close"
    >
      <div class="sl-form-container">
        <div class="sl-form">
          <el-form>
            <div class="group-con bg-fff sl-layout">
              <el-row class="sl-layout-inner">
                <el-col :span="24">
                  <el-form-item
                    label="镜像文件:"
                    :rules="[
                      { required: true, message: '请上传镜像文件', trigger: ['blur', 'change'] },
                    ]"
                    prop="_fileList"
                  >
                    <!--                   -->
                    <div class="upload-box" style="width: 100%">
                      <el-upload
                        ref="uploadRef"
                        :limit="1"
                        :drag="true"
                        accept=".xlsx,.xls"
                        :auto-upload="false"
                        :on-change="handleFileChange"
                        :file-list="fileList"
                        :http-request="handleHttpRequest"
                      >
                        <!-- :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']" -->

                        <div v-if="true" class="el-upload__text">
                          点击上传 <em>/拖拽到此区域 </em>
                        </div>
                      </el-upload>
                      <div class="el-upload__tip">
                        <span style="color: red">请上传xlsx格式文件，大小在 100M 以内</span>
                        <el-button class="ml5" type="primary" @click="handleDownload" link>
                          {{ '模板下载' }}
                        </el-button>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <!--          <el-button type="primary" @click="uploadSubmit">提交</el-button>-->
          <el-button type="primary" @click="handleUpload">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref, onMounted } from 'vue'
import { Delete, Search } from '@element-plus/icons-vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import dataListPublic from './dataListPublic.vue'
import dataList from './dataList.vue'
// import ConditionFilter from '../conditionFilter.vue'
// import { useGlobalDicStore } from '@/stores/modules/dic'
// import { useDichooks } from '../hooks/useDichooks'
// import { normalizeExportArgs } from '@/views/resourceCenter/utils'
// import { useDownload } from '@/hooks/useDownload'
// import { resourceExport } from '@/api/modules/resourecenter'
import { useRouter } from 'vue-router'
import {
  getFlavorResTypesApi,
  getPlatformListApi,
  getRegionsListApi,
  uploadFlavorApi,
} from '@/api/modules/configCenter'
import SlMessage from '@/components/base/SlMessage'
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus'
const router = useRouter()

const formRef = ref<any>(null)
const queryParams = ref<any>(null)

const formModel = reactive({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}
function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}
const shares = ref('public')
// 类型定义
type FileList = UploadFile[]
// 上传组件实例
const uploadRef = ref<UploadInstance>()
// 文件列表
const fileList = ref<FileList>([])
// 文件选择变化处理
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
}
// 手动上传触发
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  uploadRef.value?.submit()
}

// 自定义上传逻辑
const handleHttpRequest = async (options: any) => {
  const { file } = options
  console.log(file)
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('fileType', 'RESOURCE_EXPLAIN')
    uploadFlavorApi(formData).then((res: any) => {
      if (res.code === 200) {
        SlMessage.success('上传成功')
        dataListRef.value?.reloadTableList()
        dataListRefPublic.value?.reloadTableList()
        uploadDialogVisible.value = false
      }
    })
    // 上传成功后从列表中移除
    fileList.value = fileList.value.filter((f) => f.uid !== file.uid)
  } catch (error) {
    ElMessage.error(`${file.name} 上传失败`)
    console.error('上传错误:', error)
  }
}

/**
 * @description 下载文件模板
 * */
const handleDownload = async () => {
  let url: any = window.location.protocol + '//' + window.location.host
  window.location.href = url + '/flavorTemplate.xlsx'
}

// const { resourcePoolsDic } = useDichooks()
// const globalDic = useGlobalDicStore()
// const { getDic } = globalDic
// 是否默认折叠搜索项
const collapsed = ref(false)

// 打开新增页面
const openDialog = () => {
  router.push({
    path: '/standardsAdd',
    query: {},
  })
}
const categoryCodeList = ref<Option[]>([])
// 定义类型/接口
interface Option {
  label: string
  value: string
}
async function init() {
  const res1: any = await getPlatformListApi({}) //查询domincode
  dominCodeList.value = res1.entity

  const res: any = await getFlavorResTypesApi({})

  res.entity.forEach((item: any) => {
    categoryCodeList.value.push({
      label: item.resName,
      // label: item.resType,
      value: item.resType,
    })
  })
}
// 初始化表格数据
onMounted(() => {
  init()
})

const uploadDialogVisible = ref(false)

//打开上传文件弹出
const openUploadDialog = () => {
  uploadDialogVisible.value = true
}

//关闭弹窗
const close = () => {
  uploadDialogVisible.value = false
  fileList.value = []
}
const dominCodeList: any = ref([])
const regionList: any = ref([]) //  region列表

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        labelField: 'domainName',
        valueField: 'domainCode',
        options: dominCodeList,
        labelWidth: '63',
        span: 8,
        hidden: false,
        defaultSelect: true,
        onChange(option: any) {
          getRegionsListApi({
            domainCode: option.domainCode,
            pageSize: 999,
          }).then((res: any) => {
            regionList.value = res.entity.records
          })
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        options: regionList,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '规格名称',
        type: 'input',
        key: 'serviceName',
        span: 8,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '产品分类类型',
        type: 'select',
        key: 'categoryCode',
        options: categoryCodeList,
        span: 8,
        hidden: false,
        defaultSelect: true,
      },
      // {
      //   label: '状态',
      //   type: 'select',
      //   key: 'status',
      //   options: [
      //     {
      //       label: '上线',
      //       value: 1,
      //     },
      //     {
      //       label: '下线',
      //       value: 0,
      //     },
      //   ],
      //   span: 6,
      //   disabled: true,
      //   hidden: false,
      //   defaultSelect: true,
      // },
      // {
      //   label: '',
      //   type: '',
      //   key: 'shared',
      //   options: [
      //     {
      //       label: '公有',
      //       value: 1,
      //     },
      //     {
      //       label: '私有',
      //       value: 0,
      //     },
      //   ],
      //   span: 8,
      //   disabled: false,
      //   hidden: true,
      //   defaultSelect: true,
      // },
      // {
      //   span:8,
      //   hidden: true,
      // },
      {
        span: 8,
        render() {
          return <div style="display: flex;justify-content: flex-end;"></div>
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])

const dataListRef = ref()
const dataListRefPublic = ref()
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
  background: #ffffff;
  padding: 10px;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
