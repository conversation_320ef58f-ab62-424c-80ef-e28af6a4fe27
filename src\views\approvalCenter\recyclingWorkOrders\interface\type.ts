import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import type { GetResourceListResType } from '../../workOrder/interface/type'

/**
 * @name 资源列表-回收工单
 */
export interface RecyclingWorkorderType {
  [key: string]: any
  activityTask: GetResourceListResType | undefined
}

type TaskType = 'user_task' | 'business_depart_leader2' | 'operation_group' | 'tenant_task' | 'end'
/**
 *  @description 权限按钮类型
 */
export type RecyclingBtnsType = {
  [key in TaskType]: boolean
}

export type RecyclingType =
  | 'ecs'
  | 'gcs'
  | 'evs'
  | 'obs'
  | 'slb'
  | 'nat'
  | 'nas'
  | 'vpn'
  | 'vpc'
  | 'network'
  | 'eip'
  | 'mysql'
  | 'redis'
  | 'backup'
  | 'pm'
  | 'kafka'
  | 'flink'
  | 'es'
  | 'bldRedis'

export type RecyclingColumnsType<T = any> = {
  [key in RecyclingType]: ColumnProps<T>[]
}

export type RecyclingTabsType = {
  label: string
  name: RecyclingType
  count: number
  list: FormDataType[]
} & FormDataType

export type RecyclingProTableType = {
  [key in RecyclingType]: ProTableInstance | null
}
