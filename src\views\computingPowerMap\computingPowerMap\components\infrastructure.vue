<template>
  <section class="panel power-panel" :class="{ active: props.isActive }">
    <div class="card-content">
      <Title title="基础设施" />
      <div class="infrastructure">
        <div>
          <div ref="pieChartRef3" style="width: 165px; height: 125px; margin-top: 15px"></div>
        </div>
        <div class="infrastructureRight">
          <div class="infrastructureRightRow">
            <span class="item1"></span>
            <span class="item2">硬件设备</span>
            <span class="item3">占比</span>
          </div>
          <div class="infrastructureRightRow infrastructureRightRow2">
            <span class="item1">
              <i class="bg34B3E0Grid"></i>
              工程态
            </span>
            <span class="item2">{{ engineInfo.baseDeviceNum }}</span>
            <span class="item3">{{ engineInfo.rate }}</span>
          </div>
          <div class="infrastructureRightRow infrastructureRightRow2">
            <span class="item1">
              <i class="bg9BD8B4Grid"></i>
              在网态
            </span>
            <span class="item2">{{ networkInfo.baseDeviceNum }}</span>
            <span class="item3">{{ networkInfo.rate }}</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeMount, watch } from 'vue'
import Title from './Title.vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import 'echarts-gl' // 引入 3D 扩展

import { getComPowerMapStatsBaseDevice } from '@/api/modules/comPowerCenter'

const props = defineProps({
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  latest: {
    type: Object,
    default: () => ({
      vcpuTotal: 0,
    }),
  },
})

const engineInfo = ref({
  baseDeviceNum: '',
  rate: '',
})
const networkInfo = ref({
  baseDeviceNum: '',
  rate: '',
})

// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)
const getDataInfo = () => {
  getComPowerMapStatsBaseDevice({
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
    cityCode: props.requestParams.cityCode,
  }).then((res: any) => {
    if (res.code === 200) {
      networkInfo.value.baseDeviceNum = res.entity[0].baseDeviceNum
      networkInfo.value.rate = parseFloat((res.entity[0].rate * 100).toFixed(2)) + '%'
      optionsData[1].value = res.entity[0].baseDeviceNum
      engineInfo.value.baseDeviceNum = res.entity[1].baseDeviceNum
      engineInfo.value.rate = parseFloat((res.entity[1].rate * 100).toFixed(2)) + '%'
      optionsData[0].value = res.entity[1].baseDeviceNum
      setOption()
    }
  })
}
const optionsData = [
  {
    name: '工程态',
    value: 0,
    itemStyle: {
      color: '#34c7fe',
    },
  },
  {
    name: '在网态',
    value: 0,
    itemStyle: {
      color: '#69d9ba',
    },
  },
]
// 原始数值堆叠柱状图数据
onMounted(() => {
  getDataInfo()
  memLine1 = echarts.init(pieChartRef3.value!, null, {
    width: 150,
    height: 120,
    // renderer: 'svg', // 明确指定使用SVG渲染器
  })
})

onBeforeMount(() => {
  // barChart?.dispose()
})

function setOption() {
  if (!memLine1) return
  // 给最小值和最大值留一些边距
  let option1 = getPie3D(optionsData, 0.8)
  memLine1.setOption(option1)
}
const pieChartRef3 = ref<HTMLDivElement | null>(null)

let memLine1: ECharts | null = null

// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
function getParametricEquation(
  startRatio: any,
  endRatio: any,
  isSelected: any,
  isHovered: any,
  k: any,
  h: any,
) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2

  let startRadian = startRatio * Math.PI * 2
  let endRadian = endRatio * Math.PI * 2
  let midRadian = midRatio * Math.PI * 2

  // 如果只有一个扇形，则不实现选中效果。
  // if (startRatio === 0 && endRatio === 1) {
  //     isSelected = false;
  // }
  isSelected = false
  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.sin(midRadian) * 0.1 : 0
  let offsetY = isSelected ? Math.cos(midRadian) * 0.1 : 0

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    y: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    z: function (u: any, v: any) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
    },
  }
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData: any, internalDiameterRatio: any) {
  let series = []
  let sumValue = 0
  let startValue = 0
  let endValue = 0
  let legendData = []
  let k =
    typeof internalDiameterRatio !== 'undefined'
      ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
      : 1 / 3

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value
    type ChartConfig = {
      name: string
      type: string
      parametric: boolean
      wireframe: { show: boolean }
      pieData?: any // 改为可选
      pieStatus?: { selected: boolean; hovered: boolean; k: number }
      itemStyle: null | { opacity: number; color: string } // 合并 itemStyle
      parametricEquation?: {
        u: { min: number; max: number; step: number }
        v: { min: number; max: number; step: number }
        x: any
        y: any
        z: any
      }
    }
    let seriesItem: ChartConfig = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: 1 / 10,
      },
      itemStyle: null,
    }

    if (typeof pieData[i].itemStyle != 'undefined') {
      interface Style {
        color: any | null
        opacity: any | null
      }
      let itemStyle: Style = {
        color: null,
        opacity: null,
      }

      typeof pieData[i].itemStyle.color != 'undefined'
        ? (itemStyle.color = pieData[i].itemStyle.color)
        : null
      typeof pieData[i].itemStyle.opacity != 'undefined'
        ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
        : null

      seriesItem.itemStyle = itemStyle
    }
    series.push(seriesItem)
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value

    series[i].pieData.startRatio = startValue / sumValue
    series[i].pieData.endRatio = endValue / sumValue
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value,
    )

    startValue = endValue
    legendData.push(series[i].name)
  }

  // // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.1,
      color: '#E1E8EC',
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u: any, v: any) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2
      },
      y: function (u: any, v: any) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2
      },
      z: function (u: any, v: any) {
        return Math.cos(v) > 0 ? -0.5 : -2
      },
    },
  })
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',

    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.1,
      color: '#E1E8EC',
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u: any, v: any) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.2
      },
      y: function (u: any, v: any) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.2
      },
      z: function (u: any, v: any) {
        return Math.cos(v) > 0 ? -2 : -3
      },
    },
  })
  let option = {
    tooltip: {
      /** 保证 tooltip一直在图表内部 */
      position: function (point: any, params: any, dom: any, rect: any, size: any) {
        // size 中有两个属性：viewSize 和 contentSize，分别为 tooltip 的宽高
        // 这里设置的值都是为了保证 tooltip 内部显示
        let x = point[0]
        let y = point[1]
        let viewWidth = size.viewSize[0]
        let viewHeight = size.viewSize[1]
        let boxWidth = size.contentSize[0]
        let boxHeight = size.contentSize[1]

        // 判断 tooltip 位置，调整其位置使其不会超出图表边界
        if (x + boxWidth > viewWidth) {
          x = x - boxWidth
        }
        if (y + boxHeight > viewHeight) {
          y = y - boxHeight
        }

        // 保证 tooltip 始终在图表内部
        if (x < 0) {
          x = 0
        }
        if (y < 0) {
          y = 0
        }

        return [x, y]
      },
      formatter: (params: any) => {
        if (params.seriesName !== 'mouseoutSeries') {
          return `
               ${params.seriesName}<br/>
                  <span style="display:inline-block;
                  margin-right:5px;
                  border-radius:10px;
                  width:10px;
                  height:10px;
                  background-color:${params.color};"></span>
                  ${option.series[params.seriesIndex].pieData.value}
                  `
        }
        return ''
      },
    },
    xAxis3D: {},
    yAxis3D: {},
    zAxis3D: {},
    grid3D: {
      viewControl: {
        autoRotate: false,
        alpha: 30,
        beta: 50,
        distance: 110,
        rotateSensitivity: 0, //设置是否可以旋转
        zoomSensitivity: 0, //设置是否可以缩放
        panSensitivity: 0, //设置是否可以平移
      },
      left: 'center',
      width: '95%',
      show: false,
      boxHeight: 30,
    },
    series: series,
  }
  return option
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.panel {
  position: relative;
}

.power-panel {
  cursor: pointer;
  height: 201px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(42, 107, 255, 0.08);
  padding: 2px;
  position: relative;
  //background-color: #fff;
  background: url('/images/computingPower/comPowerRight4.png') no-repeat center center;
  background-size: 100% 100%;
}
.power-panel.active {
  background: url('/images/computingPower/comPowerRight4Active.png') no-repeat center center;
  background-size: 100% 100%;
}

.card-content {
  padding: 0 20px;
  height: 100%;
}

.bg34B3E0Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #34b3e0;
  margin-right: 5px;
}

.bg9BD8B4Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #9bd8b4;
  margin-right: 5px;
}

.infrastructure {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .infrastructureRight {
    width: 60%;
    .infrastructureRightRow {
      padding: 12px 0;
      &.infrastructureRightRow2 {
        border-bottom: 1px solid #dee8fb;
      }
      span {
        display: inline-block;
        width: 35%;
        font-size: 16px;
        padding: 0 6px;
        box-sizing: border-box;
      }
      span.item1 {
        font-size: 15px;
      }
      span.item2 {
        color: #004fb1;
        text-align: right;
      }
      span.item3 {
        width: 25%;
        color: #004fb1;
      }
    }
  }
}
</style>
