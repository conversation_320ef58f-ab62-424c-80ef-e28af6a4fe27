<template>
  <div class="subnet-container">
    <!-- 子网项 -->
    <div v-for="(subnet, index) in subnets" :key="index" class="subnet-item">
      <div class="subnet-form">
        <!-- 名称字段 -->
        <div class="form-row">
          <label class="form-label">名称</label>
          <div class="input-wrapper">{{ subnet.subnetName }}</div>
        </div>

        <!-- 网段字段 -->
        <div class="form-row">
          <label class="form-label">网段</label>
          <div class="ip-input-group">
            {{ subnet.startIp + ' / ' + subnet.netmask }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Subnet {
  subnetName: string
  startIp: string
  netmask: string
}

// Props
defineProps<{
  subnets: Subnet[]
}>()
</script>

<style scoped>
.subnet-container {
  width: 100%;
}

.subnet-item {
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  position: relative;
}

.subnet-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-label {
  font-weight: 500;
  color: #333;
  min-width: 60px;
  text-align: right;
}

.input-wrapper {
  flex: 1;
  max-width: 400px;
}

.name-input {
  width: 100%;
}

.ip-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.ip-segment {
  width: 60px;
}

.ip-segment :deep(.el-input__inner) {
  text-align: center;
}

.separator {
  font-weight: 500;
  color: #606266;
  font-size: 16px;
}

.cidr-select {
  width: 80px;
}

.delete-btn-wrapper {
  position: absolute;
  top: 12px;
  right: 12px;
}

.delete-btn {
  font-size: 12px;
  padding: 4px 8px;
}

.add-section {
  display: flex;
  justify-content: flex-start;
}

.add-btn {
  font-size: 14px;
  font-weight: 500;
}

.add-btn :deep(.el-icon) {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .form-label {
    min-width: auto;
    text-align: left;
  }

  .ip-input-group {
    flex-wrap: wrap;
  }
}
</style>
