<template>
  <div class="form-container">
    <sl-form size="small" show-block-title :options="formOptions">
      <!-- 添加按钮 -->
      <template #addBtnSlot="{ scope }">
        <el-button @click="addGoods" style="margin-right: 16px" :icon="Plus" plain type="primary">
          {{ scope.text }}
        </el-button>
      </template>
      <!-- 配置信息 -->
      <template #goodsInfoSlot>
        <div style="padding-top: 6px">
          <template v-for="(goods, goodsIndex) in goodsList" :key="goods">
            <goodsItem
              :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
              :goods="goods"
            ></goodsItem>
          </template>
        </div>
      </template>
    </sl-form>
  </div>
</template>
<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import type { IShareEvsModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import slForm from '@/components/form/SlForm.vue'
import { reactive } from 'vue'
import eventBus from '@/utils/eventBus'
import goodsItem from './goodsItem.vue'
import { useRoute } from 'vue-router'

const rouete = useRoute()

defineProps<{
  goodsList: IGoodsItem<IShareEvsModel>[]
}>()

function addGoods() {
  eventBus.emit('shoppingCarts:addGoods', {
    goodsType: 'shareEvs',
    isEdit: rouete.query.orderId ? true : false,
  })
}
const formOptions = reactive([
  {
    blockTitleFilter: {
      slot: 'addBtnSlot',
      text: '增加共享数据盘',
    },
    blockTitleStyle: 'position: sticky;top:0;z-index:99;background: #fff;',
    gutter: 0,
    groupName: '配置信息',
    groupItems: [
      {
        span: 24,
        noFormItem: true,
        type: 'slot',
        slotName: 'goodsInfoSlot',
      },
    ],
  },
])
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 468px);
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}
.form-container {
  display: flex;
  flex-direction: column;
}
</style>
