<template>
  <SlDialog
    id="ResourceChangeDialog"
    v-model="dialogVisible"
    :title="isDelay ? '资源延期' : '资源变更'"
    :width="dialogWidth"
    destroy-on-close
    @close="handleClose"
    @confirm="handleConfirm"
  >
    <template v-if="renderDialogContent">
      <SlProTable
        ref="proTable"
        :columns="columns"
        :data="tableData"
        style="min-height: 300px"
        hidden-table-header
        :pagination="false"
        v-if="!isDelay"
      >
      </SlProTable>
      <div v-if="isDelay" class="delay-time-container">
        <span class="label">延期时间：</span>
        <el-select v-model="delayTimeValue" placeholder="请选择延期时间">
          <el-option
            v-for="item in delayTimeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </template>
  </SlDialog>
</template>

<script lang="tsx" setup>
import { ref, computed, watch } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import type { ResourceType, ResourceChangeType } from '../types'
import { useResourceChange, type ResourceProperty } from '../hooks/useResourceChange'
import { ElMessage } from 'element-plus'
import { useGlobalDicStore } from '@/stores/modules/dic'
import eventBus from '@/utils/eventBus'

const props = defineProps<{
  visible: boolean
  resourceType: ResourceType
  selectedResources: any[]
  isDelay?: boolean
  allowedChangeTypes?: ResourceChangeType[]
  displayFields?: string[]
  dialogWidth?: string
}>()

const emit = defineEmits(['update:visible', 'confirm'])

const { updateResourceChange } = useResourceChange()

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

// 动态设置对话框宽度
const dialogWidth = computed(() => {
  if (props.dialogWidth) {
    return props.dialogWidth
  }
  if (props.isDelay) {
    return '600px'
  }
  // 根据显示列数动态设置宽度
  const columnsCount = getDisplayColumns().length + 2 // +2 为序号列和操作列
  if (columnsCount > 6) {
    return '1200px'
  } else if (columnsCount > 4) {
    return '1000px'
  }
  return '800px'
})

const tableData = ref<any[]>([])

const globalDic = useGlobalDicStore()
const { getDic } = globalDic
// 延期时间选项
const delayTimeOptions = getDic('time')

const delayTimeValue = ref('')

// 字段标签映射
const fieldLabelMap: Record<string, string> = {
  deviceName: '主机名称',
  resourceType: '资源类型',
  spec: '实例规格',
  sysDisk: '系统盘',
  dataDisk: '数据盘',
  mountOrNot: '是否挂载云主机',
  ecsName: '云主机',
  storeType: '存储类型',
  capacity: '容量',
  objectStorageName: '对象存储名称',
  loadBalancerName: '负载均衡名称',
  eip: '弹性公网IP',
  eipName: '弹性公网名称',
  eipAddress: '弹性公网地址',
  bandwidth: '带宽',
  ip: 'IP',
}

// 资源类型映射
const resourceTypeMap: Record<string, string> = {
  ecs: '云主机',
  gcs: 'GPU云主机',
  evs: '云硬盘',
  obs: '对象存储',
  slb: '负载均衡',
  eip: '弹性公网',
  nat: 'NAT',
  mysql: '云数据库',
  redis: '通用Redis',
}

// 变更类型标签映射
const changeTypeMap: Record<ResourceChangeType, string> = {
  instance_spec_change: '实例规格',
  storage_expand: '存储扩容',
  bandwidth_expand: '带宽扩容',
  delay: '资源延期',
}

// 检查资源是否有数据盘
const hasDataDisk = (resource: any): boolean => {
  // 对于云主机和GPU云主机
  if (['ecs', 'gcs', 'mysql', 'redis', 'evs'].includes(props.resourceType)) {
    return !!resource.dataDisk && resource.dataDisk !== '--' && resource.dataDisk !== ''
  }
  // 对于对象存储
  else if (props.resourceType === 'obs') {
    return true // 对象存储始终可以进行存储扩容
  }

  return false
}

// 检查资源是否有弹性公网IP
const hasEip = (resource: any): boolean => {
  if (['ecs', 'gcs', 'mysql', 'redis', 'slb'].includes(props.resourceType)) {
    return !!resource.bandWidth && resource.bandWidth !== '--' && resource.bandWidth !== ''
  }
  // 对于弹性公网IP类型
  else if (props.resourceType === 'eip') {
    return true // 弹性公网IP本身就是带宽资源
  }

  return false
}

// 获取资源类型允许的最大变更选择数
const getMaxChangeCount = (type: ResourceType): number => {
  if (['ecs', 'gcs', 'mysql', 'redis', 'evs'].includes(type)) {
    return 3 // 实例规格、存储扩容、带宽扩容
  } else if (type === 'slb') {
    return 2 // 实例规格、带宽扩容
  }
  return 1
}

// 判断是否使用多选
const isMultipleSelect = computed(() => {
  return getMaxChangeCount(props.resourceType) > 1
})

const getFieldLabel = (field: string) => {
  return fieldLabelMap[field] || field
}

const getChangeTypeLabel = (type: ResourceChangeType) => {
  return changeTypeMap[type] || type
}

// 获取资源类型名称
const getResourceTypeName = (type: string) => {
  return resourceTypeMap[type] || type
}

// 获取不同资源类型的展示列
const getDisplayColumns = () => {
  switch (props.resourceType) {
    case 'ecs':
    case 'gcs':
    case 'mysql':
    case 'redis':
      return ['deviceName', 'resourceType', 'spec', 'dataDisk', 'eip']
    case 'evs':
      return ['dataDisk', 'resourceType', 'mountOrNot', 'ecsName']
    case 'obs':
      return ['deviceName', 'resourceType', 'storeType', 'capacity']
    case 'slb':
      return ['deviceName', 'resourceType', 'spec', 'eip']
    case 'eip':
      return ['deviceName', 'resourceType', 'eip']
    default:
      return props.displayFields || []
  }
}

// 获取变更前的值
const getBeforeValue = (row: any, changeType: ResourceChangeType): string | any[] => {
  switch (changeType) {
    case 'instance_spec_change':
      return row.spec || ''
    case 'storage_expand':
      if (['ecs', 'gcs', 'mysql', 'redis', 'evs'].includes(props.resourceType)) {
        const { dataDisk, volumeId } = row
        const dataDiskList = dataDisk?.split(',') || []
        const volumnIdList = volumeId?.split(',') || []
        return dataDiskList.map((item: any, index: number) => {
          return [
            item.split(' ')[0],
            Number(item.split(' ')[1].split('GB')[0]),
            volumnIdList[index],
          ]
        })
      } else if (props.resourceType === 'obs') {
        // 对象存储特殊处理，合并类型和容量
        return [[row.storeType, Number(row.capacity?.split('GB')[0])]]
      }
      return []
    case 'bandwidth_expand':
      return row.bandWidth || ''
    case 'delay':
      return row.expireTime || ''
    default:
      return ''
  }
}

// 格式化弹性公网IP显示
const getEipName = (resource: any) => {
  return `${resource.eip || '--'}\n${resource.bandWidth ? resource.bandWidth : '--'}`
}

// 生成动态列
const generateColumns = () => {
  const displayColumns = getDisplayColumns()

  const dynamicColumns: ColumnProps<any>[] = displayColumns.map((field) => {
    // 对于deviceName字段，使用动态表头
    if (field === 'deviceName') {
      return {
        prop: field,
        label: getDeviceNameLabel(props.resourceType),
        minWidth: 120,
      }
    }
    return {
      prop: field,
      label: getFieldLabel(field),
      minWidth: 100,
    }
  })

  // 添加变更类型列（如果不是延期）
  if (!props.isDelay) {
    dynamicColumns.push({
      prop: 'changeType',
      label: '变更类型',
      minWidth: 200,
      render: ({ row }: { row: any }) => {
        if (isMultipleSelect.value) {
          // 多选模式
          return (
            <el-select
              model-value={row.changeType}
              onUpdate:modelValue={(val: any) => handleChangeTypeChange(val, row)}
              placeholder="请选择变更类型"
              multiple
              collapse-tags
              collapse-tags-tooltip
            >
              {(props.allowedChangeTypes || []).map((type) => {
                // 不存在数据盘时，存储扩容选项置灰
                const isDisabled =
                  (type === 'storage_expand' && !hasDataDisk(row)) ||
                  (type === 'bandwidth_expand' && !hasEip(row))

                return (
                  <el-option
                    key={type}
                    label={getChangeTypeLabel(type)}
                    value={type}
                    disabled={isDisabled}
                  />
                )
              })}
            </el-select>
          )
        } else {
          // 单选模式
          return (
            <el-select
              model-value={row.changeType[0]}
              onUpdate:modelValue={(val: any) => handleChangeTypeChange([val], row)}
              placeholder="请选择变更类型"
            >
              {(props.allowedChangeTypes || []).map((type) => {
                // 不存在数据盘时，存储扩容选项置灰
                const isDisabled =
                  (type === 'storage_expand' && !hasDataDisk(row)) ||
                  (type === 'bandwidth_expand' && !hasEip(row))

                return (
                  <el-option
                    key={type}
                    label={getChangeTypeLabel(type)}
                    value={type}
                    disabled={isDisabled}
                  />
                )
              })}
            </el-select>
          )
        }
      },
    })
  }

  return [
    { type: 'index', label: '序号', width: 60 },
    ...dynamicColumns,
    {
      prop: 'operation',
      label: '操作',
      width: 80,
      render: ({ row }: { row: any }) => {
        return (
          <el-button type="danger" link onClick={() => handleRemove(row)}>
            移除
          </el-button>
        )
      },
    },
  ]
}

const columns = computed(() => generateColumns() as ColumnProps<any>[])

// 关闭弹窗
const handleClose = () => {
  tableData.value = []
  delayTimeValue.value = ''
}

// 确认变更
const handleConfirm = async () => {
  try {
    if (tableData.value.length === 0) {
      ElMessage.warning('请至少选择一个资源')
      return false
    }

    // 检查延期时间是否选择
    if (props.isDelay) {
      if (!delayTimeValue.value) {
        ElMessage.warning('请选择延期时间')
        return false
      }
    } else {
      // 检查是否都选择了变更类型
      for (const item of tableData.value) {
        if (!item.changeType || item.changeType.length === 0) {
          ElMessage.warning('请为所有资源选择变更类型')
          return false
        }
      }
    }

    // 构建变更数据
    const processedData = tableData.value.map((item) => {
      const result = {} as ResourceProperty

      // 确保resourceDetailId正确设置
      result.resourceDetailId = item.id
      result.deviceName = item.deviceName
      result.resourceType = props.resourceType
      result.resourceTypeName = resourceTypeMap[props.resourceType]
      result.cloudPlatform = item.cloudPlatform
      result.resourcePoolName = item.resourcePoolName
      result.regionId = item.resourcePoolId
      result.domainCode = item.domainCode
      result.azId = item.azId
      result.deviceId = item.deviceId
      result.props = []
      if (props.isDelay) {
        // 处理延期数据
        result.props.push({
          resourceDetailId: item.id,
          resourceType: props.resourceType,
          changeType: 'delay',
          before: item.expireTime,
          after: delayTimeValue.value,
          eipId: item.eipId,
        })
      } else {
        // 处理变更数据（可能是单选或多选）
        item.changeType.forEach((changeType: ResourceChangeType) => {
          result.props.push({
            resourceDetailId: item.id,
            resourceType: props.resourceType,
            changeType: changeType,
            before: getBeforeValue(item, changeType),
            after: changeType === 'storage_expand' ? [] : '',
            eipId: item.eipId,
          })
        })
      }
      return result
    })
    // 更新资源变更
    await updateResourceChange(
      props.resourceType,
      processedData,
      props.selectedResources[0].businessSysId,
    )

    // 显示成功提示
    ElMessage.success(props.isDelay ? '延期资源已加入到变更区' : '变更资源已加入到变更区')

    // 通知父组件操作完成
    emit('confirm')

    // 关闭弹窗
    emit('update:visible', false)
    eventBus.emit('propertyChange:updateCount')

    // 清空表格数据
    tableData.value = []
    delayTimeValue.value = ''

    return true
  } catch (error) {
    console.error('确认变更失败:', error)
    return false
  }
}

// 根据资源类型获取设备名称的表头
const getDeviceNameLabel = (resourceType: ResourceType): string => {
  switch (resourceType) {
    case 'ecs':
    case 'gcs':
      return '主机名称'
    case 'mysql':
      return '云数据库'
    case 'redis':
      return '通用Redis'
    case 'obs':
      return '对象存储名称'
    case 'slb':
      return '负载均衡名称'
    case 'eip':
      return '弹性公网名称'
    case 'nat':
      return '网关名称'
    default:
      return '设备名称'
  }
}

// 优化弹窗性能，改为仅在显示时加载内容
const renderDialogContent = computed(() => {
  if (!dialogVisible.value) return false
  return true
})

// 初始化表格数据性能优化
const initTableData = () => {
  // 如果是延期，不需要太多数据处理
  if (props.isDelay) {
    tableData.value = props.selectedResources.map((resource) => ({
      ...resource,
      resourceType: getResourceTypeName(props.resourceType),
      // 确保resourceDetailId正确设置
      resourceDetailId: resource.id,
    }))
    return
  }

  // 提前计算可能会多次用到的值
  const isMulti = isMultipleSelect.value
  const resourceTypeName = getResourceTypeName(props.resourceType)

  // 批量处理选中的资源
  tableData.value = props.selectedResources.map((resource) => {
    // 根据是否是单选模式设置默认选中项
    let defaultChangeType: ResourceChangeType[] = []

    if (!isMulti && props.allowedChangeTypes && props.allowedChangeTypes.length > 0) {
      // 性能优化：对于单选模式，找到第一个可用的选项作为默认值
      for (const type of props.allowedChangeTypes) {
        if (
          (type !== 'storage_expand' || hasDataDisk(resource)) &&
          (type !== 'bandwidth_expand' || hasEip(resource))
        ) {
          defaultChangeType = [type]
          break
        }
      }
    }

    // 返回处理后的资源
    return {
      ...resource,
      changeType: defaultChangeType,
      resourceType: resourceTypeName,
      eip: getEipName(resource),
      // 确保resourceDetailId正确设置
      resourceDetailId: resource.id,
    }
  })
}

// 处理移除行
const handleRemove = (row: any) => {
  const index = tableData.value.findIndex((item) => item === row)
  if (index !== -1) {
    tableData.value.splice(index, 1)
  }
}

// 处理更新变更类型
const handleChangeTypeChange = (val: ResourceChangeType[], row: any) => {
  if (isMultipleSelect.value) {
    const maxCount = getMaxChangeCount(props.resourceType)
    if (val.length > maxCount) {
      ElMessage.warning(
        `${getResourceTypeName(props.resourceType)}最多支持${maxCount}种变更类型同时选择`,
      )
      // 保留最新选择的maxCount个选项
      row.changeType = val.slice(-maxCount)
    } else {
      row.changeType = val
    }
  } else {
    // 单选模式
    row.changeType = val
  }
}

// 初始化表格数据
initTableData()

// 监视弹窗可见性变化，用于初始化数据
watch(
  () => props.visible,
  (val) => {
    if (val) {
      // 显示弹窗时初始化数据
      initTableData()
      // 重置延期时间
      delayTimeValue.value = ''
    }
  },
)

// 暴露方法给父组件
defineExpose({
  // 不再需要暴露openDialog
})
</script>

<style lang="scss">
#ResourceChangeDialog {
  .el-tooltip {
    white-space: pre;
  }
  .el-dialog__body {
    max-height: 80vh;
    overflow: auto;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  .delay-time-container {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    .label {
      margin-right: 10px;
      font-weight: bold;
    }

    .el-select {
      width: 200px;
    }
  }
}
</style>
