<template>
  <div class="account-manager-view-breadcrumb">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>运营中心</el-breadcrumb-item>
      <el-breadcrumb-item
        v-if="route.path === '/accountManagerView' || route.path === '/accountDetail'"
        :to="{ path: '/accountManagerView' }"
        :class="{ active: route.path === '/accountManagerView' }"
      >
        客户经理视图
      </el-breadcrumb-item>
      <el-breadcrumb-item
        v-if="route.path === '/accountDetail'"
        :class="{ active: route.path === '/accountDetail' }"
      >
        客户详情
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>
<script lang="ts" setup>
import { useRoute } from 'vue-router'

const route = useRoute()
</script>

<style scoped>
.el-breadcrumb {
  font-size: 12px;
}
/* 更具体地选择面包屑内部元素 */
.account-manager-view-breadcrumb :deep(.el-breadcrumb__inner) {
  color: var(--el-text-color-regular) !important;
  font-weight: normal !important;
}
.account-manager-view-breadcrumb .active :deep(.el-breadcrumb__inner) {
  color: var(--el-text-color-primary) !important;
  font-weight: bold !important;
}
</style>
