<template>
  <div id="shopping-carts" class="table-box">
    <sl-page-header
      :title="isEdit ? '编辑' : '购物车'"
      :icon="{
        Vnode: isEdit ? Edit : ShoppingCart,
        color: '#0052D9',
        size: '30px',
      }"
    ></sl-page-header>
    <el-scrollbar wrap-class="shopping-carts-scroll-view" class="scroll-view">
      <div v-if="catrsModel.baseList.length" style="background: #fff; overflow: hidden">
        <sl-form
          :key="catrsModel.baseList[0].id"
          ref="slFormRef"
          show-block-title
          :options="formOptions"
          :model-value="catrsModel.baseList[0].orderJson"
          size="small"
        >
          <template #busiSystemIdSuffixSlot>
            <el-icon @click="addBusiSystem" class="add-busisystem-btn">
              <CirclePlusFilled />
            </el-icon>
          </template>
        </sl-form>
      </div>
      <sl-tabs show-count :tabs="tabs" v-model="activeTab" @update:modelValue="tabClick"> </sl-tabs>
      <ecs-tab
        :goods-list="catrsModel.ecsList"
        class="goods-tab"
        v-show="activeTab === 'ecs'"
      ></ecs-tab>
      <nat-tab
        :goods-list="catrsModel.natList"
        class="goods-tab"
        v-show="activeTab === 'nat'"
      ></nat-tab>
      <evs-tab
        :goods-list="catrsModel.evsList"
        class="goods-tab"
        v-show="activeTab === 'evs'"
      ></evs-tab>
      <eip-tab
        :goods-list="catrsModel.eipList"
        class="goods-tab"
        v-show="activeTab === 'eip'"
      ></eip-tab>
      <gcs-tab
        :goods-list="catrsModel.gcsList"
        class="goods-tab"
        v-show="activeTab === 'gcs'"
      ></gcs-tab>
      <obs-tab
        :goods-list="catrsModel.obsList"
        class="goods-tab"
        v-show="activeTab === 'obs'"
      ></obs-tab>
      <slb-tab
        :goods-list="catrsModel.slbList"
        class="goods-tab"
        v-show="activeTab === 'slb'"
      ></slb-tab>
      <cq-tab
        :goods-list="catrsModel.cqList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'cq'"
      ></cq-tab>
      <mysql-tab
        :goods-list="catrsModel.mysqlList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'mysql'"
      ></mysql-tab>
      <redis-tab
        :goods-list="catrsModel.redisList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'redis'"
      ></redis-tab>
      <backup-tab
        :goods-list="catrsModel.backupList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'backup'"
      ></backup-tab>
      <nas-tab
        :goods-list="catrsModel.nasList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'nas'"
      ></nas-tab>
      <vpn-tab
        :goods-list="catrsModel.vpnList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'vpn'"
      ></vpn-tab>
      <kafka-tab
        :goods-list="catrsModel.kafkaList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'kafka'"
      ></kafka-tab>
      <flink-tab
        :goods-list="catrsModel.flinkList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'flink'"
      ></flink-tab>
      <pm-tab
        :goods-list="catrsModel.pmList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'pm'"
      ></pm-tab>
      <es-tab
        :goods-list="catrsModel.esList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'es'"
      ></es-tab>
      <bld-redis-tab
        :goods-list="catrsModel.bldRedisList"
        :base-info="catrsModel.baseList"
        class="goods-tab"
        v-show="activeTab === 'bldRedis'"
      ></bld-redis-tab>
    </el-scrollbar>
    <div class="footer">
      <el-upload
        :http-request="handleFile"
        style="margin: 0 12px"
        :show-file-list="false"
        :limit="3"
        accept=".xlsx,.xls"
        ref="uploadRef"
      >
      </el-upload>
      <div class="button-group">
        <el-button @click="cancel">取消</el-button>
        <el-button-group style="margin: 0 12px" class="ml-4">
          <el-button @click="openFileDialog" type="primary">导入</el-button>
          <el-button
            @click="downloadTemplate"
            type="primary"
            :icon="Download"
            style="width: 10px"
          />
        </el-button-group>
        <template v-if="isEdit">
          <sl-button :api-function="submit" :style="{ width: '100px' }" type="primary">
            确定修改
          </sl-button>
        </template>
        <template v-else>
          <el-button
            v-permission="'Storage'"
            :style="{ width: '60px' }"
            @click="saveDraft"
            type="primary"
          >
            暂存
          </el-button>
          <sl-button
            v-if="!isEdit"
            :style="{ width: '60px' }"
            :api-function="submitDraftOrder"
            type="primary"
          >
            草稿
          </sl-button>
          <sl-button
            v-permission="'Submit'"
            :api-function="submit"
            :style="{ width: '100px' }"
            type="primary"
          >
            提交申请
          </sl-button>
        </template>
      </div>
    </div>
    <el-drawer
      v-model="showBusinessSystemCreated"
      :with-header="false"
      :before-close="handleBusinessSystemCreatedClose"
      size="600"
      destroy-on-close
    >
      <BusinessSystemCreated
        @submit="handleBusinessSystemSubmit"
        @close="handleBusinessSystemCreatedClose"
      ></BusinessSystemCreated>
    </el-drawer>
  </div>
</template>

<script setup lang="tsx" name="workOrder">
import { ref, reactive, computed } from 'vue'
import ecsTab from './tabs/ecs/ecs.vue'
import evsTab from './tabs/evs/evs.vue'
import obsTab from './tabs/obs/obs.vue'
import slbTab from './tabs/slb/slb.vue'
import gcsTab from './tabs/gcs/gcs.vue'
import natTab from './tabs/nat/nat.vue'
import eipTab from './tabs/eip/eip.vue'
import mysqlTab from './tabs/mysql/mysql.vue'
import redisTab from './tabs/redis/redis.vue'
import cqTab from './tabs/cq/cq.vue'
import backupTab from './tabs/backup/backup.vue'
import nasTab from './tabs/nas/nas.vue'
import vpnTab from './tabs/vpn/vpn.vue'
import kafkaTab from './tabs/kafka/kafka.vue'
import flinkTab from './tabs/flink/flink.vue'
import bldRedisTab from './tabs/bldRedis/bldRedis.vue'
import pmTab from './tabs/pm/pm.vue'
import esTab from './tabs/es/es.vue'
import { useRoute, useRouter } from 'vue-router'
import SlForm from '@/components/form/SlForm.vue'
import { useOrderFormOptions } from '@/views/resourceCenter/hooks/useOrderFormOptions'
import { ShoppingCart } from '@element-plus/icons-vue'
import {
  useShoppingCarts,
  type ICartsModel,
  type IGoodsItem,
} from '@/views/resourceCenter/hooks/useShoppingCarts'
import type { IBaseModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import SlMessage from '@/components/base/SlMessage'
import eventBus from '@/utils/eventBus'
import { submitResourceRequest, addDraftsOrder } from '@/api/modules/resourecenter'
import { getBusinessSystemListApi } from '@/api/modules/computingPowerMap'
import BusinessSystemCreated from '@/views/computingPowerMap/tenantView/components/BusinessSystemCreated.vue'
import { CirclePlusFilled, Edit, Download } from '@element-plus/icons-vue'
import SlButton from '@/components/base/SlButton.vue'
import importGoods from './importGoods'
import aDownload from '@/utils/aDownload'
import templateXlsx from '@/assets/tempalte/购物车产品导入模版.xlsx'
import type { UploadInstance } from 'element-plus'

const route = useRoute()
const router = useRouter()
const uploadRef = ref<UploadInstance>()

const isEdit = computed(() => route.query.orderId !== undefined)
const showBusinessSystemCreated = ref(false)
// 业务系统列表相关逻辑
const handleBusinessSystemCreatedClose = () => {
  ElMessageBox.confirm('请确认是否放弃新增业务系统')
    .then(() => {
      showBusinessSystemCreated.value = false
    })
    .catch(() => {
      // catch error
    })
}
const addBusiSystem = () => {
  showBusinessSystemCreated.value = true
}

const catrsModel = useShoppingCarts()
const handleFile = async (options: any) => {
  const { file } = options
  importGoods(file, catrsModel)
}
let temp: any = null
const orderFormOptions = useOrderFormOptions(
  catrsModel.baseList[0]?.orderJson,
  ({ busiSystemOptions }) => {
    temp = busiSystemOptions
  },
)
const handleBusinessSystemSubmit = async () => {
  if (temp) {
    const { entity: records } = await getBusinessSystemListApi()
    if (records && Array.isArray(records)) {
      temp.value = records?.map((e: any) => ({
        value: e.systemId,
        label: e.systemName,
      }))
    }
  }
  showBusinessSystemCreated.value = false
}
const formOptions = reactive([orderFormOptions])
const goodsListkeys = [
  'baseList',
  'slbList',
  'ecsList',
  'evsList',
  'obsList',
  'gcsList',
  'natList',
  'eipList',
  'cqList',
  'mysqlList',
  'redisList',
  'backupList',
  'vpnList',
  'nasList',
  'kafkaList',
  'flinkList',
  'pmList',
  'esList',
  'bldRedisList',
] as const

const slFormRef = ref()
setRef(catrsModel.baseList[0])

function setRef(goods: IGoodsItem<IBaseModel>) {
  goods.ref = slFormRef
}
const orderId = (route.query.orderId as string) || ''

function cancel() {
  router.go(-1)
}
function composeArguments(catrsModel: ICartsModel) {
  const orederItem = catrsModel.baseList[0]
  // 工单信息
  const order = {
    id: orderId,
    busiSystemId: orederItem.orderJson.busiSystemId,
    orderTitle: orederItem.orderJson.orderTitle,
    busiDepartLeaderId: orederItem.orderJson.busiDepartLeaderId,
    levelThreeLeaderId: orederItem.orderJson.levelThreeLeaderId,
    orderDesc: orederItem.orderJson.orderDesc,
    manufacturer: orederItem.orderJson.manufacturer,
    manufacturerContacts: orederItem.orderJson.manufacturerContacts,
    manufacturerMobile: orederItem.orderJson.manufacturerMobile,
    moduleId: orederItem.orderJson.moduleId,
    bureauUserName: orederItem.orderJson.applyUserName,
    departmentName: orederItem.orderJson.department,
    moduleName: orederItem.orderJson.moduleName,
    businessDepartLeaderName: orederItem.orderJson.busiDepartLeaderLabel,
    levelThreeLeaderName: orederItem.orderJson.levelThreeLeaderLabel,
    businessSystemName: orederItem.orderJson.busiSystemName,
    resourceApplyFiles: orederItem.orderJson.files.map((e: any) => {
      return {
        orderFileType: e.orderFileType,
        fileId: e.id,
        ...e,
      }
    }),
    idArr: [orederItem.id],
    ids: '',
    goods: [],

    // 产品列表
    ecsModelList: [],
    gcsModelList: [],
    evsModelList: [],
    obsModelList: [],
    slbModelList: [],
    natModelList: [],
    eipModelList: [],
    mysqlModelList: [],
    redisModelList: [],
    backupModelList: [],
    cqModelList: [],
    nasModelList: [],
    vpnModelList: [],
    kafkaModelList: [],
    flinkModelList: [],
    pmModelList: [],
    esModelList: [],
    bldRedisModelList: [],
  }
  const idArr = [orederItem.id]
  // 添加产品信息
  addGoodsParams<any>(order, catrsModel, idArr)
  if (!isEdit.value) {
    order.ids = idArr.join(',')
  }
  return order
}
const getEvsList = (formModel: any) => {
  return formModel.evs.map((i: any) => {
    return {
      sysDiskSize: i[1],
      sysDiskType: i[0],
    }
  })
}
function addGoodsParams<T extends ICartsModel>(order: any, goodsItem: T, idArr: number[]) {
  // ecs
  if (goodsItem.ecsList.length > 0) {
    order.ecsModelList = goodsItem.ecsList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        bindPublicIp: orderJson.isBindPublicNetworkIp === '1',
        ...(orderJson.isBindPublicNetworkIp === '1'
          ? {
              eipModelList: [
                {
                  bandwidth: orderJson.eipValue,
                },
              ],
            }
          : {}),
        flavorCode: null,
        flavorName: orderJson.ecs[1],
        flavorType: orderJson.ecs[0],
        imageId: null,
        functionalModule: orderJson.functionalModule,
        imageOs: orderJson.imageOs[0],
        imageVersion: orderJson.imageOs[1],
        mountDataDisk: orderJson.isMountEvs === '1',
        mountDataDiskList: orderJson.isMountEvs === '1' ? getEvsList(orderJson) : [],
        openNum: orderJson.numbers,
        productType: 'ecs',
        sysDiskSize: orderJson.sysDisk[1],
        sysDiskType: orderJson.sysDisk[0],
        plane: orderJson.planeValue.join(','),
        vmName: orderJson.instanceName,
        originName: orderJson.instanceName,
        disasterRecovery: orderJson.disasterRecovery === '1',
      }
    })
  }
  if (goodsItem.mysqlList.length > 0) {
    order.mysqlModelList = goodsItem.mysqlList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        bindPublicIp: orderJson.isBindPublicNetworkIp === '1',
        ...(orderJson.isBindPublicNetworkIp === '1'
          ? {
              eipModelList: [
                {
                  bandwidth: orderJson.eipValue,
                },
              ],
            }
          : {}),
        flavorCode: null,
        flavorName: orderJson.ecs[1],
        flavorType: orderJson.ecs[0],
        imageId: null,
        functionalModule: orderJson.functionalModule,
        imageOs: orderJson.imageOs[0],
        imageVersion: orderJson.imageOs[1],
        mountDataDisk: orderJson.isMountEvs === '1',
        mountDataDiskList: orderJson.isMountEvs === '1' ? getEvsList(orderJson) : [],
        openNum: orderJson.numbers,
        productType: 'mysql',
        sysDiskSize: orderJson.sysDisk[1],
        sysDiskType: orderJson.sysDisk[0],
        plane: orderJson.planeValue.join(','),
        vmName: orderJson.instanceName,
        originName: orderJson.instanceName,
        disasterRecovery: orderJson.disasterRecovery === '1',
        deployType: orderJson.deployType,
      }
    })
  }
  if (goodsItem.redisList.length > 0) {
    order.redisModelList = goodsItem.redisList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        bindPublicIp: orderJson.isBindPublicNetworkIp === '1',
        ...(orderJson.isBindPublicNetworkIp === '1'
          ? {
              eipModelList: [
                {
                  bandwidth: orderJson.eipValue,
                },
              ],
            }
          : {}),
        flavorCode: null,
        flavorName: orderJson.ecs[1],
        flavorType: orderJson.ecs[0],
        imageId: null,
        functionalModule: orderJson.functionalModule,
        imageOs: orderJson.imageOs[0],
        imageVersion: orderJson.imageOs[1],
        mountDataDisk: orderJson.isMountEvs === '1',
        mountDataDiskList: orderJson.isMountEvs === '1' ? getEvsList(orderJson) : [],
        openNum: orderJson.numbers,
        productType: 'redis',
        sysDiskSize: orderJson.sysDisk[1],
        sysDiskType: orderJson.sysDisk[0],
        plane: orderJson.planeValue.join(','),
        vmName: orderJson.instanceName,
        originName: orderJson.instanceName,
        disasterRecovery: orderJson.disasterRecovery === '1',
      }
    })
  }
  if (goodsItem.gcsList.length > 0) {
    order.gcsModelList = goodsItem.gcsList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        bindPublicIp: orderJson.isBindPublicNetworkIp === '1',
        ...(orderJson.isBindPublicNetworkIp === '1'
          ? {
              eipModelList: [
                {
                  bandwidth: orderJson.eipValue,
                },
              ],
            }
          : {}),
        flavorCode: null,
        flavorName: orderJson.gcs[1],
        flavorType: orderJson.gcs[0],
        imageId: null,
        functionalModule: orderJson.functionalModule,
        imageOs: orderJson.imageOs[0],
        imageVersion: orderJson.imageOs[1],
        mountDataDisk: orderJson.isMountEvs === '1',
        mountDataDiskList: orderJson.isMountEvs === '1' ? getEvsList(orderJson) : [],
        openNum: orderJson.numbers,
        productType: 'gcs',
        sysDiskSize: orderJson.sysDisk[1],
        sysDiskType: orderJson.sysDisk[0],
        vmName: orderJson.instanceName,
        originName: orderJson.instanceName,
        disasterRecovery: orderJson.disasterRecovery === '1',
        plane: orderJson.planeValue.join(','),
      }
    })
  }
  if (goodsItem.evsList.length > 0) {
    order.evsModelList = goodsItem.evsList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        vmId: orderJson.vmId,
        vmName: orderJson.ecsName,
        openNum: orderJson.numbers,
        productType: 'evs',
        sysDiskSize: orderJson.evs[0][1],
        sysDiskType: orderJson.evs[0][0],
        functionalModule: orderJson.functionalModule,
      }
    })
  }
  if (goodsItem.eipList.length > 0) {
    order.eipModelList = goodsItem.eipList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        vmId: orderJson.vmId,
        vmName: orderJson.ecsName,
        openNum: orderJson.numbers,
        productType: 'eip',
        functionalModule: orderJson.functionalModule,
        bandwidth: orderJson.eipValue,
        eipName: orderJson.instanceName,
        originName: orderJson.instanceName,
        ipVersion: orderJson.ipVersion,
      }
    })
  }
  if (goodsItem.natList.length > 0) {
    order.natModelList = goodsItem.natList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        openNum: orderJson.numbers,
        productType: 'nat',

        flavorCode: null,
        flavorName: orderJson.nat,
        flavorType: orderJson.desc,

        functionalModule: orderJson.functionalModule,
        natName: orderJson.instanceName,
        originName: orderJson.instanceName,
        catalogueDomainCode: '',
        domainCode: '',
        bindPublicIp: orderJson.isBindPublicNetworkIp === '1',
        eipModelList: [
          {
            bandwidth: orderJson.eipValue,
          },
        ],
      }
    })
  }
  if (goodsItem.obsList.length > 0) {
    order.obsModelList = goodsItem.obsList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        openNum: orderJson.numbers,
        productType: 'obs',
        storageDiskSize: orderJson.obs[1],
        storageDiskType: orderJson.obs[0],
        functionalModule: orderJson.functionalModule,
        obsName: orderJson.instanceName,
        originName: orderJson.instanceName,
      }
    })
  }
  if (goodsItem.slbList.length > 0) {
    order.slbModelList = goodsItem.slbList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        applyTime: orderJson.time,
        openNum: orderJson.numbers,
        productType: 'slb',

        flavorCode: null,
        flavorName: orderJson.slb,
        flavorType: orderJson.desc,

        functionalModule: orderJson.functionalModule,
        slbName: orderJson.instanceName,
        originName: orderJson.instanceName,
        bindPublicIp: orderJson.isBindPublicNetworkIp === '1',
        catalogueDomainCode: '',
        domainCode: '',
        eipModelList: [
          {
            bandwidth: orderJson.eipValue,
          },
        ],
      }
    })
  }
  if (goodsItem.cqList.length > 0) {
    const baseInfo = catrsModel.baseList[0]
    order.cqModelList = goodsItem.cqList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        cqName: orderJson.instanceName,
        originName: orderJson.instanceName,
        vCpus: orderJson.cpu[0],
        ram: orderJson.cpu[1],
        gpuRatio: orderJson.gpu[0],
        gpuVirtualMemory: orderJson.gpu[1],
        gpuCore: orderJson.gpu[2],
        a4Account: baseInfo.orderJson.a4Account,
        a4Phone: baseInfo.orderJson.a4Phone,
        applyTime: orderJson.time,
        openNum: 1,
        productType: 'cq',
      }
    })
  }
  if (goodsItem.backupList.length > 0) {
    order.backupModelList = goodsItem.backupList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        productType: 'backup',
        jobName: orderJson.jobName,
        originName: orderJson.jobName,
        backupType: orderJson.backupType,
        frequency: orderJson.frequency,
        daysOfWeek: orderJson.daysOfWeek,
        objectIdList: orderJson.objectIdList,
      }
    })
  }
  if (goodsItem.vpnList.length > 0) {
    order.vpnModelList = goodsItem.vpnList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        productType: 'vpn',
        name: orderJson.instanceName,
        originName: orderJson.instanceName,
        maxConnection: orderJson.maxClient,
        bandwidth: orderJson.bandwidth,
        applyTime: orderJson.time,
        openNum: orderJson.numbers,
      }
    })
  }
  if (goodsItem.nasList.length > 0) {
    order.nasModelList = goodsItem.nasList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        productType: 'nas',
        name: orderJson.instanceName,
        originName: orderJson.instanceName,
        storageSize: orderJson.storageSize,
        path: orderJson.storagePath,
        openNum: orderJson.numbers,
        applyTime: orderJson.time,
      }
    })
  }
  if (goodsItem.kafkaList.length > 0) {
    order.kafkaModelList = goodsItem.kafkaList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        productType: 'kafka',
        name: orderJson.instanceName,
        originName: orderJson.instanceName,
        partition: orderJson.partition,
        replication: orderJson.replication,
        retainTime: orderJson.retentionTime,
        dataFlow: orderJson.dataFlow.toString(),
        dataStorageTotal: orderJson.dataStorage.toString(),
        applyTime: orderJson.time,
      }
    })
  }
  if (goodsItem.flinkList.length > 0) {
    order.flinkModelList = goodsItem.flinkList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        productType: 'flink',
        flinkName: orderJson.instanceName,
        originName: orderJson.instanceName,
        openNum: 1,
        vCpus: orderJson.vCPU,
        ram: orderJson.memory,
        applyTime: orderJson.time,
      }
    })
  }
  if (goodsItem.bldRedisList.length > 0) {
    order.bldRedisModelList = goodsItem.bldRedisList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        productType: 'bldRedis',
        name: orderJson.instanceName,
        originName: orderJson.instanceName,
        ip: orderJson.ip,
        applyTime: orderJson.time,
        cpuArchitecture: orderJson.cpuArch,
      }
    })
  }
  if (goodsItem.pmList.length > 0) {
    order.pmModelList = goodsItem.pmList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        productType: 'pm',
        pmName: orderJson.instanceName,
        originName: orderJson.instanceName,
        openNum: 1,
        vCpus: orderJson.cpu,
        ram: orderJson.memory,
        diskSize: orderJson.disk,
        gpuType: orderJson.isUseGpu === '1' ? orderJson.gpuType : undefined,
        gpuNum: orderJson.isUseGpu === '1' ? orderJson.gpuCount : undefined,
        gpuCardType: orderJson.isUseGpu === '1' ? orderJson.gpuCardType : undefined,
        applyTime: orderJson.time,
      }
    })
  }
  if (goodsItem.esList.length > 0) {
    order.esModelList = goodsItem.esList.map(({ orderJson, id }) => {
      idArr.push(id)
      return {
        productType: 'es',
        name: orderJson.instanceName,
        originName: orderJson.instanceName,
        averageDailyIncrementData: orderJson.dailyIncrementData.toString(),
        retainTime: orderJson.retentionTime,
        numberOfReplicas: orderJson.replication,
        diskSize: orderJson.diskSize.toString(),
        applyTime: orderJson.time,
        indexTemplate: orderJson.template ? JSON.parse(orderJson.template) : null,
      }
    })
  }
}
function getFormRefs() {
  return goodsListkeys.reduce((acc: any[], key) => {
    return acc.concat(catrsModel[key].map((ele) => ele.ref))
  }, [])
}
async function submitDraftOrder() {
  const validate = await slFormRef.value.validateField([
    'orderTitle',
    'busiDepartLeaderId',
    'levelThreeLeaderId',
  ])
  if (!validate) return
  const order = composeArguments(catrsModel)
  const { code } = await addDraftsOrder(order)
  if (code === 200) {
    SlMessage.success('提交成功')
    eventBus.emit('shoppingCarts:refresh')
    slFormRef.value.resetFields()
  }
}
function submit() {
  return new Promise((resolve, reject) => {
    const formRefs = getFormRefs()
    if (formRefs.length < 2) {
      SlMessage.error('请添加产品')
      return reject(new Error('请添加产品'))
    }
    if (catrsModel.cqList.length > 0) {
      formRefs.push(catrsModel.baseList[0].a4FormRef)
    }
    const formPromises = formRefs.map((ele) => ele.validate())
    Promise.all(formPromises)
      .then(async (res) => {
        if (res.length > 0 && res.every(Boolean)) {
          // 组装入参
          const order = composeArguments(catrsModel)
          const { code } = await submitResourceRequest(order)
          resolve(true)
          if (code === 200) {
            SlMessage.success('提交成功')
            if (isEdit.value) {
              cancel()
            } else {
              eventBus.emit('shoppingCarts:refresh')
              slFormRef.value.resetFields()
            }
          }
        } else {
          SlMessage.error('请填写完整信息')
          reject(new Error('请填写完整信息'))
        }
      })
      .catch((err) => {
        reject(err)
      })
  })
}
function saveDraft() {
  eventBus.emit('shoppingCarts:updateGoods')
}
function downloadTemplate() {
  aDownload(templateXlsx, '购物车产品导入模版.xlsx')
}
function openFileDialog() {
  // 兼容：优先尝试 Upload 实例方法，不存在则回退到原生 input
  const instance = uploadRef?.value as any
  if (instance && typeof instance.handleStart === 'function') {
    // Element Plus 早期版本可能支持 handleStart，但部分版本被移除
    try {
      instance.handleStart()
      return
    } catch {
      // fallthrough
    }
  }
  const inputEl: HTMLInputElement | null = (instance?.$el || instance?.ref || instance)
    ?.querySelector
    ? (instance.$el as HTMLElement).querySelector('input[type="file"]')
    : (uploadRef as any)?.value?.$el?.querySelector?.('input[type="file"]')
  if (inputEl) inputEl.click()
}
const activeTab = ref('ecs')
activeTab.value = (route.query.activeTab as string) || 'ecs'

const ecsCount = computed(() => {
  return catrsModel?.ecsList?.length || 0
})
const gcsCount = computed(() => {
  return catrsModel?.gcsList?.length || 0
})
const evsCount = computed(() => {
  return catrsModel?.evsList?.length || 0
})
const obsCount = computed(() => {
  return catrsModel?.obsList?.length || 0
})
const slbCount = computed(() => {
  return catrsModel?.slbList?.length || 0
})
const natCount = computed(() => {
  return catrsModel?.natList?.length || 0
})
const eipCount = computed(() => {
  return catrsModel?.eipList?.length || 0
})
const cqCount = computed(() => {
  return catrsModel?.cqList?.length || 0
})
const mysqlCount = computed(() => {
  return catrsModel?.mysqlList?.length || 0
})
const redisCount = computed(() => {
  return catrsModel?.redisList?.length || 0
})
const backupCount = computed(() => {
  return catrsModel?.backupList?.length || 0
})
const vpnCount = computed(() => {
  return catrsModel?.vpnList?.length || 0
})
const nasCount = computed(() => {
  return catrsModel?.nasList?.length || 0
})
const kafkaCount = computed(() => {
  return catrsModel?.kafkaList?.length || 0
})
const flinkCount = computed(() => {
  return catrsModel?.flinkList?.length || 0
})
const bldRedisCount = computed(() => {
  return catrsModel?.bldRedisList?.length || 0
})
const pmCount = computed(() => {
  return catrsModel?.pmList?.length || 0
})
const esCount = computed(() => {
  return catrsModel?.esList?.length || 0
})
// tab栏切换
const tabs: any = ref([
  { label: '云主机', name: 'ecs', count: ecsCount },
  { label: 'GPU云主机', name: 'gcs', count: gcsCount },
  { label: '云硬盘', name: 'evs', count: evsCount },
  { label: '弹性公网', name: 'eip', count: eipCount },
  { label: '对象存储', name: 'obs', count: obsCount },
  { label: '负载均衡', name: 'slb', count: slbCount },
  { label: 'NAT网关', name: 'nat', count: natCount },
  { label: '容器配额', name: 'cq', count: cqCount },
  { label: 'MySQL云数据库', name: 'mysql', count: mysqlCount },
  { label: '通用Redis', name: 'redis', count: redisCount },
  { label: '云灾备', name: 'backup', count: backupCount },
  { label: 'VPN', name: 'vpn', count: vpnCount },
  { label: 'NAS', name: 'nas', count: nasCount },
  { label: 'Kafka', name: 'kafka', count: kafkaCount },
  { label: 'Flink', name: 'flink', count: flinkCount },
  { label: '裸金属', name: 'pm', count: pmCount },
  { label: 'ElasticSearch', name: 'es', count: esCount },
  { label: '国产Redis', name: 'bldRedis', count: bldRedisCount },
])
const tabClick = (name: string) => {
  router.replace({
    query: {
      ...route.query,
      activeTab: name,
    },
  })
}
</script>

<style scoped lang="scss">
.scroll-view {
  margin-top: 2px;
}
.footer {
  height: 48px;
  padding: 8px 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  .button-group {
    display: flex;
    align-items: center;
    justify-content: right;
    flex-grow: 1;
  }
}
.add-busisystem-btn {
  display: flex;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
}
#shopping-carts {
  :deep(.el-drawer__body) {
    padding: 0;
  }
}
</style>
