import {
  cycleBinCreate,
  cycleBinUpdate,
  cycleBinList,
  cycleBinDelete,
  cycleBinListProduct,
  recoveryDetail,
} from '@/api/modules/resourecenter'
import SlMessage from '@/components/base/SlMessage'
import { reactive, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import eventBus from '@/utils/eventBus'
import { useCycleBaseModel, type ICycleBinBaseModel } from './useGoodsModels'

type GoodsType =
  | 'ecs'
  | 'gcs'
  | 'obs'
  | 'slb'
  | 'vpc'
  | 'evs'
  | 'shareEvs'
  | 'eip'
  | 'nat'
  | 'vpc'
  | 'network'
  | 'base'
  | 'mysql'
  | 'postgreSql'
  | 'redis'
  | 'pm'
  | 'es'
  | 'flink'
  | 'kafka'
  | 'bldRedis'

export interface IGoodsItem {
  id: number
  orderJson?: ICycleBinBaseModel
  [key: string]: any
}

export interface ICartsModel {
  ecsList: IGoodsItem[]
  gcsList: IGoodsItem[]
  obsList: IGoodsItem[]
  slbList: IGoodsItem[]
  evsList: IGoodsItem[]
  shareEvsList: IGoodsItem[]
  natList: IGoodsItem[]
  vpcList: IGoodsItem[]
  eipList: IGoodsItem[]
  mysqlList: IGoodsItem[]
  postgreSqlList: IGoodsItem[]
  redisList: IGoodsItem[]
  networkList: IGoodsItem[]
  baseList: IGoodsItem[]
  backupList: IGoodsItem[]
  nasList: IGoodsItem[]
  vpnList: IGoodsItem[]
  pmList: IGoodsItem[]
  esList: IGoodsItem[]
  flinkList: IGoodsItem[]
  kafkaList: IGoodsItem[]
  bldRedisList: IGoodsItem[]
}
type IGoodsListKey = keyof Omit<ICartsModel, 'baseList'>

const keys: IGoodsListKey[] = [
  'ecsList',
  'evsList',
  'shareEvsList',
  'eipList',
  'gcsList',
  'obsList',
  'slbList',
  'natList',
  'vpcList',
  'networkList',
  'mysqlList',
  'postgreSqlList',
  'redisList',
  'backupList',
  'nasList',
  'vpnList',
  'pmList',
  'esList',
  'flinkList',
  'kafkaList',
  'bldRedisList',
]

/**
 * 新增产品记录
 * @param goodsType 商品类型
 * @returns
 */
export async function createRecord(goodsType: GoodsType): Promise<number[]> {
  const res = await cycleBinCreate({
    [`${goodsType}List`]: [
      {
        goodsType,
        orderJson: {},
      },
    ],
  })
  if (res.code === 200) {
    return res.entity as number[]
  }
  SlMessage.error(res.message || '接口请求失败')
  return []
}

export async function updateRecord(cycleBinModel: ICartsModel): Promise<boolean> {
  const keys = ['baseList'] as const
  const params: any = {}
  keys.forEach((key) => {
    if (cycleBinModel[key].length) {
      params[key] = cycleBinModel[key].map((ele) => ({
        id: ele.id,
        goodsType: 'base',
        orderJson: ele.orderJson,
      }))
    }
  })
  const res = await cycleBinUpdate(params)
  if (res.code === 200) {
    SlMessage.success('暂存成功')
    return true
  }
  SlMessage.error(res.message || '接口请求失败')
  return false
}

export async function deleteRecord(ids: number[], type: GoodsType | '' = ''): Promise<boolean> {
  const res = await cycleBinDelete({ ids: ids, type })
  if (res.code === 200) {
    return true
  }
  return false
}

async function loadDatafromOrderEcho(cycleBinModel: ICartsModel, orderId: string): Promise<void> {
  const { entity, code } = await recoveryDetail({ workOrderId: orderId })
  if (code !== 200) return
  const baseJson = cycleBinModel.baseList[0].orderJson!
  baseJson.id = orderId
  baseJson.orderTitle = entity.orderTitle
  baseJson.levelThreeLeaderId = entity.levelThreeLeaderId
  baseJson.orderDesc = entity.orderDesc
  // ⚠️注意：后端的坑 和 标准工单字段不一样
  baseJson.busiSystemId = entity.businessSystemId
  baseJson.busiSystemName = entity.businessSystemName

  keys.forEach((list: IGoodsListKey) => {
    cycleBinModel[list] = entity[list] || []
  })
}
async function loadDatafromCycleBin(cycleBinModel: ICartsModel): Promise<void> {
  const { entity, code } = await cycleBinList()
  if (code === 200) {
    let businessSystemId = ''
    let busiSystemName = ''
    // 从暂存list 提取业务系统
    keys.forEach((list: IGoodsListKey) => {
      const goods = entity[list] || []
      if (!businessSystemId && goods.length) {
        businessSystemId = goods[0].businessSystemId
        busiSystemName = goods[0].businessSysName
      }
    })

    // 构建baseList 有则取无则创建
    if (!entity.baseList?.length) {
      const baseId = await createRecord('base')
      if (baseId.length) {
        cycleBinModel.baseList[0].id = baseId[0]
        Object.assign(cycleBinModel.baseList[0].orderJson!, useCycleBaseModel())
        nextTick(() => {
          cycleBinModel.baseList[0].ref.clearValidate()
        })
      }
    } else {
      const item: { id: number; goodsType: GoodsType; orderJson: string } = entity.baseList[0]
      cycleBinModel.baseList[0].id = item.id
      cycleBinModel.baseList[0].goodsType = item.goodsType
      cycleBinModel.baseList[0].orderJson = Object.assign(
        cycleBinModel.baseList[0].orderJson!,
        item.orderJson,
      )
    }
    cycleBinModel.baseList[0].orderJson!.busiSystemId = businessSystemId
    cycleBinModel.baseList[0].orderJson!.busiSystemName = busiSystemName

    const productListParams: any = entity
    delete productListParams.baseList
    const { entity: productList, code: productListCode } =
      await cycleBinListProduct(productListParams)
    if (productListCode !== 200) return

    keys.forEach((list: IGoodsListKey) => {
      cycleBinModel[list] = productList[list] || []
    })
  }
  return
}
export function useCycleBin() {
  const route = useRoute()
  const cycleBinModel: ICartsModel = reactive({
    ecsList: [],
    evsList: [],
    shareEvsList: [],
    eipList: [],
    gcsList: [],
    obsList: [],
    slbList: [],
    natList: [],
    vpcList: [],
    networkList: [],
    redisList: [],
    mysqlList: [],
    postgreSqlList: [],
    nasList: [],
    vpnList: [],
    pmList: [],
    esList: [],
    flinkList: [],
    kafkaList: [],
    bldRedisList: [],
    baseList: [
      {
        id: 0,
        goodsType: 'base',
        orderJson: useCycleBaseModel(),
      },
    ],
    backupList: [],
  })
  if (route.query.orderId) {
    // 从工单回显数据
    loadDatafromOrderEcho(cycleBinModel, route.query.orderId as string)
  } else {
    // 从暂存回显数据
    loadDatafromCycleBin(cycleBinModel)
  }

  eventBus.on('cycleBins:deleteGoods', handleDelGoods)
  eventBus.on('cycleBins:updateGoods', handleUpdateGoods)
  eventBus.on('cycleBins:refresh', handleRefresh)

  onUnmounted(() => {
    eventBus.off('cycleBins:handleDelGoods')
    eventBus.off('cycleBins:updateGoods')
    eventBus.off('cycleBins:refresh')
  })
  function handleRefresh() {
    eventBus.emit('cycleBins:updateCount')
    if (route.query.orderId) {
      loadDatafromOrderEcho(cycleBinModel, route.query.orderId as string)
    } else {
      loadDatafromCycleBin(cycleBinModel)
    }
  }

  function handleUpdateGoods() {
    updateRecord(cycleBinModel)
  }

  async function handleDelGoods({ goods, isEdit }: any) {
    // detail接口没有storageId，只有id
    const uid = goods.storageId || goods.id
    const res = !isEdit ? await deleteRecord([uid], goods.type) : true
    if (res) {
      if (!isEdit) eventBus.emit('cycleBins:updateCount')
      const listKey = `${goods.type}List` as IGoodsListKey
      const list = cycleBinModel[listKey]
      const index = list.findIndex((item: any) => item.storageId === uid || item.id === uid)
      if (index > -1) {
        list.splice(index, 1)
        if (getCycleBinLength(cycleBinModel) === 0 && !isEdit) {
          eventBus.emit('cycleBins:refresh')
        }
      }
    }
  }
  return cycleBinModel
}

function getCycleBinLength(cycleBinModel: ICartsModel): number {
  return keys.reduce((acc, list) => acc + (cycleBinModel[list]?.length || 0), 0)
}
