<template>
  <section class="panel power-panel fancy-corner-panel">
    <div class="card-content">
      <img src="/images/dashboard/left-top.png" class="corner-tl" alt="left-top" />
      <img src="/images/dashboard/right-top.png" class="corner-tr" alt="right-top" />
      <img src="/images/dashboard/left-bottom.png" class="corner-bl" alt="left-bottom" />
      <img src="/images/dashboard/right-bottom.png" class="corner-br" alt="right-bottom" />
      <slot></slot>
    </div>
  </section>
</template>

<style scoped>
.panel {
  position: relative;
}
.card-content {
  background: linear-gradient(45deg, #fff 50%, #c7def8 100%);
}
.power-panel {
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(42, 107, 255, 0.08);
  padding: 2px;
  position: relative;
  background-color: #fff;
}
.fancy-corner-panel {
  position: relative;
  border-radius: 16px;
}

.fancy-corner-panel .corner-tl,
.fancy-corner-panel .corner-tr,
.fancy-corner-panel .corner-bl,
.fancy-corner-panel .corner-br {
  position: absolute;
  width: 20px;
  z-index: 2;
}

.fancy-corner-panel .corner-tl {
  left: 0;
  top: 0;
}

.fancy-corner-panel .corner-tr {
  right: 0;
  top: 0;
}

.fancy-corner-panel .corner-bl {
  left: 0;
  bottom: 0;
}

.fancy-corner-panel .corner-br {
  right: 0;
  bottom: 0;
}
</style>
