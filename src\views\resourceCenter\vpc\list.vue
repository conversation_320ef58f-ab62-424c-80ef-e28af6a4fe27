<template>
  <div class="table-box">
    <sl-page-header
      title="VPC"
      title-line="VPC（Virtual Private Cloud）为您搭建专属的私有网络空间，并可以自定义IP地址范围、网段、路由、网关等"
      :icon="{
        class: 'page_VPC',
        color: '#0052D9',
        size: '40px',
      }"
    >
      <template #custom>
        <sl-base-tabs
          :tabs="availableTabs"
          v-model="activeTab"
          v-if="!shouldHideTabs"
        ></sl-base-tabs>
      </template>
    </sl-page-header>
    <div class="resource-tab" v-if="activeTab === 'INNER'">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button v-permission="'Create'" @click="handleCreateVpc" type="primary">
          开通VPC
        </el-button>
        <el-button @click="handleBatchRecycle" type="primary" v-permission="'BatchRecycle'">
          批量回收
        </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: collapsed }"
          ref="formRef"
          :options="formOptions"
          v-model="formModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <dataList
          ref="dataListRef"
          :query-params="queryParams"
          :hide-operations="shouldHideResourceOperations"
        ></dataList>
      </div>
    </div>
    <div v-if="activeTab === 'DG'">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <!-- v-permission="'CreateDG'" -->
        <el-button @click="handleCreateDGVpc" type="primary" v-permission="'Create'">
          开通VPC
        </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: dgCollapsed }"
          ref="dgFormRef"
          :options="DGFormOptions"
          v-model="dgFormModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <PublicDataList
          ref="PubRef"
          :query-params="dgQueryParams"
          :hide-operations="shouldHideResourceOperations"
        ></PublicDataList>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp, Upload } from '@element-plus/icons-vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
import PublicDataList from './PublicDataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { useDichooks } from '../hooks/useDichooks'
import { normalizeExportArgs } from '@/views/resourceCenter/utils'
import { useDownload } from '@/hooks/useDownload'
import { resourceExport } from '@/api/modules/resourecenter'
import { useRouter, useRoute } from 'vue-router'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useRolePermission } from '../hooks/useRolePermission'
import { useResourceTabs } from '../hooks/useResourceTabs'

// 获取路由信息
const route = useRoute()

// 从query参数中获取业务系统的初始值
const getBusinessSysIdsFromQuery = () => {
  const businessSysIds = route.query.businessSysIds
  if (businessSysIds && typeof businessSysIds === 'string') {
    // 字符串格式，用逗号分割
    return businessSysIds.split(',').filter((id: string) => id.trim())
  }
  return []
}

const { shouldHideResourceOperations } = useRolePermission()
const { busiSystemOptions } = useBusiSystemOptions()
const { resourcePoolsDic } = useDichooks()
const { resourcePoolsDic: dgResourcePoolsDic } = useDichooks({
  resourcePools: {
    realmType: 'iaas',
    domainCodes: [
      'plf_prov_moc_zj_vmware',
      'plf_prov_moc_zj_h3c',
      'plf_prov_moc_zj_huawei',
      'plf_prov_moc_zj_inspur',
    ],
  },
})
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

// 使用统一的tabs管理hook
const { availableTabs, activeTab, shouldHideTabs } = useResourceTabs()

const router = useRouter()
const formRef = ref<any>(null)
const formModel = reactive<any>({
  businessSysIds: getBusinessSysIdsFromQuery(),
})

const queryParams = ref<any>({ ...formModel })
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}
function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: 'VPC名称',
        type: 'input',
        key: 'vpcName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSysIds',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
        props: {
          select: {
            multiple: true,
            filterable: true,
            clearable: true,
            collapseTags: true,
          },
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'vpc', 'VPC.xlsx', {
                      ...formModel,
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '网段',
        type: 'select',
        key: 'cidr',
        options: getDic('netRange'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '子网个数',
        type: 'input',
        key: 'subnetNum',
        span: 8,
        props: {
          type: 'number',
        },
        disabled: false,
        hidden: true,
      },
      {
        label: '申请人',
        type: 'input',
        key: 'userName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '工单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createdTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 批量回收
const dataListRef = ref()
const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}
const handleCreateVpc = () => {
  router.push('/vpcForm')
}
const handleCreateDGVpc = () => {
  router.push({
    path: '/corporate/vpc',
    query: {
      showBack: 1,
    },
  })
}
// 对公列表筛选条件及配置
const dgFormRef = ref<any>(null)
const dgQueryParams = ref<any>({ type: 'vpc', sourceType: 'DG' })

const dgFormModel = reactive<any>({})
function resetDgSearch() {
  dgFormRef.value!.resetFields()
  dgQueryParams.value = { ...dgQueryParams.value, ...dgFormModel }
}
function doDgSearch() {
  dgQueryParams.value = { ...dgQueryParams.value, ...dgFormModel }
}

// 是否默认折叠搜索项
const dgCollapsed = ref(true)

// 对公资源搜索表单配置
const DGFormOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: 'VPC名称',
        type: 'input',
        key: 'vpcName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={dgFormModel}
                  resourceList={DGFormOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={resetDgSearch} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={doDgSearch} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'vpc', 'VPC.xlsx', {
                      ...dgFormModel,
                      sourceType: 'DG',
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (dgCollapsed.value = !dgCollapsed.value)}
              >
                {dgCollapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {dgCollapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '网段',
        type: 'input',
        key: 'cidr',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '子网个数',
        type: 'input',
        key: 'subnetNum',
        span: 8,
        props: {
          type: 'number',
        },
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: dgResourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订购人',
        type: 'input',
        key: 'userName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订购时间',
        type: 'date',
        key: 'createdTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

const PubRef = ref()
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
