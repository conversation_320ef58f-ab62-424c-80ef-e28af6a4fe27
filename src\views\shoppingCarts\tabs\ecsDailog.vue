<template>
  <el-dialog class="escDialog" @close="close" width="1000">
    <!-- 添加查询输入框和查询按钮 -->
    <div style="margin-bottom: 16px; display: flex; gap: 30px">
      <el-radio-group v-model="queryParams.type" active-value="ecs" inactive-value="gcs">
        <el-radio label="云主机" value="ecs" />
        <el-radio label="GPU云主机" value="gcs" />
      </el-radio-group>
      <el-input
        v-model="keywords"
        placeholder="请输入云主机/IP/所属云/资源池"
        clearable
        style="width: 50%; margin-right: 10px"
      />
      <el-button type="primary" @click="handleSearch">查询</el-button>
    </div>
    <dataList is-evs-select :query-params="queryParams" @currentChange="currentChange" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :disabled="confirmDisabled" @click="confirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="ecsDailog">
import { computed, ref } from 'vue'
import dataList from '@/views/resourceCenter/ecs/dataList.vue'
const props = defineProps<{
  ipVersion?: 'IPv4' | 'IPv6'
}>()
let currentRow = ref(null)

const queryParams = ref<any>({
  type: 'ecs',
  recoveryStatusList: [0],
  ipv6Enable: props.ipVersion === 'IPv6' ? true : undefined,
})
const keywords = ref('') // 定义 keywords 变量
// 定义查询方法
const handleSearch = () => {
  // 这里可以添加查询逻辑，例如触发 dataList 的查询方法
  queryParams.value.keywords = keywords.value.trim()
}

const emit = defineEmits(['update:modelValue', 'confirm', 'dialogClose'])

const currentChange = (row: any) => {
  currentRow.value = row
}

const confirmDisabled = computed(() => !currentRow.value)

const close = () => {
  emit('update:modelValue', false)
  emit('dialogClose')
}

const confirm = () => {
  emit('update:modelValue', false)
  emit('confirm', currentRow.value)
}
</script>

<style>
.escDialog .el-dialog__body {
  padding: 8px 0 0 0;
}
</style>
