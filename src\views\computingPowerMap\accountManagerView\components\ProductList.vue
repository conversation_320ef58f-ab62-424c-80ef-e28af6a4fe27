<template>
  <div class="product-list">
    <div class="product-list-header">产品清单</div>
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabs" :key="item.name">
        <template #label>
          <span class="tab-label">{{ item.label }}</span>
          <span class="tab-count">({{ item.count }})</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 筛选表单部分 -->
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="queryParams"
        v-if="showTable"
      >
      </sl-form>
    </div>

    <!-- 表格部分 -->
    <div class="table-layout">
      <SlProTable
        ref="proTable"
        :key="`table-${activeTab}`"
        :data="currentList"
        :columns="currentColumns"
        :pagination="false"
        v-if="showTable && currentList.length > 0"
      >
      </SlProTable>
      <el-empty description="暂无数据" v-if="currentList.length === 0" />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, onMounted, nextTick, reactive, watch } from 'vue'
import { ElTabs, ElTabPane, ElEmpty } from 'element-plus'
import type { TabPaneName } from 'element-plus'
import { useRoute } from 'vue-router'
import slForm from '@/components/form/SlForm.vue'
import ConditionFilter from '@/views/resourceCenter/conditionFilter.vue'
import { useProductConfig, type ProductType } from '../hooks/useProductConfig'
import { getResourceList, getCountCustomApi } from '@/api/modules/computingPowerMap'

const { tabs, getProductColumns, getFormOptions, getProductFilterOptions, updateTabCount } =
  useProductConfig()
const route = useRoute()

const activeTab = ref<ProductType>('ecs')
const currentList = ref<any[]>([])
const formRef = ref<any>(null)
const queryParams = reactive<any>({})
const collapsed = ref(true)
const showTable = ref(true)
const loading = ref(false)

// 创建响应式的筛选条件数组
const currentFilterItems = ref<any[]>([])

// 从路由参数获取customId，无值时使用默认值
const getCustomId = () => {
  return (route.query.customId as string) || 'c9d478379597475eb024661ca72700f8'
}

// 当前选中产品的列配置
const currentColumns = computed(() => {
  return getProductColumns(activeTab.value)
})

// 当前产品的筛选表单配置
// const formOptions = computed(() => {
//   return getFormOptions(
//     activeTab.value,
//     queryParams,
//     collapsed,
//     reset,
//     search,
//     ConditionFilter,
//     currentFilterItems.value,
//   )
// })

const formOptions = ref<any[]>([])

// 监听activeTab变化，更新筛选条件
watch(
  activeTab,
  (newTab) => {
    // 获取新产品的筛选条件并创建响应式副本
    const newFilterItems = getProductFilterOptions(newTab)
    currentFilterItems.value = reactive(newFilterItems)
    formOptions.value = getFormOptions(
      activeTab.value,
      queryParams,
      collapsed,
      reset,
      search,
      ConditionFilter,
      currentFilterItems.value,
    )
  },
  { immediate: true },
)

// 表单操作方法
function reset() {
  formRef.value?.resetFields()
  Object.keys(queryParams).forEach((key) => {
    queryParams[key] = ''
  })
  // 重置后重新加载数据（仅在非tab切换时调用）
  if (!isTabChanging.value) {
    loadProductData(activeTab.value)
  }
}

function search() {
  // 搜索时重新加载数据
  loadProductData(activeTab.value)
}

// 添加tab切换状态标识
const isTabChanging = ref(false)

// Tab切换处理
const handleTabChange = async (tab: TabPaneName) => {
  const newTab = tab as ProductType

  isTabChanging.value = true
  activeTab.value = newTab
  showTable.value = false

  // 重置表单（不触发loadProductData）
  formRef.value?.resetFields()
  Object.keys(queryParams).forEach((key) => {
    queryParams[key] = ''
  })

  // 只调用一次loadProductData
  await loadProductData(newTab)

  // 使用nextTick确保DOM更新完成后再显示表格
  await nextTick()
  showTable.value = true
  isTabChanging.value = false
}

// 加载产品数据
const loadProductData = async (productType: ProductType) => {
  try {
    loading.value = true

    // 调用实际的后端接口
    const res = await getResourceList({
      type: productType,
      customId: getCustomId(),
      sourceType: 'DG',
      pageNum: 1,
      pageSize: 10,
      ...queryParams, // 合并筛选条件
    })

    if (res && res.entity) {
      currentList.value = res.entity.records || []
      // 移除数量更新逻辑，数量统一通过getCountCustomApi接口获取
    } else {
      currentList.value = []
    }
  } catch (error) {
    console.error('加载产品数据失败:', error)
    currentList.value = []

    // 可以在这里添加错误提示
    // ElMessage.error('数据加载失败，请重试')
  } finally {
    loading.value = false
  }
}

// 加载各个产品类型的数量
const loadProductCounts = async () => {
  try {
    const res = await getCountCustomApi({
      customId: getCustomId(),
      sourceType: 'DG',
    })

    if (res && res.entity) {
      // 根据返回的数据格式更新各个tab的数量
      const counts = res.entity

      // 更新各个产品类型的数量
      updateTabCount('ecs', counts.ecs || 0)
      updateTabCount('gcs', counts.gcs || 0)
      updateTabCount('evs', counts.evs || 0)
      updateTabCount('obs', counts.obs || 0)
      updateTabCount('nat', counts.nat || 0)
      updateTabCount('slb', counts.slb || 0)
      updateTabCount('eip', counts.eip || 0)
      updateTabCount('vpn', counts.vpn || 0)
      updateTabCount('rdsMysql', counts.rdsMysql || 0)
    }
  } catch (error) {
    console.error('加载产品数量失败:', error)
    // 如果接口失败，设置默认值0
    tabs.value.forEach((tab) => {
      updateTabCount(tab.name, 0)
    })
  }
}

onMounted(() => {
  // 初始化时先加载数量，再加载第一个tab的数据
  loadProductCounts()
  loadProductData(activeTab.value)
})
</script>

<style lang="scss" scoped>
.product-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;

  .product-list-header {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }
  :deep(.el-tabs__item) {
    min-width: 120px;
  }
  .tab-label {
    margin-right: 4px;
  }
}

.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}

.filter-form.collapsed {
  height: 50px;
}

.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}

.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
</style>
