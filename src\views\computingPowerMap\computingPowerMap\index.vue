<template>
  <div class="outer-container">
    <div class="dashboard-container">
      <!-- 顶部标题和算力总数 -->
      <Head :city="cityParam" name="运营视图切换" />
      <div class="dashboard-main">
        <!-- 左侧面板 -->
        <aside class="dashboard-side dashboard-side-left">
          <PowerBar :request-params="requestParams" />
          <domestic
            :is-active="domesticDialogVisible"
            @click="showDialogByType('domestic')"
            :data="memLineData"
            :request-params="requestParams"
            :x-axis-data="xAxisData"
          />
        </aside>
        <!-- 中间地图和卡片 -->
        <main class="dashboard-center">
          <Dashboardmap
            :city="cityParam"
            @skip-city-page="skipCityPage"
            @showLeftDialog="showLeftDialog"
            @transferData="changeRequestData"
            @selectChange="handleSelectChange"
            @transferCloudData="changeCloudData"
          />
        </main>
        <aside class="dashboard-side dashboard-side-right">
          <!-- 右侧面板 -->
          <div class="dashboard-side-right-top">
            <importantBusiness
              :request-params="requestParams"
              :is-active="zhongbaoDialogVisible"
              @showImportantDialog="showImportantDialog"
            ></importantBusiness>
          </div>
          <div class="dashboard-side-right-bottom">
            <saveEnergy
              :request-params="requestParams"
              :is-active="saveEnergrDialogVisible"
              @click="showDialogByType('saveEnergy')"
            ></saveEnergy>
          </div>
          <div class="dashboard-side-right-bottom2">
            <assetAnalysis
              :request-params="requestParams"
              :is-active="assetAnalysisDialogVisible"
              @click="showDialogByType('assetAnalysis')"
            ></assetAnalysis>
          </div>
          <div class="dashboard-side-right-bottom1">
            <infrastructure
              :request-params="requestParams"
              :is-active="infrastruDialogVisible"
              @click="showDialogByType('infrastructure')"
            ></infrastructure>
          </div>
        </aside>
        <!--        <div v-if="isMask" class="mask"></div>-->
        <!--        国产化弹窗-->
        <domesticDialog
          v-if="domesticDialogVisible"
          :base-device-hand-resource-pool-list="baseDeviceHandResourcePoolList"
          :request-params="requestParams"
          :cloud-list-arr="cloudListArr"
          @close="closeBottomDialog"
        ></domesticDialog>
        <!--        重保业务弹窗-->
        <importantBusinessDialog
          v-if="zhongbaoDialogVisible"
          :base-device-hand-resource-pool-list="baseDeviceHandResourcePoolList"
          :request-params="requestParams"
          :cloud-list-arr="cloudListArr"
          :bs-name-type="bsNameType"
          @close="closeBottomDialog"
        ></importantBusinessDialog>
        <!--        资产分析弹窗-->
        <assetAnalysisDialog
          v-if="assetAnalysisDialogVisible"
          :request-params="requestParams"
          :cloud-list-arr="cloudListArr"
          :base-device-hand-resource-pool-list="baseDeviceHandResourcePoolList"
          @close="closeBottomDialog"
        ></assetAnalysisDialog>
        <!--        绿色节能弹窗-->
        <saveEnergyDialog
          v-if="saveEnergrDialogVisible"
          :request-params="requestParams"
          :cloud-list-arr="cloudListArr"
          :base-device-hand-resource-pool-list="baseDeviceHandResourcePoolList"
          @close="closeBottomDialog"
        ></saveEnergyDialog>
        <!--        左侧弹窗-->
        <leftDetailDialog
          v-if="showComPowerLeftDialog"
          :request-params="requestParams"
          @close="closeBottomDialog"
        ></leftDetailDialog>
        <!--        基础设施弹窗-->
        <infrastructureDialog
          v-if="infrastruDialogVisible"
          :base-device-hand-resource-pool-list="baseDeviceHandResourcePoolList"
          :request-params="requestParams"
          :cloud-list-arr="cloudListArr"
          @close="closeBottomDialog"
        ></infrastructureDialog>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PowerBar from './components/comPowerTotal.vue'
import Dashboardmap from './components/Dashboardmap.vue'
// 国产化
import domestic from './components/domestic.vue'
import domesticDialog from './components/domesticDialog.vue'
//资产分析
import assetAnalysis from './components/assetAnalysis.vue'
import assetAnalysisDialog from './components/assetAnalysisDialog.vue'
// 绿色节能
import saveEnergy from './components/saveEnergy.vue'
import saveEnergyDialog from './components/saveEnergyDialog.vue'
//重保业务
import importantBusiness from './components/importantBusiness.vue'
import importantBusinessDialog from './components/importantBusinessDialog.vue'
// 基础设施
import infrastructure from './components/infrastructure.vue'
import infrastructureDialog from './components/infrastructureDialog.vue'

import { setStorage } from '@/utils/storage'
import leftDetailDialog from './components/leftDetailDialog.vue'
import Head from './components/Head.vue'
import { onMounted, ref, computed, toValue } from 'vue'
import { queryDevicesMetricPercentApi } from '@/api/modules/zsMap'
import { useRouter, useRoute } from 'vue-router'
import { getComPowerMapPageBaseDeviceHandResourcePool } from '@/api/modules/comPowerCenter'
const router = useRouter()
const route = useRoute()

// 从URL查询参数中获取城市信息，默认为浙江省
const cityParam = computed(() => {
  const city = requestParams.value.city || (route.query.city as string)
  // 验证城市参数是否为支持的城市，如果不是则返回默认值
  const supportedCities = [
    'zhejiang',
    'hangzhou',
    'ningbo',
    'wenzhou',
    'jiaxing',
    'huzhou',
    'shaoxing',
    'jinhua',
    'quzhou',
    'zhoushan',
    'taizhou',
    'lishui',
  ]

  return city && supportedCities.includes(city) ? city : 'zhejiang'
})

const areaCode = ref<string>('')
let maxInnerWidth = 1920
let maxInnerHeight = 1080

const colorMap = {
  杭州: '#2a6bff',
  绍兴: '#00d4aa',
  台州: '#ff6b35',
  温州: '#ffd700',
  丽水: '#ff4757',
  衢州: '#a55eea',
  嘉兴: '#26de81',
  湖州: '#fd79a8',
  舟山: '#fdcb6e',
  宁波: '#6c5ce7',
}

const memLineData = ref<any>([])
const gpuLineData = ref<any>([])
const xAxisData = ref<any>([])

//国产化弹窗
const domesticDialogVisible = ref(false)
//重保业务弹窗
const zhongbaoDialogVisible = ref(false)
// 基础设施弹窗
const infrastruDialogVisible = ref(false)
//绿色节能弹窗
const saveEnergrDialogVisible = ref(false)
// 资产分析弹窗
const assetAnalysisDialogVisible = ref(false)
//左侧弹窗
const showComPowerLeftDialog = ref(false)

const showLeftDialog = (data: any) => {
  closeBottomDialog()
  requestParams.value.cloudName = data.cloudName
  requestParams.value.platformTypeName = data.platformTypeName
  requestParams.value.cityCode = data.cityCode
  requestParams.value.areaCode = data.areaCode
  // requestParams.value.city = data.city
  isMask.value = true
  showComPowerLeftDialog.value = true
}
const isMask = ref(false)
//总览数据
const skipCityPage = () => {
  router.push({
    path: '/computingPowerMapDetail',
    query: { city: 'hangzhou' },
  })
}
const storeData: any = {}
function openNewWin(moduleName: string, props: any) {
  storeData[moduleName] = props
  setStorage('computingPowerMap', storeData)
  const routeData = router.resolve({
    name: 'computingPowerMap-moduleDetail',
    params: {
      moduleName,
    },
    query: {
      page: 1,
    },
  })
  window.open(routeData.href, '_blank')
}
//重保业务需要传递的名称
const bsNameType = ref('')
const showImportantDialog = (item: any) => {
  // console.log(item)
  bsNameType.value = item.bsNameType
  openNewWin('importantBusiness', {
    requestParams: toValue(requestParams),
    cloudListArr: toValue(cloudListArr),
    bsNameType: toValue(bsNameType),
  })
}
// 展示不同业务的弹窗
// const showDialogByType = (type: string) => {
//   closeBottomDialog()
//   if (type == 'importantBusiness') {
//     zhongbaoDialogVisible.value = true
//   } else if (type == 'infrastructure') {
//     infrastruDialogVisible.value = true
//   } else if (type == 'saveEnergy') {
//     saveEnergrDialogVisible.value = true
//   } else if (type == 'assetAnalysis') {
//     assetAnalysisDialogVisible.value = true
//   } else if (type == 'domestic') {
//     domesticDialogVisible.value = true
//   }
//   isMask.value = true
// }
const showDialogByType = (type: string) => {
  openNewWin(type, {
    requestParams: toValue(requestParams),
    cloudListArr: toValue(cloudListArr),
    baseDeviceHandResourcePoolList: toValue(baseDeviceHandResourcePoolList),
  })
}
//关闭底部弹窗
const closeBottomDialog = () => {
  zhongbaoDialogVisible.value = false
  infrastruDialogVisible.value = false
  saveEnergrDialogVisible.value = false
  assetAnalysisDialogVisible.value = false
  domesticDialogVisible.value = false
  showComPowerLeftDialog.value = false
  isMask.value = false
}

async function getDevicesMetricPercent() {
  const { entity }: any = await queryDevicesMetricPercentApi({
    areaCode: areaCode.value,
  })
  xAxisData.value = entity[0]?.modelList.map((ele: any) => ele.time.split(' ')[1])
  const tempMemLineData: any[] = []
  const tempGpuLineData: any[] = []
  entity.forEach((item: any) => {
    tempMemLineData.push({
      city: {
        name: item.areaCode + '市',
        color: colorMap[item.areaCode as keyof typeof colorMap]!,
      },
      data: item.modelList.map((ele: any) => ele.memUtilpercent),
    })
    tempGpuLineData.push({
      city: {
        name: item.areaCode + '市',
        color: colorMap[item.areaCode as keyof typeof colorMap]!,
      },
      data: item.modelList.map((ele: any) => ele.gpuUtilPercent),
    })
  })
  memLineData.value = tempMemLineData
  gpuLineData.value = tempGpuLineData
}
getDevicesMetricPercent()

const changeRequestData = (data: any) => {
  requestParams.value.cloudName = data.cloudName
  requestParams.value.platformTypeName = data.platformTypeName
  requestParams.value.cityCode = data.cityCode
  requestParams.value.areaCode = data.areaCode
  requestParams.value.city = data.city
  getBaseDeviceHandResourcePool()
}
// 定义要传递给子组件的请求参数
const requestParams = ref({
  cloudName: '',
  platformTypeName: '',
  cityCode: '',
  areaCode: '',
  city: '',
})
//从地图页面获取云类型 ，云平台信息
const cloudListArr = ref([])
const changeCloudData = (data: any) => {
  cloudListArr.value = data
}
// 定义类型/接口
interface OptionItem {
  name: string
  instanceId: string
}
const baseDeviceHandResourcePoolList = ref<OptionItem[]>([])
//查询物理资源池
const getBaseDeviceHandResourcePool = () => {
  let params = {
    cloudName: requestParams.value.cloudName,
    platformTypeName: requestParams.value.platformTypeName,
    cityCode: requestParams.value.cityCode,
    pageNum: 1,
    pageSize: 9999,
  }
  getComPowerMapPageBaseDeviceHandResourcePool(params).then((res: any) => {
    if (res.code == 200) {
      baseDeviceHandResourcePoolList.value = res.entity.records || []
    }
  })
}
onMounted(() => {
  getBaseDeviceHandResourcePool()
  const container = document.querySelector('.dashboard-container') as HTMLElement
  const scaleX = window.innerWidth / maxInnerWidth
  const scaleY = window.innerHeight / maxInnerHeight
  const transValueX = (1 - 1 / scaleX) * 50
  const transValueY = (1 - 1 / scaleY) * 50
  container.style.transform = `scale(${scaleX}, ${scaleY}) translate(${transValueX}%, ${transValueY}%)`
})
window.addEventListener('resize', () => {
  const container = document.querySelector('.dashboard-container') as HTMLElement
  const scaleX = window.innerWidth / maxInnerWidth
  const scaleY = window.innerHeight / maxInnerHeight
  const transValueX = (1 - 1 / scaleX) * 50
  const transValueY = (1 - 1 / scaleY) * 50
  container.style.transform = `scale(${scaleX}, ${scaleY}) translate(${transValueX}%, ${transValueY}%)`
})

const handleSelectChange = (selectedRegion: string) => {
  areaCode.value = selectedRegion
  getDevicesMetricPercent()
}
</script>

<style lang="scss" scoped>
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 10;
}
.outer-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
.dashboard-container {
  width: 1920px;
  height: 1080px;
  background: url('/images/computingPower/bg.png') no-repeat 0 0;
  background-size: contain;
  display: flex;
  flex-direction: column;
}
.dashboard-main {
  flex: 1;
  display: flex;
  padding: 0 17px;
  box-sizing: border-box;
  margin-top: -34px;
}
.dashboard-side {
  width: 462px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.dashboard-side-left {
  margin-right: 6px;
}
.dashboard-side-right {
  margin-left: 6px;
}
.dashboard-side-right-top {
}

.dashboard-side-right-bottom {
}
.dashboard-side-right-bottom2 {
  height: 200px;
}
.dashboard-side-right-bottom1 {
  height: 200px;
}
.dashboard-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  background: url('/images/computingPower/computingPowerMapBg.png') no-repeat center bottom;
  background-size: contain;
  padding-top: 30px;
  position: relative;
  margin: 0 -20px;
}
</style>
<style>
.comPowerNoData {
  width: 100%;
  height: 100%;
  display: flex;
  min-height: 100px;
  align-items: center;
  justify-content: center;
}
.bottom-dialog-search .el-select .el-select__wrapper .el-select__placeholder {
  color: #ffffff;
}
.bottom-dialog-search .el-select .el-select__wrapper {
  background: transparent;
  box-shadow: none;
  border-radius: 2px;
  border: 1px solid #3069b0;
}
.bottom-dialog-search .el-input .el-input__wrapper {
  background: transparent;
  box-shadow: none;
  border-radius: 2px;
  border: 1px solid #3069b0;
}

.bottom-dialog-search .el-input .el-input__wrapper .el-input__inner::placeholder {
  color: #ffffff;
}
.bottom-dialog-search
  .el-select
  .el-select__wrapper
  .el-select__selection
  .el-select__input-wrapper
  .el-select__input {
  color: #ffffff !important;
}
.bottom-dialog-table {
  .el-table {
    background: transparent;
    tr {
      background: transparent;
    }
    th.el-table__cell {
      background: transparent;
      border-bottom: 1px solid rgba(102, 102, 102, 0.5);
      font-size: 15px !important;
      color: #b7d7ff !important;
    }
  }
  .el-table__inner-wrapper:before {
    background-color: rgba(102, 102, 102, 0.5) !important;
  }
}

.el-table.comPowerTable .el-table__empty-block .el-table__empty-text {
  color: #ffffff;
}
</style>
