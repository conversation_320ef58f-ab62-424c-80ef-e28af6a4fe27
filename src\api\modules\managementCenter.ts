import http from '@/api'
import { USER, WOC } from '../config/servicePort'
import type { ResPage } from '../interface'
import type { RoleListType, RoleType } from '@/views/managementCenter/roleManagement/interface/type'
import { changeDateFormat } from '@/utils'
import type { WithoutUserType } from '@/views/managementCenter/externalUserManagement/interface/type'

// ------------------------租户管理-------------------------
/**
 * @name 租户管理-列表
 */

export const getTenantListApi = (config: any) =>
  http.get<ResPage<any>>(USER + '/tenant/tenantSelect', changeDateFormat(config, ['createdTime']))

/**
 * @name 租户管理-删除租户
 */
export const deleteTenantApi = (config: { id: string[] }) =>
  http.delete(USER + `/tenant/delete/${config.id}`, {}, { loading: true })

/**
 * @name 租户管理-获取租户下用户列表
 * @param config
 * @returns
 */
export const getTenantUserListApi = (config: { tenantId: string }) =>
  http.post<ResPage<any>>(
    USER + `/tenant/users/${config.tenantId}`,
    changeDateFormat(config, ['createdTime']),
  )

/**
 * @name 租户管理-移交管理权
 * @param config
 * @returns
 */
export const tenantUserTransferOwner = (config: { userId: string; tenantId: number }) =>
  http.post(USER + '/tenant/transfer/owner', config, { loading: true })

/**
 * @name 获取租户信息下拉框
 */
export const getSelectTenantListApi = () =>
  http.get<RoleType[]>(USER + '/tenant/tenantSelectByType')
// ------------------------end------------------------

// ------------------------用户管理-------------------------
/**
 * @name 用户管理-列表
 */
export const getUserListApi = (config: any) =>
  http.post<ResPage<any>>(USER + '/users/list', changeDateFormat(config, ['createdTime']))

/**
 * @name 用户管理-冻结用户
 */
export const freezeUserApi = (config: { id: string }) =>
  http.post(USER + `/users/freeze/${config.id}`, config, { loading: true })

/**
 * @name 用户管理-解冻用户
 */
export const unfreezeUserApi = (config: { id: string }) =>
  http.post(USER + `/users/unfreeze/${config.id}`, config, { loading: true })

/**
 * @name 获取角色详情
 * @param config
 * @returns
 */
export const userGetDetailApi = (config: { id: number }) =>
  http.get<any>(USER + `/users/detail/${config.id}`)

/**
 * @name 用户管理-授权角色
 */
export const userAuthorizationRoleApi = (config: any) =>
  http.post<any>(USER + '/users/role/update', config, { loading: true })

/**
 * @name 用户管理-绑定租户
 */
export const userBindTenantApi = (config: any) =>
  http.post<any>(USER + `/tenant/binding/${config.userId}`, config.accounts, { loading: true })

/**
 * @name 用户管理-创建用户
 */
export const createUserApi = (config: any) =>
  http.post<any>(USER + '/users/create', config, { loading: true })

/**
 * @name 用户管理-编辑用户
 */
export const updateUserApi = (config: any) =>
  http.post<any>(USER + '/users/update', config, { loading: true })

// ------------------------end------------------------

// ------------------------角色管理-------------------------
/**
 * @name 获取角色信息下拉框
 */
export const getSelectRoleListApi = () => http.get<RoleType[]>(USER + '/role/getRoleAll')

/**
 * @name 角色管理-列表
 */
export const getRoleListApi = (config: any) =>
  http.get<ResPage<RoleListType[]>>(
    USER + '/role/getRoleList',
    changeDateFormat(config, ['createdTime']),
  )

/**
 * @name 角色管理-添加
 */
export const addRoleApi = (config: RoleType) => http.post(USER + '/role/addRole', config)

/**
 * @name 角色管理-修改
 */
export const updateRoleApi = (config: RoleType) => http.post(USER + '/role/updateRole', config)

/**
 * @name 角色管理-删除
 */
export const deleteRoleByIdApi = (config: any) => http.delete(USER + `/role/deleteRoleById`, config)

/**
 * @name 角色管理-给角色分配权限
 */

export const updateRolePermissionApi = (config: any) =>
  http.post(USER + `/menu/updateUserMenu`, config)
// ------------------------角色管理end-------------------------

// ------------------------权限管理-------------------------
/**
 * @name 权限管理-获取全部权限列表
 */
export const selectMenusDetailApi = (config: any) =>
  http.get<ResPage<any[]>>(USER + `/menu/selectMenusDetail`, config)

/**
 * @name 权限管理-删除
 */
export const deletePermissionByIdApi = (config: any) =>
  http.delete(USER + `/menu/deleteMenu`, config)

/**
 * @name 权限管理-添加
 */
export const addPermissionApi = (config: any) => http.post(USER + `/menu/addMenu`, config)

/**
 * @name 权限管理-编辑
 */
export const updatePermissionApi = (config: any) => http.put(USER + `/menu/updateMenu`, config)

export const getPermissionByRolesApi = (config: any) =>
  http.get<any[]>(USER + `/menu/selectUserMenus`, config)

// ------------------------权限管理End-------------------------

// ------------------------外部用户管理-------------------------
/**
 * @name 外部用户管理-获取全部权限列表
 */
export const withoutListApi = (config: any) =>
  http.post<ResPage<WithoutUserType[]>>(USER + `/users/withoutList`, config)

/**
 * @name 外部用户管理-添加
 */
export const createWithoutUserApi = (config: WithoutUserType) =>
  http.post(USER + `/users/createWithoutUser`, config)

/**
 * @name 外部用户管理-编辑
 */
export const updateWithoutUserApi = (config: WithoutUserType) =>
  http.post(USER + `/users/updateWithoutUser`, config)

// ------------------------权限管理End-------------------------

// ------------------------系统日志-------------------------

// ----------- 操作日志---
/**
 * @name 系统日志-操作日志 -列表
 */
export const operationLogPageApi = (config: any) => http.post(WOC + `/operationLog/page`, config)

// ----------- 操作日志---
/**
 * @name 系统日志-操作日志 -查询
 */

export const getSystemMaintenanceListApi = (config: any) =>
  http.post<ResPage<any>>(WOC + '/systemMaintenance/page', config, {
    loading: true,
  })

/**
 * @name 系统日志-操作日志 -增加-修改
 */
export const systemMaintenancesSaveApi = (config: any) =>
  http.post(WOC + '/systemMaintenance/save', config, { loading: true })

/**
 * @name 系统日志-操作日志 -删除
 */
export const systemMaintenancesDeleteApi = (config: any) =>
  http.post(WOC + '/systemMaintenance/delete', config, { loading: true })

/**
 * @name 系统日志-操作日志 -详情
 */
export const getSystemMaintenanceDetailApi = (config: any) =>
  http.post<any>(WOC + '/systemMaintenance/detail', config, { loading: true })

// ----------- 操作日志---
/**
 * @name 系统日志-操作日志 -查询
 */

export const getProblemHandlingListApi = (config: any) =>
  http.post<ResPage<any>>(WOC + '/problemHandling/page', config, {
    loading: true,
  })

/**
 * @name 系统日志-操作日志 -增加-修改
 */
export const problemHandlingsSaveApi = (config: any) =>
  http.post(WOC + '/problemHandling/save', config, { loading: true })

/**
 * @name 系统日志-操作日志 -删除
 */
export const problemHandlingsDeleteApi = (config: any) =>
  http.post(WOC + '/problemHandling/delete', config, { loading: true })

/**
 * @name 系统日志-操作日志 -详情
 */
export const getProblemHandlingDetailApi = (config: any) =>
  http.post<any>(WOC + '/problemHandling/detail', config, { loading: true })

//  -------------------------系统日志end-----------------------------

//  -------------------------运营分析-----------------------------
export const activeStatisticsAggregateApi = (config: any) =>
  http.post(WOC + '/activeStatistics/aggregate', config)
//  -------------------------运营分析end-----------------------------

//  -------------------------报表管理-----------------------------
/**
 * @name 报表管理-列表
 */
export const getReportList = (config: any) =>
  http.post<ResPage<any>>(WOC + '/exportTask/page', config)

/**
 * @name 报表管理-删除
 */
export const deleteReport = (config: any) =>
  http.post(WOC + '/exportTask/delete', config, { loading: true })

/**
 * @name 报表管理-详情
 */
export const getReportDetail = (config: any) =>
  http.post<any>(WOC + '/exportTask/detail', config, { loading: true })

/**
 * @name 报表管理-创建-资源池
 */
export const createReport = (config: any) =>
  http.post(WOC + '/exportTask/create', config, { loading: true })

/**
 * @name 报表管理-创建-租户
 */
export const createReportTenant = (config: any) =>
  http.post(WOC + '/exportTask/tenantReportCreate', config, { loading: true })

/**
 * @name 报表管理-创建-GPU
 */
export const createReportGpu = (config: any) =>
  http.post(WOC + '/exportTask/gpuReportCreate', config, { loading: true })

/**
 * @name 报表管理-创建-容器云
 */
export const createReportK8s = (config: any) =>
  http.post(WOC + '/exportTask/k8sReportCreate', config, { loading: true })
/**
 * @name 报表管理-资源池列表
 */
export const getReportRegionList = (config: any) => http.post(WOC + '/region/reportList', config)
//  -------------------------报表管理end-----------------------------

// ------------------------API管理-------------------------
/**
 * @name 权限管理-获取全部权限列表
 */
export const selectAPiDetailApi = (config: any) =>
  http.get<ResPage<any[]>>(USER + `/platform/interface/list`, config)

/**
 * @name 获取云平台列表
 */
export const getApiPlatformListApi = (config: any) => {
  return http.get(USER + '/platform/interface/platform', config)
}
/**
 * @name 获取资源类型
 */
export const getApiResTypeListApi = (config: any) => {
  return http.get(USER + '/platform/interface/resTypes', config)
}

/**
 * @name API管理-添加
 */
export const addApiFunc = (config: any) => http.post(USER + `/platform/interface/create`, config)

/**
 * @name API管理-编辑
 */
export const updateApiFunc = (config: any) => http.post(USER + `/platform/interface/update`, config)

/**
 * @name API管理-删除
 */
export const deleteApiByIdApi = (config: any) =>
  http.post(USER + `/platform/interface/delete`, config)

/**
 * @name API管理-查询当前用户绑定的API
 */
export const getUserBindApiInfo = (config: any) =>
  http.get(USER + `/platform/interface/user/list`, config)

/**
 * @name API管理-绑定到用户上
 */
export const bindUserApiByIdApi = (config: any) =>
  http.post(USER + `/platform/interface/user/add`, config)

/**
 * @name API管理-查看当前api是否被用户绑定
 */
export const checkApiIsUseFunc = (config: any) =>
  http.put(USER + `/platform/interface/valid/` + config.id, config)

/**
//  * @name 权限管理-删除
//  */
// export const deletePermissionByIdApi = (config: any) =>
//   http.delete(USER + `/menu/deleteMenu`, config)
//
// /**
//  * @name 权限管理-添加
//  */
// export const addPermissionApi = (config: any) => http.post(USER + `/menu/addMenu`, config)
//
// /**
//  * @name 权限管理-编辑
//  */
// export const updatePermissionApi = (config: any) => http.put(USER + `/menu/updateMenu`, config)
//
// export const getPermissionByRolesApi = (config: any) =>
//   http.get<any[]>(USER + `/menu/selectUserMenus`, config)

// ------------------------API管理End-------------------------
