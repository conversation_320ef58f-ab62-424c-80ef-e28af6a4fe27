<template>
  <div class="table-box">
    <sl-page-header
      title="网络"
      title-line="网络（Network）是指由多个计算机、设备或系统通过通信技术连接在一起，实现信息共享和资源交换的系统。"
      :icon="{
        class: 'page_wangluo',
        color: '#0052D9',
        size: '40px',
      }"
    >
    </sl-page-header>
    <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
      <el-button v-permission="'Create'" @click="handleCreateNetwork" type="primary">
        开通网络
      </el-button>
      <el-button @click="handleBatchRecycle" type="primary" v-permission="'BatchRecycle'">
        批量回收
      </el-button>
    </div>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <div class="table-layout">
      <dataList
        ref="dataListRef"
        :query-params="queryParams"
        :hide-operations="shouldHideResourceOperations"
      ></dataList>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { onMounted, reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp, Upload } from '@element-plus/icons-vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import { useDichooks } from '../hooks/useDichooks'
import { getCloudTypeDic, getCloudPlatformDic } from '@/api/modules/dic'
import { normalizeExportArgs } from '@/views/resourceCenter/utils'
import { useDownload } from '@/hooks/useDownload'
import { resourceExport } from '@/api/modules/resourecenter'
import { useRouter } from 'vue-router'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useRolePermission } from '../hooks/useRolePermission'

const { shouldHideResourceOperations } = useRolePermission()

const router = useRouter()
const formRef = ref<any>(null)
const queryParams = ref<any>({ type: 'network' })

const { busiSystemOptions } = useBusiSystemOptions()
const formModel = reactive({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}
function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}
const { resourcePoolsDic } = useDichooks()
// 是否默认折叠搜索项
const collapsed = ref(true)

// 获取云类型字典
const cloudTypeDic = ref<any>([])
const getCloudTypeDicList = async () => {
  const { entity } = await getCloudTypeDic(null)
  entity.forEach((i) => {
    i.catalogueDomainCode = i.code
    i.catalogueDomainName = i.name
  })
  cloudTypeDic.value = entity
}

// 获取云平台字典
const cloudPlatformDic = ref<any>([])
const getCloudPlatformDicList = async () => {
  const { entity } = await getCloudPlatformDic({ parentCode: '' })
  cloudPlatformDic.value = entity
}
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '网络名称',
        type: 'input',
        key: 'name',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSysId',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'network', '网络.xlsx', {
                      ...formModel,
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        options: cloudTypeDic,
        labelField: 'catalogueDomainName',
        valueField: 'catalogueDomainCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '网络类型',
        type: 'select',
        key: 'networkType',
        options: [
          { value: 'vlan', label: 'vlan' },
          { value: 'vxlan', label: 'vxlan' },
        ],
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        options: cloudPlatformDic,
        labelField: 'name',
        valueField: 'code',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '工单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      // {
      //   label: '状态',
      //   type: 'input',
      //   key: 'status',
      //   span: 8,
      //   disabled: false,
      //   hidden: true,
      // },
      {
        label: '申请人',
        type: 'input',
        key: 'userName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请时间',
        type: 'date',
        key: 'createdTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 批量回收
const dataListRef = ref()
const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}
function handleCreateNetwork() {
  router.push({
    path: '/networkForm',
  })
}
onMounted(() => {
  getCloudTypeDicList()
  getCloudPlatformDicList()
})
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
