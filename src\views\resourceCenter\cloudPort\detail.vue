<template>
  <div id="CloudPortDetail" class="table-box">
    <sl-page-header
      title="云端口详情"
      :icon="{
        class: 'page_yunduankou',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="150"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        ></sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getCloudPortDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  cloudPortName: '',
  businessSystemName: '',
  catalogueDomainName: '',
  platformName: '',
  regionName: '',
  azName: '',
  vpcName: '',
  vlanId: '',
  srcIp: '',
  peerIp: '',
  peerPassword: '',
  createTime: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '云端口名称',
        type: 'text',
        key: 'cloudPortName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSystemName',
        span: 8,
      },
      {
        label: '云类型',
        type: 'text',
        key: 'catalogueDomainName',
        span: 8,
      },
      {
        label: '云平台',
        type: 'text',
        key: 'platformName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'regionName',
        span: 8,
      },
      {
        label: '可用区',
        type: 'text',
        key: 'azName',
        span: 8,
      },
      {
        label: 'VPC名称',
        type: 'text',
        key: 'vpcName',
        span: 8,
      },
      {
        label: 'VLAN ID',
        type: 'text',
        key: 'vlanId',
        span: 8,
      },
      {
        label: '本段地址',
        type: 'text',
        key: 'srcIp',
        span: 8,
      },
      {
        label: 'GM2地址',
        type: 'text',
        key: 'peerIp',
        span: 8,
      },
      {
        label: 'BGP密钥',
        type: 'text',
        key: 'peerPassword',
        span: 8,
      },
      {
        label: '创建时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getCloudPortDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/cloudPortList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}
</style>
