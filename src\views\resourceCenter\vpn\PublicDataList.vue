<template>
  <div
    class="table-main"
    v-loading.fullscreen.lock="tableLoading"
    element-loading-text="操作中，请稍候..."
  >
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="getCorporateResourceList"
      :init-param="queryParams"
      :current-change="currentChange"
      @selection-change="handleSelectionChange"
      hidden-table-header
      row-key="goodsOrderId"
    >
    </SlProTable>

    <!-- 资源变更弹窗 -->
    <ResourceChangeDialog
      v-model:visible="changeDialogVisible"
      resource-type="vpn"
      :selected-resources="selectedResources"
      :allowed-change-types="allowedChangeTypes"
      @confirm="handleConfirm"
    />

    <!-- 资源延期弹窗 -->
    <ResourceChangeDialog
      v-model:visible="delayDialogVisible"
      resource-type="vpn"
      :selected-resources="selectedResources"
      :is-delay="true"
      @confirm="handleConfirm"
    />
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getCorporateResourceList, dgRecoveryWorkOrderCreate } from '@/api/modules/resourecenter'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import type { ResourceChangeType } from '../types'
import { ElMessage } from 'element-plus'
import SlMessage from '@/components/base/SlMessage'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import { useRouter } from 'vue-router'

const router = useRouter()
const { queryParams } = defineProps<{
  queryParams: Record<string, any>
}>()

const { validateResources } = useResourceChange()

// 格式化计费类型
const formatBillType = (type: string): string => {
  const billTypeMap: Record<string, string> = {
    day: '按天计费',
    month: '按月计费',
    year: '按年计费',
  }
  return billTypeMap[type] || type || '--'
}

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 表格配置项 - 对公资源字段配置
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'deviceName',
    label: 'VPN名称',
    width: 200,
    fixed: 'left',
    render: ({ row }) => (
      <el-button onClick={() => handleViewDetail(row)} type="primary" link>
        {row.deviceName}
      </el-button>
    ),
  },
  {
    prop: 'billType',
    label: '计费类型',
    width: 100,
    render: ({ row }) => formatBillType(row.billType),
  },
  { prop: 'spec', label: '最大客户端数', width: 120 },
  { prop: 'vpcName', label: 'VPC', width: 150 },
  { prop: 'subnetName', label: '子网', width: 150 },
  { prop: 'eip', label: '公网IP', width: 150 },
  { prop: 'bandWidth', label: '带宽', width: 100 },
  { prop: 'cloudPlatform', label: '云平台', width: 120 },
  { prop: 'resourcePoolName', label: '资源池', width: 120 },
  { prop: 'tenantName', label: '租户', width: 100 },
  { prop: 'orderCode', label: '订单编号', width: 150 },
  { prop: 'createTime', label: '订购时间', width: 150 },
  { prop: 'applyUserName', label: '订购人', width: 100 },
])

const proTable = ref<ProTableInstance>()
const tableLoading = ref(false)

const currentRecycleIdsList = ref<any[]>([])

// 使用回收校验钩子函数
// 使用回收校验钩子函数
const { validateUnsubscribe, validateChange, getDgFormData } = useRecycleValidation()

const handleCreateUnsubscribe = async (goodsItems: any[]) => {
  const res = await dgRecoveryWorkOrderCreate(getDgFormData('vpn', goodsItems))
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  SlMessage.success('已提交退订')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}

// 批量回收功能
const handleBatchUnsubscribe = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateUnsubscribe(selectedList, 'vpn')) {
    currentRecycleIdsList.value = selectedList.map((i) => ({ goodsId: i.id.trim() }))
    handleCreateUnsubscribe(currentRecycleIdsList.value)
  }
}

// 多选数据
const multipleSelection = ref<any[]>([])
const changeDialogVisible = ref(false)
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])

// VPN允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = ['bandwidth_expand']

// 使用计算属性缓存处理过的资源列表以提高性能
const processedResources = computed(() => {
  if (multipleSelection.value.length === 0) return []
  return multipleSelection.value.map((resource) => ({
    ...resource,
    _resourceId: resource.id, // 添加一个唯一标识，用于跟踪变化
  }))
})

// 使用计算属性判断是否禁用变更和延期按钮
const isChangeDisabled = computed(() => multipleSelection.value.length === 0)

// 处理资源变更，优化性能
const handleResourceChange = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要变更的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

// 处理资源延期，优化性能
const handleResourceDelay = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要延期的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

// 处理确认
const handleConfirm = () => {
  proTable.value?.getTableList()
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 查看详情
const handleViewDetail = (row: any) => {
  router.push({
    path: '/vpnDetail',
    query: {
      id: row.id,
      orderId: row.goodsOrderId,
      sourceType: 'DG',
    },
  })
}

// 暴露组件方法
defineExpose({
  handleBatchUnsubscribe,
  handleResourceChange,
  handleResourceDelay,
})
</script>
<style lang="scss" scoped>
.table-main {
  position: relative;
}
</style>
