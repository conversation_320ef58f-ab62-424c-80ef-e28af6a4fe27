<template>
  <div class="vmware-console-page">
    <VmwareConsole @close="handleClose" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import VmwareConsole from './VmwareConsole.vue'

// 保存原始的body样式
let originalBodyStyle = ''

onMounted(() => {
  // 保存原始样式
  originalBodyStyle = document.body.style.cssText

  // 设置body为全屏样式
  document.body.style.margin = '0'
  document.body.style.padding = '0'
  document.body.style.overflow = 'hidden'
  document.body.style.height = '100vh'
  document.body.style.width = '100vw'

  // 设置html元素样式
  document.documentElement.style.margin = '0'
  document.documentElement.style.padding = '0'
  document.documentElement.style.height = '100%'
  document.documentElement.style.width = '100%'
})

onUnmounted(() => {
  // 恢复原始的body样式
  document.body.style.cssText = originalBodyStyle

  // 恢复html元素样式
  document.documentElement.style.margin = ''
  document.documentElement.style.padding = ''
  document.documentElement.style.height = ''
  document.documentElement.style.width = ''
})

const handleClose = () => {
  // 清理sessionStorage
  sessionStorage.removeItem('sid')
  sessionStorage.removeItem('ticket')

  // 关闭当前窗口
  window.close()
}
</script>

<style scoped lang="scss">
.vmware-console-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: #000;
  z-index: 9999;
}
</style>

// 全局样式，确保页面完全全屏
<style lang="scss">
// 当VMware控制台页面加载时，移除所有可能的边距和滚动条
html,
body {
  &.vmware-fullscreen {
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    height: 100% !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
}

// 移除Vue应用容器的默认样式
#app {
  &.vmware-fullscreen {
    margin: 0 !important;
    padding: 0 !important;
    height: 100% !important;
    width: 100% !important;
  }
}
</style>
