<template>
  <div id="PmDetail" class="table-box">
    <sl-page-header
      title="裸金属详情"
      :icon="{
        class: 'page_luojinsu',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="pm-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
          <template #gpuInfoSlot>
            <span>
              {{
                detailData.gpuNum && detailData.gpuType
                  ? `${detailData.gpuNum}*${detailData.gpuType}`
                  : '-'
              }}
            </span>
          </template>
        </sl-form>

        <!-- 添加性能数据tab -->
        <div class="performance-section" v-if="detailData.ip">
          <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleTabsClick">
            <el-tab-pane
              :label="item.label"
              :name="item.name"
              v-for="item in tabs"
              :key="item.name"
            >
              <!-- 性能数据tab -->
              <PmPerformanceChart v-if="item.name === 'performance'" :ip="detailData.ip" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getResourceDetail } from '@/api/modules/resourecenter'
import PmPerformanceChart from './components/PmPerformanceChart.vue'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

// 定义标签页
const tabs = ref([{ name: 'performance', label: '性能数据' }])

// 定义激活的标签页
const activeTab = ref('performance')
const handleTabsClick = (tab: any) => {
  activeTab.value = tab.name
}

const detailData = reactive<any>({
  deviceName: '',
  handoverStatus: '',
  osVersion: '',
  gpuNum: '',
  gpuType: '',
  spec: '',
  dataDisk: '',
  ip: '',
  applyTime: '',
  tenantName: '',
  businessSysName: '',
  domainName: '',
  resourcePoolName: '',
  orderCode: '',
  resourceApplyTime: '',
  expireTime: '',
  deviceStatusCn: '',
  applyUserName: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '裸金属名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '交维状态',
        type: 'text',
        key: 'handoverStatus',
        span: 8,
      },
      {
        label: '系统版本',
        type: 'text',
        key: 'osVersion',
        span: 8,
      },
      {
        label: 'GPU/NPU信息',
        type: 'slot',
        slotName: 'gpuInfoSlot',
        span: 8,
      },
      {
        label: '规格',
        type: 'text',
        key: 'spec',
        span: 8,
      },
      {
        label: '硬盘',
        type: 'text',
        key: 'dataDisk',
        span: 8,
      },
      {
        label: 'IP',
        type: 'text',
        key: 'ip',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'domainName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'resourceApplyTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getResourceDetail({
    id: resourceId.value,
    type: 'pm',
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/pmList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: 0;
}

.pm-detail-scroll-view {
  height: 100%;
}

.sl-card {
  margin: 8px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}

.performance-section {
  margin-top: 20px;
  .demo-tabs {
    margin-top: 20px;
  }
}
</style>
