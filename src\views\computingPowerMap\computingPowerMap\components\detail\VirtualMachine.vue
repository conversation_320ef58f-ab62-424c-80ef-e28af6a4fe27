<template>
  <div class="virtualMachine">
    <div class="virtualMachine-left">
      <img src="/images/computingPower/virtualMachine.png" alt="" width="101px" height="95px" />
    </div>

    <div class="virtualMachine-rigth">
      <div class="virtualMachine-value">
        <span class="virtualMachine-value-num">{{ value }}</span>
        <span>台</span>
      </div>
      <!-- 描述 -->
      <div class="virtualMachine-desc">虚拟机数量</div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  value: string | number
}>()
</script>

<style lang="scss" scoped>
.virtualMachine {
  display: flex;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;

  .virtualMachine-rigth {
    flex: 1;
    padding-top: 20px;
    padding-left: 90px;
    font-size: 18px;
    color: #000;
    .virtualMachine-value {
      color: #004fb1;

      &-num {
        margin-right: 5px;
        font-size: 32px;
        font-weight: 700;
      }
    }
  }
}
</style>
