<template>
  <div style="width: 100%; display: flex; flex-direction: column; height: 400px">
    <div style="margin-bottom: 10px">
      <el-row :gutter="16">
        <div style="margin-right: 16px">筛选</div>
        <el-col :span="4">
          <el-input
            class="image-search-input"
            clearable
            v-model="queryParams.vcpus"
            placeholder="输入vCPU"
          >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input clearable v-model="queryParams.ram" placeholder="输入内存"></el-input>
        </el-col>
        <el-col :span="12">
          <el-button :icon="Search" @click="getEcsSpecListFn(false)" type="primary">
            查询
          </el-button>
          <!-- 重置 -->
          <el-button :icon="Refresh" @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>
    <div
      style="flex: 1; min-height: 0; overflow: hidden"
      :style="{ width: totalWidth + 20 + 'px' }"
    >
      <SlProTable
        v-if="ecsSpecList.length && !loading"
        ref="proTable"
        highlight-current-row
        :columns="columns"
        :height="300"
        :data="ecsSpecList"
        @selection-change="handleSelectionChange"
        :current-change="currentChange"
        hidden-table-header
        row-key="id"
      >
      </SlProTable>
    </div>
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, computed, watch } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getEcsSpecList } from '@/api/modules/resourecenter'
import { Search, Refresh } from '@element-plus/icons-vue'
import { showTips } from '@/utils'

const {
  form,
  item,
  type: ParamsType,
  tableParams = {},
} = defineProps<{
  form: any
  item: any
  type: string
  tableParams?: FormDataType
}>()

const emit = defineEmits({
  validate: () => true,
  changeResourcePool: () => true,
})

const model = form

const queryParams = ref({
  type: ParamsType,
  vcpus: '',
  ram: '',
})
const ecsSpecList = ref<any[]>([])

const loading = ref(false)

const resetFilters = () => {
  queryParams.value.vcpus = ''
  queryParams.value.ram = ''
  getEcsSpecListFn(false)
}

const getEcsSpecListFn = async (falg = true) => {
  try {
    loading.value = true
    let params: any = {}
    if (model.resourcePool) {
      params.regionId = model.resourcePool.id
    }

    const res = await getEcsSpecList({
      ...params,
      ...tableParams,
      ...queryParams.value,
      pageSize: 10000,
    })
    if (!res.entity?.records?.length && falg) {
      showTips('该资源池暂无该产品')
      emit('changeResourcePool')
    }
    ecsSpecList.value = res.entity?.records || []
    radioValue.value = null
    model[item.key] = null // 清空选中值
  } finally {
    loading.value = false
  }
}

watch(
  () => [model.resourcePool?.id, tableParams],
  () => {
    getEcsSpecListFn()
  },
  { deep: true, immediate: true },
)

const radioValue = ref<any>(null)

const currentChange = (currentRow: any) => {
  if (!currentRow) return
  radioValue.value = currentRow.id
  model[item.key] = currentRow
  // 抛出校验
  emit('validate')
}
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  {
    render: ({ row }) => {
      return <el-radio modelValue={radioValue.value} value={row.id} size="large"></el-radio>
    },
    label: '选择',
    width: 55,
  },
  {
    prop: 'type',
    label: '规格类型',
    minWidth: 150,
    render: ({}) => (
      <div>
        <p>通用型</p>
      </div>
    ),
  },
  { prop: 'vcpus', label: 'vCPU', minWidth: 100 },
  { prop: 'ram', label: '内存(GB)', minWidth: 120 },
  {
    prop: 'gpuType',
    label: 'GPU',
    minWidth: 200,
    render: ({ row }) => (
      <div>
        <p>{row.name?.split('/')[1] ?? '--'}</p>
      </div>
    ),
  },
])

const totalWidth = computed(() =>
  item.totalWidth
    ? item.totalWidth
    : columns.reduce((acc: number, column: any) => acc + Number(column.width || 150), 0),
)

const proTable = ref<ProTableInstance>()

// 多选数据
const multipleSelection = ref<any[]>([])

// // 处理确认
// const handleConfirm = () => {
//   proTable.value?.getTableList()
// }

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

defineExpose({
  selectedList: multipleSelection,
})
</script>
<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  border-color: #dcdfe6 !important;
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}
:deep(.el-input__wrapper):hover {
  border-color: #c0c4cc !important;
  box-shadow: 0 0 0 1px #c0c4cc inset !important;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 1px #409eff inset !important;
}
:deep(.el-select__wrapper) {
  border-color: #dcdfe6 !important;
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}
:deep(.el-select__wrapper):hover {
  border-color: #c0c4cc !important;
  box-shadow: 0 0 0 1px #c0c4cc inset !important;
}

:deep(.el-select__wrapper.is-focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 1px #409eff inset !important;
}
.warning {
  width: 90%;
  margin: 0 auto;
  margin-top: -10px;
  word-break: break-all;
  color: red;
}

// 去除表格阴影，增加边框
:deep(.sl-pro-table) {
  box-shadow: none;
  border: 1px solid #e4e7ed;

  .el-table {
    box-shadow: none;
    border: 1px solid #e4e7ed;
  }

  .el-table__header-wrapper {
    border-bottom: 1px solid #e4e7ed;
  }

  .el-table__body-wrapper {
    border: none;
  }
}
</style>
