import { getAllMenuListApi, getAuthMenuListApi } from '@/api/modules'
import { getShowMenuList, processMenuHideLogic } from '@/utils'
import { defineStore } from 'pinia'

/* AuthState */
export interface AuthState {
  routeName: string
  authButtonList: {
    [key: string]: string[]
  }
  authMenuList: Menu.MenuOptions[]
  initialRouteName: string
}

export const useAuthStore = defineStore('sl-auth', {
  state: (): AuthState => ({
    // 按钮权限列表
    authButtonList: {},
    // 菜单权限列表
    authMenuList: [],
    // 当前页面的 router name，用来做按钮权限筛选
    routeName: '',
    initialRouteName: '', // 初始跳转路由名称
  }),
  getters: {
    // 按钮权限列表
    authButtonListGet: (state) => state.authButtonList,
    // 菜单权限列表 ==> 这里的菜单没有经过任何处理
    authMenuListGet: (state) => state.authMenuList,
    // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
    showMenuListGet: (state) => {
      // 先处理菜单隐藏逻辑，再过滤显示菜单
      const processedMenuList = processMenuHideLogic(state.authMenuList, state.authButtonList)
      return getShowMenuList(processedMenuList)
    },
    initialRouteNameGet: (state) => state.initialRouteName,
  },
  actions: {
    // Get AuthButtonList
    async getAuthButtonList(list: any[]) {
      this.authButtonList = list.reduce(
        (acc, menu) => {
          acc[menu.url] = menu.menus.map((i: any) => i.url)
          return acc
        },
        {} as { [key: string]: string[] },
      )
    },
    // Get AuthMenuList
    async getAuthMenuList() {
      const { entity: allMenuList } = await getAllMenuListApi()
      const { entity: authEntity } = await getAuthMenuListApi()
      this.getAuthButtonList(authEntity)
      this.authMenuList = this.setDisabled(allMenuList, authEntity)
    },
    // Set RouteName
    setRouteName(name: string) {
      this.routeName = name
    },
    hasPermission(permission: string): boolean {
      const btnPermissions = this.authButtonList[this.routeName]
      return btnPermissions && btnPermissions.includes(permission)
    },
    // 递归函数，设置 meta.isDisabled
    setDisabled(entity: Menu.MenuOptions[], authEntity: any[]): Menu.MenuOptions[] {
      const authUrls = new Set(authEntity.map((auth) => auth.url))

      return entity.map((menu) => {
        const isAuthed = authUrls.has(menu.name)
        const isDisabled = menu.meta?.isDisabled || !isAuthed
        if (!isDisabled && !this.initialRouteName) {
          this.initialRouteName = menu.name
        }
        menu.meta = {
          ...menu.meta,
          isDisabled: isDisabled,
        }

        if (menu.children && menu.children.length > 0) {
          menu.children = this.setDisabled(menu.children, authEntity)
        }

        return menu
      })
    },
  },
  // 添加动态路由删除掉
  persist: {
    key: 'sl-auth',
    storage: localStorage,
    omit: ['initialRouteName'],
  },
})
