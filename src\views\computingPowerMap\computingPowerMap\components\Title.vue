<template>
  <div class="panel-title power-panel-title">
    <!--    <span class="panel-title-icon">-->
    <!--      <img src="./arrow.svg" alt="icon" width="24" height="24" />-->
    <!--    </span>-->
    <span class="panel-title-text">{{ title }}</span>
  </div>
</template>

<script setup lang="ts">
const { title = '算力分配状态' } = defineProps<{
  title: string
}>()
</script>

<style scoped lang="scss">
@font-face {
  font-family: 'ziHuiJingDianYaHei';
  src: url('/fonts/zhihui.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.power-panel-title {
  font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei', Arial, sans-serif;
  /*background: url('/images/dashboard/title.png') no-repeat -10px top;*/
  border-radius: 6px;
  /* /background-size: cover;*/
  color: #fff;
  font-size: 18px;
  font-weight: 100;
  padding: 10px 0 6px 24px;
  //border-top-left-radius: 6px;
  //border-top-right-radius: 6px;
  position: relative;
  display: flex;
  align-items: center;
  //box-shadow: 0 2px 8px rgba(42, 107, 255, 0.04);
}
.panel-title-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}
.panel-title-text {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.43);
  //line-height: 28px;
}
</style>
