<template>
  <div class="table-main">
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="getCorporateResourceList"
      :init-param="queryParams"
      :current-change="currentChange"
      hidden-table-header
      row-key="id"
    >
    </SlProTable>

    <!-- 绑定/解绑资源弹窗 -->
    <ResourceBindDialog
      v-model:visible="bindDialogVisible"
      :backup-type="currentBackup.backupType?.toLowerCase() || 'ecs'"
      :resource-pool-id="dialogType === 'BIND' ? currentBackup.resourcePoolId : ''"
      :dialog-type="dialogType"
      :device-id="dialogType === 'UNBIND' ? currentBackup.deviceId : ''"
      :backup-id="currentBackup.deviceId"
      source-type="DG"
      @selectDevice="handleDeviceSelect"
      @refresh="refreshTable"
    />
  </div>
</template>
<script setup lang="tsx" name="PublicDataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getCorporateResourceList, backupOperate } from '@/api/modules/resourecenter'
import { dgRecoveryWorkOrderCreate } from '@/api/modules/resourecenter'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import ResourceBindDialog from '@/views/resourceCenter/backup/components/ResourceBindDialog.vue'
import { useRecycleValidation } from '../../hooks/useRecycleValidation'
import SlMessage from '@/components/base/SlMessage'

const router = useRouter()
const { queryParams, hideOperations } = defineProps<{
  queryParams: Record<string, any>
  hideOperations?: boolean
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 格式化计费类型
const formatBillType = (type: string): string => {
  const billTypeMap: Record<string, string> = {
    day: '按天计费',
    month: '按月计费',
    year: '按年计费',
  }
  return billTypeMap[type] || type || '--'
}

// 格式化计费方式
const formatChargeType = (type: string): string => {
  const chargeTypeMap: Record<string, string> = {
    quant: '按量计费',
    require: '按需计费',
  }
  return chargeTypeMap[type] || type || '--'
}

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'deviceName',
    label: '云灾备名称',
    minWidth: 150,
    fixed: 'left',
    render: ({ row }) => (
      <el-button onClick={() => handleViewDetail(row)} type="primary" link>
        {row.deviceName}
      </el-button>
    ),
  },
  {
    prop: 'backupType',
    label: '备份类型',
    minWidth: 120,
    enum: [
      { label: '云主机', value: 'ECS' },
      {
        label: '云硬盘',
        value: 'EVS',
      },
    ],
  },
  {
    prop: 'billType',
    label: '计费类型',
    width: 100,
    render: ({ row }) => formatBillType(row.billType),
  },
  {
    prop: 'chargeType',
    label: '计费方式',
    width: 100,
    render: ({ row }) => formatChargeType(row.chargeType),
  },
  {
    prop: 'frequency',
    label: '备份频率',
    minWidth: 120,
    enum: [
      { label: '每天', value: 'days' },
      { label: '每周', value: 'weeks' },
    ],
  },
  { prop: 'daysOfWeek', label: '星期', minWidth: 100 },
  { prop: 'domainName', label: '所属云', minWidth: 150, filter: true },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 150, filter: true },
  { prop: 'azName', label: '可用区', minWidth: 120 },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'orderCode', label: '订单编号', minWidth: 150 },
  { prop: 'billId', label: '计费号', minWidth: 150 },
  { prop: 'effectiveTime', label: '订购时间', minWidth: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 },
  { prop: 'applyUserName', label: '订购人', minWidth: 100 },
  ...(!hideOperations
    ? [
        {
          prop: 'operation',
          label: '操作',
          width: 180,
          fixed: 'right',
          render: operationRender,
        },
      ]
    : []),
])

// 表格操作渲染
function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleBindResource(row)} type="primary" link>
        绑定资源
      </el-button>
      <el-button onClick={() => handleUnbindResource(row)} type="danger" link>
        解绑资源
      </el-button>
    </>
  )
}

const proTable = ref<ProTableInstance>()
const tableLoading = ref(false)

// 绑定/解绑相关
const bindDialogVisible = ref(false)
const currentBackup = ref<any>({})
const dialogType = ref<'BIND' | 'UNBIND'>('BIND')

// 查看详情
const handleViewDetail = (row: any) => {
  router.push(`/backupDetail?id=${row.id}&sourceType=DG`)
}

// 绑定资源
const handleBindResource = (row: any) => {
  currentBackup.value = row
  dialogType.value = 'BIND'
  bindDialogVisible.value = true
}

// 解绑资源
const handleUnbindResource = (row: any) => {
  currentBackup.value = row
  dialogType.value = 'UNBIND'
  bindDialogVisible.value = true
}

const bindingLoading = ref('')

// 处理设备选择
const handleDeviceSelect = async (devices: any[]) => {
  if (bindingLoading.value) return
  if (!currentBackup.value.id) return
  if (!devices || devices.length === 0) return

  try {
    bindingLoading.value = currentBackup.value.id
    tableLoading.value = true

    await backupOperate({
      type: dialogType.value,
      detailId: devices.map((item: any) => item.id),
      backupDetailId: currentBackup.value.id,
    })

    ElMessage.success(dialogType.value === 'BIND' ? '绑定设备成功' : '解绑设备成功')
    proTable.value?.getTableList()
    bindDialogVisible.value = false
    currentBackup.value = {}
  } catch (error) {
    console.error(error)
  } finally {
    bindingLoading.value = ''
    tableLoading.value = false
  }
}

// 刷新表格
const refreshTable = () => {
  if (proTable.value) {
    proTable.value.getTableList()
  }
}

// 使用回收校验钩子函数
const { validateUnsubscribe, getDgFormData } = useRecycleValidation()

const handleCreateUnsubscribe = async (goodsItems: any[]) => {
  const res = await dgRecoveryWorkOrderCreate(getDgFormData('backup', goodsItems))
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  SlMessage.success('已提交退订')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
}

const currentRecycleIdsList = ref<any[]>([])
// 批量退订功能
const handleBatchUnsubscribe = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateUnsubscribe(selectedList, 'backup')) {
    currentRecycleIdsList.value = selectedList.map((i) => ({ goodsId: i.id.trim() }))
    handleCreateUnsubscribe(currentRecycleIdsList.value)
  }
}

defineExpose({
  proTable,
  refreshTable,
  handleBatchUnsubscribe,
})
</script>
<style lang="scss" scoped>
.table-main {
  margin: 8px;
}
</style>
