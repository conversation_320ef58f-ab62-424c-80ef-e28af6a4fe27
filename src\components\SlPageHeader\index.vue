<template>
  <div class="header-fixed">
    <div class="header flx-align-center">
      <!-- 图标容器 -->
      <div class="icon-container" v-if="!icon.hidden">
        <i
          :class="icon.class"
          class="iconfont sub-icon"
          :style="{ fontSize: icon.size || 20, color: icon.color }"
          v-if="icon.class"
        ></i>
        <el-icon :size="icon.size || 20" :color="icon.color" v-else>
          <component :is="icon.Vnode" />
        </el-icon>
      </div>

      <!-- 标题区域 -->
      <div class="header-title ml5 mr5">
        <span class="title mr5" style="font-size: 18px">{{ title }}</span>

        <!-- 返回按钮，使用插槽自定义内容 -->
        <slot name="back-button">
          <el-button
            v-if="showBack"
            type="primary"
            :icon="Back"
            @click="goBack"
            class="back-button"
          >
            {{ back?.title || '返回' }}
          </el-button>
        </slot>

        <!-- 描述区域 -->
        <span class="ml5">
          <slot name="detail">
            <span v-if="detail" class="detail-content">{{ detail }}</span>
          </slot>
        </span>

        <!-- 标题描述，可通过titleLine传入描述内容 -->
        <div v-if="titleLine" class="title-line">
          {{ titleLine }}
        </div>
      </div>
      <div class="header-right-custom">
        <slot name="header-right-custom"></slot>
      </div>
    </div>

    <div class="header-custom">
      <!-- 自定义插槽 -->
      <slot name="custom"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Platform, Back } from '@element-plus/icons-vue'

// Props 类型定义
interface Props {
  title: string // 页面标题
  detail?: string // 描述内容
  titleLine?: string // 标题下方的描述行
  icon?: {
    Vnode?: any // 图标组件
    size?: number | string // 图标大小
    color?: string // 图标颜色
    hidden?: boolean // 是否隐藏图标
    class?: string // 图标类名
  }
  showBack?: boolean // 是否显示返回按钮
  back?: {
    title: string // 返回按钮的标题
    function: () => void // 返回按钮的回调函数
  }
}

// 使用 withDefaults 为 Props 提供默认值
const props = withDefaults(defineProps<Props>(), {
  icon: () => ({
    Vnode: Platform,
    size: 20,
    color: '#409eff',
    hidden: false,
    class: '',
  }),
  showBack: false,
})

// Vue Router 实现返回功能或自定义返回功能
const router = useRouter()
const goBack = () => {
  if (props.back && props.back.function) {
    // 如果提供了back回调函数，则执行它
    props.back.function()
  } else {
    // 否则调用Vue Router的返回
    router.back()
  }
}
</script>

<style lang="scss" scoped>
.header-fixed {
  //固定定位
  position: sticky;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header {
  background: #fff;
  padding: 20px 10px;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 20px;
  font-weight: 700;
  line-height: 23px;
}

.back-button {
  margin-top: -3px;
}

.title-line {
  font-size: 14px;
  line-height: 14px;
  color: #666;
  margin-top: 4px;
}

.detail-content {
  font-size: 12px;
  color: var(--el-color-primary);
  background: rgba(72, 127, 239, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}
.header-custom {
  width: 100%;
}

.header-right-custom {
  position: absolute;
  right: 10px;
  display: flex;
  align-items: center;
}
</style>
