<template>
  <div>
    <sl-form
      class="goods-info-form"
      size="small"
      ref="slFormRef"
      :options="goodsInfoOptions"
      :model-value="goods.orderJson"
    >
      <!-- 删除按钮 -->
      <template #globalFormSlot>
        <div @click="handleGoodsDelete" class="goods-del-btn">
          <el-icon><CircleCloseFilled /></el-icon>
        </div>
      </template>
      <template #frequencySlot="{ form, item }">
        <el-select
          style="flex: 1"
          clearable
          v-model="form[item.key]"
          v-bind="item.props?.select"
          :disabled="item.disabled"
          @change="(value) => handleFrequencyChange(value, form)"
        >
          <el-option
            :key="option.value"
            v-for="option in item.options"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
    </sl-form>
  </div>
</template>
<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import type { IBackupModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import { useRoute } from 'vue-router'
import { validateGoodsName } from '@/views/resourceCenter/utils'

const rouete = useRoute()

const props = defineProps<{
  goods: IGoodsItem<IBackupModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: rouete.query.orderId ? true : false,
  })
}

const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IBackupModel>) {
  goods.ref = slFormRef
}

const daysOfWeekDisabled = computed(() => {
  return props.goods.orderJson.frequency !== 'weeks'
})

function handleFrequencyChange(value: string, form: IBackupModel) {
  form.daysOfWeek = undefined
  if (slFormRef.value) {
    slFormRef.value.clearValidate(['daysOfWeek'])
  }
}

function validateDaysOfWeek(rule: any, value: any, callback: any) {
  if (daysOfWeekDisabled.value) callback()
  else {
    validateEmpty(rule, value, callback)
  }
}

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '策略名称',
        type: 'input',
        key: 'jobName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入策略名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '备份类型',
        type: 'select',
        key: 'backupType',
        options: [
          { label: '云主机', value: 'ECS' },
          {
            label: '云硬盘',
            value: 'EVS',
          },
        ],
        span: 8,
        rules: {
          required: true,
          message: '请选择备份类型',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '备份频率',
        type: 'slot',
        key: 'frequency',
        slotName: 'frequencySlot',
        options: [
          { label: '每天', value: 'days' },
          { label: '每周', value: 'weeks' },
        ],
        span: 8,
        rules: {
          required: true,
          message: '请选择备份频率',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '星期',
        type: 'select',
        key: 'daysOfWeek',
        span: 8,
        props: {
          select: {
            disabled: daysOfWeekDisabled,
          },
        },
        options: [
          { label: '1', value: '1' },
          { label: '2', value: '2' },
          { label: '3', value: '3' },
          { label: '4', value: '4' },
          { label: '5', value: '5' },
          { label: '6', value: '6' },
          { label: '7', value: '7' },
        ],
        rules: [
          {
            validator: validateDaysOfWeek,
            message: '请选择星期',
            trigger: ['blur', 'change'],
          },
        ],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.eip-item {
  width: 100%;
  margin-bottom: 15px;
  .eip-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .eip-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
