import {
  shoppingCartCreate,
  shoppingCartUpdate,
  shoppingCartList,
  shoppingCartDelete,
} from '@/api/modules/resourecenter'
import SlMessage from '@/components/base/SlMessage'
import { reactive, onUnmounted, type Reactive, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import eventBus from '@/utils/eventBus'
import {
  useBaseModel,
  useEvsModel,
  useEcsModel,
  useGcsModel,
  useNatModel,
  useObsModel,
  useSlbModel,
  useEipModel,
  useCqModel,
  useMysqlModel,
  useRedisModel,
  useBackupModel,
  useNasModel,
  useVpnModel,
  useKafkaModel,
  useEsModel,
  useFlinkModel,
  usePmModel,
  useBldRedisModel,
  type IEcsModel,
  type IBaseModel,
  type IEvsModel,
  type IGcsModel,
  type INatModel,
  type IObsModel,
  type ISlbModel,
  type IEipModel,
  type ICqModel,
  type IMysqlModel,
  type IRedisModel,
  type IBackupModel,
  type INasModel,
  type IVpnModel,
  type IKafkaModel,
  type IEsModel,
  type IFlinkModel,
  type IPmModel,
  type IBldRedisModel,
} from './useGoodsModels'
import { displayBackOrder } from '@/api/modules/resourecenter'
import { generateRandom8DigitNumber } from '@/utils'

type GoodsType =
  | 'ecs'
  | 'gcs'
  | 'obs'
  | 'slb'
  | 'evs'
  | 'nat'
  | 'base'
  | 'eip'
  | 'cq'
  | 'mysql'
  | 'redis'
  | 'backup'
  | 'nas'
  | 'vpn'
  | 'kafka'
  | 'es'
  | 'flink'
  | 'pm'
  | 'bldRedis'
export interface IGoodsItem<T> {
  id: number
  goodsType: GoodsType
  orderJson: T
  ref?: Reactive<any>
  a4FormRef?: Reactive<any>
}

export interface ICartsModel {
  ecsList: IGoodsItem<IEcsModel>[]
  gcsList: IGoodsItem<IGcsModel>[]
  obsList: IGoodsItem<IObsModel>[]
  slbList: IGoodsItem<ISlbModel>[]
  evsList: IGoodsItem<IEvsModel>[]
  natList: IGoodsItem<INatModel>[]
  eipList: IGoodsItem<IEipModel>[]
  baseList: IGoodsItem<IBaseModel>[]
  cqList: IGoodsItem<ICqModel>[]
  mysqlList: IGoodsItem<IMysqlModel>[]
  redisList: IGoodsItem<IRedisModel>[]
  backupList: IGoodsItem<IBackupModel>[]
  nasList: IGoodsItem<INasModel>[]
  vpnList: IGoodsItem<IVpnModel>[]
  kafkaList: IGoodsItem<IKafkaModel>[]
  esList: IGoodsItem<IEsModel>[]
  flinkList: IGoodsItem<IFlinkModel>[]
  pmList: IGoodsItem<IPmModel>[]
  bldRedisList: IGoodsItem<IBldRedisModel>[]
}
type CartListKey = keyof Omit<ICartsModel, 'baseList'>

/**
 * 新增产品记录
 * @param goodsType 商品类型
 * @returns
 */
export async function createRecord(goodsType: GoodsType): Promise<number> {
  const res = await shoppingCartCreate({
    [`${goodsType}List`]: [
      {
        goodsType,
        orderJson: {},
      },
    ],
  })
  if (res.code === 200) {
    return res.entity as number
  }
  SlMessage.error(res.message || '接口请求失败')
  return 0
}

export async function updateRecord(cartsModel: ICartsModel): Promise<boolean> {
  const keys = [
    'ecsList',
    'evsList',
    'gcsList',
    'obsList',
    'slbList',
    'natList',
    'baseList',
    'eipList',
    'cqList',
    'mysqlList',
    'redisList',
    'backupList',
    'nasList',
    'vpnList',
    'kafkaList',
    'esList',
    'flinkList',
    'pmList',
    'bldRedisList',
  ] as const
  const params: any = {}
  keys.forEach((key) => {
    if (cartsModel[key].length) {
      params[key] = cartsModel[key].map((ele) => ({
        id: ele.id,
        goodsType: ele.goodsType,
        orderJson: ele.orderJson,
      }))
    }
  })
  const res = await shoppingCartUpdate(params)
  if (res.code === 200) {
    SlMessage.success('暂存成功')
    return true
  }
  SlMessage.error(res.message || '接口请求失败')
  return false
}

async function clearRecord(cartsModel: ICartsModel): Promise<boolean> {
  const keys = [
    'ecsList',
    'evsList',
    'gcsList',
    'obsList',
    'slbList',
    'natList',
    'baseList',
    'eipList',
    'cqList',
    'mysqlList',
    'redisList',
    'backupList',
    'nasList',
    'vpnList',
    'kafkaList',
    'esList',
    'flinkList',
    'pmList',
    'bldRedisList',
  ] as const
  const ids: number[] = []
  keys.forEach((key) => {
    if (cartsModel[key].length) {
      ids.push(...cartsModel[key].map((ele) => ele.id))
    }
  })
  const res = await deleteRecord(ids)
  return res
}

export async function deleteRecord(ids: number[], type: GoodsType | '' = ''): Promise<boolean> {
  const res = await shoppingCartDelete({ ids: ids.join(','), type })
  if (res.code === 200) {
    return true
  }
  return false
}

async function loadDatafromOrderEcho(cartsModel: ICartsModel, orderId: string): Promise<void> {
  const { entity, code } = await displayBackOrder({ workOrderId: orderId, aggregation: true })
  if (code !== 200) return
  const baseJson = cartsModel.baseList[0].orderJson
  baseJson.id = orderId
  baseJson.files = entity.resourceApplyFiles || []
  baseJson.orderTitle = entity.orderTitle
  baseJson.busiDepartLeaderId = entity.busiDepartLeaderId
  baseJson.levelThreeLeaderId = entity.levelThreeLeaderId
  baseJson.moduleId = entity.moduleId
  baseJson.orderDesc = entity.orderDesc
  baseJson.busiSystemId = entity.busiSystemId
  baseJson.a4Account = entity.cqList?.[0]?.a4Account
  baseJson.a4Phone = entity.cqList?.[0]?.a4Phone

  cartsModel.ecsList =
    entity.ecsModelList?.map((goods: any) => {
      const orderJson = useEcsModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.disasterRecovery = goods.disasterRecovery ? '1' : '0'
      orderJson.eipValue = goods.bindPublicIp ? Number(goods.eipModelList?.[0]?.bandwidth || 0) : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.planeValue = goods.plane?.split(',') || []
      orderJson.functionalModule = String(goods.functionalModule || '')
      orderJson.ecs = [goods.flavorType || '', goods.flavorName || '']
      orderJson.isMountEvs = goods.mountDataDisk ? '1' : '0'
      orderJson.evs = goods.mountDataDiskList?.map((item: any) => [
        String(item.sysDiskType || ''),
        item.sysDiskSize || 0,
      ])
      orderJson.sysDisk = [String(goods.sysDiskType || ''), Number(goods.sysDiskSize || 0)]
      orderJson.imageOs = [goods.imageOs, goods.imageVersion]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'ecs',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.mysqlList =
    entity.mysqlModelList?.map((goods: any) => {
      const orderJson = useMysqlModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.disasterRecovery = goods.disasterRecovery ? '1' : '0'
      orderJson.eipValue = goods.bindPublicIp ? Number(goods.eipModelList?.[0]?.bandwidth || 0) : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.planeValue = goods.plane?.split(',') || []
      orderJson.functionalModule = String(goods.functionalModule || '')
      orderJson.ecs = [goods.flavorType || '', goods.flavorName || '']
      orderJson.isMountEvs = goods.mountDataDisk ? '1' : '0'
      orderJson.evs = goods.mountDataDiskList?.map((item: any) => [
        String(item.sysDiskType || ''),
        item.sysDiskSize || 0,
      ])
      orderJson.sysDisk = [String(goods.sysDiskType || ''), Number(goods.sysDiskSize || 0)]
      orderJson.imageOs = [goods.imageOs, goods.imageVersion]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      orderJson.deployType = goods.deployType
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'mysql',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.redisList =
    entity.redisModelList?.map((goods: any) => {
      const orderJson = useRedisModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.disasterRecovery = goods.disasterRecovery ? '1' : '0'
      orderJson.eipValue = goods.bindPublicIp ? Number(goods.eipModelList?.[0]?.bandwidth || 0) : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.planeValue = goods.plane?.split(',') || []
      orderJson.functionalModule = String(goods.functionalModule || '')
      orderJson.ecs = [goods.flavorType || '', goods.flavorName || '']
      orderJson.isMountEvs = goods.mountDataDisk ? '1' : '0'
      orderJson.evs = goods.mountDataDiskList?.map((item: any) => [
        String(item.sysDiskType || ''),
        item.sysDiskSize || 0,
      ])
      orderJson.sysDisk = [String(goods.sysDiskType || ''), Number(goods.sysDiskSize || 0)]
      orderJson.imageOs = [goods.imageOs, goods.imageVersion]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'redis',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.gcsList =
    entity.gcsModelList?.map((goods: any) => {
      const orderJson = useGcsModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.disasterRecovery = goods.disasterRecovery ? '1' : '0'
      orderJson.eipValue = goods.bindPublicIp ? Number(goods.eipModelList?.[0]?.bandwidth || 0) : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.planeValue = goods.plane?.split(',') || []
      orderJson.functionalModule = String(goods.functionalModule || '')
      orderJson.gcs = [goods.flavorType || '', goods.flavorName || '']
      orderJson.isMountEvs = goods.mountDataDisk ? '1' : '0'
      orderJson.evs = goods.mountDataDiskList?.map((item: any) => [
        String(item.sysDiskType || ''),
        item.sysDiskSize || 0,
      ])

      orderJson.sysDisk = [String(goods.sysDiskType || ''), Number(goods.sysDiskSize || 0)]
      orderJson.imageOs = [goods.imageOs, goods.imageVersion]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'gcs',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.evsList =
    entity.evsModelList?.map((goods: any) => {
      const orderJson = useEvsModel()
      orderJson.isMountEcs = goods.vmId ? '1' : '0'
      orderJson.ecsName = goods.vmName
      orderJson.vmId = goods.vmId
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.functionalModule = goods.functionalModule
      orderJson.evs = [[goods.sysDiskType, goods.sysDiskSize]]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'evs',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.eipList =
    entity.eipModelList?.map((goods: any) => {
      const orderJson = useEipModel()
      orderJson.eipValue = goods.bandwidth
      orderJson.ecsName = goods.vmName
      orderJson.vmId = goods.vmId
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.functionalModule = goods.functionalModule
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'eip',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.obsList =
    entity.obsModelList?.map((goods: any) => {
      const orderJson = useObsModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.functionalModule = goods.functionalModule
      orderJson.obs = [goods.storageDiskType, goods.storageDiskSize]
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'obs',
        orderJson: orderJson,
      }
    }) || []

  cartsModel.slbList =
    entity.slbModelList?.map((goods: any) => {
      const orderJson = useSlbModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.eipValue = goods.bindPublicIp ? goods.eipModelList?.[0]?.bandwidth || 0 : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.functionalModule = goods.functionalModule
      orderJson.slb = goods.flavorName
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'slb',
        orderJson: orderJson,
      }
    }) || []

  cartsModel.natList =
    entity.natModelList?.map((goods: any) => {
      const orderJson = useNatModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.eipValue = goods.bindPublicIp ? goods.eipModelList?.[0]?.bandwidth || 0 : 0
      orderJson.isBindPublicNetworkIp = goods.bindPublicIp ? '1' : '0'
      orderJson.functionalModule = goods.functionalModule
      orderJson.nat = goods.flavorName
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'nat',
        orderJson: orderJson,
      }
    }) || []

  cartsModel.cqList =
    entity.cqModelList?.map((goods: any) => {
      const orderJson = useCqModel()
      orderJson.cpu[0] = goods.vCpus
      orderJson.cpu[1] = goods.ram
      orderJson.gpu[0] = goods.gpuRatio
      orderJson.gpu[1] = goods.gpuVirtualMemory
      orderJson.gpu[2] = goods.gpuCore
      orderJson.time = goods.applyTime
      orderJson.instanceName = goods.originName
      orderJson.isUseGpu = goods.gpuRatio ? '1' : '0'
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'cq',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.backupList =
    entity.backupModelList?.map((goods: any) => {
      const orderJson = useBackupModel()
      orderJson.jobName = goods.originName
      orderJson.backupType = goods.backupType
      orderJson.frequency = goods.frequency
      orderJson.daysOfWeek = goods.daysOfWeek
      orderJson.objectIdList = goods.objectIdList
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'backup',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.nasList =
    entity.nasModelList?.map((goods: any) => {
      const orderJson = useNasModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      orderJson.storageSize = goods.storageSize
      orderJson.storagePath = goods.path
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'nas',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.vpnList =
    entity.vpnModelList?.map((goods: any) => {
      const orderJson = useVpnModel()
      orderJson.numbers = goods.openNum
      orderJson.goodsId = goods.id
      orderJson.instanceName = goods.originName
      orderJson.time = goods.applyTime
      orderJson.maxClient = goods.maxConnection
      orderJson.bandwidth = goods.bandwidth
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'vpn',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.kafkaList =
    entity.kafkaModelList?.map((goods: any) => {
      const orderJson = useKafkaModel()
      orderJson.instanceName = goods.originName
      orderJson.partition = goods.partition
      orderJson.replication = goods.replication
      orderJson.retentionTime = goods.retainTime
      orderJson.dataFlow = Number(goods.dataFlow || 0)
      orderJson.dataStorage = Number(goods.dataStorageTotal || 0)
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'kafka',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.esList =
    entity.esModelList?.map((goods: any) => {
      const orderJson = useEsModel()
      orderJson.instanceName = goods.originName
      orderJson.dailyIncrementData = Number(goods.averageDailyIncrementData || 0)
      orderJson.retentionTime = goods.retainTime
      orderJson.replication = goods.numberOfReplicas
      orderJson.diskSize = Number(goods.diskSize || 0)
      orderJson.time = goods.applyTime
      orderJson.template = goods.indexTemplate ? JSON.stringify(goods.indexTemplate) : ''
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'es',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.flinkList =
    entity.flinkModelList?.map((goods: any) => {
      const orderJson = useFlinkModel()
      orderJson.instanceName = goods.originName
      orderJson.vCPU = goods.vCpus
      orderJson.memory = goods.ram
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'flink',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.bldRedisList =
    entity.bldRedisModelList?.map((goods: any) => {
      const orderJson = useBldRedisModel()
      orderJson.instanceName = goods.originName
      orderJson.ip = goods.ip
      orderJson.time = goods.applyTime
      orderJson.cpuArch = goods.cpuArchitecture
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'bldRedis',
        orderJson: orderJson,
      }
    }) || []
  cartsModel.pmList =
    entity.pmModelList?.map((goods: any) => {
      const orderJson = usePmModel()
      orderJson.instanceName = goods.originName
      orderJson.cpu = goods.vCpus
      orderJson.memory = goods.ram
      orderJson.disk = goods.diskSize
      orderJson.isUseGpu = goods.gpuType ? '1' : '0'
      orderJson.gpuType = goods.gpuType || ''
      orderJson.gpuCount = goods.gpuNum || 0
      orderJson.gpuCardType = goods.gpuCardType || ''
      orderJson.time = goods.applyTime
      return {
        id: generateRandom8DigitNumber(),
        goodsType: 'pm',
        orderJson: orderJson,
      }
    }) || []
}
async function loadDatafromShoppingCarts(cartsModel: ICartsModel): Promise<void> {
  const { entity, code } = await shoppingCartList()
  if (code === 200) {
    if (!entity.baseList?.length) {
      const baseId = await createRecord('base')
      if (baseId) {
        cartsModel.baseList[0].id = baseId
        Object.assign(cartsModel.baseList[0].orderJson, useBaseModel())
        nextTick(() => {
          cartsModel.baseList[0].ref.clearValidate()
        })
      }
    } else {
      const item: { id: number; goodsType: GoodsType; orderJson: string } = entity.baseList[0]
      cartsModel.baseList[0].id = item.id
      cartsModel.baseList[0].goodsType = item.goodsType
      cartsModel.baseList[0].orderJson = Object.assign(
        cartsModel.baseList[0].orderJson,
        item.orderJson,
      )
    }
    const keys: CartListKey[] = [
      'ecsList',
      'evsList',
      'gcsList',
      'obsList',
      'slbList',
      'natList',
      'eipList',
      'cqList',
      'mysqlList',
      'redisList',
      'backupList',
      'nasList',
      'vpnList',
      'kafkaList',
      'esList',
      'flinkList',
      'pmList',
      'bldRedisList',
    ]
    keys.forEach((list: CartListKey) => {
      cartsModel[list] =
        entity[list]?.map((ele: { id: number; goodsType: GoodsType; orderJson: string }) => {
          let parsedOrderJson:
            | IEcsModel
            | IEvsModel
            | IGcsModel
            | IObsModel
            | ISlbModel
            | INatModel
            | IEipModel
            | ICqModel
            | IMysqlModel
            | IRedisModel
            | IBackupModel
            | INasModel
            | IVpnModel
            | IKafkaModel
            | IEsModel
            | IFlinkModel
            | IPmModel
            | IBldRedisModel
          switch (ele.goodsType) {
            case 'ecs':
              parsedOrderJson = Object.assign(useEcsModel(), ele.orderJson)
              break
            case 'evs':
              parsedOrderJson = Object.assign(useEvsModel(), ele.orderJson)
              break
            case 'gcs':
              parsedOrderJson = Object.assign(useGcsModel(), ele.orderJson)
              break
            case 'obs':
              parsedOrderJson = Object.assign(useObsModel(), ele.orderJson)
              break
            case 'slb':
              parsedOrderJson = Object.assign(useSlbModel(), ele.orderJson)
              break
            case 'nat':
              parsedOrderJson = Object.assign(useNatModel(), ele.orderJson)
              break
            case 'eip':
              parsedOrderJson = Object.assign(useEipModel(), ele.orderJson)
              break
            case 'cq':
              parsedOrderJson = Object.assign(useCqModel(), ele.orderJson)
              break
            case 'mysql':
              parsedOrderJson = Object.assign(useMysqlModel(), ele.orderJson)
              break
            case 'redis':
              parsedOrderJson = Object.assign(useRedisModel(), ele.orderJson)
              break
            case 'backup':
              parsedOrderJson = Object.assign(useBackupModel(), ele.orderJson)
              break
            case 'nas':
              parsedOrderJson = Object.assign(useNasModel(), ele.orderJson)
              break
            case 'vpn':
              parsedOrderJson = Object.assign(useVpnModel(), ele.orderJson)
              break
            case 'kafka':
              parsedOrderJson = Object.assign(useKafkaModel(), ele.orderJson)
              break
            case 'es':
              parsedOrderJson = Object.assign(useEsModel(), ele.orderJson)
              break
            case 'flink':
              parsedOrderJson = Object.assign(useFlinkModel(), ele.orderJson)
              break
            case 'pm':
              parsedOrderJson = Object.assign(usePmModel(), ele.orderJson)
              break
            case 'bldRedis':
              parsedOrderJson = Object.assign(useBldRedisModel(), ele.orderJson)
              break
            default:
              throw new Error(`Unknown goods type: ${ele.goodsType}`)
          }

          return {
            id: ele.id,
            goodsType: ele.goodsType,
            orderJson: parsedOrderJson,
          }
        }) || []
    })
  }
  return
}
export function useShoppingCarts() {
  const route = useRoute()
  const cartsModel: ICartsModel = reactive({
    baseList: [
      {
        id: 0,
        goodsType: 'base',
        orderJson: useBaseModel(),
      },
    ],
    ecsList: [],
    evsList: [],
    gcsList: [],
    obsList: [],
    slbList: [],
    natList: [],
    eipList: [],
    cqList: [],
    mysqlList: [],
    redisList: [],
    backupList: [],
    nasList: [],
    vpnList: [],
    kafkaList: [],
    esList: [],
    flinkList: [],
    pmList: [],
    bldRedisList: [],
  })
  if (route.query.orderId) {
    // 从工单回显数据
    loadDatafromOrderEcho(cartsModel, route.query.orderId as string)
  } else {
    // 从暂存回显数据
    loadDatafromShoppingCarts(cartsModel)
  }

  eventBus.on('shoppingCarts:addGoods', handleAddGoods)
  eventBus.on('shoppingCarts:deleteGoods', handleDelGoods)
  eventBus.on('shoppingCarts:updateGoods', handleUpdateGoods)
  eventBus.on('shoppingCarts:clear', handleClear)
  eventBus.on('shoppingCarts:refresh', handleRefresh)

  onUnmounted(() => {
    eventBus.off('shoppingCarts:addGoods')
    eventBus.off('shoppingCarts:handleDelGoods')
    eventBus.off('shoppingCarts:updateGoods')
    eventBus.off('shoppingCarts:clear')
    eventBus.off('shoppingCarts:refresh')
  })
  function handleRefresh() {
    eventBus.emit('shoppingCarts:updateCount')
    if (route.query.orderId) {
      loadDatafromOrderEcho(cartsModel, route.query.orderId as string)
    } else {
      loadDatafromShoppingCarts(cartsModel)
    }
  }
  async function handleClear() {
    const res = await clearRecord(cartsModel)
    if (res) {
      eventBus.emit('shoppingCarts:updateCount')
      loadDatafromShoppingCarts(cartsModel)
    }
  }

  function handleUpdateGoods(goods: any) {
    console.log('shoppingCarts:updateGoods', goods)
    updateRecord(cartsModel)
  }

  async function handleDelGoods({ goods, isEdit }: any) {
    const res = !isEdit ? await deleteRecord([goods.id], goods.goodsType) : true
    if (res) {
      if (!isEdit) eventBus.emit('shoppingCarts:updateCount')
      const listKey = `${goods.goodsType}List` as CartListKey
      const list = cartsModel[listKey]
      const index = list.findIndex((item: any) => item.id === goods.id)
      if (index > -1) {
        list.splice(index, 1)
      }
    }
  }

  async function handleAddGoods({ goodsType, isEdit }: any) {
    console.log('shoppingCarts:addGoods', goodsType)
    setTimeout(() => {
      const scrollView = document.querySelector('.shopping-carts-scroll-view')
      scrollView?.scrollTo({
        top: scrollView?.scrollHeight,
        behavior: 'smooth',
      })
    }, 200)
    // 暂存时，请求ID，创建记录；编辑时，生成随机ID，不创建记录
    const goodsId = !isEdit ? await createRecord(goodsType) : generateRandom8DigitNumber()
    if (goodsId) {
      if (!isEdit) eventBus.emit('shoppingCarts:updateCount')
      switch (goodsType) {
        case 'ecs':
          cartsModel.ecsList.push({
            id: goodsId,
            goodsType: 'ecs',
            orderJson: useEcsModel(),
          })
          break
        case 'obs':
          cartsModel.obsList.push({
            id: goodsId,
            goodsType: 'obs',
            orderJson: useObsModel(),
          })
          break
        case 'slb':
          cartsModel.slbList.push({
            id: goodsId,
            goodsType: 'slb',
            orderJson: useSlbModel(),
          })
          break
        case 'nat':
          cartsModel.natList.push({
            id: goodsId,
            goodsType: 'nat',
            orderJson: useNatModel(),
          })
          break

        case 'gcs':
          cartsModel.gcsList.push({
            id: goodsId,
            goodsType: 'gcs',
            orderJson: useGcsModel(),
          })
          break
        case 'evs':
          cartsModel.evsList.push({
            id: goodsId,
            goodsType: 'evs',
            orderJson: useEvsModel(),
          })
          break
        case 'eip':
          cartsModel.eipList.push({
            id: goodsId,
            goodsType: 'eip',
            orderJson: useEipModel(),
          })
          break
        case 'cq':
          cartsModel.cqList.push({
            id: goodsId,
            goodsType: 'cq',
            orderJson: useCqModel(),
          })
          break
        case 'mysql':
          cartsModel.mysqlList.push({
            id: goodsId,
            goodsType: 'mysql',
            orderJson: useMysqlModel(),
          })
          break
        case 'redis':
          cartsModel.redisList.push({
            id: goodsId,
            goodsType: 'redis',
            orderJson: useRedisModel(),
          })
          break
        case 'backup':
          cartsModel.backupList.push({
            id: goodsId,
            goodsType: 'backup',
            orderJson: useBackupModel(),
          })
          break
        case 'nas':
          cartsModel.nasList.push({
            id: goodsId,
            goodsType: 'nas',
            orderJson: useNasModel(),
          })
          break
        case 'vpn':
          cartsModel.vpnList.push({
            id: goodsId,
            goodsType: 'vpn',
            orderJson: useVpnModel(),
          })
          break
        case 'kafka':
          cartsModel.kafkaList.push({
            id: goodsId,
            goodsType: 'kafka',
            orderJson: useKafkaModel(),
          })
          break
        case 'es':
          cartsModel.esList.push({
            id: goodsId,
            goodsType: 'es',
            orderJson: useEsModel(),
          })
          break
        case 'flink':
          cartsModel.flinkList.push({
            id: goodsId,
            goodsType: 'flink',
            orderJson: useFlinkModel(),
          })
          break
        case 'pm':
          cartsModel.pmList.push({
            id: goodsId,
            goodsType: 'pm',
            orderJson: usePmModel(),
          })
          break
        case 'bldRedis':
          cartsModel.bldRedisList.push({
            id: goodsId,
            goodsType: 'bldRedis',
            orderJson: useBldRedisModel(),
          })
          break
        default:
          throw new Error(`Unknown goods type: ${goodsType}`)
      }
    }
  }
  return cartsModel
}
