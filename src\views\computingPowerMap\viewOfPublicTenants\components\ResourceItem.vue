<template>
  <div class="resource">
    <div class="resource-header">
      <img :src="detail.img" />
      <span>{{ detail.name }}</span>
      <el-button type="primary" link @click="handleRouterGo(detail.key)"> 订购 </el-button>
      <!-- v-permission="'AddToCart'" -->
    </div>
    <div class="resource-body">
      <div class="resource-amount" @click="handleRouterToList(detail.path)">
        <span>{{ detail.data?.amount }}</span>
        <span class="unit">{{ detail.unit }}</span>
      </div>
      <div class="resource-ratio" :class="detail.data?.diffAmount! >= 0 ? 'increase' : 'decrease'">
        {{ detail.data?.ratio }}
      </div>
    </div>
    <div class="resource-footer">
      本月{{ detail.data?.diffAmount! >= 0 ? '增加' : '减少' }}
      <span :class="detail.data?.diffAmount! >= 0 ? 'increase' : 'decrease'">{{
        detail.data?.diffAmount! >= 0 ? detail.data?.diffAmount : -detail.data?.diffAmount!
      }}</span>
      相比上个月
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

withDefaults(
  defineProps<{
    detail: FormDataType
  }>(),
  {},
)

const router = useRouter()
const handleRouterGo = (key: string) => {
  router.push(`/corporate/${key}`)
}

/**
 * 跳转至对应资源中心页面
 * @param key
 */
const handleRouterToList = (key: string) => {
  router.push(`/${key}List`)
}
</script>

<style lang="scss" scoped>
.resource {
  width: 100%;
  padding-bottom: 20px;

  border-bottom: 1px solid #eee;

  .resource-header {
    height: 50px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    background-color: #f3f4f9;
    img {
      width: 50px;
      height: 50px;
    }
    & > span {
      width: 100%;
      margin-left: 10px;
    }
    .cart {
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--el-color-primary);
      cursor: pointer;
    }
    .width-40 {
      width: 40px;
      height: 40px;
      margin-left: 5px;
      margin-right: 5px;
    }
    .width-30 {
      width: 30px;
      height: 30px;
      margin-left: 5px;
      margin-right: 5px;
    }
  }
  .resource-body {
    height: 50px;
    padding: 10px;
    padding-bottom: 0;
    display: flex;
    align-items: center;
    .resource-amount {
      font-size: 28px;
      cursor: pointer;
      .unit {
        font-size: 20px;
      }
    }
    .resource-ratio {
      padding: 0 5px;
      height: 24px;
      margin-left: 10px;
      border-radius: 10px;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      &.increase {
        color: var(--el-color-primary);
        background-color: #ecf2fd;
      }
      &.decrease {
        color: #fcb72e;
        background-color: #fff1dd;
      }
    }
  }
  .resource-footer {
    padding: 0 10px;
    font-size: 14px;
    .increase {
      color: #70cda8;
    }
    .decrease {
      color: #fcb72e;
    }
  }
}
</style>
