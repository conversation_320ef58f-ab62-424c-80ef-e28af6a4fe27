<template>
  <section class="panel power-panel" :class="{ active: props.isActive }">
    <div class="card-content">
      <Title title="绿色节能" />
      <div class="saveEnergy">
        <div class="saveEnergyTop">
          <div class="saveEnergyTopItem">
            <p>工程态</p>
            <p>
              <img src="/images/computingPower/comPowerIconGongCheng.png" alt="" />
              <span>{{ dataInfo.gongchengNum }}</span>
              <b>台</b>
            </p>
          </div>
          <div class="saveEnergyTopItem">
            <p>在网态</p>
            <p>
              <img src="/images/computingPower/comPowerIconWangLuo.png" alt="" />
              <span>{{ dataInfo.zaiwangNum }}</span>
              <b>台</b>
            </p>
          </div>
          <div class="saveEnergyTopItem" style="">
            <p>节约能耗</p>
            <p>
              <img src="/images/computingPower/comPowerIconJieNeng.png" alt="" />
              <span>{{ dataInfo.savedEnergy }}</span>
              <b>w</b>
            </p>
          </div>
          <div class="saveEnergyTopItem" style="">
            <p>节约电量</p>
            <p>
              <img src="/images/computingPower/comPowerIconJieNeng.png" alt="" />
              <span>{{ dataInfo.savedElectricity }}</span>
              <b>度</b>
            </p>
          </div>

          <!--          </div>-->
        </div>
        <div v-if="reportList.length > 0">
          <div ref="pieChartRef3" style="width: 100%; height: 160px; margin-top: 15px"></div>
        </div>
        <div class="comPowerNoData" v-else>暂无数据</div>
      </div>
    </div>
  </section>
  <!--  <Card>-->
  <!--  </Card>-->
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeMount, watch, nextTick } from 'vue'
import Title from './Title.vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import 'echarts-gl' // 引入 3D 扩展

import { getComPowerMapStatsGreenEnergySaved } from '@/api/modules/comPowerCenter'

const pieChartRef3 = ref<HTMLDivElement | null>(null)

const props = defineProps({
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  isActive: {
    type: Boolean,
    default: false,
  },
})
const dataInfo = ref({
  zaiwangNum: '0',
  gongchengNum: '0',
  savedElectricity: '0',
  savedEnergy: '0',
})
const reportList = ref([])
const getDataInfo = () => {
  getComPowerMapStatsGreenEnergySaved({
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
    cityCode: props.requestParams.cityCode,
  }).then((res: any) => {
    if (res.code === 200) {
      reportList.value = res.entity.report || []
      dataInfo.value.savedEnergy = res.entity.savedEnergy || 0
      dataInfo.value.savedElectricity = res.entity.savedElectricity || 0
      dataInfo.value.zaiwangNum = '0'
      dataInfo.value.gongchengNum = '0'
      let data = res.entity.states || []
      data.forEach((item: any) => {
        if (item.lifeCycleState == '在网') {
          dataInfo.value.zaiwangNum = item.greenEnergyNum
        } else if (item.lifeCycleState == '工程') {
          dataInfo.value.gongchengNum = item.greenEnergyNum || 0
        }
      })
      console.log(reportList.value)
      nextTick(() => {
        if (reportList.value.length > 0) {
          memLine1 = echarts.init(pieChartRef3.value!, null, {})
          setOption()
        }
      })
    }
  })
}

// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)

// 原始数值堆叠柱状图数据
onMounted(() => {
  getDataInfo()
})

onBeforeMount(() => {
  memLine1?.dispose()
})

let memLine1: ECharts | null = null

function setOption() {
  if (!memLine1) return
  let base = +new Date(2016, 9, 3)
  let oneDay = 24 * 3600 * 1000
  let valueBase = Math.random() * 300
  let valueBase2 = Math.random() * 50
  let data = []
  let data2 = []
  for (let i = 1; i < 10; i++) {
    let now = new Date((base += oneDay))
    let dayStr = [now.getFullYear(), now.getMonth() + 1, now.getDate()].join('-')
    valueBase = Math.round((Math.random() - 0.5) * 20 + valueBase)
    valueBase <= 0 && (valueBase = Math.random() * 300)
    data.push([dayStr, valueBase])
    valueBase2 = Math.round((Math.random() - 0.5) * 20 + valueBase2)
    valueBase2 <= 0 && (valueBase2 = Math.random() * 50)
    data2.push([dayStr, valueBase2])
  }

  let datas = []
  if (reportList.value.length) {
    datas = reportList.value.map((item: any) => {
      return {
        ...item,
        value: Number(item.savedElectricity),
        standbyMonth: `${item.standbyMonth}`,
      }
    })
  }
  // 给最小值和最大值留一些边距
  let option1 = {
    xAxis: {
      type: 'category',
      splitLine: {
        show: false,
      },
      data: datas.map((item_: any) => item_['standbyMonth']),
    },
    tooltip: {
      trigger: 'axis',
      valueFormatter: function (value: any) {
        return value + 'w'
      },
    },
    yAxis: {
      type: 'value',
      axisTick: {
        inside: true,
      },
      splitLine: {
        show: true,
      },
      // axisLabel: {
      //   inside: true,
      //   formatter: '{value}\n'
      // },
      z: 10,
    },
    grid: {
      top: 10,
      left: 60,
      right: 15,
      // height: 160
    },
    series: [
      {
        name: '节能',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        sampling: 'average',
        itemStyle: {
          color: '#00e4ff',
        },
        stack: 'a',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0,228,255,0.9)',
            },
            {
              offset: 1,
              color: 'rgba(187,211,247,0.9)',
            },
          ]),
        },
        emphasis: {
          disabled: true,
        },
        data: datas,
      },
    ],
  }
  memLine1.setOption(option1)
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.panel {
  position: relative;
}

.power-panel {
  cursor: pointer;
  height: 261px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(42, 107, 255, 0.08);
  padding: 2px;
  position: relative;
  //background-color: #fff;
  background: url('/images/computingPower/comPowerRight2Bg.png') no-repeat center center;
  background-size: 100% 100%;
}

.power-panel.active {
  background: url('/images/computingPower/comPowerRight2BgActive.png') no-repeat center center;
  background-size: 100% 100%;
}
.card-content {
  padding: 0 20px;
  height: 100%;
}

.bg34B3E0Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #34b3e0;
  margin-right: 5px;
}

.bg9BD8B4Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #9bd8b4;
  margin-right: 5px;
}
.saveEnergy {
  .saveEnergyTop {
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .saveEnergyTopItem {
      //display: inline-block;
      p {
        font-size: 17px;
        color: #004fb1;
        margin-bottom: 12px;
        img {
          display: inline-block;
          vertical-align: middle;
          margin-right: 4px;
        }
        span {
          font-size: 20px;
          display: inline-block;
          vertical-align: middle;
          color: #004fb1;
        }
        b {
          font-weight: normal;
          font-size: 15px;
          color: #080c13;
        }
        i {
          display: inline-block;
          margin-left: 4px;
          font-style: normal;
        }
      }
      // p:nth-child(2) {
      //   font-family: 'ziHuiJingDianYaHei';
      //   padding-left: 19px;
      // }
    }
  }
}
</style>
