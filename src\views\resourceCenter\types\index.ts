// 资源类型
export type ResourceType =
  | 'ecs'
  | 'gcs'
  | 'evs'
  | 'obs'
  | 'slb'
  | 'nat'
  | 'eip'
  | 'mysql'
  | 'redis'
  | 'vpn'
  | 'vpc'
  | 'securityGroup'
  | 'nas'
  | 'kafka'
  | 'es'
  | 'flink'
  | 'pm'
  | 'bldRedis'
  | 'rdsMysql'
  | 'postgreSql'
  | 'shareEvs'

// 变更类型
export type ResourceChangeType =
  | 'instance_spec_change' // 实例规格变更
  | 'storage_expand' // 存储扩容
  | 'bandwidth_expand' // 带宽扩容
  | 'delay' // 资源延期

// 资源变更配置
export interface ResourceChangeConfig {
  resourceType: ResourceType
  allowedChangeTypes: ResourceChangeType[]
  displayFields: string[]
}

// 虚拟IP类型定义
export interface VirtualIpItem {
  id: string
  vipName: string
  catalogueDomainCode: string
  catalogueDomainName: string
  domainCode: string
  domainName: string
  regionId: string
  regionName: string
  regionCode: string
  azId: string
  azName: string
  azCode: string
  vpcId: string
  vpcName: string
  subnetId: string
  subnetName: string
  ipAddress: string
  cidr: string
  cloudHost: string
  description: string
  createTime: string
}

// 虚拟网卡类型定义
export interface VirtualNicItem {
  id: string
  vnicName: string
  catalogueDomainCode: string
  catalogueDomainName: string
  domainCode: string
  domainName: string
  regionId: string
  regionName: string
  regionCode: string
  azId: string
  azName: string
  azCode: string
  vpcId: string
  vpcName: string
  subnetId: string
  subnetName: string
  macAddress: string
  cloudHost: string
  description: string
  createTime: string
}

// 证书管理类型定义
export interface CertificateItem {
  id: string
  certificateName: string
  businessSystemCode: string
  businessSystemName: string
  catalogueDomainCode: string
  catalogueDomainName: string
  domainCode: string
  domainName: string
  regionId: string
  regionName: string
  regionCode: string
  certificateType: string
  publicKeyCertificate: string
  privateKeyCertificate: string
  associatedListeners: string
  createTime: string
}

// 对公资源类型
export type PublicResourceType =
  | 'ecs'
  | 'gcs'
  | 'evs'
  | 'obs'
  | 'nat'
  | 'slb'
  | 'eip'
  | 'vpn'
  | 'rdsMysql'
  | 'backup'
  | 'postgreSql'
