<template>
  <div>
    <el-dialog
      title="配置网络"
      v-model="dialogVisible"
      width="1000px"
      :show-close="false"
      :close-on-click-modal="false"
      @close="close"
    >
      <div class="table-container" v-if="dialogVisible">
        <el-button v-if="!isDisabled" type="primary" @click="addNetwork">添加网卡</el-button>

        <sl-pro-table
          :columns="tableColumns"
          :data="planeNetworkModel"
          :pagination="false"
          :is-show-search="false"
          row-key="orderGoodsIds"
          ref="proTable"
          style="height: 100%"
        >
        </sl-pro-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ isDisabled ? '关 闭' : '取 消' }}</el-button>
          <el-button v-if="!isDisabled" type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { getNetworkTreeApi, getvpcTreeApi } from '@/api/modules/approvalCenter'
import type { ColumnProps } from '@/components/SlProTable/interface'
import {
  isCombinedValueUniqueInArray,
  isObjectValuesNonEmpty,
  showTips,
  uniqueByKeys,
} from '@/utils'
import { computed, ref } from 'vue'

const dialogVisible = ref(false)

const isDisabled = ref(false)
const rowIndex = ref(-1)
const isMultiple = ref(false) //控制子网是否多选
const isVpc = ref(false) //控制是否是vpc网络
const getDicParams = ref<FormDataType>({}) //获取字典参数

const planeNetworkModel = ref<FormDataType[]>([]) //网络平面数据

const planes = ref<string[]>([]) //网络平面字典

// const getSubnetIp = inject('getSubnetIp', ref([]))

// -------------表格参数-------------------

const planeColumns = () => {
  let obj: ColumnProps = {
    prop: 'plane',
    label: '网络平面',
    minWidth: '250px',
  }
  if (!isDisabled.value) {
    obj.render = ({ row }: { row: any }) => (
      <el-select
        filterable
        v-model={row.plane}
        onChange={() => planeChange(row)}
        placeholder="请选择网络平面"
      >
        {planes.value.map((item) => (
          <el-option key={item} label={item} value={item} />
        ))}
      </el-select>
    )
  }

  return obj
}
const networkColumns = () => {
  // isCombinedValueUniqueInArray
  let obj: ColumnProps = {
    prop: 'name',
    label: '网络',
    minWidth: '250px',
  }
  if (!isDisabled.value) {
    obj.render = ({ row }: { row: any }) => (
      <el-select
        class="mr10"
        clearable
        filterable
        v-model={row.id}
        style="width: 100%"
        placeholder="请选择网络"
        onChange={(value: number) => getPrivateNetworkDic(value, row)}
      >
        {networkDic.value[`${getDicParams.value.azCode}-${row.plane}`]?.map((option: any) => (
          <el-option key={option.id} label={option.name} value={option.id} />
        ))}
      </el-select>
    )
  }
  return obj
}

// 子网组件

/**
 * 处理子网选择变化
 */
const handleSubnetChange = (value: string[], row: any): void => {
  if (!value || value.length === 0) {
    row.subnets = []
    row.subnetId = ''
    row.subnetNames = ''
    return
  }

  row.subnets = []
  row.subnetId = value.join(',')

  // 从私网字典中过滤出选中的子网
  const selectedSubnets =
    row.privateNetworkDic?.filter((item: any) => value.includes(item.id as string)) || []

  // 构建子网数据
  row.subnets = selectedSubnets.map((item: any) => ({
    subnetId: item.id,
    subnetName: item.subnetName,
    subnetIp: item.cidr,
    ipAddress: '', // 初始化IP地址列表
  }))

  // 更新子网名称显示
  row.subnetNames = selectedSubnets.map((item: any) => item.subnetName).join(',')

  //  获取子网对应的ip集合
  // Promise.all(
  //   row.subnets.map(async (subnet: any) => {
  //     const params = {
  //       subnetId: subnet.subnetId,
  //       count: 1,
  //       filterIps: getSubnetIp.value.filter((item: any) => item !== subnet.ipAddress),
  //     }

  //     const { entity } = await getAvailableIp(params)

  //     // 更新子网的IP地址列表
  //     subnet.ipAddress = entity.availableIps.join(',')

  //     // 目前只考虑ipv4
  //     if (entity.ipVersion === 'IPv6') subnet.ipAddress = null

  //     return subnet
  //   }),
  // )
}

const subnetColumns = () => {
  let obj: ColumnProps = {
    prop: 'subnetNames',
    label: '子网',
    minWidth: '250px',
  }
  if (!isDisabled.value) {
    obj.render = ({ row }: { row: any }) => (
      <>
        <el-select
          clearable
          filterable
          multiple
          multiple-limit={isMultiple.value ? 0 : 1}
          v-model={row.subnetIds}
          style="width: 100%"
          placeholder="请选择子网络"
          onChange={(value: string[]) => handleSubnetChange(value, row)}
        >
          {row.privateNetworkDic?.map((option: any) => (
            <el-option key={option.id} label={option.subnetName} value={option.id} />
          ))}
        </el-select>
      </>
    )
  } else {
    obj.render = ({ row }: { row: any }) => (
      <span>{row.subnets?.map((item: any) => item.subnetName).join(',')}</span>
    )
  }
  return obj
}

const operationColumns = (): ColumnProps => {
  return {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '80px',
    isShow: !isDisabled.value,
    render: ({ $index }: { $index: number }) => (
      <el-button type="danger" onClick={() => planeNetworkModel.value.splice($index, 1)} link>
        删除
      </el-button>
    ),
  }
}

const tableColumns = computed(() => [
  planeColumns(),
  networkColumns(),
  subnetColumns(),
  operationColumns(),
  {
    prop: 'sort',
    label: '排序',
    fixed: 'right',
    width: '80px',
  },
])
// ------------end------------------

const addNetwork = () => {
  let sort = 0
  if (planeNetworkModel.value.length > 0) {
    sort = Math.max(...planeNetworkModel.value.map((item) => item.sort)) + 1
  }
  planeNetworkModel.value.push({
    plane: '',
    id: '',
    subnetId: '',
    sort,
  })
}

// 公网字典
const networkDic = ref<{ [key: string]: any[] }>({})
// 获取公网字典
const getNetworkDicApi = async (params: any, key: string) => {
  if (networkDic.value[key]) return

  const { entity } = isVpc.value ? await getNetworkTreeApi(params) : await getvpcTreeApi(params)
  let arr = entity.map((item: any) => {
    return {
      ...item,
      name: item.vpcName ? item.vpcName : item.name,
      subnetOrderList: item.subnetOrderList ? item.subnetOrderList : item.vpcSubnetOrderList,
    }
  })
  networkDic.value[key] = arr
}

const planeChange = (row: any) => {
  row['subnetIds'] = []
  row['subnetName'] = ''
  row['privateNetworkDic'] = []
  row['id'] = ''
  row['name'] = ''
  if (!row.plane) return
  getNetworkDicApi(
    { ...getDicParams.value, plane: row.plane },
    `${getDicParams.value.azCode}-${row.plane}`,
  )
}

// 私网字典
const getPrivateNetworkDic = async (value: number, row: any) => {
  row['subnetIds'] = []
  row['subnetName'] = ''
  if (!value) return (row['privateNetworkDic'] = [])

  if (!isCombinedValueUniqueInArray(planeNetworkModel.value, ['id'])) {
    row['id'] = ''
    return showTips('请勿重复选择网络')
  }

  const obj = networkDic.value[`${getDicParams.value.azCode}-${row.plane}`].find(
    (item: any) => item.id === value,
  )
  row['name'] = obj.name
  row['privateNetworkDic'] = obj?.subnetOrderList ?? []
}

type OpenType = {
  row: any
  index: number
  disabled: boolean
  vpc: boolean
  multiple: boolean
  params: any
}
const openDialog = async ({ row, index, disabled, multiple, vpc, params }: OpenType) => {
  row.plane && (planes.value = row.plane.split(','))
  row.planeNetworkModel &&
    (planeNetworkModel.value = JSON.parse(JSON.stringify(row.planeNetworkModel)))
  isDisabled.value = disabled
  rowIndex.value = index
  isMultiple.value = multiple
  isVpc.value = vpc
  getDicParams.value = params

  if (planeNetworkModel.value && !isDisabled.value) {
    let networkParams: any = planeNetworkModel.value.map((item: any) => {
      return {
        ...getDicParams.value,
        plane: item.plane,
      }
    })
    networkParams = uniqueByKeys(networkParams, ['regionCode', 'azCode', 'plane'])

    await Promise.all(
      networkParams.map((item: any) => getNetworkDicApi(item, `${item.azCode}-${item.plane}`)),
    )
  }
  dialogVisible.value = true
}

const emit = defineEmits(['updateNetworks'])

const submit = () => {
  let falg = true
  planeNetworkModel.value.forEach((item: any) => {
    falg = falg && isObjectValuesNonEmpty(item, ['plane', 'id', 'subnetId'])
  })

  if (!falg) return showTips('请完善网络配置')
  emit('updateNetworks', planeNetworkModel.value, rowIndex.value)
  dialogVisible.value = false
}

const close = () => {
  planeNetworkModel.value = []
  isDisabled.value = false
  rowIndex.value = -1
  isMultiple.value = false
  isVpc.value = false
  // planes.value = []
}

defineExpose({
  openDialog,
})
</script>

<style lang="scss" scoped>
.table-container {
  max-height: 800px;
  min-height: 100px;
}
</style>
