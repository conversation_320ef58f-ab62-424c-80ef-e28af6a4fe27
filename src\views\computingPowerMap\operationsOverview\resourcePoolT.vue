<template>
  <!-- <div class="f-r f-j-e">
        <div class="f-r-c-b search">
          <p class="p-h-10 f-r-c-b  p-v-14 f-1">
            <span class="fz-16">请选择目标资源池</span>
            <span><el-icon :size="16">
                <ArrowLeft />
              </el-icon></span>
          </p>
          <el-icon :size="18" class="m-h-10">
            <View />
          </el-icon>
        </div>
      </div> -->
  <div class="‌continer‌" v-loading="dialogLoading">
    <p class="fz-16 m-t-4">云平台</p>
    <div class="m-v-10">
      <el-tag
        size="large"
        @click="handclick(index, 'cloudPlatforms')"
        :class="['m-r-8', 'p-h-16', 'm-b-8', 'c-p', item.check == false ? 'untag' : '']"
        v-for="(item, index) in cloudPlatforms"
        :key="index"
        >{{ item.name }}</el-tag>
    </div>
    <p class="fz-16 m-t-4"></p>
    <p class="fz-16 m-t-4">地市选择</p>
    <div class="m-v-10">
      <el-tag
        size="large"
        @click="handclick(index, 'city')"
        :class="[
          'm-r-8',
          'p-h-16',
          'm-b-8',
          'c-p',
          item.check == false || !item.check ? 'untag' : '',
        ]"
        v-for="(item, index) in cityList"
        :key="item.code"
        >{{ item.name }}</el-tag>
      <el-tag
        v-if="cityList.length == 0"
        size="large"
        :class="['m-r-8', 'p-h-16', 'm-b-8', 'untag']"
        >- -</el-tag>
    </div>
    <p class="fz-16 m-t-4">地区选择</p>
    <div class="m-v-10">
      <el-tag
        size="large"
        @click="handclick(index, 'area')"
        :class="[
          'm-r-8',
          'p-h-16',
          'm-b-8',
          'c-p',
          item.check == false || !item.check ? 'untag' : '',
        ]"
        v-for="(item, index) in areaList"
        :key="item.code"
        >{{ item.name }}</el-tag>
      <el-tag
        v-if="areaList.length == 0"
        size="large"
        :class="['m-r-8', 'p-h-16', 'm-b-8', 'untag']"
        >- -</el-tag>
    </div>
  </div>

  <div class="m-t-10" v-for="(item, index) in regionList" :key="index">
    <p class="fz-16 m-t-4 f-r f-a-c">
      {{ item.platformName }}<span class="m-h-10 c-9">全选</span>
      <el-switch
        @change="handclick(null, 'regionAll', index)"
        v-model="item.checkAll"
        :disabled="!item.regions ? true : false"
      />
    </p>
    <div class="m-v-10">
      <el-tag
        v-for="(items, indexs) in item.regions"
        :key="indexs"
        size="large"
        @click="handclick(indexs, 'region', index)"
        :class="[
          'm-r-8',
          'p-h-16',
          'm-b-8',
          'c-p',
          items.check == false || !items.check ? 'untag_' : 'tag_',
        ]"
        :title="items.name"
        >{{ items.name }}</el-tag>
    </div>
    <el-tag v-if="!item.regions" size="large" :class="['m-r-8', 'p-h-16', 'm-b-8', 'untag_']">- -</el-tag>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import type { CloudPlatformType, cityListType, regionsApiType } from './interface/type'
import { getCitysApi, getRegionsApi } from '@/api/modules/operationsOverview'
import cloneDeep from 'lodash/cloneDeep'
// import { getPlatformTypesApiData, entityData, getRegionsApiData } from './data.json'
const props = defineProps({
  cloudPlatform: {
    type: Array,
    default: () => [],
  },
  drawerO: {
    type: Boolean,
    default: false,
  },
  confirmPage: {
    type: String,
    default: '',
  },
})
onMounted(() => {
  cloudPlatforms.value = cloneDeep(props.cloudPlatform)
  // getCitysApiHttp()
  // console.log('props.confirmPage ', props.confirmPage)
  // getPlatformTypesApiHttp()
})
let cloudPlatforms = ref<any[]>([])
// console.log('cloudPlatforms', cloudPlatforms)
//市区地市
let cityList = ref<cityListType[]>([])
let areaList = ref<cityListType[]>([])
let regionList = ref<regionsApiType[]>([])
const dialogLoading = ref(false)
//云平台-点击
const handclick = (i: any, type: string, i_: any = 0) => {
  switch (type) {
    // 点击云信息
    case 'cloudPlatforms':
      if (cloudPlatforms.value[i].check == false) {
        cloudPlatforms.value[i].check = true
        cloudPlatforms.value.forEach((item: CloudPlatformType, index: number) => {
          if (index != i) {
            item.check = false
          }
        })
        cityList.value = []
        areaList.value = []
        regionList.value = []
        //查询当前云下对应地市信息
        getCitysApiHttp()
      } else {
        cloudPlatforms.value[i].check = false
        cityList.value = []
        areaList.value = []
        regionList.value = []
      }

      break
    // 点击城市，切换地区信息
    case 'city':
      areaList.value = []
      if (cityList.value && cityList.value.length) {
        cityList.value[i].check =
          cityList.value[i].check == false || !cityList.value[i].check ? true : false
        if (cityList.value[i].check == true) {
          areaListUpdata(true, cityList.value)
        } else {
          areaListUpdata(false, cityList.value)
        }
      }

      getRegionsApiHttp()
      break
    //点击地区，切换资源池信息
    case 'area':
      areaList.value[i].check =
        areaList.value[i].check == false || !areaList.value[i].check ? true : false
      getRegionsApiHttp()
      break

    case 'region':
      // console.log('i', i, regionList.value[i_].regions)
      regionList.value[i_].regions[i].check =
        regionList.value[i_].regions[i].check == false || !regionList.value[i_].regions[i].check
          ? true
          : false
      // console.log('regionList2', regionList)
      const allChecked = regionList.value[i_].regions.every((region: any) => region.check === true)
      if (allChecked) {
        regionList.value[i_].checkAll = true
        handclick(null, 'regionAll', i_)
      } else {
        regionList.value[i_].checkAll = false
      }
      break
    case 'regionAll':
      if (regionList.value[i_].regions) {
        regionList.value[i_].regions.forEach((item: any) => {
          item.check = regionList.value[i_].checkAll
        })
      }
      break
    default:
      break
  }
}
// 地市数组更新
let areaListUpdata = (type: boolean, item: any, isALL: string = '') => {
  //默认全选的情况
  function areaListAll() {
    let list_: any = []
    item.forEach((item: any) => {
      if (item.children && item.children.length) {
        item.children.forEach((item_: any) => {
          list_.push({
            ...item_,
            // check: true
          })
        })
      } else {
        // list_ = []
      }
    })
    console.log(list_)
    areaList.value = list_
  }
  if (isALL) {
    areaListAll()
    getRegionsApiHttp()
    PageValue['cloudPlatforms'] = cloneDeep(cloudPlatforms.value)
    // PageValue['cityList'] = cloneDeep(cityList.value)
    // PageValue['areaList'] = cloneDeep(areaList.value)
  } else {
    let list: any[] = []
    item.forEach((item: any) => {
      if (item.check) {
        list = [...list, ...(item.children && item?.children.length > 0 ? item.children : [])]
      } else {
        if (item.children && item?.children.length > 0) {
          item.children.forEach((item_: any) => {
            item_.check = false
          })
        }
      }
    })
    //判断当前是否有选择的，如果有选择项，但是其下边没有数据，还是展示空
    const hasChecked = item.some((item1: any) => item1.check === true)
    if (list.length == 0) {
      if (hasChecked) {
        areaList.value = []
      } else {
        areaListAll()
      }
    } else {
      areaList.value = list
    }
  }
}

// 地市选择
let getCitysApiHttp = () => {
  dialogLoading.value = true
  let platTypeId =
    cloudPlatforms.value.filter((item: CloudPlatformType) => item.check === true)?.[0]?.id || ''
  getCitysApi({ platTypeId: platTypeId })
    .then((res: any) => {
      if (res.code == 200) {
        const { entity } = res
        cityList.value = entity
        areaListUpdata(true, cityList.value, 'ALL')
        let timer = setTimeout(() => {
          dialogLoading.value = false
          clearTimeout(timer)
        }, 1000)
      } else {
        dialogLoading.value = false
      }
    })
    .catch(() => {
      dialogLoading.value = false
    })
}
// 查询VM一类
let getRegionsApiHttp = () => {
  let cityList_ = cityList.value.filter((item: any) => item.check === true)
  let areaList_ = areaList.value.filter((item: any) => item.check === true)
  let cities: any
  if (cityList_.length) {
    const codesArray = cityList_.map((item: any) => ({ code: item.code }))
    cities = codesArray
  }
  if (areaList_.length) {
    if (cityList_.length != 0) {
      cities.forEach((items: CloudPlatformType) => {
        items.children = []
        areaList_.forEach((item: any) => {
          if (item.parentId == items.code) {
            items.children.push({ code: item.code })
          }
        })
      })
    } else {
      const organizedData: any = {}
      // 遍历每个项目，根据 parentId 构建结构
      areaList_.forEach((item) => {
        // 如果没有 parentId，则它是一个顶级城市
        if (item.parentId === null) {
          organizedData[item.code] = { code: item.code, children: [] }
        } else {
          // 否则，将其添加到其父级的 children 数组中
          if (!organizedData[item.parentId]) {
            organizedData[item.parentId] = { code: item.parentId, children: [] }
          }
          organizedData[item.parentId].children.push({ code: item.code })
        }
      })

      // 提取所有顶级城市（即 parentId 为 null 的城市）
      cities = Object.values(organizedData).filter(
        (city: any) => city.parentId === undefined || city.parentId === null,
      )
      // console.log('cities', cities)
    }
  }
  // console.log('areaList 参数', areaList.value)
  getRegionsApi({
    platTypeId:
      (cloudPlatforms.value.filter((item: CloudPlatformType) => item.check === true)[0] &&
        cloudPlatforms.value.filter((item: CloudPlatformType) => item.check === true)[0].id) ||
      '',
    cities: cities,
  }).then((res: any) => {
    if (res.code == 200) {
      const { entity } = res
      // console.log('entity', entity)
      let list = entity.map((item: any) => {
        return {
          ...item,
          checkAll: false,
        }
      })
      // // 保留之前的选项,对比
      // if (
      //   cityList.value.filter((item: any) => item.check === true).length == 0 &&
      //   areaList.value.filter((item: any) => item.check === true).length == 0
      // ) {
      //   regionList.value = contrastlist([], list)
      // } else {
      //   regionList.value = contrastlist(regionList.value, list)
      // }
      regionList.value = contrastlist([], list)
      //默认全选
      if (!PageValue['regionList']) {
        regionList.value.map((item: any, index: number) => {
          if (item.regions) {
            item.checkAll = true
            handclick(null, 'regionAll', index)
          }
        })
        PageValue['regionList'] = cloneDeep(regionList.value)
      }
    }
  })
}
let contrastlist = (oldList: any, newList: any) => {
  if (!oldList || !oldList.length) {
    newList.forEach((itemNew: any) => {
      if (itemNew.regions) {
        itemNew.checkAll = true
        itemNew.regions.forEach((region: any) => {
          region.check = true
        })
      }
    })
    return newList
  }

  // 使用 Map 存储 oldList，以 platformCode 为键
  const oldListMap = new Map()
  oldList.forEach((itemOld: any) => {
    if (!oldListMap.has(itemOld.platformCode)) {
      oldListMap.set(itemOld.platformCode, itemOld)
    }
  })
  // console.log('oldList', oldListMap)
  newList.forEach((itemNew: any) => {
    const itemOld = oldListMap.get(itemNew.platformCode)
    if (itemOld) {
      if (itemOld.checkAll && itemNew.regions) {
        itemNew.checkAll = itemOld.checkAll
        itemNew.regions.forEach((region: any) => {
          region.check = true
        })
      } else if (itemNew.regions && itemOld.regions) {
        // 创建一个 Set 来快速检查已经匹配的区域
        const matchedCodes = new Set(itemOld.regions.map((region: any) => region.code))
        let checkNum: number = 0
        itemNew.regions.forEach((regionNew: any) => {
          if (matchedCodes.has(regionNew.code)) {
            regionNew.check = itemOld.regions.find(
              (regionOld: any) => regionOld.code === regionNew.code,
            ).check
          }
          if (regionNew.check) {
            checkNum += 1
          }
        })
        if (checkNum == itemNew.regions.length) {
          itemNew.checkAll = true
        }
      }
    }
  })
  return newList
}
let PageValue: any = {}
watch(
  () => props.drawerO,
  (newVal) => {
    if (newVal) {
      if (props.confirmPage == 'confirm') {
        PageValue['cloudPlatforms'] = cloneDeep(cloudPlatforms.value)
        PageValue['cityList'] = cloneDeep(cityList.value)
        PageValue['areaList'] = cloneDeep(areaList.value)
        PageValue['regionList'] = cloneDeep(regionList.value)
        // console.log('确认',PageValue)
      }
      if (props.confirmPage == 'cancel' && PageValue && PageValue.cloudPlatforms) {
        // console.log('取消',PageValue)
        cloudPlatforms.value = cloneDeep(props.cloudPlatform)
        console.log(cloudPlatforms.value)
        if (!PageValue.cityList && !PageValue.areaList) {
          console.log(3333333)
          if (
            cloudPlatforms.value.filter((item: CloudPlatformType) => item.check === true).length > 0
          ) {
            getCitysApiHttp()
          }
        } else {
          cityList.value = cloneDeep(PageValue.cityList)
          areaList.value = cloneDeep(PageValue.areaList)
        }
        regionList.value = cloneDeep(PageValue.regionList)
      }
    }
  },
)

defineExpose({
  cloudPlatforms,
  cityList,
  areaList,
  regionList,
})
</script>
<style scoped lang="scss"></style>
