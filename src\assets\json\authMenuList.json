{"code": 200, "entity": [{"path": "/computingWorkbench", "name": "computingWorkbench", "redirect": "/computingWorkbench", "meta": {"icon": "icon_computing_workbench", "title": "运营中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/operationsOverview", "name": "operationsOverview", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "menu_operationsOverview", "title": "运营总览", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/computingPowerMap", "name": "computingPowerMap", "component": "/views/computingPowerMap/computingPowerMap/index.vue", "meta": {"icon": "menu_suanliMAP", "title": "算力地图", "isLink": "/computingPowerMap", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/dashboard", "name": "dashboard", "component": "/views/dashboard/index.vue", "meta": {"icon": "menu_smartView", "title": "智算地图", "isLink": "/dashboard", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/tenantView", "name": "tenantView", "component": "/computingPowerMap/tenantView/index.vue", "meta": {"icon": "menu_tenantView", "title": "租户视图", "activeMenu": "/tenantView", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/viewOfPublicTenants", "name": "viewOfPublicTenants", "component": "/computingPowerMap/viewOfPublicTenants/index.vue", "meta": {"icon": "menu_OrderView", "title": "订购视图", "activeMenu": "viewOfPublicTenants", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/accountManagerView", "name": "accountManagerView", "component": "/computingPowerMap/accountManagerView/index.vue", "meta": {"icon": "menu_CustomerView", "title": "客户经理视图", "activeMenu": "/accountManagerView", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "operationMaintenanceView", "title": "运维视图", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}, {"path": "/computingKeepWorkbench", "name": "computingKeepWorkbench", "redirect": "/computingKeepWorkbench", "meta": {"icon": "icon_computing_workbench", "title": "运维中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/alarmCenterList", "name": "alarmCenterList", "component": "/computingPowerMap/alarmList/index.vue", "meta": {"icon": "menu_gao<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "告警管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/workOrder", "name": "workOrder", "component": "/approvalCenter/workOrder/index.vue", "meta": {"icon": "icon_work_order", "title": "审批中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/workOrder", "name": "workOrder", "component": "/approvalCenter/workOrder/index.vue", "meta": {"icon": "menu_workOrderApproval", "title": "开通工单审批", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/changeWorkOrder", "name": "changeWorkOrder", "component": "/approvalCenter/changeWorkOrders/index.vue", "meta": {"icon": "menu_chageOrderApproval", "title": "变更工单审批", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/recycleWorkOrder", "name": "recycleWorkOrder", "component": "/approvalCenter/recyclingWorkOrders/index.vue", "meta": {"icon": "menu_reOrderApproval", "title": "回收工单审批", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/nonStandardOrder", "name": "nonStandardOrder", "component": "/approvalCenter/nonStandardOrders/index.vue", "meta": {"icon": "menu_SPOrderApproval", "title": "非标工单审批", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/orderCenter", "name": "orderCenter", "component": "/orderCenter/corporateOrder/list.vue", "meta": {"icon": "icon_product_center", "title": "订单中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}, "children": [{"path": "/corporateOrder", "name": "corporateOrder", "component": "/orderCenter/corporateOrder/list.vue", "meta": {"icon": "menu_orderlist", "title": "订单列表", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/unsubscribeList", "name": "unsubscribeList", "component": "/orderCenter/unsubscribeList/list.vue", "meta": {"icon": "menu_CancelOder", "title": "退订列表", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/changeList", "name": "changeList", "component": "/orderCenter/changeList/list.vue", "meta": {"icon": "menu_changeorderlist", "title": "变更列表", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/resourceCenter", "name": "resourceCenter", "redirect": "/ecsList", "meta": {"icon": "icon_resource_center", "title": "资源中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/ecsList", "name": "ecsList", "component": "/resourceCenter/ecs/list.vue", "meta": {"icon": "menu_computingResources", "title": "计算资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/ecsList", "name": "ecsList", "component": "/resourceCenter/ecs/list.vue", "meta": {"icon": "", "title": "云主机", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/gcsList", "name": "gcsList", "component": "/resourceCenter/gcs/list.vue", "meta": {"icon": "", "title": "GPU云主机", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/pmList", "name": "pmList", "component": "/resourceCenter/pm/list.vue", "meta": {"icon": "", "title": "裸金属", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/imagesList", "name": "imagesList", "component": "/resourceCenter/images/list.vue", "meta": {"icon": "", "title": "镜像", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/evsList", "name": "evsList", "component": "/resourceCenter/evs/list.vue", "meta": {"icon": "menu_storageResources", "title": "存储资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/evsList", "name": "evsList", "component": "/resourceCenter/evs/list.vue", "meta": {"icon": "", "title": "云硬盘", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/evsForm", "name": "evsForm", "component": "/resourceCenter/evs/form.vue", "meta": {"icon": "", "title": "云硬盘资源开通", "activeMenu": "/evsList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/obsList", "name": "obsList", "component": "/resourceCenter/obs/list.vue", "meta": {"icon": "", "title": "对象存储", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/backupList", "name": "backupList", "component": "/resourceCenter/backup/list.vue", "meta": {"icon": "", "title": "云灾备", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/nasList", "name": "nasList", "component": "/resourceCenter/nas/list.vue", "meta": {"icon": "", "title": "NAS", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/obsForm", "name": "obsForm", "component": "/resourceCenter/obs/form.vue", "meta": {"icon": "", "title": "对象存储资源开通", "activeMenu": "/obsList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "", "title": "云备份", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}, {"path": "/shareEvsList", "name": "shareEvsList", "component": "/resourceCenter/shareEvs/list.vue", "meta": {"icon": "", "title": "共享数据盘", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/vpcList", "name": "vpcList", "component": "/resourceCenter/vpc/list.vue", "meta": {"icon": "menu_networkResources", "title": "网络资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/vpcList", "name": "vpcList", "component": "/resourceCenter/vpc/list.vue", "meta": {"icon": "", "title": "VPC", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/vpcForm", "name": "vpcForm", "component": "/resourceCenter/vpc/form.vue", "meta": {"icon": "", "title": "VPC资源开通", "activeMenu": "/vpcList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/networkList", "name": "networkList", "component": "/resourceCenter/network/list.vue", "meta": {"icon": "", "title": "网络", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/networkForm", "name": "networkForm", "component": "/resourceCenter/network/form.vue", "meta": {"icon": "", "title": "网络资源开通", "activeMenu": "/networkList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/slbList", "name": "slbList", "component": "/resourceCenter/slb/list.vue", "meta": {"icon": "", "title": "负载均衡", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/slbForm", "name": "slbForm", "component": "/resourceCenter/slb/form.vue", "meta": {"icon": "", "title": "负载均衡资源开通", "activeMenu": "/slbList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/eipList", "name": "eipList", "component": "/resourceCenter/eip/list.vue", "meta": {"icon": "", "title": "弹性公网", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/natList", "name": "natList", "component": "/resourceCenter/nat/list.vue", "meta": {"icon": "", "title": "NAT", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/natForm", "name": "natForm", "component": "/resourceCenter/nat/form.vue", "meta": {"icon": "", "title": "NAT网关资源开通", "activeMenu": "/natList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/natRuleList", "name": "natRuleList", "component": "/resourceCenter/nat/ruleList.vue", "meta": {"icon": "", "title": "NAT网关规则", "activeMenu": "/natRuleList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/securityGroupList", "name": "securityGroupList", "component": "/resourceCenter/securityGroup/list.vue", "meta": {"icon": "", "title": "安全组", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/virtualNicList", "name": "virtualNicList", "component": "/resourceCenter/virtualNic/list.vue", "meta": {"icon": "", "title": "虚拟网卡", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/virtualIpList", "name": "virtualIpList", "component": "/resourceCenter/virtualIpList/list.vue", "meta": {"icon": "", "title": "虚拟IP", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/certificateList", "name": "certificateList", "component": "/resourceCenter/certificate/list.vue", "meta": {"icon": "", "title": "证书管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/cloudPortList", "name": "cloudPortList", "component": "/resourceCenter/cloudPort/list.vue", "meta": {"icon": "", "title": "云端口", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/vpnList", "name": "vpnList", "component": "/resourceCenter/vpn/list.vue", "meta": {"icon": "", "title": "VPN", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/containerResources", "name": "containerResources", "component": "/computingPowerMap/containerResources/index.vue", "meta": {"icon": "menu_containerResources", "title": "容器资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/cqList", "name": "cqList", "component": "/resourceCenter/cq/list.vue", "meta": {"icon": "", "title": "容器配额", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/operationsOverview", "name": "middlewareResources", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "menu_middlewareResources", "title": "中间件资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/redisList", "name": "redisList", "component": "/resourceCenter/redis/list.vue", "meta": {"icon": "", "title": "通用Redis", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/bldRedisList", "name": "bldRedisList", "component": "/resourceCenter/bldRedis/list.vue", "meta": {"icon": "", "title": "国产Redis", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/kafkaList", "name": "kafkaList", "component": "/resourceCenter/kafka/list.vue", "meta": {"icon": "", "title": "Kafka", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/esList", "name": "esList", "component": "/resourceCenter/es/list.vue", "meta": {"icon": "", "title": "ElasticSearch", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/flinkList", "name": "flinkList", "component": "/resourceCenter/flink/list.vue", "meta": {"icon": "", "title": "Flink", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/databaseResources", "name": "databaseResources", "component": "/resourceCenter/mysql/list.vue", "meta": {"icon": "menu_cloudDatabase", "title": "云数据库", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/mysqlList", "name": "mysqlList", "component": "/resourceCenter/mysql/list.vue", "meta": {"icon": "", "title": "MySQL", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/postgreSqlList", "name": "postgreSqlList", "component": "/resourceCenter/postgreSql/list.vue", "meta": {"icon": "", "title": "PostgreSQL", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}]}, {"path": "/productCenter", "name": "productCenter", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "icon_product_center", "title": "产品中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}, "children": [{"path": "/resArrangementList", "name": "resArrangementList", "component": "/resArrangement/list.vue", "meta": {"icon": "menu_productAutomaticOrchestration", "title": "产品组合编排", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/standards", "name": "standards", "component": "/configCenter/standards/list.vue", "meta": {"icon": "menu_chanpinguige", "title": "产品规格管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/expenseCenter", "name": "expenseCenter", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "icon_expense_center", "title": "费用中心", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "billManagement", "title": "账单管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}, {"path": "/operationsOverview", "name": "undeveloped", "component": "/computingPowerMap/operationsOverview/index.vue", "meta": {"icon": "costAnalysis", "title": "费用分析", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": true, "isKeepAlive": true}}]}, {"path": "/userCenter", "name": "userCenter", "redirect": "/userManagement", "meta": {"icon": "icon_management", "title": "用户中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/userManagement", "name": "userManagement", "component": "/managementCenter/userManagement/index.vue", "meta": {"icon": "menu_yonghuguanli", "title": "用户管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/externalUserManagement", "name": "externalUserManagement", "component": "/managementCenter/externalUserManagement/index.vue", "meta": {"icon": "menu_waibuUers", "title": "外部用户管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/roleManagement", "name": "roleManagement", "component": "/managementCenter/roleManagement/index.vue", "meta": {"icon": "menu_jiao<PERSON><PERSON><PERSON><PERSON>", "title": "角色管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/permissionManagement", "name": "permissionManagement", "component": "/managementCenter/permissionManagement/index.vue", "meta": {"icon": "menu_quanxianguanli", "title": "权限管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/tenantManagement", "name": "tenantManagement", "component": "/managementCenter/tenantManagement/index.vue", "meta": {"icon": "menu_zuhuguanli", "title": "租户管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/managementCenter", "name": "managementCenter", "redirect": "/workOrder", "meta": {"icon": "icon_management", "title": "管理中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/operationalAnalysis", "name": "operationalAnalysis", "component": "/managementCenter/operationalAnalysis/index.vue", "meta": {"icon": "menu_yunyingfenxi", "title": "平台运营分析", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/systemLogs", "name": "systemLogs", "component": "/computingPowerMap/systemLogs/index.vue", "meta": {"icon": "menu_logManagement", "title": "系统日志", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/reportCenter", "name": "reportCenter", "component": "/managementCenter/reportCenter/list.vue", "meta": {"icon": "menu_bao<PERSON><PERSON><PERSON>xing", "title": "报表中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/projectManagement", "name": "projectManagement", "component": "/computingPowerMap/projectManagement/index.vue", "meta": {"icon": "menu_xiang<PERSON><PERSON><PERSON>li", "title": "项目管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/resourcePool", "name": "resourcePool", "component": "/configCenter/resourcepool/list.vue", "meta": {"icon": "menu_zi<PERSON>chi", "title": "资源池", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/mirrorImage", "name": "mirrorImage", "component": "/configCenter/mirrorImage/list.vue", "meta": {"icon": "menu_jingxiangguanli", "title": "镜像管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/apiManagement", "name": "apiManagement", "component": "/managementCenter/apiManagement/index.vue", "meta": {"icon": "menu_apiGetway", "title": "API管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/monitorCenter", "name": "monitorCenter", "redirect": "/alarmModule/alarmList", "meta": {"icon": "icon_monitor_center", "title": "监控中心", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/businessMonitor", "name": "businessMonitor", "component": "/components/MicroPage.vue", "meta": {"icon": "menu_yewuSC", "title": "视图管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/businessList", "name": "businessList", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-yewu", "title": "视图列表", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/netWork/flowLog", "name": "flowLog", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-rizhi", "title": "流日志", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/alarmModule/alarmList", "name": "alarmList", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-gaojing", "title": "告警列表", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/businessList/detail", "name": "businessDetail", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-xiangqing", "title": "业务详情", "activeMenu": "/businessList", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/networkMonitor", "name": "networkMonitor", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-menu_NetSC", "title": "PCAP管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/netWork/pcapStrategy", "name": "pcapStrategy", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-gongdan", "title": "PCAP策略", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/netWork/pcapDownLoad", "name": "pcapDownLoad", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-xia<PERSON>", "title": "PCAP下载", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/topologyMonitor", "name": "topologyMonitor", "component": "/components/MicroPage.vue", "meta": {"icon": "menu_NetTopologySC", "title": "IP拓扑图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/topologyView", "name": "<PERSON><PERSON><PERSON><PERSON>", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-tuopu", "title": "拓扑视图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/topologyView/create", "name": "topologyCreate", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-<PERSON><PERSON><PERSON><PERSON>", "title": "创建拓扑", "activeMenu": "/topologyView", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/topologyView/detail", "name": "topologyDetail", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-xiangqing", "title": "拓扑详情", "activeMenu": "/topologyView", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/dialingTest", "name": "dialingTest", "component": "/components/MicroPage.vue", "meta": {"icon": "menu_baceSC", "title": "拨测管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/dialingTest/testTask", "name": "testTask", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-c<PERSON>i", "title": "拨测任务", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/dialingTest/meshpingDetail", "name": "meshpingDetail", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-xiangqing", "title": "拨测详情", "activeMenu": "/dialingTest/testTask", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/alarmMonitor", "name": "alarmMonitor", "component": "/components/MicroPage.vue", "meta": {"icon": "menu_gaojinSCa", "title": "告警管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/alarmModule/alarmRules", "name": "alarmRules", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-guize", "title": "告警规则", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/alarmModule/alarmControll", "name": "alarmControll", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-kongzhi", "title": "告警抑制", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/resourceMonitor", "name": "resourceMonitor", "component": "/components/MicroPage.vue", "meta": {"icon": "menu_ziyuanSC", "title": "资源管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/reSource/networkResource", "name": "networkResource", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-z<PERSON><PERSON>", "title": "网络资源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/selfMonitor/collector", "name": "collector", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-caiji", "title": "采集器监控", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/selfMonitor/component", "name": "component", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-zujian", "title": "组件监控", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/selfMonitor/diskManagement", "name": "diskManagement", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-cipan", "title": "磁盘清理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}, {"path": "/configurationManagement", "name": "configurationManagement", "component": "/components/MicroPage.vue", "meta": {"icon": "menu_selectSC", "title": "配置管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}, "children": [{"path": "/configurationManagement/BusinessReport", "name": "businessReport", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-b<PERSON><PERSON><PERSON>", "title": "业务报表", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}, {"path": "/selfMonitor/notificationMethod", "name": "notificationMethod", "component": "/components/MicroPage.vue", "meta": {"icon": "icon-tong<PERSON>", "title": "通知方式", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isDisabled": false, "isKeepAlive": true}}]}]}], "message": "成功"}