<template>
  <Card>
    <Title title="算力分配状态" />
    <div ref="barChartRef" style="width: 100%" class="power-bar-chart"></div>
  </Card>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeMount } from 'vue'
import Card from './Card.vue'
import Title from './Title.vue'
import * as echarts from 'echarts'
import { type ECharts } from 'echarts'
import { totalPhysicaltApi } from '@/api/modules/zsMap'

let barChart: ECharts | null = null
const props = defineProps<{
  areaCode: string
}>()

const emit = defineEmits(['click'])

const barChartRef = ref<HTMLDivElement | null>(null)

let deviceTypes: string[] = []
let used: number[] = []
let remain: number[] = []
let total: number[] = []

async function getTotalCompute() {
  const { entity }: any = await totalPhysicaltApi({ areaCode: props.areaCode })
  used = []
  remain = []
  total = []
  deviceTypes = []
  entity.forEach((item: any) => {
    used.push(item.allocatedCount)
    remain.push(item.availableCount)
    total.push(item.totalCount)
    deviceTypes.push(item.modelName)
  })
  setChartOption()
}
getTotalCompute()

watch(
  () => props.areaCode,
  () => {
    getTotalCompute()
  },
)

function setChartOption() {
  if (!barChart) return
  const totalOffset = 0
  barChart.setOption({
    grid: { left: 40, right: 20, top: 40, bottom: 30 },
    legend: {
      data: ['总量', '已分配', '剩余量'],
      right: 20,
      top: 8,
      itemWidth: 16,
      itemHeight: 8,
      icon: 'circle',
      selectedMode: false,
      textStyle: { color: '#3d3d3d', fontSize: 10 },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      textStyle: { fontSize: 10 },
      formatter: function (params: any) {
        let result = params[0].name + '<br/>'
        params.forEach(function (item: any) {
          let value = item.value
          if (item.seriesName === '总量') {
            value = item.value - totalOffset // 总量系列减去偏移量
          }
          result += item.marker + item.seriesName + ': ' + value + '<br/>'
        })
        return result
      },
    },
    xAxis: {
      type: 'category',
      data: deviceTypes,
      axisLine: { lineStyle: { color: '#b3cfff' } },
      axisLabel: { color: 'black', fontSize: 10, margin: 12 },
    },
    yAxis: {
      type: 'value',
      min: 0,
      splitNumber: 5,
      axisLine: { show: false },
      splitLine: { lineStyle: { color: '#eaf6ff', width: 1 } },
      axisLabel: { color: 'black', fontSize: 10 },
    },
    series: [
      {
        name: '总量',
        type: 'bar',
        data: total.map((v) => v + totalOffset),
        barWidth: '20px',
        barGap: '-100%',
        itemStyle: { color: '#e85d58', borderRadius: 0 },
        label: {
          show: true,
          position: 'top',
          color: '#3d3d3d',
          fontSize: 10,
          formatter: (params: any) => params.value - totalOffset,
        },
        z: 1,
      },
      {
        name: '已分配',
        type: 'bar',
        stack: 'usage',
        data: used,
        barWidth: '20px',
        itemStyle: { color: '#5d8af7', borderRadius: 0 },
        label: {
          position: 'inside',
          color: '#3d3d3d',
          fontSize: 10,
          formatter: (params: any) => (params.value > 0 ? params.value : ''),
        },
        z: 3,
      },
      {
        name: '剩余量',
        type: 'bar',
        stack: 'usage',
        data: remain,
        barWidth: '20px',
        itemStyle: { color: '#c6cbd3', borderRadius: 0 },
        label: {
          position: 'inside',
          color: '#3d3d3d',
          fontSize: 10,
          formatter: (params: any) => (params.value > 0 ? params.value : ''),
        },
        z: 2,
      },
    ],
  })

  // 添加点击事件监听
  barChart.off('click')
  barChart.on('click', (params) => {
    if (params.componentType === 'series' && params.seriesType === 'bar') {
      // 获取点击的柱状图类型（设备类型）
      const clickedModelName = deviceTypes[params.dataIndex]
      emit('click', clickedModelName)
    }
  })
}

// 原始数值堆叠柱状图数据
onMounted(() => {
  barChart = echarts.init(barChartRef.value!, null, { height: 185, renderer: 'svg' })
  setChartOption()
})

onBeforeMount(() => {
  barChart?.dispose()
})
</script>

<style scoped>
.power-bar-chart {
  width: 100%;
  background: transparent;
  margin-top: 0;
}
</style>
