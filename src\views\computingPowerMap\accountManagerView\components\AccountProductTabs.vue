<template>
  <div class="product-tabs">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="云主机(6)" name="ecs"></el-tab-pane>
      <el-tab-pane label="GPU云主机(8)" name="gpu"></el-tab-pane>
      <el-tab-pane label="云硬盘(18)" name="disk"></el-tab-pane>
      <el-tab-pane label="对象存储(19)" name="oss"></el-tab-pane>
      <el-tab-pane label="NAT网关(12)" name="nat"></el-tab-pane>
      <el-tab-pane label="负载均衡(12)" name="lb"></el-tab-pane>
      <el-tab-pane label="弹性公网(12)" name="eip"></el-tab-pane>
      <el-tab-pane label="VPN(12)" name="vpn"></el-tab-pane>
      <el-tab-pane label="MySQL云数据库(12)" name="db"></el-tab-pane>
    </el-tabs>
    <div class="table-toolbar">
      <el-input placeholder="主机名称" style="width: 180px; margin-right: 12px" />
      <el-select placeholder="租户" style="width: 120px; margin-right: 12px" />
      <el-button icon="el-icon-search" type="primary">搜索</el-button>
      <el-button icon="el-icon-filter">筛选</el-button>
      <el-button icon="el-icon-arrow-down">展开</el-button>
    </div>
    <el-table :data="tableData" style="width: 100%; margin-top: 12px">
      <el-table-column prop="index" label="序号" width="60" />
      <el-table-column prop="name" label="主机名称" />
      <el-table-column prop="tenant" label="租户" />
      <el-table-column prop="platform" label="云平台" />
      <el-table-column prop="resource" label="资源池" />
      <el-table-column prop="zone" label="可用区" />
      <el-table-column prop="vpc" label="VPC" />
      <el-table-column prop="subnet" label="子网" />
      <el-table-column prop="spec" label="实例规格" />
      <el-table-column prop="image" label="镜像" />
      <el-table-column prop="systemDisk" label="系统盘" />
      <el-table-column prop="dataDisk" label="数据盘" />
      <el-table-column prop="bandwidth" label="公网带宽" />
      <el-table-column prop="instanceName" label="实例名称" />
      <el-table-column prop="loginName" label="登录名" />
      <el-table-column prop="owner" label="所属租户" />
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next, sizes, total"
      :total="100"
      :page-size="20"
      style="margin-top: 12px; text-align: right"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const activeTab = ref('ecs')
const tableData = ref([
  {
    index: 1,
    name: '按月计费',
    tenant: 'test',
    platform: '融合公有云',
    resource: '浙江-某资源池',
    zone: '某可用区',
    vpc: 'hezitao_vpc',
    subnet: 'hezitao_vnet',
    spec: '通用型/1C1GB',
    image: '7.6/CentOS',
    systemDisk: 'SAS/40GB',
    dataDisk: 'SAS/40GB',
    bandwidth: '5Mbps',
    instanceName: 'hjl-0615-01',
    loginName: 'root',
    owner: 'e5520250612',
  },
])
</script>

<style scoped>
.product-tabs {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
}
.table-toolbar {
  display: flex;
  align-items: center;
  margin: 12px 0;
}
</style>
