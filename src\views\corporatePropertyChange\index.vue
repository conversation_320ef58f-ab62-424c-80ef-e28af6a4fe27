<template>
  <el-drawer size="80%" title="变更信息" direction="btt" :before-close="handleCloseBefore">
    <template v-for="(goods, goodsIndex) in goodsList" :key="goods.uuid">
      <goodsItem
        @deleteGoods="deleteGoods(goodsList, goodsIndex)"
        :style="goodsIndex !== 0 ? 'margin-top:20px' : ''"
        :goods="goods"
        :type="type"
      ></goodsItem>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ElMessageBox } from 'element-plus'
import goodsItem from './goodsItem.vue'
import type { ICartsModel } from './propertyChange'
import SlMessage from '@/components/base/SlMessage'
import { corporateChangeWorkOrderCreate } from '@/api/modules/resourecenter'
import type { IGoodsType } from './model'

const props = defineProps<{
  goodsList: ICartsModel<IGoodsType>
  type: IGoodsType
}>()

const emit = defineEmits(['update:modelValue', 'confirm'])
const handleCloseBefore = (done: () => void) => {
  ElMessageBox.confirm('表单未提交，确定要关闭吗？').then(() => {
    done()
    emit('update:modelValue', false)
  })
}
const deleteGoods = (goodsList: any[], goodsIndex: number) => {
  goodsList.splice(goodsIndex, 1)
  if (goodsList.length === 0) {
    emit('update:modelValue', false)
  }
}
const handleClose = () => {
  handleCloseBefore(() => {
    emit('update:modelValue', false)
  })
}
function addGoodsParams(order: any, goodsItem: ICartsModel<IGoodsType>, diff: boolean = false) {
  const goods = goodsItem
  order[`${props.type}PropertyList`] = goods
    .map((item) => {
      const parmas = {
        resourceDetailId: item.resourceDetailId,
        resourceType: item.resourceType,
        changeType: [] as string[],
      } as any
      item.props.forEach((prop) => {
        switch (prop.changeType) {
          case 'instance_spec_change':
            if (prop.resourceType === 'ecs' && item.domainCode === 'plf_prov_nwc_zj_nfvo') {
              parmas.templateCode = prop.templateCode
            }
            if (!diff) {
              parmas.flavorName = prop.after[0]
              parmas.changeType.push(prop.changeType)
              return
            }
            if (prop.after[0] && prop.after[0] !== prop.before) {
              parmas.flavorName = prop.after[0]
              parmas.changeType.push(prop.changeType)
            }
            break
          case 'storage_expand':
            const changed = prop.after
              .map((e, index) => {
                if (!diff) {
                  return {
                    volumeType: e[0],
                    volumeSize: e[1],
                    id: e[2],
                  }
                }
                if (e[1] && e[1] === prop.before[index][1]) return null
                return {
                  volumeType: e[0],
                  volumeSize: e[1],
                  id: e[2],
                }
              })
              .filter(Boolean)
            if (changed.length > 0) {
              parmas.volumeChangeReqModels = changed
              parmas.changeType.push(prop.changeType)
            }
            break
          case 'bandwidth_expand':
            if (!diff) {
              parmas.eipBandwidth = prop.after
              parmas.eipId = prop.eipId
              parmas.changeType.push(prop.changeType)
              return
            }
            if (prop.after && prop.after !== prop.before) {
              parmas.eipBandwidth = prop.after
              parmas.eipId = prop.eipId
              parmas.changeType.push(prop.changeType)
            }
            break
        }
      })
      if (parmas.changeType.length > 0) return parmas
    })
    .filter(Boolean)
}
function composeArguments(catrsModel: ICartsModel<IGoodsType>, diff: boolean = false) {
  const order = {
    // 产品列表
    ecsPropertyList: [],
    gcsPropertyList: [],
    evsPropertyList: [],
    obsPropertyList: [],
    slbPropertyList: [],
    natPropertyList: [],
    eipPropertyList: [],
    rdsMysqlPropertyList: [],
    redisPropertyList: [],
  }
  // 添加产品信息
  addGoodsParams(order, catrsModel, diff)
  return order
}
function isEmptyChange(order: any) {
  return Object.values(order).every((item: any) => item.length === 0)
}
function getFormRefs() {
  return props.goodsList.map((ele) => ele.props.map((e) => e.ref)).flat()
}
function submit() {
  return new Promise((resolve, reject) => {
    const formRefs = getFormRefs()
    if (formRefs.length < 1) {
      SlMessage.error('请添加产品')
      return reject(new Error('请添加产品'))
    }
    const formPromises = formRefs.map((ele: any) => ele.validate())
    Promise.all(formPromises)
      .then(async (res) => {
        if (res.length > 0 && res.every(Boolean)) {
          // 组装入参
          const order = composeArguments(props.goodsList, true)
          const isEmpty = isEmptyChange(order)
          if (isEmpty) {
            SlMessage.warning('无变更（变更前后不一致才视为变更）')
            return reject(new Error('无变更'))
          }
          const { code } = await corporateChangeWorkOrderCreate(order)
          resolve(true)
          if (code === 200) {
            SlMessage.success('提交成功')
            emit('update:modelValue', false)
            emit('confirm')
          }
        } else {
          SlMessage.error('请填写完整信息')
          reject(new Error('请填写完整信息'))
        }
      })
      .catch((err) => {
        reject(err)
      })
  })
}
</script>
