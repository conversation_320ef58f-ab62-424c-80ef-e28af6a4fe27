<template>
  <div class="dashboard-title-power">
    <div class="dashboard-title-power-text">
      <span
        class="dashboard-title-power-text-item"
        v-for="(num, index) in numList"
        :key="num + index"
        >{{ num }}
      </span>
    </div>
    <div class="dashboard-title-power-total">算力总数</div>
    <div class="dashboard-title-power-unit">TFLOPS</div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps({
  totalPower: {
    type: Number,
    default: 0,
  },
})

const numList = computed(() => {
  return props.totalPower.toString().padStart(7, '0').split('')
})
</script>
<style scoped>
.dashboard-title-power-total {
  top: 14px;
  position: absolute;
  width: 100%;
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', Arial, sans-serif;
  color: #5272a6;
  font-size: 32px;
  font-weight: 100;
}
.dashboard-title-power-unit {
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', Arial, sans-serif;
  top: 50px;
  position: absolute;
  width: 100%;
  font-size: 16px;
  color: #5272a6;
}
.dashboard-title-power-text {
  position: absolute;
  top: -26px;
  width: 100%;
  height: 40px;
  line-height: 40px;
  z-index: 1000;
  color: #32517c;
  font-weight: bold;
}
.dashboard-title-power-text-item {
  display: inline-block;
  width: 30px;
  /* 磨砂白色背景 */
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  margin: 0 2px;
}

.dashboard-title-power {
  position: relative;
  z-index: 100;
  min-width: 360px;
  text-align: center;
  font-size: 28px;
  height: 80px;
}

.dashboard-title-power::before {
  content: '';
  position: absolute;
  left: -206px;
  top: -20px;
  width: 150%;
  height: 100%;
  background-image: url('/images/dashboard/decoration.png');
  background-repeat: no-repeat;
  background-size: auto 100%;
  transform: scaleX(-1);
  z-index: -1;
}

.dashboard-title-power::after {
  content: '';
  position: absolute;
  right: -206px;
  top: -20px;
  width: 150%;
  height: 100%;
  background-image: url('/images/dashboard/decoration.png');
  background-repeat: no-repeat;
  background-size: auto 100%;
  z-index: -1;
}
</style>
