<template>
  <div class="account-detail-card">
    <div class="title">
      <img
        src="/images/accountView/kehuguanli.svg"
        style="width: 24px; height: 26px"
        alt="客户详情"
        class="title-icon"
      />
      <span>客户详情</span>
    </div>
    <el-row class="info-list" :gutter="24">
      <el-col v-for="item in columns" :key="item.prop" :span="item.span" class="info-item">
        <div class="label">{{ item.label }}:</div>
        <div class="value">{{ props.data?.[item.prop] ?? '-' }}</div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ data: any }>()

const columns = [
  { label: '客户账号', prop: 'customNo', span: 8 },
  { label: '企业名称', prop: 'customName', span: 15 },
  { label: '计费号', prop: 'billId', span: 8 },
  { label: '企业联系人', prop: 'contactName', span: 7 },
  { label: '邮箱', prop: 'email', span: 9 },
  { label: '联系电话', prop: 'contactMobile', span: 8 },
  { label: '创建时间', prop: 'createdTime', span: 15 },
]
</script>

<style lang="scss" scoped>
.account-detail-card {
  background: #fff;
  background-image: url('/images/accountView/客户详情.png');
  background-size: 240px 140px;
  background-repeat: no-repeat;
  background-position: 100% 0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  // min-height: 200px;
}
.title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}
.info-list {
  font-size: 12px;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .label {
    color: #999;
    font-weight: bold;
    max-width: 80px;
    min-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .value {
    color: #333;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
