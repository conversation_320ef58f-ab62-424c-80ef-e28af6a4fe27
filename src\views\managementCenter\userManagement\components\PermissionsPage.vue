<template>
  <div class="table-box">
    <sl-page-header
      title="绑定API"
      title-line=""
      :icon="{
        class: 'page_yonghuguanli',
        color: '#0052D9',
        size: '40px',
      }"
    ></sl-page-header>
    <div class="sl-page-content table-main">
      <SlProTable
        ref="proTable"
        :columns="columns"
        :request-api="getPermissionList"
        row-key="id"
        :is-show-pagination="false"
      >
        <template #detailList="{ row }">
          <el-checkbox-group v-model="row.permissions">
            <el-checkbox
              v-for="item in row.detailList"
              :key="item.id"
              :label="item.urlNote"
              :value="item.id"
            />
          </el-checkbox-group>
        </template>
      </SlProTable>
    </div>
    <div class="page-footer">
      <el-button @click="emit('close')">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'

import { reactive, ref, onMounted } from 'vue'
import SlMessage from '@/components/base/SlMessage'
import { selectAPiDetailApi } from '@/api/modules/managementCenter'
import { getUserBindApiInfo, bindUserApiByIdApi } from '@/api/modules/managementCenter'
const props = defineProps<{
  roleId: string | number
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const proTable = ref<ProTableInstance>()

const getPermissionList = () => {
  return selectAPiDetailApi({
    pageNum: 1,
    pageSize: 9999,
  })
}

onMounted(async () => {
  const { entity }: any = await getUserBindApiInfo({
    userId: props.roleId,
  })
  setTimeout(() => {
    const tableData = proTable.value?.tableData || []

    entity.forEach((item: any) => {
      // proTable.value?.element?.toggleRowSelection(item, true)
      const menu = tableData.find(
        (i: any) => i.domainCode == item.domainCode && i.resTypeName == item.resTypeName,
      )
      if (menu) {
        menu.permissions = item.detailList.map((i: any) => i.id)
      }
    })
  }, 1000)
})

const columns = reactive<ColumnProps[]>([
  { type: 'index', label: '序号', width: 55 },
  { prop: 'domainName', label: '云平台', width: 230 },
  { prop: 'resTypeName', label: 'API名称', width: 230 },
  // { type: 'selection', width: 55 },
  { prop: 'detailList', label: '操作权限', align: 'left', minWidth: 350 },
])

const submit = async () => {
  const tableData = proTable.value?.tableData || []

  // const selectedListIds = proTable.value?.selectedListIds || []
  // console.log(selectedListIds)
  // 确保 item.permissions 是一个数组，并且扁平化处理
  const ids = tableData.reduce<string[]>((acc, item) => {
    if (Array.isArray(item.permissions)) {
      return [...acc, ...item.permissions]
    }
    return acc
  }, [])
  console.log(ids)
  await bindUserApiByIdApi({
    userIds: [props.roleId],
    interfaceIds: [...ids],
    // interfaceIds: [...selectedListIds, ...ids].map((i) => {
    //   return { privilegeId: i, status: 1 }
    // }),
  })
  SlMessage.success('保存成功')
  emit('close')
}
</script>

<style scoped>
.sl-page-content {
  overflow: hidden;
}
.page-footer {
  height: 48px;
  padding: 8px 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
