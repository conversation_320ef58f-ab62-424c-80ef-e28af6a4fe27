<template>
  <div class="table-box">
    <sl-page-header
      title="权限管理"
      title-line="权限管理提供了菜单级权限和按钮级权限的管理功能。"
      :icon="{
        class: 'page-quanxian',
        color: '#0052D9',
        size: '40px',
      }"
    ></sl-page-header>
    <div class="sl-page-content table-main">
      <SlProTable ref="proTable" :columns="columns" :request-api="selectMenusDetailApi">
        <template #search>
          <el-button type="primary" :icon="Plus" @click="() => openDialog()">添加权限</el-button>
        </template>
        <template #thirdName="scope">
          <span class="operation-btn" v-for="item in scope.row.menus" :key="item.id">{{
            item.name
          }}</span>
        </template>
        <template #operation="scope">
          <el-button
            type="primary"
            link
            :icon="EditPen"
            @click="openDialog(scope.row)"
            v-permission="'Update'"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            link
            :icon="Delete"
            @click="deletePermission(scope.row.id)"
            v-permission="'Delete'"
          >
            删除
          </el-button>
        </template>
      </SlProTable>
    </div>
    <!-- 创建权限 -->
    <el-drawer
      v-model="showPermissionAdd"
      :title="currentPermission?.id ? '编辑权限' : '添加权限'"
      :before-close="handlePermissionAddClose"
      size="800"
      destroy-on-close
      :close-on-press-escape="false"
    >
      <PermissionAdd
        :current-permission="currentPermission"
        @submit="handlePermissionAddSubmit"
        @close="handlePermissionAddClose"
      ></PermissionAdd>
    </el-drawer>
  </div>
</template>

<script setup lang="ts" name="tenantManagement">
import { deletePermissionByIdApi, selectMenusDetailApi } from '@/api/modules/managementCenter'
import { Delete, EditPen, Plus } from '@element-plus/icons-vue'
import { reactive, ref } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import type { PermissionType } from './interface/type'
import PermissionAdd from './components/PermissionAdd.vue'
import SlMessage from '@/components/base/SlMessage'
// -----------------------用户列表------------------------------
const proTable = ref<ProTableInstance>()

const columns = reactive<ColumnProps<PermissionType>[]>([
  { type: 'index', fixed: 'left', label: '序号', width: 55 },
  {
    prop: 'name',
    label: '菜单权限',
    width: 200,
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  {
    prop: 'thirdName',
    label: '操作权限',
    align: 'left',
    search: { el: 'input', checked: true, defaultDisabled: true },
  },
  { prop: 'operation', label: '操作', fixed: 'right', width: 250 },
])

const showPermissionAdd = ref(false)
const currentPermission = ref()
const openDialog = (row?: any) => {
  showPermissionAdd.value = true
  currentPermission.value = {}
  if (!row) return
  currentPermission.value = row
}

const handlePermissionAddClose = () => {
  showPermissionAdd.value = false
}

const handlePermissionAddSubmit = () => {
  proTable.value?.getTableList()
  showPermissionAdd.value = false
}

/**
 * 删除
 * @param id
 */
const deletePermission = async (id: number) => {
  await ElMessageBox.confirm('确认删除该权限吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await deletePermissionByIdApi({
    menuId: id,
  })
  SlMessage.success('删除成功')
  proTable.value?.getTableList()
}
</script>

<style lang="scss" scoped>
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow: hidden;
}
.operation-btn {
  margin-right: 10px;
}
</style>
