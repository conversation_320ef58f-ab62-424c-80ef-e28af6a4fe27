import type { RouteRecordRaw } from 'vue-router'

const overviewRouter: RouteRecordRaw[] = [
  {
    path: '/operationsOverview',
    component: () => import('@/views/computingPowerMap/operationsOverview/index.vue'),
    name: 'operationsOverview',
    // name: 'VPC',
    meta: {
      title: '运营总览',
      icon: 'operationsOverview',
    },
  },
  {
    path: '/tenantView',
    component: () => import('@/views/computingPowerMap/tenantView/index.vue'),
    name: 'tenantView',
    meta: {
      title: '租户视图',
      icon: 'icon-gongdan',
      activeMenu: 'tenantView',
    },
  },
  {
    path: '/viewOfPublicTenants',
    component: () => import('@/views/computingPowerMap/viewOfPublicTenants/index.vue'),
    name: 'viewOfPublicTenants',
    meta: {
      title: '租户视图',
      icon: 'icon-gongdan',
      activeMenu: 'viewOfPublicTenants',
    },
  },
  {
    path: '/alarmCenterList',
    component: () => import('@/views/computingPowerMap/alarmList/index.vue'),
    name: 'alarmCenterList',
    meta: {
      title: '告警管理',
      activeMenu: 'alarmCenterList',
    },
  },
  {
    path: '/accountManagerView',
    component: () => import('@/views/computingPowerMap/accountManagerView/index.vue'),
    name: 'accountManagerView',
    meta: {
      title: '  客户经理视图',
      activeMenu: 'accountManagerView',
    },
  },
  {
    path: '/accountDetail',
    component: () => import('@/views/computingPowerMap/accountManagerView/detail.vue'),
    name: 'accountDetail',
    meta: {
      title: '客户详情',
      activeMenu: 'accountDetail',
    },
  },
]

export default overviewRouter
