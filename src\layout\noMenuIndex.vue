<!-- 经典布局 -->
<template>
  <el-container class="layout">
    <el-header>
      <div class="header-lf mask-image">
        <div class="logo flx-center">
          <img class="logo-img" src="@/assets/images/logo.png" alt="logo" />
          <span class="logo-text"> 算力工作台 </span>
        </div>
        <!--        <ToolBarLeft />-->
      </div>
      <div class="header-ri">
        <ToolBarRight />
      </div>
    </el-header>
    <el-container class="classic-content">
      <el-container class="classic-main">
        <bizsinfo />
      </el-container>
    </el-container>
  </el-container>
</template>

<script setup lang="ts" name="layoutClassic">
import bizsinfo from '@/views/computingPowerMap/bizsinfo/index.vue'
import ToolBarRight from '@/layout/components/Header/ToolBarRight.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'

const globalDic = useGlobalDicStore()
globalDic.initDic()
</script>

<style scoped lang="scss">
.el-container {
  width: 100%;
  height: 100%;
  :deep(.el-header) {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    padding: 0 15px 0 0;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-header-border-color);
    .header-lf {
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      .logo {
        flex-shrink: 0;
        width: 210px;
        margin-right: 16px;
        .logo-img {
          width: 28px;
          object-fit: contain;
        }
        .logo-text {
          margin-left: 6px;
          font-size: 21.5px;
          font-weight: bold;
          color: var(--el-header-logo-text-color);
          white-space: nowrap;
        }
      }
    }
  }
  .classic-content {
    display: flex;
    height: calc(100% - 55px);
    :deep(.el-aside) {
      width: auto;
      background-color: var(--el-menu-bg-color);
      border-right: 1px solid var(--el-aside-border-color);
      .aside-box {
        display: flex;
        flex-direction: column;
        height: 100%;
        transition: width 0.3s ease;
        .el-menu {
          width: 100%;
          overflow-x: hidden;
          border-right: none;
        }
      }
    }
    .classic-main {
      display: flex;
      flex-direction: column;
    }
  }
}
.layout {
  background-color: #fff;
  height: 100vh;
  .menu-title {
    font-size: 16px;
    font-weight: 600;
  }
  :deep(.el-menu-item-group__title) {
    height: 40px;
    padding: 0 10px;
    margin-top: 10px;
    margin-bottom: 5px;
    color: #333;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to bottom, white, rgb(224, 232, 246));
    img {
      width: 30px;
      height: 30px;
      margin-top: 10px;
    }
  }

  :deep(.sidebar .el-scrollbar__view) {
    padding: 0 10px;
  }
  :deep(.sidebar.is-collapse .el-scrollbar__view) {
    padding: 0 10px 0 0;
  }

  :deep(.el-menu--collapse .el-menu-item-group__title) {
    height: 0;
  }
}
// 下面是根据产品图修改的样式  -- 后面估计会该
.aside-box {
  border-right: 1px solid rgba(50, 60, 76, 0.1);
}
</style>
