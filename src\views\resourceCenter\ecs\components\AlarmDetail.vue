<template>
  <div class="alarm-detail">
    <div class="alarm-detail-item">
      <div class="alarm-detail-item-label">告警级别：</div>
      <div class="alarm-detail-item-value">{{ alarmData.alarmSeverity }}</div>
    </div>
    <div class="alarm-detail-item">
      <div class="alarm-detail-item-label">告警标题：</div>
      <div class="alarm-detail-item-value">{{ alarmData.alarmTitle }}</div>
    </div>

    <div class="alarm-detail-item">
      <div class="alarm-detail-item-label">时间：</div>
      <div class="alarm-detail-item-value">{{ alarmData.eventTime }}</div>
    </div>
    <div class="alarm-detail-item">
      <div class="alarm-detail-item-label">告警正文：</div>
      <div class="alarm-detail-item-value-pre">{{ alarmData.alarmText }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  alarmData: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style lang="scss" scoped>
.alarm-detail {
  .alarm-detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .alarm-detail-item-label {
      width: 100px;
      font-weight: bold;
      // text-align: right;
    }
    .alarm-detail-item-value {
      flex: 1;
    }
    .alarm-detail-item-value-pre {
      flex: 1;
      overflow: auto;
      background: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
    }
  }
}
</style>
