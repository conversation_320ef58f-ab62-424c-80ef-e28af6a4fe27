<template>
  <div class="sl-card mb8">
    <sl-block-title class="pb20" title="资源申请概览"></sl-block-title>

    <div class="box-row">
      <div class="box-col mr30" v-for="item in showInfo" :key="item.label">
        <p class="information-lable">{{ item.label }} &nbsp;:</p>

        <RedVlaueNode :item="item" />
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed, type VNode } from 'vue'
import type { goodsTypeCodeEnum } from '../../../interface/type'

const props = withDefaults(
  defineProps<{
    formData: any
    goodsType: goodsTypeCodeEnum | undefined
  }>(),
  {
    formData: () => ({}),
  },
)
const showInfoList = {
  ecs: {
    resourceNumbers: '云主机',
    vcpuNumbers: 'vCPU',
    ramNumbers: '内存',
    storageNumbers: '存储',
    bandWidthNumbers: '带宽',
  },
  gcs: {
    resourceNumbers: 'GPU云主机',
    vcpuNumbers: 'vCPU',
    vgpuNumbers: 'vGPU',
    ramNumbers: '内存',
    storageNumbers: '存储',
    bandWidthNumbers: '带宽',
  },
  obs: {
    resourceNumbers: '对象存储',
    storageNumbers: '存储',
  },
  evs: {
    resourceNumbers: '云硬盘',
    storageNumbers: '存储',
  },
  shareEvs: {
    resourceNumbers: '共享数据盘',
    storageNumbers: '存储',
  },
  slb: {
    resourceNumbers: '负载均衡',
    bandWidthNumbers: '带宽',
  },
  nat: {
    resourceNumbers: 'NAT网关',
    bandWidthNumbers: '带宽',
  },
  eip: {
    resourceNumbers: '弹性公网',
    bandWidthNumbers: '带宽',
  },
  cq: {
    resourceNumbers: '容器配置',
    cpuNumbers: 'vCPU',
    memoryNumbers: '内存',
    gpuRatioNumbersTmp: 'GPU算力',
    gpuMemoryNumbers: 'GPU显存',
    gpuCoreNumbers: 'GPU卡数量',
  },
  mysql: {
    resourceNumbers: 'MySQL云数据库',
    vcpuNumbers: 'vCPU',
    ramNumbers: '内存',
    storageNumbers: '存储',
    bandWidthNumbers: '带宽',
  },
  postgreSql: {
    resourceNumbers: 'PostgreSQL云数据库',
    vcpuNumbers: 'vCPU',
    ramNumbers: '内存',
    storageNumbers: '存储',
    bandWidthNumbers: '带宽',
  },
  redis: {
    resourceNumbers: '通用Redis',
    vcpuNumbers: 'vCPU',
    ramNumbers: '内存',
    storageNumbers: '存储',
    bandWidthNumbers: '带宽',
  },
  bldRedis: {
    resourceNumbers: '国产Redis',
  },
  backup: {
    resourceNumbers: '云灾备',
  },
  nas: {
    resourceNumbers: 'NAS',
    storageNumbers: '存储大小',
  },
  vpn: {
    resourceNumbers: 'VPN',
    bandWidthNumbers: '带宽',
  },
  kafka: {
    resourceNumbers: 'Kafka',
  },
  pm: {
    resourceNumbers: '裸金属',
  },
  flink: {
    resourceNumbers: 'Flink',
  },
  es: {
    resourceNumbers: 'ElasticSearch',
  },
}
const showInfo = computed(() => {
  let key = props.goodsType as keyof typeof showInfoList
  const info = showInfoList[key] || {}

  // 2. 定义过滤函数，提取到单独的函数中增强可读性
  const isValidValue = (value: any): boolean => {
    // 排除常见的无效值
    if (value === undefined || value === null || value === '') return false

    // 特殊处理数值类型
    if (typeof value === 'number' && value === 0) return false

    // 特殊处理字符串类型
    if (typeof value === 'string') {
      // 排除特定的字符串值
      const invalidStrings = ['0', '0M', '0GB', '0G', '0个']
      return !invalidStrings.includes(value)
    }

    return true
  }

  // 3. 转换并过滤数据
  return Object.keys(info)
    .map((key: string) => ({
      label: info[key as keyof typeof info] as string,
      value: props.formData[key],
    }))
    .filter((item) => isValidValue(item.value))
})

// 回显value的值

const RedVlaueNode = ({ item }: { item: FormDataType }): VNode => {
  return <p class="information-value"> &nbsp;{item.value}</p>
}
</script>

<style lang="scss" scoped>
.box-row {
  display: flex; /* 使用Flexbox布局 */
  flex-wrap: wrap; /* 允许换行 */
  // justify-content: space-between;
}

.box-col {
  margin-right: 10px;
  width: fit-content;
}
.information-lable {
  font-weight: 700;
}
p {
  display: inline-block;
  margin-left: 10px;
}
</style>
