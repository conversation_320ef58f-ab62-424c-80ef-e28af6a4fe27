<template>
  <div class="outer-container">
    <div class="dashboard-container">
      <!-- 顶部标题和算力总数 -->
      <Head />
      <div class="dashboard-main">
        <!-- 左侧面板 -->
        <aside class="dashboard-side dashboard-side-left">
          <PowerBar :area-code="areaCode" />
          <MemLine :data="memLineData" :x-axis-data="xAxisData" />
          <GpuLine :data="gpuLineData" :x-axis-data="xAxisData" />
        </aside>
        <!-- 中间地图和卡片 -->
        <main class="dashboard-center">
          <Dashboardmap @selectChange="handleSelectChange" />
        </main>
        <aside class="dashboard-side dashboard-side-right">
          <!-- 右侧面板 -->
          <div class="dashboard-side-right-top">
            <Slicestatus :area-code="areaCode" />
          </div>
          <div class="dashboard-side-right-bottom">
            <SliceGaugestatus :area-code="areaCode" />
          </div>
        </aside>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PowerBar from './components/PowerBar.vue'
import MemLine from './components/MemLine.vue'
import GpuLine from './components/GpuLine.vue'
import Dashboardmap from './components/Dashboardmap.vue'
import Slicestatus from './components/Slicestatus.vue'
import SliceGaugestatus from './components/SliceGaugestatus.vue'
import Head from './components/Head.vue'
import { onMounted, ref } from 'vue'
import { queryDevicesMetricPercentApi } from '@/api/modules/zsMap'

const areaCode = ref<string>('')
let maxInnerWidth = 1440
let maxInnerHeight = 778

const colorMap = {
  杭州: '#2a6bff',
  绍兴: '#00d4aa',
  台州: '#ff6b35',
  温州: '#ffd700',
  丽水: '#ff4757',
  衢州: '#a55eea',
  嘉兴: '#26de81',
  湖州: '#fd79a8',
  舟山: '#fdcb6e',
  宁波: '#6c5ce7',
}

const memLineData = ref<any>([])
const gpuLineData = ref<any>([])
const xAxisData = ref<any>([])

async function getDevicesMetricPercent() {
  const { entity }: any = await queryDevicesMetricPercentApi({
    areaCode: areaCode.value,
  })
  xAxisData.value = entity[0]?.modelList.map((ele: any) => ele.time.split(' ')[1])
  const tempMemLineData: any[] = []
  const tempGpuLineData: any[] = []
  entity.forEach((item: any) => {
    tempMemLineData.push({
      city: {
        name: item.areaCode + '市',
        color: colorMap[item.areaCode as keyof typeof colorMap]!,
      },
      data: item.modelList.map((ele: any) => ele.memUtilpercent),
    })
    tempGpuLineData.push({
      city: {
        name: item.areaCode + '市',
        color: colorMap[item.areaCode as keyof typeof colorMap]!,
      },
      data: item.modelList.map((ele: any) => ele.gpuUtilPercent),
    })
  })
  memLineData.value = tempMemLineData
  gpuLineData.value = tempGpuLineData
}
getDevicesMetricPercent()
onMounted(() => {
  const container = document.querySelector('.dashboard-container') as HTMLElement
  const scaleX = window.innerWidth / maxInnerWidth
  const scaleY = window.innerHeight / maxInnerHeight
  const transValueX = (1 - 1 / scaleX) * 50
  const transValueY = (1 - 1 / scaleY) * 50
  container.style.transform = `scale(${scaleX}, ${scaleY}) translate(${transValueX}%, ${transValueY}%)`
})
window.addEventListener('resize', () => {
  const container = document.querySelector('.dashboard-container') as HTMLElement
  const scaleX = window.innerWidth / maxInnerWidth
  const scaleY = window.innerHeight / maxInnerHeight
  const transValueX = (1 - 1 / scaleX) * 50
  const transValueY = (1 - 1 / scaleY) * 50
  container.style.transform = `scale(${scaleX}, ${scaleY}) translate(${transValueX}%, ${transValueY}%)`
})

const handleSelectChange = (selectedRegion: string) => {
  areaCode.value = selectedRegion
  getDevicesMetricPercent()
}
</script>

<style scoped>
.outer-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
.dashboard-container {
  width: 1440px;
  height: 778px;
  background: url('/images/dashboard/bg.png') no-repeat 0 0;
  background-size: contain;
  display: flex;
  flex-direction: column;
}
.dashboard-main {
  flex: 1;
  display: flex;
  padding: 12px 10px 0px 12px;
  box-sizing: border-box;
}
.dashboard-side {
  width: 360px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.dashboard-side-left {
  margin-right: 6px;
}
.dashboard-side-right {
  margin-left: 6px;
  padding-right: 12px;
  padding-bottom: 20px;
}
.dashboard-side-right-top {
  height: 42%;
}

.dashboard-side-right-bottom {
  height: 58%;
}

.dashboard-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  background: url('/images/dashboard/bottom.png') no-repeat left bottom;
  background-size: contain;
}
</style>
