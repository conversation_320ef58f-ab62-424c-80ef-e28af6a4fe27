import http from '@/api'
import { WOC } from '../config/servicePort'
import { changeDateFormat } from '@/utils'

/**
 * @name 订单中心 -- 获取订单列表
 */
export const corporateOrderPage = (config: any) =>
  http.post<any>(WOC + '/corporate/order/page', changeDateFormat(config, ['createTime']))

/**
 * @name 订单中心 -- 获取订单详情
 */
export const corporateOrderDetail = (config: any) =>
  http.post<any>(WOC + '/corporate/order/detail', config)

/**
 * @name 订单中心 -- 获取退订列表
 */
export const unsubscribeOrderPage = (config: any) =>
  http.post<any>(WOC + '/dg/recovery/workOrder/page', changeDateFormat(config, ['createTime']))

/**
 * @name 订单中心 -- 获取退订详情
 */
export const unsubscribeOrderDetail = (config: any) =>
  http.post<any>(WOC + '/dg/recovery/workOrder/detail', config)

/**
 * @name 订单中心 -- 取消退订
 */
export const cancelUnsubscribeOrder = (config: any) =>
  http.post<any>(WOC + '/dg/recovery/workOrder/cancel', config)

/**
 * @name 订单中心 -- 变更列表
 */
export const changeWorkOrderPageApi = (config: any) =>
  http.post<any>(WOC + '/dg/changeWorkOrder/page', changeDateFormat(config, ['createTime']))

/**
 * @name 订单中心 -- 变更详情
 */
export const changeWorkOrderDetailApi = (config: any) =>
  http.post<any>(WOC + '/dg/changeWorkOrder/detail', config)
