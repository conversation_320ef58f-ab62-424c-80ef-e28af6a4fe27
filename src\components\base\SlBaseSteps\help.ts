export interface IDataItem {
  title: string
  branch?: IDataItem[]
  branchSpace?: number
  spaceTotal?: number
  fontSize?: string
  id: string
}
interface IConfig {
  branchSpace?: number
  activeId: string
  fontSize?: string
}

type IData = IDataItem[]

export function configData(data: IData, config: IConfig) {
  const temp: IDataItem[] = []
  const positon: number[] = []
  let isFindActive = false

  const fn = (data: IData, config: IConfig, parents: IDataItem[] = temp) => {
    if (data.length <= 0) return []
    for (let index = data.length - 1; index >= 0; index--) {
      const prevElement = data[index + 1]
      let prevSpaceTotal = prevElement ? prevElement.spaceTotal || 0 : 0
      const element = data[index]

      if (!isFindActive) positon.push(index)
      if (element.id === config.activeId) {
        isFindActive = true
      }
      element.spaceTotal = prevSpaceTotal

      if (element.branch) {
        parents.push(element)
        element.branchSpace =
          prevSpaceTotal * 50 + (prevSpaceTotal + 1) * (config.branchSpace ?? 20)
        parents.forEach((ele) => (ele.spaceTotal as number)++)
        fn(element.branch, config)
        parents.pop()
      }
      if (!isFindActive) positon.pop()
    }
  }

  fn(data, config, temp)
  const activeChain: any = []

  positon.reduce((prev, curr) => {
    activeChain.push(...prev.slice(0, curr + 1))
    return prev[curr]?.branch || []
  }, data)

  return {
    data,
    active: positon.join('-'),
    activeChain,
  }
}
