<template>
  <div class="user-list-container">
    <!-- 搜索表单 -->
    <div class="filter-form-con">
      <sl-form class="filter-form" ref="formRef" :options="formOptions" v-model="formModel">
      </sl-form>
    </div>

    <!-- 用户列表表格 -->
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="getRdsUserPage"
      :init-param="queryParams"
      @selection-change="handleSelectionChange"
      hidden-table-header
      row-key="id"
    />

    <!-- 新增/编辑用户弹窗 -->
    <SlDialog
      v-model="userDialogVisible"
      :title="isEdit ? '编辑用户' : '新增用户'"
      width="500px"
      destroy-on-close
      @close="handleUserDialogClose"
      @confirm="handleUserConfirm"
    >
      <sl-form ref="userFormRef" :options="userFormOptions" v-model="userFormModel"></sl-form>
      <template #footer>
        <el-button @click="handleUserDialogClose">取消</el-button>
        <sl-button type="primary" :api-function="handleUserConfirm">确定</sl-button>
      </template>
    </SlDialog>

    <!-- 修改密码弹窗 -->
    <SlDialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="500px"
      destroy-on-close
      @close="handlePasswordDialogClose"
      @confirm="handlePasswordConfirm"
    >
      <sl-form
        ref="passwordFormRef"
        :options="passwordFormOptions"
        v-model="passwordFormModel"
      ></sl-form>
      <template #footer>
        <el-button @click="handlePasswordDialogClose">取消</el-button>
        <sl-button type="primary" :api-function="handlePasswordConfirm">确定</sl-button>
      </template>
    </SlDialog>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Delete } from '@element-plus/icons-vue'
import SlForm from '@/components/form/SlForm.vue'
import SlDialog from '@/components/SlDialog/index.vue'
import SlButton from '@/components/base/SlButton.vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import {
  getRdsUserPage,
  createRdsUser,
  updateRdsUser,
  deleteRdsUser,
} from '@/api/modules/resourecenter'

// 定义props
const props = defineProps<{
  resourceId: string
  rdsId: string
}>()

// 表单引用
const formRef = ref()
const userFormRef = ref()
const passwordFormRef = ref()
const proTable = ref<ProTableInstance>()

// 查询参数
const queryParams = ref<any>({
  resourceId: props.resourceId,
  rdsId: props.rdsId,
})

// 搜索表单
const formModel = reactive({
  username: '',
})

// 搜索重置功能
function reset() {
  formRef.value!.resetFields()
  queryParams.value = {
    resourceId: props.resourceId,
    rdsId: props.rdsId,
    ...formModel,
  }
}
function search() {
  queryParams.value = {
    resourceId: props.resourceId,
    rdsId: props.rdsId,
    ...formModel,
  }
}

// 搜索表单配置
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '用户名',
        type: 'input',
        key: 'username',
        span: 8,
        props: {
          placeholder: '请输入用户名',
          clearable: true,
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button onClick={handleAdd} icon={<Plus />} type="primary">
                新增用户
              </el-button>
            </div>
          )
        },
      },
    ],
  },
])

// 表格列配置
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', fixed: 'left', minWidth: 50 },
  { type: 'index', label: '序号', minWidth: 60 },
  { prop: 'userName', label: '用户名', minWidth: 200 },
  { prop: 'createTime', label: '创建时间', minWidth: 160 },
  {
    prop: 'operation',
    label: '操作',
    width: 250,
    fixed: 'right',
    render: ({ row }) => (
      <div>
        <el-button type="primary" link onClick={() => handleChangePassword(row)}>
          修改密码
        </el-button>
        <el-button type="danger" link onClick={() => handleDelete(row)}>
          删除
        </el-button>
      </div>
    ),
  },
])

// 选择变更处理
const handleSelectionChange = (selection: any[]) => {
  console.log('选择的用户:', selection)
}

// 用户管理弹窗
const userDialogVisible = ref(false)
const isEdit = ref(false)
const userFormModel = reactive({
  id: '',
  username: '',
  password: '',
  confirmPassword: '',
})

const hiddenPassword = computed(() => {
  return isEdit.value ? true : false
})

const userFormOptions = reactive([
  {
    groupName: '',
    groupItems: [
      {
        label: '用户名',
        type: 'input',
        key: 'username',
        span: 24,
        props: {
          placeholder: '请输入用户名',
        },
        rules: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' },
        ],
      },
      {
        label: '密码',
        type: 'input',
        key: 'password',
        span: 24,
        hidden: hiddenPassword,
        props: {
          type: 'password',
          placeholder: '请输入密码',
          showPassword: true,
        },
        rules: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 8, max: 32, message: '密码长度在8到32个字符', trigger: 'blur' },
          {
            pattern:
              /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=])[A-Za-z\d!@#$%^&*()_+\-=]{8,32}$/,
            message: '密码必须包含大小写字母、数字、特殊字符(!@#$%^&*()_+-=)，长度为8-32位',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '确认密码',
        type: 'input',
        key: 'confirmPassword',
        span: 24,
        hidden: hiddenPassword,
        props: {
          type: 'password',
          placeholder: '请确认密码',
          showPassword: true,
        },
        rules: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          {
            validator: (rule: any, value: string, callback: Function) => {
              if (value !== userFormModel.password) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
      },
    ],
  },
])

const handleAdd = () => {
  isEdit.value = false
  userFormModel.id = ''
  userFormModel.username = ''
  userFormModel.password = ''
  userFormModel.confirmPassword = ''
  userDialogVisible.value = true
}

const handleUserDialogClose = () => {
  userDialogVisible.value = false
}

const handleUserConfirm = async () => {
  if (!(await userFormRef.value?.validate(() => true))) return

  try {
    const apiData = {
      resourceId: props.resourceId,
      rdsId: props.rdsId,
      userName: userFormModel.username,
      password: userFormModel.password,
    }

    let response
    if (isEdit.value) {
      // 编辑用户只更新用户名
      response = await updateRdsUser({
        id: userFormModel.id,
        resourceId: props.resourceId,
        rdsId: props.rdsId,
        userName: userFormModel.username,
      })
    } else {
      // 新增用户
      response = await createRdsUser(apiData)
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
      userDialogVisible.value = false
      proTable.value?.getTableList() // 刷新表格
    } else {
      ElMessage.error(response.message || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  } catch (error) {
    console.error('保存用户失败:', error)
    ElMessage.error(isEdit.value ? '编辑失败' : '新增失败')
  }

  return Promise.resolve()
}

// 修改密码弹窗
const passwordDialogVisible = ref(false)
const passwordFormModel = reactive({
  id: '',
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const passwordFormOptions = reactive([
  {
    groupName: '',
    groupItems: [
      {
        label: '旧密码',
        type: 'input',
        key: 'oldPassword',
        span: 24,
        props: {
          type: 'password',
          placeholder: '请输入旧密码',
          showPassword: true,
        },
        rules: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
      },
      {
        label: '新密码',
        type: 'input',
        key: 'newPassword',
        span: 24,
        props: {
          type: 'password',
          placeholder: '请输入新密码',
          showPassword: true,
        },
        rules: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 8, max: 32, message: '密码长度在8到32个字符', trigger: 'blur' },
          {
            pattern:
              /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=])[A-Za-z\d!@#$%^&*()_+\-=]{8,32}$/,
            message: '密码必须包含大小写字母、数字、特殊字符(!@#$%^&*()_+-=)，长度为8-32位',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '确认新密码',
        type: 'input',
        key: 'confirmPassword',
        span: 24,
        props: {
          type: 'password',
          placeholder: '请确认新密码',
          showPassword: true,
        },
        rules: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          {
            validator: (rule: any, value: string, callback: Function) => {
              if (value !== passwordFormModel.newPassword) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
      },
    ],
  },
])

const handleChangePassword = (row: any) => {
  passwordFormModel.id = row.id
  passwordFormModel.oldPassword = ''
  passwordFormModel.newPassword = ''
  passwordFormModel.confirmPassword = ''
  passwordDialogVisible.value = true
}

const handlePasswordDialogClose = () => {
  passwordDialogVisible.value = false
}

const handlePasswordConfirm = async () => {
  if (!(await passwordFormRef.value?.validate(() => true))) return

  try {
    const response = await updateRdsUser({
      id: passwordFormModel.id,
      resourceId: props.resourceId,
      rdsId: props.rdsId,
      oldPassword: passwordFormModel.oldPassword,
      newPassword: passwordFormModel.newPassword,
    })

    if (response.code === 200) {
      ElMessage.success('密码修改成功')
      passwordDialogVisible.value = false
    } else {
      ElMessage.error(response.message || '密码修改失败')
    }
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('密码修改失败')
  }

  return Promise.resolve()
}

// 删除用户
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await deleteRdsUser({
      id: row.id,
      resourceId: props.resourceId,
      rdsId: props.rdsId,
    })

    if (response.code === 200) {
      ElMessage.success('删除成功')
      proTable.value?.getTableList() // 刷新表格
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败', error)
      ElMessage.error('删除失败')
    }
  }
}
</script>

<style lang="scss" scoped>
.user-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .filter-form-con {
    padding: 16px;
  }

  .table-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }
}
</style>
