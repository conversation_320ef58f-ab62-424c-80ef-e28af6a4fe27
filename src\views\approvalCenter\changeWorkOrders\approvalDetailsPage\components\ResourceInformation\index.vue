<template>
  <div>
    <div class="sl-card mb8">
      <sl-block-title>资源变更建议</sl-block-title>
      <sl-form
        :show-block-title="false"
        ref="slFormRef"
        v-model="form"
        :options="visibleFields"
        :gutter="20"
        label-width="175px"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="form" :fields="item" :dic-collection="[]" />
        </template>
      </sl-form>
    </div>

    <div class="sl-card mb8 no-card" v-if="changesTabs.length">
      <sl-block-title title="变更资源"> </sl-block-title>
      <div class="orderDetailTab">
        <sl-tabs class="mb10" show-count :tabs="changesTabs" v-model="changeType"></sl-tabs>
      </div>
      <div class="search-form mt-20 mb20 ml20">
        <template v-if="pageStatus">
          <el-button v-if="btnAuth?.tenant_task" type="primary" @click="() => batchConfirmation()">
            <el-icon><Check /></el-icon>批量确认
          </el-button>

          <el-button
            v-if="
              btnAuth?.shutdown &&
              ['ecs', 'gcs', 'mysql', 'redis', 'postgreSql'].includes(changeType)
            "
            type="primary"
            @click="() => batchConfirmation()"
          >
            <el-icon><SwitchButton /></el-icon>批量关机
          </el-button>
        </template>
      </div>
      <template v-for="item in changesTabs" :key="item.label">
        <template v-if="item.name === changeType">
          <SlProTable
            :key="item.name"
            :ref="(el) => setProTableRef(el as unknown as ProTableInstance, item.name)"
            :data="item.list"
            :columns="resourcesColumns(item.name)"
            :pagination="false"
            row-key="id"
          >
          </SlProTable>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed, inject, nextTick, ref, watchEffect, type VNode } from 'vue'
import { Check, SwitchButton } from '@element-plus/icons-vue'
import type {
  ChangeBtnsType,
  ChangeProTableType,
  ChangeTabsType,
  ChangeType,
} from '../../../interface/type'
import changeAllColumns, {
  indexColumn,
  changeValueEnum,
  deviceStatusColumn,
  messageColumn,
  changeStatusColumn,
  tenantConfirmColumn,
  changeTypeEnum,
} from './goodsColumns'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { showTips } from '@/utils'
import { changeTentantConfirmrApi } from '@/api/modules/approvalCenter'
import EnumText from '../../../../components/EnumText.vue'
import { executeResourceOperation } from '@/api/modules/resourecenter'
import { goodsNameKey } from '@/views/approvalCenter/workOrder/orderApproval/components/ResourceInformation/goodsColumns'

const pageStatus = inject('pageStatus', ref(true))
const btnAuth = inject('btnAuth', ref<ChangeBtnsType>())

const emit = defineEmits(['refresh', 'update:disabledBtn'])

const proTable = ref<ChangeProTableType>({
  ecs: null,
  gcs: null,
  evs: null,
  obs: null,
  slb: null,
  nat: null,
  eip: null,
  mysql: null,
  redis: null,
  postgreSql: null,
})

const setProTableRef = (el: ProTableInstance | null, name: ChangeType) => {
  if (el) {
    proTable.value[name] = el
  }
}

const changeType = ref<ChangeType>('ecs')
const changesTabs = ref<ChangeTabsType[]>([])

const initData = (data: any, flag: boolean = true) => {
  const tabs: ChangeTabsType[] = []
  changeValueEnum.forEach((item) => {
    if (data[item.goodsList] && data[item.goodsList].length) {
      tabs.push({
        label: item.desc,
        name: item.code as ChangeType,
        count: data[item.goodsList].length,
        list: setGoodsList(data[item.goodsList], item.code),
      })
    }
  })
  changesTabs.value = tabs
  flag && (changeType.value = tabs[0]?.name || 'ecs')

  // 2.其他信息
  cahgeFormData(data)
  changeFields()
  nextTick(() => {
    disabledTenant(tabs)
  })
}

const setGoodsList = (list: any[], type: string) => {
  return list.map((item) => {
    let changeTypeList: any[] = changeTypeEnum.filter((obj) => item.changeType.includes(obj.value))
    return {
      ...item,
      originName: item[goodsNameKey[type as ChangeType]],
      changeTypeList: changeTypeList.map((obj) => setOldAndNewValue(item, obj, type)),
    }
  })
}

/*
  { value: 'instance_spec_change', label: '实例规格更变' },
  { value: 'storage_expand', label: '存储扩容' },
  { value: 'bandwidth_expand', label: '带宽扩容' },
  { value: 'delay', label: '延期' },
*/
/*
ecs gcs 支持 实例规格、存储扩容、带宽扩容、延期
evs obs 支持 存储扩容、延期
slb 支持 实例规格、带宽扩容、延期
eip 支持 带宽扩容、延期
nat 支持 延期

*/
const setOldAndNewValue = (item: any, obj: any, type: string) => {
  if (obj.value === 'instance_spec_change') {
    let oldValue = item.vmSpec

    if (type === 'slb') {
      oldValue = item.slbSpec
    }
    return {
      ...obj,
      oldValue,
      newValue: item.changeFlavorName,
    }
  }

  if (obj.value === 'storage_expand') {
    let oldValue = item.sysDiskSize
    let newValue = item.changeVolumeSize
    if (['gcs', 'ecs', 'mysql', 'redis', 'postgreSql'].includes(type)) {
      oldValue = item.evsModelList?.map((item: any) => item.spec).join(',') ?? ''
      newValue =
        item.evsModelList
          ?.map((item: any) => {
            let arr = item.spec.split(' ')

            return arr[0] + ' ' + item.changeVolumeSize + 'GB'
          })
          .join(',') ?? ''
    }
    if (type === 'obs') {
      oldValue = item.obsSpec
      let arr = item.obsSpec.split(' ')

      newValue = arr[0] + ' ' + item.changeVolumeSize + 'GB'
    }

    if (type === 'evs') {
      oldValue = item.spec
      let arr = item.spec.split(' ')

      newValue = arr[0] + ' ' + item.changeVolumeSize + 'GB'
    }
    return {
      ...obj,
      oldValue, //改
      newValue,
    }
  }

  if (obj.value === 'bandwidth_expand') {
    let oldValue = item.bandwidth
    let newValue = item.ChangeBandwidth
    if (['gcs', 'ecs', 'slb', 'eip', 'mysql', 'redis', 'postgreSql'].includes(type)) {
      oldValue = item.eipModel.eipBandwidth + 'M'
      newValue = item.eipModel.changeBandwidth + 'M'
    }
    return {
      ...obj,
      oldValue, //改
      newValue,
    }
  }

  if (obj.value === 'delay') {
    return {
      ...obj,
      oldValue: item.expireTime + ' 到期',
      newValue: item.newExpireTime + ' 到期',
    }
  }

  return {
    ...obj,
  }
}

// 判断是否禁用
const disabledTenant = (tabs: ChangeTabsType[]) => {
  if (!tabs.length) return true
  let falg = true

  if (btnAuth.value?.shutdown) {
    let arr = tabs.filter((item) =>
      ['ecs', 'gcs', 'mysql', 'redis', 'postgreSql'].includes(item.name),
    )
    falg = arr.every((item) => item.list.every((item) => item.deviceStatus === 'STOPED'))
  }

  if (btnAuth.value?.resource_change) {
    let falg1 = tabs.every((goodItem) => {
      return goodItem.list.every((item) => item.changeStatus === 'change_success')
    })
    let arr = tabs.filter((item) =>
      ['ecs', 'gcs', 'mysql', 'redis', 'postgreSql'].includes(item.name),
    )
    let falg2 = arr.every((item) => item.list.every((item) => item.deviceStatus === 'RUNING'))

    falg = falg1 && falg2
  }

  if (btnAuth.value?.tenant_task) {
    falg = tabs.every((goodItem) => {
      return goodItem.list.every((item) => item.tenantConfirm)
    })
  }

  emit('update:disabledBtn', !falg)
}

//  ---------------------------- 列表按钮操作  ----------------------------

// 直接根据 当前节点来判断
const theNameOfTheActionType = computed(() => {
  // 租户确认
  if (btnAuth.value?.tenant_task)
    return { name: '确认', api: changeTentantConfirmrApi, msg: '确认成功' }

  // 关机
  if (btnAuth.value?.shutdown) return { name: '关机', api: executeFn, msg: '已提交关机' }

  return null
})

// 关机 抛出结果
const executeFn = async (data: any[]) => {
  try {
    const res = await Promise.all(data.map((item) => executeResourceOperation(item)))
    return new Promise((resolve) => resolve(res))
  } catch (error) {
    console.log()
    return new Promise((_, rejected) => rejected(error))
  }
}

//批量确认
const batchConfirmation = async () => {
  const isSelected = proTable.value?.[changeType.value]?.isSelected
  if (!isSelected) {
    return showTips(`请先选择要${theNameOfTheActionType.value?.name}的资源`)
  }

  let ids = proTable.value?.[changeType.value]?.selectedList

  let data: any
  let falg = false
  if (theNameOfTheActionType.value?.name === '关机') {
    data = ids!.map((item: any) => ({ id: item.resourceDetailId, operationType: 'STOP' }))
    falg = ids!.some((item: any) => item.handoverStatus === '已交维')
  } else {
    data = { productIds: ids!.map((item: any) => item.id) }
  }
  // 批量确认
  await tenantConfirmFn(data, falg)
  proTable.value?.[changeType.value]?.clearSelection()
}

const tenantConfirmFn = async (params: any, flag = false) => {
  if (theNameOfTheActionType.value?.name === '关机' && flag) {
    //  默认不提示 包含已经交维的才会提示 已交维
    await ElMessageBox.confirm(
      `请在云主机关机之前联系总控台（13588724724）完成云主机告警屏蔽，不然会产生告警事件。`,
      '提示',
      {
        type: 'warning',
        confirmButtonText: '继 续',
        cancelButtonText: '取 消',
      },
    )
  } else {
    await ElMessageBox.confirm(`确定${theNameOfTheActionType.value?.name}选中的资源吗？`, '提示', {
      type: 'warning',
      confirmButtonText: '确 认',
      cancelButtonText: '取 消',
    })
  }
  await theNameOfTheActionType.value?.api(params)
  ElMessage.success(`${theNameOfTheActionType.value?.msg}`)
  emit('refresh')
}

// ------------------------- end -------------------------------

// 选择
const selectionColumn: ColumnProps = {
  type: 'selection',
  width: 55,
  fixed: 'left',
  selectable: (row: any) => {
    if (btnAuth.value?.tenant_task) return !row.tenantConfirm

    // 关机
    if (btnAuth.value?.shutdown) return row.deviceStatus === 'RUNING' && row.canClose

    return false
  },
}

// 变更按钮 @param flag 是否是资源 默认为资源 false 为网络
const operationColumns = (): ColumnProps => {
  return {
    prop: 'operation',
    label: '操作',
    align: 'center',
    fixed: 'right',
    width: '100px',
    render: ({ row }: { row: any; $index: number }): VNode => {
      return (
        <>
          {btnAuth.value?.shutdown && row.deviceStatus === 'RUNING' && (
            <el-button
              onclick={() =>
                tenantConfirmFn(
                  [
                    {
                      operationType: 'STOP',
                      id: row.resourceDetailId,
                    },
                  ],
                  row.handoverStatus == '已交维',
                )
              }
              disabled={!row.canClose}
              type="primary"
              link
            >
              关机
            </el-button>
          )}
          {btnAuth.value?.tenant_task && !row.tenantConfirm && (
            <el-button
              onclick={() =>
                tenantConfirmFn({
                  productIds: [row.id],
                })
              }
              type="primary"
              link
            >
              确认
            </el-button>
          )}
        </>
      )
    },
  }
}

const resourcesColumns = (name: ChangeType) => {
  const columns: ColumnProps[] = [indexColumn, ...changeAllColumns[`${name}`]]

  // 告警
  const ExtType = ['ecs', 'gcs', 'mysql', 'redis', 'postgreSql']

  if (btnAuth.value?.shutdown && ExtType.includes(name)) {
    columns.push(deviceStatusColumn)
    pageStatus.value && columns.push(operationColumns())
    pageStatus.value && columns.unshift(selectionColumn)
  }

  if (btnAuth.value?.resource_change) {
    ExtType.includes(name) && columns.push(deviceStatusColumn)
    columns.push(changeStatusColumn)
    columns.push(messageColumn)
  }
  if (btnAuth.value?.tenant_task) {
    columns.push(tenantConfirmColumn)
    pageStatus.value && columns.push(operationColumns())
    pageStatus.value && columns.unshift(selectionColumn)
  }

  return columns
}

const validateForm = async () => {
  return true
}
const submitForm = async () => {
  let params: any = {}
  //  2.1 架构审核节点
  if (btnAuth.value?.schema_administrator) {
    params = {
      ...params,
      ...form.value,
      auditAdvice: `1、云资源${form.value.cloudResources == false ? '不' : ''}满足要求 ; 2、${form.value.cloudArchitecture == false ? '不' : ''}满足云化架构要求 ; 3、业务架构${form.value.businessArchitecture == false ? '不' : ''}合理 ; 4、业务${form.value.businessPlanning == false ? '没' : ''}有规划设计 ;`,
    }
    return params
  }
  return params
}

//--------------------------------------------------

const form = ref<FormDataType>({
  businessPlanning: true,
  businessArchitecture: true,
  cloudArchitecture: true,
  cloudResources: true,
})

watchEffect(() => {
  if (btnAuth.value?.schema_administrator) {
    const falg =
      form.value.cloudResources == false ||
      form.value.cloudArchitecture == false ||
      form.value.businessArchitecture == false ||
      form.value.businessPlanning == false
    emit('update:disabledBtn', falg)
  }
})

const optionsEume = [
  {
    value: true,
    label: '是',
  },
  {
    value: false,
    label: '否',
  },
]
const visibleFields = ref([
  {
    groupName: '资源开通建议',
    groupItems: [
      {
        label: '云资源是否满足要求',
        type: 'radio',
        slotName: 'enumText',
        key: 'cloudResources',
        rules: [{ required: true, message: '请选择云资源是否满足要求', trigger: 'change' }],
        props: {},
        options: optionsEume,
        span: 8,
      },
      {
        label: '云化架构是否满足要求',
        type: 'radio',
        slotName: 'enumText',
        key: 'cloudArchitecture',
        rules: [{ required: true, message: '请选择云化架构是否满足要求', trigger: 'change' }],
        props: {},
        options: optionsEume,
        span: 8,
      },
      {
        label: '业务架构是否合理',
        type: 'radio',
        slotName: 'enumText',
        key: 'businessArchitecture',
        rules: [{ required: true, message: '请选择业务架构是否合理', trigger: 'change' }],
        props: {},
        options: optionsEume,
        span: 8,
      },
      {
        label: '业务是否有规划设计',
        type: 'radio',
        slotName: 'enumText',
        key: 'businessPlanning',
        rules: [{ required: true, message: '请选择业务是否有规划设计', trigger: 'change' }],
        props: {},
        options: optionsEume,
        span: 8,
      },
      // {
      //   label: '云类型',
      //   type: 'slot',
      //   key: 'catalogueDomainCode',
      //   keyName: 'catalogueDomainName',
      //   span: 8,
      // },
      // {
      //   label: '云平台',
      //   slotName: 'enumText',
      //   key: 'domainCode',
      //   keyName: 'domainName',
      //   span: 8,
      // },
    ],
  },
])

function cahgeFormData(data: any) {
  form.value.businessPlanning = [null, undefined].includes(data.businessPlanning)
    ? true
    : data.businessPlanning
  form.value.businessArchitecture = [null, undefined].includes(data.businessArchitecture)
    ? true
    : data.businessArchitecture
  form.value.cloudArchitecture = [null, undefined].includes(data.cloudArchitecture)
    ? true
    : data.cloudArchitecture
  form.value.cloudResources = [null, undefined].includes(data.cloudResources)
    ? true
    : data.cloudResources
}

function changeFields() {
  if (!pageStatus.value || !btnAuth.value?.schema_administrator) {
    //禁用云平台,云类型
    visibleFields.value = visibleFields.value.map((field: any) => {
      field.groupItems = field.groupItems.map((item: any) => {
        return {
          ...item,
          type: 'slot',
          slotName: 'enumText',
          key: item.keyName ? item.keyName : item.key,
        }
      })
      return field
    })
  }
}

defineExpose({
  initData,
  validateForm,
  submitForm,
})
</script>

<style lang="scss" scoped>
.orderDetailTab {
  background-color: #f2f3f5;
  position: relative;
  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    left: 5px;
    top: 11px;
    width: 4px;
    z-index: 10;
    background-color: var(--el-color-primary);
    margin-right: 10px;
    height: 0.78em;
    font-size: 18px;
  }
}
.tip {
  font-size: 14px;
  line-height: 30px;
  color: red;
}
.tip-message {
  color: #000;
}
</style>
