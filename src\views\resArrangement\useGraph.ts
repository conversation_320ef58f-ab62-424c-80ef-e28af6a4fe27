import { type Nodes, type Edges, type Layouts, defineConfigs } from 'v-network-graph'
import { reactive, ref } from 'vue'
import type {
  IEcsModel,
  IMysqlModel,
  IGcsModel,
  IObsModel,
  ISlbModel,
  INatModel,
  ICloudPortModel,
  IEipModel,
  IEvsModel,
  IRedisModel,
  INetworkModel,
  IPmModel,
  ISecurityGroupModel,
  IVpcModel,
  ICqModel,
} from './model'
import {
  useEcsModel,
  useMysqlModel,
  useGcsModel,
  useEvsModel,
  useObsModel,
  useSlbModel,
  useNatModel,
  useCloudPortModel,
  useRedisModel,
  useEipModel,
  useNetworkModel,
  useVpcModel,
  usePmModel,
  useSecurityGroupModel,
  useCqModel,
} from './model'
import { uuid } from '@/utils'

interface INode {
  name: string
  icon: string
  type: string
  model:
    | IEcsModel
    | IMysqlModel
    | IGcsModel
    | IEvsModel
    | IObsModel
    | ISlbModel
    | INatModel
    | ICloudPortModel
    | IEipModel
    | IRedisModel
    | INetworkModel
    | IVpcModel
    | IPmModel
    | ISecurityGroupModel
    | ICqModel
}
export type IProductType =
  | 'ecs'
  | 'mysql'
  | 'gcs'
  | 'evs'
  | 'obs'
  | 'slb'
  | 'nat'
  | 'cloudPort'
  | 'redis'
  | 'eip'
  | 'network'
  | 'vpc'
  | 'pm'
  | 'securityGroup'
  | 'cq'

const genNode = (type: IProductType): INode => {
  switch (type) {
    case 'ecs':
      return {
        name: '云主机',
        icon: 'ecs.png',
        type: 'ecs',
        model: useEcsModel(),
      }
    case 'mysql':
      return {
        name: 'MySQL云数据库',
        icon: 'mysql.png',
        type: 'mysql',
        model: useMysqlModel(),
      }
    case 'gcs':
      return {
        name: 'GPU云主机',
        icon: 'gcs.png',
        type: 'gcs',
        model: useGcsModel(),
      }
    case 'evs':
      return {
        name: '云硬盘',
        icon: 'evs.png',
        type: 'evs',
        model: useEvsModel(),
      }
    case 'obs':
      return {
        name: '对象存储',
        icon: 'obs.png',
        type: 'obs',
        model: useObsModel(),
      }
    case 'slb':
      return {
        name: '负载均衡',
        icon: 'slb.png',
        type: 'slb',
        model: useSlbModel(),
      }
    case 'nat':
      return {
        name: 'NAT网关',
        icon: 'nat.png',
        type: 'nat',
        model: useNatModel(),
      }
    case 'cloudPort':
      return {
        name: '云端口',
        icon: 'cloudPort.png',
        type: 'cloudPort',
        model: useCloudPortModel(),
      }
    case 'redis':
      return {
        name: '通用Redis',
        icon: 'redis.png',
        type: 'redis',
        model: useRedisModel(),
      }
    case 'eip':
      return {
        name: '弹性公网IP',
        icon: 'eip.png',
        type: 'eip',
        model: useEipModel(),
      }
    case 'network':
      return {
        name: '网络',
        icon: 'network.png',
        type: 'network',
        model: useNetworkModel(),
      }
    case 'vpc':
      return {
        name: 'VPC',
        icon: 'vpc.png',
        type: 'vpc',
        model: useVpcModel(),
      }
    case 'pm':
      return {
        name: '裸金属',
        icon: 'pm.png',
        type: 'pm',
        model: usePmModel(),
      }
    case 'securityGroup':
      return {
        name: '防火墙',
        icon: 'securityGroup.png',
        type: 'securityGroup',
        model: useSecurityGroupModel(),
      }
    case 'cq':
      return {
        name: '容器配额',
        icon: 'cq.png',
        type: 'cq',
        model: useCqModel(),
      }
  }
}

export const useGraph = () => {
  const nodes = reactive<Nodes>({})
  const edges = reactive<Edges>({})
  const layouts = reactive<Layouts>({ nodes: {} })
  const selectedNodes = ref<string[]>([])
  const selectedEdges = ref<string[]>([])
  const templateName = ref('产品组合编排模板名称')

  // 添加画布视图信息
  const viewInfo = reactive({
    left: 0,
    top: 0,
    right: 1000,
    bottom: 1000,
  })

  const configs = reactive(
    defineConfigs({
      node: {
        normal: {
          type: 'rect',
          width: 56,
          height: 56,
          color: '#fff',
          strokeWidth: 3,
          strokeColor: '#eee',
          borderRadius: 6,
        },
        hover: {
          strokeWidth: 3,
          color: '#666',
        },
        selected: {
          type: 'rect',
          width: 56,
          height: 56,
          color: '#666',
          strokeWidth: 3,
          strokeColor: '#eee',
          borderRadius: 6,
        },
        draggable: true,
        selectable: true,
        label: {
          fontSize: 14,
          fontFamily: 'Arial, sans-serif',
          margin: 8,
          direction: 'south',
        },
        focusring: {
          visible: true,
          width: 3,
          padding: 0,
          color: '#de8802',
          dasharray: '0',
        },
      },
      edge: {
        normal: {
          width: 2,
          color: '#999',
        },
        hover: {
          color: '#666666',
        },
        selectable: true,
        gap: 5,
        type: 'straight',
        margin: 4,
        marker: {
          source: {
            type: 'none',
            width: 4,
            height: 4,
            margin: -1,
            offset: 0,
            units: 'strokeWidth',
            color: null,
          },
          target: {
            type: 'arrow',
            width: 4,
            height: 4,
            margin: -1,
            offset: 0,
            units: 'strokeWidth',
            color: null,
          },
        },
      },
      view: {
        scalingObjects: true,
        minZoomLevel: 0.1,
        maxZoomLevel: 2.0,
        panEnabled: true,
        zoomEnabled: true,
        fitContentMargin: '8%',
        mouseWheelZoomEnabled: true,
        doubleClickZoomEnabled: true,
        autoPanOnResize: true,
        grid: {
          visible: true,
          interval: 50,
          thickIncrements: 2,
          line: {
            color: '#eee',
            width: 1,
            dasharray: 1,
          },
          thick: {
            color: '#ededed',
            width: 1,
            dasharray: 0,
          },
        },
      },
    }),
  )

  // 添加节点
  const addNode = (type: IProductType) => {
    const nodeId = `node${uuid(16)}`
    // 添加节点数据
    nodes[nodeId] = genNode(type)
    // 计算新节点的位置 - 默认添加到画布中心并添加随机偏移
    layouts.nodes[nodeId] = {
      x: -300 + Math.random() * 100,
      y: -50 + Math.random() * 100,
    }
  }

  // 删除节点
  const deleteNode = (nodeId: string) => {
    if (nodeId) {
      // 删除相关的边
      Object.entries(edges).forEach(([edgeId, edge]) => {
        if (edge.source === nodeId || edge.target === nodeId) {
          delete edges[edgeId]
        }
      })
      // 删除节点
      delete nodes[nodeId]
      delete layouts.nodes[nodeId]
    }
  }

  // 删除边
  const deleteEdge = (edgeId: string) => {
    delete edges[edgeId]
  }

  // 复制节点
  const copyNode = (nodeId: string) => {
    if (nodeId) {
      const sourceNode = nodes[nodeId]
      const sourceLayout = layouts.nodes[nodeId]
      const newNodeId = `node${uuid(16)}`

      // 复制节点数据
      nodes[newNodeId] = { ...sourceNode }
      // 在原节点位置偏移一点创建新节点
      layouts.nodes[newNodeId] = {
        x: sourceLayout.x + 50,
        y: sourceLayout.y + 50,
      }
    }
  }
  return {
    nodes,
    edges,
    layouts,
    configs,
    addNode,
    deleteNode,
    deleteEdge,
    copyNode,
    selectedNodes,
    selectedEdges,
    templateName,
    viewInfo,
  }
}
