const disableNumberWheel = {
  mounted(el: { querySelector: (arg0: string) => any; _disableWheelHandler: (e: any) => void }) {
    const input = el.querySelector('input[type="number"]') || el

    const handler = (e: { preventDefault: () => void; stopPropagation: () => void }) => {
      e.preventDefault() // 阻止滚轮事件
      e.stopPropagation() // 防止冒泡
    }

    input.addEventListener('wheel', handler, { passive: false })

    // 添加销毁时的清理
    el._disableWheelHandler = handler
  },

  unmounted(el: { querySelector: (arg0: string) => any; _disableWheelHandler: any }) {
    const input = el.querySelector('input[type="number"]') || el
    if (el._disableWheelHandler) {
      input.removeEventListener('wheel', el._disableWheelHandler)
      delete el._disableWheelHandler
    }
  },
}

export default disableNumberWheel
