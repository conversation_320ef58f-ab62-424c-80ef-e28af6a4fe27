<template>
  <div>
    <div>
      <sl-page-header title="云数据库"></sl-page-header>
    </div>
    <sl-form
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formModel"
      :label-width="120"
      ref="formRef"
    >
      <template #spec-slot="{ form, item }">
        <EcsSpec
          v-if="form.resourcePool && tableParams.description.length > 0"
          :form="form"
          :item="item"
          @validate="validateSpec"
          type="rdsMysql"
          :table-params="tableParams"
          @change-resource-pool="handleChangeResourcePool"
        />
      </template>
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="price-section"></div>
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
          <el-button type="primary" plain class="cart-btn" @click="handleAddToCart">
            加入清单
          </el-button>
          <el-button type="primary" class="order-btn" @click="handleDirectOrder" v-if="vifOpened">
            直接开通
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { markRaw, ref, computed } from 'vue'
import CustomRadio from './components/CustomRadio.vue'
import RegionSelect from './components/RegionSelect.vue'
import VpcSelect from './components/VpcSelect.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import EcsSpec from './components/EcsSpec.vue'
import CustomTable from './components/CustomTable.vue'
import { useTenant } from './hooks/useTenant'
import { corporateOrderApi, corporateOrderTemSaveApi } from '@/api/modules/resourecenter'
import { useGlobalDicStore } from '@/stores/modules/dic'
import useGetTheParameters from './hooks/useGetTheParameters'
import { useRouter } from 'vue-router'
import SlForm from '@/components/form/SlForm.vue'
import { ElMessage } from 'element-plus'
import { validateGoodsName } from '../resourceCenter/utils'
import eventBus from '@/utils/eventBus'
import { useAuthStore } from '@/stores/modules/auth'
const authStore = useAuthStore()
const vifOpened = computed(() =>
  authStore.authButtonListGet?.viewOfPublicTenants?.includes('Opened'),
)

const globalDic = useGlobalDicStore()
const { getDic } = globalDic
const { tenantList: tenantListOptions } = useTenant()

const formModel = ref<FormDataType>({
  isBindEip: false,
  catalogueDomainCode: 'cloudst_group_moc',
  catalogueDomainName: '移动云',
  domain: '',
  resourcePool: null,
  az: '',
  bandwidth: 1,
  instanceName: '',
  tenant: '',
  number: 1,
  paymentType: 'month',
  region: 'placeholder', // 占位 无意义
  vpc: null,
  subnet: null,
  spec: null,

  systemDisk: '',
  productType: 'rdsMysql',
  engineVersion: '5.7',
  deployType: '',
})

const handleChangeResourcePool = () => {
  formModel.value.resourcePool = null
}

const tableParams = computed(() => ({
  description: formModel.value.deployType,
}))

const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '付费类型',
        type: 'component',
        key: 'paymentType',
        span: 24,
        required: true,
        options: [
          {
            label: '按日付费',
            value: 'day',
          },
          {
            label: '按月付费',
            value: 'month',
          },
          {
            label: '按年付费',
            value: 'year',
          },
        ],
        component: markRaw(CustomRadio),
      },
      {
        label: '区域',
        type: 'component',
        key: 'region',
        span: 24,
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formModel.value.domain) {
                callback(new Error('请选择云平台'))
              }
              if (!formModel.value.resourcePool) {
                callback(new Error('请选择资源池'))
              }
              if (!formModel.value.az) {
                callback(new Error('请选择可用区'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
        component: markRaw(RegionSelect),
        props: {
          maxlength: 64,
        },
      },
      {
        label: '租户',
        type: 'select',
        key: 'tenant',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请选择租户', trigger: ['blur', 'change'] }],
        options: tenantListOptions,
        props: {
          select: {
            valueKey: 'id',
          },
        },
      },
      {
        label: '网络',
        type: 'component',
        key: 'vpc',
        span: 24,
        required: true,
        rules: [
          { required: true, message: '请选择VPC' },
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formModel.value.subnet) {
                callback(new Error('请选择子网'))
              }
              callback()
            },
          },
        ],
        component: markRaw(VpcSelect),
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '类型',
        type: 'component',
        key: 'productType',
        span: 24,
        required: true,
        options: [
          {
            label: 'MySQL',
            value: 'rdsMysql',
          },
        ],
        component: markRaw(CustomRadio),
      },
      {
        label: '数据库版本',
        type: 'component',
        key: 'engineVersion',
        span: 24,
        required: true,
        options: [
          {
            label: '5.7',
            value: '5.7',
          },
          {
            label: '8.0',
            value: '8.0',
          },
        ],
        component: markRaw(CustomRadio),
      },
      {
        label: '系列',
        type: 'component',
        key: 'deployType',
        span: 24,
        required: true,
        options: [
          {
            label: '单机版本',
            value: 'ALONE',
          },
          {
            label: '主备版本',
            value: 'COLONY',
          },
        ],
        component: markRaw(CustomRadio),
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '实例规格',
        type: 'slot',
        slotName: 'spec-slot',
        key: 'spec',
        span: 24,
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formModel.value.spec) {
                callback(new Error('请选择实例规格'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
        totalWidth: '900px',
        // component: EcsSpec,
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '存储大小',
        type: 'component',
        key: 'systemDisk',
        span: 24,
        component: markRaw(CustomTable),
        columns: [
          {
            prop: 'type',
            label: '类型',
            width: 180,
            type: 'select',
            placeholder: '请选择类型',
            options: getDic('sysDisk'),
          },
          {
            prop: 'capacity',
            label: '容量',
            width: 200,
            type: 'number',
            min: 10,
            max: 2048,
            unit: 'GB',
            inputWidth: '120px',
          },
          {
            prop: 'quantity',
            label: '数量',
            width: 180,
            type: 'text',
            min: 1,
            max: 100,
            inputWidth: '100px',
          },
        ],
        canAdd: false,
        showEmptyState: false,
        defaultRow: {
          type: 'SAS',
          capacity: 10,
          quantity: 1,
        },
        required: true,
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              管理设置
            </SlBlockTitle>
          )
        },
      },
      {
        label: '云数据库名称',
        type: 'input',
        key: 'instanceName',
        required: true,
        rules: [
          { required: true, message: '请输入云数据库名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
        span: 13,
      },

      {
        label: '购买数量',
        type: 'inputNumber',
        key: 'number',
        span: 13,
        inputWidth: '100px',
        props: {
          min: 1,
          max: 10,
        },
        required: true,
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              配置概要
            </SlBlockTitle>
          )
        },
      },
      {
        label: '云数据库名称',
        type: 'text',
        getter: (form: any) => form.instanceName || '',
        span: 8,
      },

      {
        label: '区域',
        type: 'text',
        getter: (form: any) =>
          form.az?.name ? `${form.domain?.name} , ${form.resourcePool.name} , ${form.az.name}` : '',
        span: 8,
      },
      {
        label: '可用区',
        type: 'text',
        getter: (form: any) => form.az?.name || '',
        span: 8,
      },
      {
        label: '实例规格',
        type: 'text',
        getter: (form: any) => form.spec?.name || '',
        span: 8,
      },
      {
        label: '网络',
        type: 'text',
        getter: (form: any) =>
          form.subnet?.subnetName ? `${form.vpc.vpcName} , ${form.subnet?.subnetName}` : '',
        span: 8,
      },
      {
        label: '购买数量',
        type: 'text',
        getter: (form: any) => form.number || '',
        span: 8,
      },
      {
        label: '储存大小',
        type: 'text',
        getter: (form: any) =>
          form.systemDisk?.[0]?.capacity
            ? `${form.systemDisk?.[0].type} / ${form.systemDisk?.[0]?.capacity}G / ${form.systemDisk?.[0]?.quantity}`
            : '',
        span: 8,
      },
      {
        label: '数据库版本',
        type: 'text',
        getter: (form: any) => form.engineVersion || '',
        span: 8,
      },
      {
        label: '系列',
        type: 'text',
        getter: (form: any) =>
          [
            {
              label: '单机版本',
              value: 'ALONE',
            },
            {
              label: '主备版本',
              value: 'COLONY',
            },
          ].find((item) => item.value === form.deployType)?.label ?? '',
        span: 8,
      },
      {
        label: '付费类型',
        type: 'text',
        getter: (form: any) =>
          form.paymentType === 'day'
            ? '按日付费'
            : form.paymentType === 'month'
              ? '按月付费'
              : '按年付费',
        span: 8,
      },
      {
        label: '所属租户',
        type: 'text',
        getter: (form: any) => form.tenant.name || '',
        span: 8,
      },
      {
        label: '类型',
        type: 'text',
        getter: () => 'MySQL',
        span: 8,
      },
    ],
  },
])

const formRef = ref<InstanceType<typeof SlForm>>()

// 校验规格
const validateSpec = () => {
  formRef.value?.elFormRef?.validateField('spec')
}

const router = useRouter()
// 处理取消操作
const handleCancel = () => {
  // 可以添加取消逻辑，比如清空表单或返回上一页
  router.go(-1)
  eventBus.emit('corporateShoppingList:updateCount')
}

// 处理加入清单操作
const handleAddToCart = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formModel.value, 'rdsMysql', false)

    // 3. 调用API
    await corporateOrderTemSaveApi(params)
    ElMessage.success('加入清单成功')

    handleCancel()
  } catch (error: any) {
    // console.error(error)
    console.error(error)
  }
}

// 处理直接开通操作
const handleDirectOrder = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formModel.value, 'rdsMysql')

    // 3. 调用API
    await corporateOrderApi(params)
    ElMessage.success('发起开通成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}
</script>

<style scoped>
/* 镜像搜索框样式 - 防止校验时变红 */
.image-search-input :deep(.el-input__wrapper) {
  border-color: #dcdfe6 !important;
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}

.image-search-input :deep(.el-input__wrapper):hover {
  border-color: #c0c4cc !important;
  box-shadow: 0 0 0 1px #c0c4cc inset !important;
}

.image-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 1px #409eff inset !important;
}

/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}

/* EIP配置样式 */
.eip-config {
  margin-bottom: 16px;
}

.eip-config :deep(.el-checkbox) {
  font-size: 14px;
}

.eip-config :deep(.el-checkbox__label) {
  font-weight: normal;
  color: #606266;
}

/* 展开按钮容器样式 */
.expand-container {
  display: flex;
  align-items: center;
  position: relative;
}
/* 图标样式 */
.expand-icon {
  transition: transform 0.3s ease;
  font-size: 14px;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.price-section {
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.price-info {
  display: block;
}

.discount-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.discount-price {
  font-size: 24px;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 8px;
}

.discount-badge {
  font-size: 12px;
  color: #ff6b35;
  background: #fff2e8;
  padding: 2px 6px;
  border-radius: 4px;
}

.original-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.original-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.original-price {
  font-size: 12px;
  color: #909399;
  text-decoration: line-through;
  margin-right: 8px;
}

.detail-link {
  font-size: 12px;
  padding: 0;
  height: auto;
}

.warning-text {
  font-size: 11px;
  color: #f56c6c;
  line-height: 1.4;
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
