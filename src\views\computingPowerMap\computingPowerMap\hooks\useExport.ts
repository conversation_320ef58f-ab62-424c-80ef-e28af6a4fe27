import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  downloadVirtualMachineListApi,
  downloadStorageListApi,
  downloadNetworkEipListApi,
  downloadDcnListApi,
} from '@/api/modules/computingPowerMap'

export type ExportType = 'virtualMachine' | 'storage' | 'eip' | 'dcn'

interface ExportParams {
  type: ExportType
  params: Record<string, any>
}

export function useExport() {
  const exportLoading = ref(false)

  const handleExport = async ({ type, params }: ExportParams) => {
    try {
      exportLoading.value = true
      let response: any
      let fileName = ''
      switch (type) {
        case 'virtualMachine':
          response = await downloadVirtualMachineListApi(params)
          fileName = `虚拟机列表_${new Date().toISOString().slice(0, 10)}.xlsx`
          break
        case 'storage':
          response = await downloadStorageListApi(params)
          fileName = `存储列表_${new Date().toISOString().slice(0, 10)}.xlsx`
          break
        case 'eip':
          response = await downloadNetworkEipListApi(params)
          fileName = `公网IP列表_${new Date().toISOString().slice(0, 10)}.xlsx`
          break
        case 'dcn':
          response = await downloadDcnListApi(params)
          fileName = `DCN列表_${new Date().toISOString().slice(0, 10)}.xlsx`
          break
        default:
          throw new Error('不支持的导出类型')
      }
      const blob = new Blob([response as any], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      ElMessage.success(`${fileName.replace(/_.*$/, '')}导出成功`)
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请稍后重试')
    } finally {
      exportLoading.value = false
    }
  }

  return {
    exportLoading,
    handleExport,
  }
}
