import type { ColumnProps } from '@/components/SlProTable/interface'
import { ElLink } from 'element-plus'
import type { ChangeColumnsType } from '../interface'

export const changeValueEnum = [
  { code: 'ecs', desc: '云主机', goodsList: 'ecsChangeList' },
  { code: 'gcs', desc: 'GPU云主机', goodsList: 'gcsChangeList' },
  { code: 'evs', desc: '云硬盘', goodsList: 'evsChangeList' },
  { code: 'obs', desc: '对象存储', goodsList: 'obsChangeList' },
  { code: 'slb', desc: '负载均衡', goodsList: 'slbChangeList' },
  { code: 'nat', desc: 'NAT网关', goodsList: 'natChangeList' },
  { code: 'eip', desc: '弹性公网', goodsList: 'eipChangeList' },
  { code: 'mysql', desc: 'MySQL云数据库', goodsList: 'mysqlChangeList' },
  { code: 'redis', desc: '通用Redis', goodsList: 'redisChangeList' },
  { code: 'rdsMysql', desc: 'MySQL云数据库', goodsList: 'rdsMysqlChangeList' },
  // { code: 'pm', desc: '裸金属', goodsList: 'pmChangeList' },
  // { code: 'flink', desc: 'Flink', goodsList: 'flinkChangeList' },
  // { code: 'es', desc: 'ElasticSearch', goodsList: 'esChangeList' },
  // { code: 'bldRedis', desc: '国产Redis', goodsList: 'bldRedisChangeList' },
  // { code: 'other', desc: '其他产品', goodsList: 'otherChangeList' },
]

// 云服务器
const ecsColumns: ChangeColumnsType['ecs'] = [
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },
  {
    prop: 'eip',
    label: '弹性公网',
    minWidth: 180,
    render: ({ row }) => {
      return (
        <div>
          {row.eip ? (
            <>
              <p> {row.eip}</p> <p>{row.bandwidth?.replace('Mbps', 'M')}</p>
            </>
          ) : (
            '--'
          )}
        </div>
      )
    },
  },
  { prop: 'dataDisk', label: '数据盘', width: 120 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]

// 云硬盘
const evsColumns: ChangeColumnsType['evs'] = [
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },
  {
    label: '是否挂载云主机',
    prop: 'mountDataDisk',
    minWidth: 130,
    render(scope) {
      return <div>{scope.row.vmName ? '是' : '否'}</div>
    },
  },
  { prop: 'vmName', label: '云主机', minWidth: 200 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]

// 对象存储
const obsColumns: ChangeColumnsType['obs'] = [
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]

// 负载均衡
const slbColumns: ChangeColumnsType['slb'] = [
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },

  {
    prop: 'eip',
    label: '弹性公网',
    minWidth: 180,
    render: ({ row }) => {
      return (
        <div>
          {row.eip ? (
            <>
              <p> {row.eip}</p> <p>{row.bandwidth?.replace('Mbps', 'M')}</p>
            </>
          ) : (
            '--'
          )}
        </div>
      )
    },
  },
  { prop: 'domainName', label: '所属云', width: 180 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]

// NAT网关
const natColumns: ChangeColumnsType['nat'] = [
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },

  { prop: 'domainName', label: '所属云', width: 180 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]

// eip弹性ip
const eipColumns: ChangeColumnsType['eip'] = [
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },
  {
    prop: 'eip',
    label: '弹性公网',
    minWidth: 180,
    render: ({ row }) => {
      return (
        <div>
          {row.eip ? (
            <>
              <p> {row.eip}</p> <p>{row.bandwidth?.replace('Mbps', 'M')}</p>
            </>
          ) : (
            '--'
          )}
        </div>
      )
    },
  },
  { prop: 'domainName', label: '所属云', width: 180 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]

// MySQL数据库
const mysqlColumns: ChangeColumnsType['mysql'] = [
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },
  {
    label: '系列',
    prop: 'deployType',
    minWidth: 100,
    enum: [
      { label: '单机版本', value: 'ALONE' },
      { label: '主备版本', value: 'COLONY' },
    ],
  },
  {
    prop: 'eip',
    label: '弹性公网',
    minWidth: 180,
    render: ({ row }) => {
      return (
        <div>
          {row.eip ? (
            <>
              <p> {row.eip}</p> <p>{row.bandwidth?.replace('Mbps', 'M')}</p>
            </>
          ) : (
            '--'
          )}
        </div>
      )
    },
  },
  { prop: 'dataDisk', label: '数据盘', width: 120 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]

// Redis缓存
const redisColumns: ChangeColumnsType['redis'] = [
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },
  {
    prop: 'eip',
    label: '弹性公网',
    minWidth: 180,
    render: ({ row }) => {
      return (
        <div>
          {row.eip ? (
            <>
              <p> {row.eip}</p> <p>{row.bandwidth?.replace('Mbps', 'M')}</p>
            </>
          ) : (
            '--'
          )}
        </div>
      )
    },
  },
  { prop: 'dataDisk', label: '数据盘', width: 120 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]

const rdsMysqlColumns: ChangeColumnsType['rdsMysql'] = [
  { prop: 'originName', label: '资源名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  {
    prop: 'productType',
    label: '资源类型',
    width: 130,
    fieldNames: {
      label: 'desc',
      value: 'code',
    },
    enum: changeValueEnum,
  },
  { prop: 'engineVersion', label: '数据库版本', width: 120 },
  {
    prop: 'deployType',
    label: '系列',
    width: 100,
    enum: [
      {
        label: '单机版本',
        value: 'ALONE',
      },
      {
        label: '主备版本',
        value: 'COLONY',
      },
    ],
  },
  { prop: 'spec', label: '实例规格', width: 120 },
  { prop: 'sysDisk', label: '存储大小', width: 100 },
  { prop: 'ip', label: 'IP', width: 220 },
  { prop: 'tenantName', label: '租户', width: 100 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'changeType', label: '变更类型', minWidth: 180, render: changeTypeRender },
  { prop: 'sysDisk', label: '变更前', minWidth: 220, render: changeOldRender },
  { prop: 'evsInfo', label: '变更后', minWidth: 220, render: changeNewRender },
]
const changeAllColumns: ChangeColumnsType = {
  ecs: ecsColumns,
  gcs: ecsColumns,
  evs: evsColumns,
  obs: obsColumns,
  slb: slbColumns,
  nat: natColumns,
  eip: eipColumns,
  mysql: mysqlColumns,
  redis: redisColumns,
  rdsMysql: rdsMysqlColumns,
}

export default changeAllColumns

export const indexColumn: ColumnProps = { type: 'index', label: '序号', width: 55 }

export const deviceStatusColumn: ColumnProps = {
  prop: 'deviceStatus',
  label: '设备状态',
  width: 100,
  link: true,
  enum: [
    {
      id: '100000000087',
      label: '运行中',
      value: 'RUNING',
      desc: null,
      elType: 'success',
    },
    {
      id: '100000000112',
      label: '开机中',
      value: 'STARTING',
      desc: null,
      elType: 'warning',
    },
    {
      id: '100000000113',
      label: '关机中',
      value: 'STOPING',
      desc: null,
      elType: 'warning',
    },
    {
      id: '100000000088',
      label: '关机',
      value: 'STOPED',
      desc: null,
      elType: 'warning',
    },
    {
      id: '100000000114',
      label: '重启中',
      value: 'RESTARTING',
      desc: null,
      elType: 'danger',
    },
    {
      id: '100000000089',
      label: '错误',
      value: 'ERROR',
      desc: null,
      elType: 'warning',
    },
    {
      id: '100000000090',
      label: '创建中',
      value: 'CREATING',
      desc: null,
      elType: 'warning',
    },
    {
      id: '100000000091',
      label: '已删除',
      value: 'DELETED',
      desc: null,
      elType: 'danger',
    },
  ],
}
// 屏蔽告警
export const blockWarningColumn: ColumnProps = {
  prop: 'blockWarning',
  label: '屏蔽状态',
  width: 100,
  link: true,
  enum: [
    {
      label: '已屏蔽',
      value: true,
      elType: 'warning',
    },
    {
      label: '未屏蔽',
      value: false,
      elType: 'primary',
    },
  ],
  render: ({ row }: { row: any }) => {
    return (
      <>
        {row.handoverStatus == '未交维' ? (
          '--'
        ) : (
          <ElLink type={row.blockWarning ? 'warning' : 'primary'} underline={false}>
            {row.blockWarning ? '已屏蔽' : '未屏蔽'}
          </ElLink>
        )}
      </>
    )
  },
}
//  --变更状态--
export const changeStatusList = [
  { label: '变更中', value: 'changing', elType: 'warning' },
  { label: '变更成功', value: 'change_success', elType: 'success' },
  { label: '变更失败', value: 'change_fail', elType: 'danger' },
  { label: '待更变', value: 'wait_change', elType: 'primary' },
]

export const changeStatusColumn: ColumnProps = {
  prop: 'changeStatus',
  label: '变更状态',
  width: 100,
  link: true,
  enum: changeStatusList,
}

export const messageColumn: ColumnProps = { prop: 'message', label: '失败原因', minWidth: 300 }

// 确认
export const tenantConfirmColumn: ColumnProps = {
  prop: 'tenantConfirm',
  label: '确认状态',
  width: 100,
  link: true,
  enum: [
    {
      label: '已确认',
      value: true,
      elType: 'primary',
    },
    {
      label: '未确认',
      value: false,
      elType: 'danger',
    },
  ],
}

function changeTypeRender({ row }: { row: any }) {
  return (
    <>
      {row.changeTypeList.map((item: any) => (
        <p> {item.label}</p>
      ))}
    </>
  )
}

function changeOldRender({ row }: { row: any }) {
  return (
    <>
      {row.changeTypeList.map((item: any) => (
        <p> {item.oldValue}</p>
      ))}
    </>
  )
}

function changeNewRender({ row }: { row: any }) {
  return (
    <>
      {row.changeTypeList.map((item: any) => (
        <p style="color: blue;"> {item.newValue}</p>
      ))}
    </>
  )
}

export const changeTypeEnum = [
  { value: 'instance_spec_change', label: '实例规格更变' },
  { value: 'storage_expand', label: '存储扩容' },
  { value: 'bandwidth_expand', label: '带宽扩容' },
  { value: 'delay', label: '延期' },
]

export const goodsNameKey = {
  ecs: 'vmName',
  gcs: 'vmName',
  evs: 'evsName',
  obs: 'obsName',
  slb: 'slbName',
  nat: 'natName',
  nas: 'name',
  vpn: 'name',
  eip: 'eipName',
  cq: 'cqName',
  backup: 'jobName',
  kafka: 'name',
  pm: 'pmName',
  flink: 'flinkName',
  es: 'name',
  bldRedis: 'name',
  mysql: 'vmName',
  redis: 'vmName',
  rdsMysql: 'mysqlName',
}
