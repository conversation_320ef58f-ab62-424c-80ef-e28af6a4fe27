<template>
  <div>
    <div>
      <sl-page-header title="云硬盘"></sl-page-header>
    </div>
    <sl-form
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formData"
      :label-width="120"
      ref="formRef"
    >
      <template #is-bind-slot="{ form, item }">
        <div>
          <div>
            <el-checkbox v-model="form[item.key]" @change="changeCheckbox">
              挂载到云主机
            </el-checkbox>
          </div>
          <div>
            <el-select
              :disabled="!form[item.key]"
              style="width: 30%"
              v-model="form[item.ecsKey]"
              placeholder="选择云主机"
              value-key="id"
            >
              <el-option
                v-for="option in ecsOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </div>
      </template>
      <template #region-slot="{ form, item }">
        <region-select :form="form" :item="item" v-if="!disabledTenant" />
        <region-input :form="form" :item="item" v-else />
      </template>

      <template #tenant-slot="{ form, item }">
        <el-select
          v-if="!disabledTenant"
          style="width: 30%"
          v-model="form[item.key]"
          placeholder="请选择租户"
          value-key="id"
          clearable
          filterable
        >
          <el-option
            v-for="option in tenantListOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <el-input
          v-else
          style="width: 30%"
          disabled
          :value="form[item.key]?.name"
          placeholder="选择租户"
        />
      </template>
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="price-section"></div>
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
          <el-button type="primary" plain class="cart-btn" @click="handleAddToCart">
            加入清单
          </el-button>
          <el-button type="primary" class="order-btn" @click="handleDirectOrder" v-if="vifOpened">
            直接开通
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, watch, markRaw } from 'vue'
import CustomRadio from './components/CustomRadio.vue'
import RegionSelect from './components/RegionSelect.vue'
import RegionInput from './components/RegionInput.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import CustomTable from './components/CustomTable.vue'
import { useTenant } from './hooks/useTenant'
import {
  getCorporateResourceList,
  corporateOrderApi,
  corporateOrderTemSaveApi,
} from '@/api/modules/resourecenter'
import useGetTheParameters from './hooks/useGetTheParameters'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type SlForm from '@/components/form/SlForm.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import eventBus from '@/utils/eventBus'
import { useAuthStore } from '@/stores/modules/auth'
const authStore = useAuthStore()
const vifOpened = computed(() =>
  authStore.authButtonListGet?.viewOfPublicTenants?.includes('Opened'),
)
const { tenantList: tenantListOptions } = useTenant()
const router = useRouter()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const formData = ref<any>({
  isBindEip: false,
  isBindEcs: false,
  domain: null,
  resourcePool: null,
  az: null,
  dataDisk: null,
  paymentType: 'month',
  tenant: '',
  ecs: null,
  region: 'placeholder',
  catalogueDomainCode: 'cloudst_group_moc',
  catalogueDomainName: '移动云',
})

const ecsOptions = ref<any[]>([])

const getEcsList = async () => {
  const {
    entity: { records = [] },
  } = await getCorporateResourceList({
    pageNum: 1,
    pageSize: 1000,
    typeList: ['ecs', 'gcs'],
    sourceType: 'DG',
  })
  ecsOptions.value = records.map((item: any) => ({
    label: item.deviceName,
    value: item,
  }))
}
getEcsList()

const changeCheckbox = () => {
  formData.value.ecs = null
  formData.value.tenant = null
  formData.value.domain = null
  formData.value.resourcePool = null
  formData.value.az = null
}

watch(
  () => formData.value.ecs,
  (ecs: any) => {
    if (ecs) {
      formData.value.tenant = {
        id: ecs.tenantId,
        name: ecs.tenantName,
      }
      formData.value.domain = {
        code: ecs.domainCode,
        name: ecs.domainName,
        id: ecs.domainId,
      }
      formData.value.resourcePool = {
        code: ecs.resourcePoolCode,
        name: ecs.resourcePoolName,
        id: ecs.resourcePoolId,
      }
      formData.value.az = {
        code: ecs.azCode,
        name: ecs.azName,
        id: ecs.azId,
      }
    }
  },
)

const disabledTenant = computed(() => {
  return formData.value.isBindEcs
})
const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '是否挂载',
        type: 'slot',
        key: 'isBindEcs',
        ecsKey: 'ecs',
        span: 24,
        slotName: 'is-bind-slot',
      },
      {
        label: '区域',
        type: 'slot',
        slotName: 'region-slot',
        key: 'region',
        span: 24,
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formData.value.domain) {
                callback(new Error('请选择云平台'))
              }
              if (!formData.value.resourcePool) {
                callback(new Error('请选择资源池'))
              }
              if (!formData.value.az) {
                callback(new Error('请选择可用区'))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
      },
      {
        label: '租户',
        type: 'slot',
        slotName: 'tenant-slot',
        key: 'tenant',
        required: true,
        rules: [{ required: true, message: '请选择租户', trigger: 'blur' }],
        props: {
          select: {
            disabled: disabledTenant,
            valueKey: 'id',
          },
        },
        span: 10,
        options: tenantListOptions,
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '付费类型',
        type: 'component',
        key: 'paymentType',
        span: 24,
        required: true,
        options: [
          {
            label: '按日付费',
            value: 'day',
          },
          {
            label: '按月付费',
            value: 'month',
          },
          {
            label: '按年付费',
            value: 'year',
          },
        ],
        component: markRaw(CustomRadio),
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '数据盘',
        type: 'component',
        key: 'dataDisk',
        span: 24,
        component: markRaw(CustomTable),
        required: true,
        rules: [
          {
            validator: (_rule: any, _value: any, callback: any) => {
              if (formData.value.dataDisk && formData.value.dataDisk.length > 0) {
                callback()
              } else {
                callback(new Error('请添加数据盘'))
              }
            },
            message: '请添加数据盘',
          },
        ],
        columns: [
          {
            prop: 'type',
            label: '类型',
            width: 180,
            type: 'select',
            placeholder: '请选择类型',
            options: getDic('evs'),
          },
          {
            prop: 'capacity',
            label: '容量',
            width: 200,
            type: 'number',
            min: 20,
            max: 2048,
            unit: 'GB',
            inputWidth: '120px',
          },
          {
            prop: 'quantity',
            label: '数量',
            width: 180,
            type: 'number',
            min: 1,
            max: 10,
            inputWidth: '100px',
          },
          {
            prop: 'action',
            label: '操作',
            width: 60,
            type: 'action',
            action: 'delete',
          },
        ],
        canAdd: true,
        maxRows: 16,
        addButtonText: '添加数据盘',
        emptyText: '暂无数据盘配置',
        showEmptyState: true,
        defaultRow: {
          type: 'SSD',
          capacity: 20,
          quantity: 1,
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              配置概要
            </SlBlockTitle>
          )
        },
      },
      {
        label: '是否挂载',
        type: 'text',
        getter: (form: any) => (form.isBindEcs ? '是' : '否'),
        span: 8,
      },
      {
        label: '云主机',
        type: 'text',
        getter: (form: any) => form.ecs?.deviceName || '',
        span: 8,
      },
      {
        label: '区域',
        type: 'text',
        getter: (form: any) =>
          form.az?.name ? `${form.domain?.name} - ${form.resourcePool.name} - ${form.az.name}` : '',
        span: 8,
      },
      {
        label: '付费类型',
        type: 'text',
        getter: (form: any) =>
          form.paymentType === 'day'
            ? '按日付费'
            : form.paymentType === 'month'
              ? '按月付费'
              : '按年付费',
        span: 8,
      },
      {
        label: '存储',
        type: 'text',
        getter: (form: any) =>
          form.dataDisk
            ?.map((item: any) => `${item.type} / ${item.capacity}GB / ${item.quantity}`)
            .join(',') || '',
        span: 8,
      },
      {
        label: '所属租户',
        type: 'text',
        getter: (form: any) => form.tenant?.name || '',
        span: 8,
      },
    ],
  },
])

const formRef = ref<InstanceType<typeof SlForm>>()

// 处理取消操作
const handleCancel = () => {
  router.go(-1)
  eventBus.emit('corporateShoppingList:updateCount')
}

// 处理加入清单操作
const handleAddToCart = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'evs', false)

    // 3. 调用API
    await corporateOrderTemSaveApi(params)
    ElMessage.success('加入清单成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}

// 处理直接开通操作
const handleDirectOrder = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'evs')

    // 3. 调用API
    await corporateOrderApi(params)
    ElMessage.success('发起开通成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}
</script>

<style scoped>
/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}
.corporate-products​ {
  min-height: calc(100vh - 190px);
}
/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.price-section {
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
