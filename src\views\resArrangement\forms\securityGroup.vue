<template>
  <sl-form
    size="small"
    ref="slFormRef"
    label-position="top"
    :options="goodsInfoOptions"
    :model-value="formModel"
  >
    <template #gpuSlot="{ form, item }">
      <div style="display: flex; flex: 1 0 100%; width: 100%; min-width: 0; align-items: center">
        <el-switch
          style="flex-grow: 0.2"
          v-model="form[item.swithKey]"
          active-value="1"
          inactive-value="0"
        />
        <el-select
          :disabled="form.isUseGpu === '0'"
          style="margin-left: 10px"
          placeholder="请选择显卡类型"
          v-model="form.gpuCardType"
          @change="() => (form.gpuType = '')"
          clearable
        >
          <el-option label="GPU" value="GPU" />
          <el-option label="NPU" value="NPU" />
        </el-select>
        <el-select
          :disabled="form.isUseGpu === '0'"
          style="margin-left: 10px; min-width: 100px"
          placeholder="请选择显卡型号"
          v-model="form.gpuType"
          clearable
        >
          <!-- GPU选项 -->
          <template v-if="form.gpuCardType === 'GPU'">
            <el-option label="A10" value="A10" />
            <el-option label="V100" value="V100" />
            <el-option label="T4" value="T4" />
            <el-option label="A40" value="A40" />
          </template>
          <!-- NPU选项 -->
          <template v-else-if="form.gpuCardType === 'NPU'">
            <el-option label="300I" value="300I" />
            <el-option label="910B2" value="910B2" />
            <el-option label="910B4" value="910B4" />
          </template>
        </el-select>
        <el-input-number
          :disabled="form.isUseGpu === '0'"
          :min="0"
          style="margin-left: 10px"
          placeholder="请输入显卡数量"
          v-model="form.gpuCount"
          clearable
        />
        <span style="margin-left: 10px">张</span>
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="tsx">
import { reactive, ref, onMounted } from 'vue'
import { type ISecurityGroupModel, useSecurityGroupModel } from '@/views/resArrangement/model'
import slForm from '@/components/form/SlForm.vue'
import { vpcList } from '@/api/modules/resourecenter'
import {
  getCloudTypeDic,
  getCloudPlatformDic,
  getResourcePoolsDic,
  getAzListDic,
} from '@/api/modules/dic'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import {
  Plus,
  CircleCloseFilled,
  //  Delete
} from '@element-plus/icons-vue'

const props = defineProps<{
  goods: ISecurityGroupModel
}>()

const formModel = reactive(Object.assign(useSecurityGroupModel(), props.goods))

const slFormRef = ref()
// 字典数据
const cloudTypeOptions = ref<any[]>([])
const cloudPlatformOptions = ref<any[]>([])
const resourcePoolOptions = ref<any[]>([])
const azOptions = ref<any[]>([])
const vpcOptions = ref<any[]>([])

// 获取云类型列表
const getCloudTypeList = async () => {
  try {
    const { entity } = await getCloudTypeDic(null)
    cloudTypeOptions.value = entity || []
  } catch (error) {
    console.error('获取云类型失败', error)
  }
}

// 获取云平台列表
const getCloudPlatformList = async () => {
  try {
    const params: any = {
      parentCode: formModel.catalogueDomainCode,
      businessCode: 'securityGroup',
    }

    const { entity } = await getCloudPlatformDic(params)
    cloudPlatformOptions.value = entity || []
  } catch (error) {
    console.error('获取云平台失败', error)
  }
}

// 获取资源池列表
const getResourcePools = async () => {
  try {
    const { entity } = await getResourcePoolsDic({
      domainCode: formModel.domainCode,
      realmType: '',
    })
    resourcePoolOptions.value = entity || []
  } catch (error) {
    console.error('获取资源池失败', error)
  }
}

// 获取可用区列表，使用dic中的getAzListDic方法，入参为resourcePoolId
const getAzList = async () => {
  try {
    const { entity } = await getAzListDic({
      regionId: formModel.resourcePoolId,
    })
    azOptions.value = entity || []
  } catch (error) {
    console.error('获取可用区失败', error)
  }
}

// 获取VPC列表
const getVpcList = async () => {
  try {
    const params: any = {
      pageNum: 1,
      pageSize: 9999,
      resourcePoolId: formModel.resourcePoolId,
      azCode: formModel.azCode,
    }

    const { entity } = await vpcList(params)
    vpcOptions.value = entity.records || []
  } catch (error) {
    console.error('获取VPC失败', error)
  }
}
function setChildNetRef(ref: any, model: any) {
  // vue3 先创建再销毁，所以需要判断
  if (ref) model.ref = ref
}
// 添加规则（仅在创建模式下直接添加到表格）
const addRule = () => {
  // 创建模式下，直接添加到表格
  formModel.ruleList.push({
    direction: 'ingress', // 入方向 ingress, 出方向 egress
    accessStatus: 1, // 授权策略：拒绝-0/允许-1
    priority: 1, // 优先级
    protocol: 'TCP', // 协议类型
    portRange: '', // 默认常用端口
    accreditIp: '', // 默认所有IP
    // 用于UI展示的字段
    directionText: '入方向',
    policyText: '允许',
  })
}
// 初始化数据
onMounted(async () => {
  // 获取云类型数据
  await getCloudTypeList()
  if (formModel.ruleList.length === 0) {
    addRule()
  }
})
const childNetFormOptions = [
  {
    style: 'padding:16px 0 0 0;margin:0;background: rgb(237 245 255)',
    groupItems: [
      {
        label: '方向',
        type: 'select',
        key: 'direction',
        rules: [{ required: true, message: '请选择方向', trigger: ['blur', 'change'] }],
        options: [
          { label: '入方向', value: 'ingress' },
          { label: '出方向', value: 'egress' },
        ],
        span: 24,
      },
      {
        label: '授权策略',
        type: 'select',
        key: 'accessStatus',
        span: 24,
        rules: [{ required: true, message: '请选择授权策略', trigger: ['blur', 'change'] }],
        options: [
          { label: '允许', value: 1 },
          { label: '拒绝', value: 0 },
        ],
      },
      {
        label: '优先级',
        type: 'inputNumber',
        key: 'priority',
        span: 24,
        rules: [{ required: true, message: '请输入优先级', trigger: ['blur', 'change'] }],
      },
      {
        label: '协议类型',
        type: 'select',
        key: 'protocol',
        options: [
          { label: 'TCP', value: 'TCP' },
          { label: 'UDP', value: 'UDP' },
          { label: 'ICMP', value: 'ICMP' },
        ],
        span: 24,
        rules: [{ required: true, message: '请选择协议', trigger: ['blur', 'change'] }],
      },
      {
        label: '端口范围',
        type: 'input',
        key: 'portRange',
        span: 24,
        props: {
          placeholder: '请输入端口范围,多个端口用逗号分隔',
        },
        rules: [{ required: true, message: '请输入端口范围', trigger: ['blur', 'change'] }],
      },

      {
        label: '授权对象',
        type: 'input',
        key: 'accreditIp',
        props: {
          placeholder: '请输入CIDR,多个用逗号分隔',
        },
        span: 24,
        rules: [{ required: true, message: '请输入授权IP', trigger: ['blur', 'change'] }],
      },
    ],
  },
]

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px" no-bar>
              基础信息:
            </SlBlockTitle>
          )
        },
      },
      {
        label: '防火墙名称',
        type: 'input',
        key: 'name',
        span: 24,
        rules: [{ required: true, message: '请输入防火墙名称', trigger: 'blur' }],
      },
      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        span: 24,
        options: cloudTypeOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择云类型', trigger: 'change' }],
        onChange: function () {
          formModel.catalogueDomainName = ''
          if (formModel.catalogueDomainCode) {
            const obj = cloudTypeOptions.value.find(
              (item: any) => item.code === formModel.catalogueDomainCode,
            )
            formModel.catalogueDomainName = obj?.name
          }
          formModel.domainCode = ''
          formModel.domainName = ''
          formModel.resourcePoolId = ''
          formModel.regionName = ''
          formModel.regionCode = ''
          formModel.azCode = ''
          formModel.azName = ''
          formModel.vpcId = ''
          formModel.vpcName = ''
          cloudPlatformOptions.value = []
          resourcePoolOptions.value = []
          azOptions.value = []
          vpcOptions.value = []
          if (formModel.catalogueDomainCode) {
            getCloudPlatformList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        span: 24,
        options: cloudPlatformOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择云平台', trigger: 'change' }],
        onChange: function () {
          formModel.domainName = ''
          if (formModel.domainCode) {
            const obj = cloudPlatformOptions.value.find(
              (item: any) => item.code === formModel.domainCode,
            )
            formModel.domainName = obj?.name
          }
          formModel.resourcePoolId = ''
          formModel.regionName = ''
          formModel.regionCode = ''
          formModel.azCode = ''
          formModel.azName = ''
          formModel.vpcId = ''
          formModel.vpcName = ''
          resourcePoolOptions.value = []
          azOptions.value = []
          vpcOptions.value = []
          if (formModel.domainCode) {
            getResourcePools()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        span: 24,
        options: resourcePoolOptions,
        labelField: 'name',
        valueField: 'id',
        rules: [{ required: true, message: '请选择资源池', trigger: 'change' }],
        onChange: function () {
          formModel.regionName = ''
          formModel.regionCode = ''
          if (formModel.resourcePoolId) {
            const obj = resourcePoolOptions.value.find(
              (item: any) => item.id === formModel.resourcePoolId,
            )
            if (obj) {
              formModel.regionName = obj.name
              formModel.regionCode = obj.code // 保存资源池code
            }
          }
          formModel.azCode = ''
          formModel.azName = ''
          formModel.vpcId = ''
          formModel.vpcName = ''
          azOptions.value = []
          vpcOptions.value = []
          if (formModel.resourcePoolId) {
            getAzList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '可用区',
        type: 'select',
        key: 'azCode',
        span: 24,
        options: azOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择可用区', trigger: 'change' }],
        onChange: function () {
          formModel.azName = ''
          if (formModel.azCode) {
            const obj = azOptions.value.find((item: any) => item.code === formModel.azCode)
            formModel.azName = obj?.name
          }
          formModel.vpcId = ''
          formModel.vpcName = ''
          vpcOptions.value = []
          if (formModel.azCode && formModel.regionCode) {
            getVpcList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: 'VPC',
        type: 'select',
        key: 'vpcId',
        span: 24,
        options: vpcOptions,
        labelField: 'vpcName',
        valueField: 'id',
        rules: [{ required: true, message: '请选择VPC', trigger: 'change' }],
        onChange: function () {
          formModel.vpcName = ''
          if (formModel.vpcId) {
            const obj = vpcOptions.value.find((item: any) => item.id === formModel.vpcId)
            formModel.vpcName = obj?.name
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '描述',
        type: 'input',
        key: 'description',
        span: 24,
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 200,
          showWordLimit: true,
        },
      },
      {
        span: 24,
        render() {
          return <el-divider></el-divider>
        },
      },
      {
        span: 24,
        style: 'position: sticky;top: -20px;z-index: 100;',
        render() {
          return (
            <SlBlockTitle
              block-title-filter
              title="规则配置:"
              size={14}
              style="margin-bottom:20px;background: #fff"
              no-bar
            >
              {{
                blockTitleFilter: () => {
                  return (
                    <el-button type="primary" plain onClick={addRule}>
                      <el-icon>
                        <Plus />
                      </el-icon>
                      增加规则
                    </el-button>
                  )
                },
              }}
            </SlBlockTitle>
          )
        },
      },
      {
        span: 24,
        render() {
          return formModel.ruleList.map((model: any) => (
            <el-row style="margin-bottom: 16px">
              <el-col span={24}>
                <sl-form
                  size="small"
                  class="goods-info-form"
                  key={model}
                  label-position="top"
                  ref={(ref: any) => setChildNetRef(ref, model)}
                  options={childNetFormOptions}
                  modelValue={model}
                >
                  {{
                    globalFormSlot: () => {
                      return (
                        <div
                          onClick={() => {
                            formModel.ruleList.splice(formModel.ruleList.indexOf(model), 1)
                          }}
                          class="goods-del-btn"
                        >
                          <el-icon>
                            <CircleCloseFilled />
                          </el-icon>
                        </div>
                      )
                    },
                  }}
                </sl-form>
              </el-col>
            </el-row>
          ))
        },
      },
    ],
  },
])

const validateForm = async () => {
  const childNetFormRef = formModel.ruleList.map((model: any) => {
    return model.ref.validate()
  })
  const res = await slFormRef.value.validate()
  const res2 = await Promise.all(childNetFormRef)
  if (res2.some((item: any) => item !== true)) {
    return false
  }
  return res
}
const submitForm = async () => {
  const goods = props.goods
  Object.assign(goods, formModel)
}
defineExpose({
  validateForm,
  submitForm,
})
</script>
<style scoped>
:deep(.goods-del-btn) {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
:deep(.goods-info-form:hover .goods-del-btn) {
  display: block;
}
/* 让表单项内容容器撑满并启用 flex，使内部 div 的 flex:1 生效 */
.goods-info-form :deep(.el-form-item__content) {
  display: flex;
  width: 100%;
}
</style>
