<template>
  <div class="mric-container">
    <WujieVue
      width="100%"
      height="100%"
      name="mric-sub"
      :url="subAppUrl"
      :sync="true"
      :props="wujieProps"
      @beforeLoad="beforeLoad"
      @beforeMount="beforeMount"
      @afterMount="afterMount"
      @beforeUnmount="beforeUnmount"
      @afterUnmount="afterUnmount"
    ></WujieVue>
  </div>
</template>

<script setup lang="ts">
import WujieVue from 'wujie-vue3'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'

// 定义props类型
interface Props {
  moduleType?: string
  subAppUrl?: string
}

// 接收从路由传递的props
const props = withDefaults(defineProps<Props>(), {
  moduleType: 'alarm',
  subAppUrl: 'http://localhost:8081',
})

const userStore = useUserStore()

const router = useRouter()
const logOut = () => {
  window.location.href = 'login'
}

// 使用从props传递的子应用地址
const subAppUrl = ref(props.subAppUrl)

// 传递给子应用的数据
const wujieProps = {
  data: {
    router,
    logOut,
    token: userStore.token,
    moduleType: props.moduleType,
  },
}

// 生命周期钩子
const beforeLoad = (url: string) => {
  console.log('[主应用] 子应用开始加载', url)
}

const beforeMount = () => {
  console.log('[主应用] 子应用开始挂载')
}

const afterMount = () => {
  console.log('[主应用] 子应用挂载完成')
}

const beforeUnmount = () => {
  console.log('[主应用] 子应用开始卸载')
}

const afterUnmount = () => {
  console.log('[主应用] 子应用卸载完成')
}
</script>

<style scoped>
.mric-container {
  width: 100%;
  height: 100vh;
}
</style>
