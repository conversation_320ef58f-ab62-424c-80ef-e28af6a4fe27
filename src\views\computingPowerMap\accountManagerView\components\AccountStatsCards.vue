<template>
  <el-row :gutter="24">
    <el-col v-for="item in columns" :key="item.prop" :span="item.span">
      <div class="stat-card">
        <div class="stat-title">{{ item.label }}</div>

        <div class="stat-value">
          {{
            props.data?.[item.prop] || props.data?.[item.prop] === 0
              ? formatNumber(props.data?.[item.prop])
              : '0'
          }}
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
const props = defineProps<{ data: any }>()

const columns = [
  { label: '总订单数', prop: 'totalOrder', span: 6 },
  { label: '当月订单数', prop: 'monthTotalOrder', span: 6 },
  { label: '产品总数', prop: 'totalProduct', span: 6 },
  { label: '当月产品数', prop: 'monthTotalProduct', span: 6 },
]
/* 每三位数字打一个逗号 */
const formatNumber = (value: number) => {
  return value.toLocaleString()
}
</script>

<style lang="scss" scoped>
.stat-card {
  background: linear-gradient(to top left, #3581fd, #6ec0fe);
  border-radius: 8px;
  padding: 5px 8px;
  text-align: center;
  color: #fff;

  .stat-title {
    font-size: 16px;
    margin-bottom: 8px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .stat-value {
    font-size: 25px;
    font-weight: bold;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
