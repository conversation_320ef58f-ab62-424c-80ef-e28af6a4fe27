<template>
  <div class="table-box publicTenantView">
    <SlPageHeader
      class="publicTenantView-header"
      title="订购视图"
      detail="实时计算资源状态"
      :icon="{ hidden: true }"
    >
    </SlPageHeader>
    <div class="filter-form-con">
      <sl-form
        size="small"
        ref="slFormRef"
        :options="options"
        :model-value="form"
        :dic-collection="dicCollection"
      >
        ><template #SelectTab>
          <SelectTab
            v-model:selected-ids="form.tenantIds"
            :dic-collection="businessSystemList"
            @change="initData"
          ></SelectTab>
        </template>
      </sl-form>
    </div>
    <el-row class="resource-list" :gutter="30">
      <el-col :span="6" v-for="item in resourceList" :key="item.key" class="resource-item">
        <ResourceItem :detail="item"></ResourceItem>
      </el-col>
    </el-row>
    <div class="upper">
      <SystemUsageChart
        class="upper-left"
        :system-list="businessSystemList"
        :selected-system-ids="form.tenantIds"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed, reactive, ref } from 'vue'
import type { BusinessSystemListType } from '../tenantView/interface/type'
import {
  getAzListDic as getAzList,
  getCloudPlatformDic,
  getResourcePoolsDic,
} from '@/api/modules/dic'
import SelectTab from './components/SelectTab.vue'
import ResourceItem from './components/ResourceItem.vue'
import { getTenantList, resourceOpenCorporateApi } from '@/api/modules/resourecenter'
import SystemUsageChart from '../tenantView/components/SystemUsageChart.vue'

const form = ref<{
  domainCode: string
  azId: string
  regionId: string
  tenantIds: number[]
}>({
  domainCode: '',
  azId: '',
  regionId: '',
  tenantIds: [],
})

const cloudPlatformDic = ref<FormDataType[]>([]) //云平台字典
const resourcePoolsDic = ref<FormDataType[]>([]) // 资源池字典
const azDic = ref<FormDataType[]>([]) // 可用区字典
const businessSystemList = ref<BusinessSystemListType[]>([]) //租户字典

const dicCollection = computed<{ [key: string]: any }>(() => {
  return {
    cloudPlatformDic: cloudPlatformDic.value,
    resourcePoolsDic: resourcePoolsDic.value,
    azDic: azDic.value,
  }
})

//获取租户
const getBusinessSystemList = async () => {
  const { entity } = await getTenantList({
    type: 1,
  })
  businessSystemList.value = entity.map((item: any) => {
    return {
      busiSystemCode: item.code,
      busiSystemId: item.id,
      busiSystemName: item.name,
      tenantId: item.id,
    }
  })
  form.value.tenantIds = businessSystemList.value.map((item) => item.busiSystemId)
  initData()
}
getBusinessSystemList()

// 获取云平台字典
const getDomainCode = async () => {
  const { entity } = await getCloudPlatformDic({
    parentCode: 'cloudst_group_moc',
    businessCode: 'corporate',
  })
  cloudPlatformDic.value = entity
}
getDomainCode()

// 获取资源池字典
const getResourcePools = async (domainCode: string) => {
  const { entity } = await getResourcePoolsDic({
    domainCode,
    realmType: 'iaas',
  })
  resourcePoolsDic.value = entity
}

//获取可用区字典
const getAzListDic = async (regionId: string) => {
  const { entity } = await getAzList({
    regionId: regionId,
  })
  if (entity) {
    azDic.value = entity
  }
}

const options = reactive([
  {
    groupName: '资源开通建议',
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    groupItems: [
      {
        label: '租户',
        type: 'slot',
        slotName: 'SelectTab',
        key: 'tenantIds',
        span: 24,
        labelField: 'name',
        valueField: 'code',
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        span: 8,
        dicKey: 'cloudPlatformDic',
        labelField: 'name',
        valueField: 'code',
        onChange: function () {
          // 获取资源池
          form.value.regionId = ''
          form.value.azId = ''
          resourcePoolsDic.value = []
          azDic.value = []

          form.value.domainCode && getResourcePools(form.value.domainCode)
          initData()
        },
        props: {
          select: {
            clearable: true,
            filterable: true,
          },
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        span: 8,
        dicKey: 'resourcePoolsDic',
        labelField: 'name',
        valueField: 'id',
        onChange: function () {
          // 获取可用区
          form.value.azId = ''
          azDic.value = []
          form.value.regionId && getAzListDic(form.value.regionId)
          initData()
        },
        props: {
          select: {
            clearable: true,
            filterable: true,
          },
        },
      },
      {
        label: '可用区',
        type: 'select',
        key: 'azId',
        span: 8,
        dicKey: 'azDic',
        labelField: 'name',
        valueField: 'id',
        onChange: function () {
          initData()
        },
        props: {
          select: {
            clearable: true,
            filterable: true,
          },
        },
      },
    ],
  },
])

// 引入图片
const images: Record<string, string> = {
  'ecs.png': new URL('@/assets/images/overview/icon_ecs.png', import.meta.url).href,
  'gcs.png': new URL('@/assets/images/overview/icon_gcs.png', import.meta.url).href,
  'evs.png': new URL('@/assets/images/overview/icon_evs.png', import.meta.url).href,
  'obs.png': new URL('@/assets/images/overview/icon_obs.png', import.meta.url).href,
  'nat.png': new URL('@/assets/images/overview/icon_nat.png', import.meta.url).href,
  'slb.png': new URL('@/assets/images/overview/icon_slb.png', import.meta.url).href,
  'eip.png': new URL('@/assets/images/overview/icon_eip.png', import.meta.url).href,
  'vpc.png': new URL('@/assets/images/overview/icon_vpc.png', import.meta.url).href,
  'network.png': new URL('@/assets/images/overview/icon_network.png', import.meta.url).href,
  'mysql.png': new URL('@/assets/images/overview/icon_mysql.png', import.meta.url).href,
  'redis.png': new URL('@/assets/images/overview/icon_redis.png', import.meta.url).href,
  'cloudport.png': new URL('@/assets/images/overview/icon_cloudport.png', import.meta.url).href,
  'backup.png': new URL('@/assets/images/overview/icon_backup.png', import.meta.url).href,
  'cq.png': new URL('@/assets/images/overview/icon_cq.png', import.meta.url).href,
  'kafka.png': new URL('@/assets/images/overview/icon_kafka.png', import.meta.url).href,
  'vpn.png': new URL('@/assets/images/overview/icon_vpn.png', import.meta.url).href,
}

const resourceList = ref([
  {
    key: 'ecs',
    name: '云主机',
    path: 'ecs',
    show: true,
    data: {},
    img: images['ecs.png'],
  },
  {
    key: 'gcs',
    name: 'GPU云主机',
    path: 'gcs',
    show: true,
    data: {},
    img: images['gcs.png'],
  },
  {
    key: 'evs',
    name: '云硬盘',
    path: 'evs',
    show: true,
    data: {},
    img: images['evs.png'],
  },
  {
    key: 'obs',
    name: '对象存储',
    path: 'obs',
    show: true,
    unit: 'GB',
    data: {},
    img: images['obs.png'],
  },
  {
    key: 'nat',
    name: 'NAT网关',
    path: 'nat',
    show: true,
    data: {},
    img: images['nat.png'],
  },
  {
    key: 'slb',
    name: '负载均衡',
    path: 'slb',
    show: true,
    data: {},
    img: images['slb.png'],
  },
  {
    key: 'eip',
    name: '弹性公网',
    path: 'eip',
    show: true,
    data: {},
    img: images['eip.png'],
  },
  {
    key: 'vpn',
    name: 'VPN',
    path: 'vpn',
    show: true,
    data: {},
    img: images['vpn.png'],
  },
  {
    key: 'rdsMysql',
    name: '云数据库',
    path: 'mysql',
    show: true,
    data: {},
    img: images['mysql.png'],
  },
  {
    key: 'backup',
    name: '云灾备',
    path: 'backup',
    show: true,
    data: {},
    img: images['backup.png'],
  },
])

const initData = async () => {
  // 获取资源数量

  await resourceOpenCorporateApi(form.value)
  const { entity } = await resourceOpenCorporateApi(form.value)
  const initEntityItem = { goodType: '', amount: 0, diffAmount: 0, lastMount: 0, ratio: '0%' }
  resourceList.value.forEach((item: any) => {
    const entityItem = entity.find((entityItem: any) => entityItem.goodType === item.key)
    item.data = entityItem || JSON.parse(JSON.stringify(initEntityItem))
  })
}
</script>

<style scoped lang="scss">
.publicTenantView {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;

  :deep(.el-form-item__label) {
    font-weight: 600;
    font-size: 14px;
    color: var(--el-color-primary);
  }

  .publicTenantView-header {
    box-sizing: border-box;
    padding-left: 20px;
  }

  .filter-form-con {
    padding: 0 30px 20px;
    background-color: #fff;
  }

  .resource-list {
    // display: flex;
    // justify-content: space-between;
    // flex-wrap: wrap;
    padding: 0 30px 20px;
    background-color: #fff;

    .resource-item {
      margin-bottom: 20px;
    }
  }
}
.upper {
  display: flex;
  border-top: 1px solid #eee;
  padding: 30px;
  background-color: #fff;
}
</style>
