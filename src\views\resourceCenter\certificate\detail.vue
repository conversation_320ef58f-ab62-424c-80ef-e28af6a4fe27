<template>
  <div id="CertificateDetail" class="table-box">
    <sl-page-header
      title="证书详情"
      :icon="{
        class: 'page_zhengshuguanli',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        ></sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getCertificateDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  certificateName: '',
  businessSystemName: '',
  domainName: '',
  regionName: '',
  certificateTypeName: '',
  slbListenerName: '',
  createTime: '',
  publicKeyContent: '',
  privateKeyContent: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '证书名称',
        type: 'text',
        key: 'certificateName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSystemName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'domainName',
        span: 8,
      },
      {
        label: '所属资源池',
        type: 'text',
        key: 'regionName',
        span: 8,
      },
      {
        label: '证书类型',
        type: 'text',
        key: 'certificateTypeName',
        span: 8,
      },
      {
        label: '关联监听',
        type: 'text',
        key: 'slbListenerName',
        span: 8,
      },
      {
        label: '创建时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
      {
        label: '公钥证书内容',
        type: 'input',
        key: 'publicKeyContent',
        span: 24,
        props: {
          type: 'textarea',
          rows: 4,
          readonly: true,
          disabled: true,
        },
      },
      {
        label: '私钥证书内容',
        type: 'input',
        key: 'privateKeyContent',
        span: 24,
        props: {
          type: 'textarea',
          rows: 4,
          readonly: true,
          disabled: true,
        },
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getCertificateDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/certificateList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}
</style>
