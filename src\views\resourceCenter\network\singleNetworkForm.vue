<template>
  <div class="resourcerequest">
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <sl-form
            ref="slFormRef"
            show-block-title
            :options="formOptions"
            :model-value="formModel"
            style="overflow: hidden"
          >
          </sl-form>
        </div>
      </div>
    </div>
    <netRangeListDialog
      @confirm="selectIpRange"
      v-if="networkDialogVisible"
      title="选择网段"
      v-model="networkDialogVisible"
      :ip-version="netRangeListDialogProps.ipVersion"
      :form-item="netRangeListDialogProps.formItem"
      :level="netRangeListDialogProps.level"
      :region-code="baseFormProvider.regionCode"
    ></netRangeListDialog>
    <vlanListDialog
      @confirm="selectVlan"
      v-if="vlanDialogVisible"
      title="选择vlan"
      v-model="vlanDialogVisible"
      :ip-version="netRangeListDialogProps.ipVersion"
      :form-item="netRangeListDialogProps.formItem"
      :level="netRangeListDialogProps.level"
      :region-code="baseFormProvider.regionCode"
    ></vlanListDialog>
  </div>
</template>
<script setup lang="tsx">
import { Check } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref, computed, watch, onBeforeUnmount, inject, type Ref } from 'vue'
import { useRoute } from 'vue-router'
import SlMessage from '@/components/base/SlMessage'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import useModel from './model'
import { CirclePlus, CircleCloseFilled } from '@element-plus/icons-vue'
import netRangeListDialog from './netRangeListDialog.vue'
import vlanListDialog from './vlanListDialog.vue'
import { ipRangeCheck, ipRangeSplit, getExternalNetworkList } from '@/api/modules/resourecenter'
import { validateIpv4Range, validateIpv6Range } from '@/utils'
import { validateGoodsName } from '../utils'
import SlButton from '@/components/base/SlButton.vue'
import type { IBaseFormProvider } from './types'
import { uuid } from '@/utils'
import { ElMessageBox } from 'element-plus'
import { useSelectLabel } from '@/hooks/useSelectLabel'

const props = defineProps<{
  plane: string
}>()

const baseFormProvider = inject<Ref<IBaseFormProvider>>('baseFormProvider')!
const opType = baseFormProvider.value.opType

let netRangeListDialogProps = {
  ipVersion: '',
  formItem: {},
  level: 2,
}
const model = useModel()
const formModel = reactive(model)
formModel.plane = props.plane

const slFormRef = ref()
const route = useRoute()
const orderId = route.query.orderId as string
const networkDialogVisible = ref(false)
const vlanDialogVisible = ref(false)
const externalNetworkOptions = ref([])
const hiddenExternalNetwork = computed(
  () => baseFormProvider.value.domainCode !== 'plf_prov_nwc_zj_nfvo',
)

async function getExternalNetworkOptions() {
  const { entity } = await getExternalNetworkList({ regionCode: baseFormProvider.value.regionCode })
  externalNetworkOptions.value = entity.map((item: any) => ({
    label: item.name,
    value: item.id,
  }))
}

useSelectLabel(
  () => externalNetworkOptions,
  () => formModel.externalNetworkId,
  (option: any) => {
    formModel.externalNetworkName = option.label
  },
)

watch(
  () => hiddenExternalNetwork.value,
  () => {
    if (!hiddenExternalNetwork.value) getExternalNetworkOptions()
  },
  { immediate: true },
)

function validateIpRangeRule(rule: any, value: any, callback: any) {
  console.log(rule)

  const fn = rule.form.ipVersion === 'IPv4' ? validateIpv4Range : validateIpv6Range
  if (!fn(value)) {
    callback(new Error(`请输入正确的${rule.form.ipVersion}网段`))
  } else {
    callback()
  }
}

async function ipCheck(form: any) {
  const validate = await form.ref.validateField(['subnetStr'])
  if (!validate) return

  const params = {
    regionCode: baseFormProvider.value.regionCode,
    instanceId: form.instanceId2,
    subnets: form.subnetStr.split(';'),
    networkPlane: form.type2,
  }
  try {
    const { entity } = await ipRangeCheck(params)
    if (!entity.length) return SlMessage.error('校验失败')
    if (entity.length && entity.every((ele: any) => ele.status)) {
      form.ipCheckSuccess = true
      SlMessage.success('校验成功')
    } else {
      SlMessage.error(
        entity
          .filter((ele: any) => !ele.status)
          .map((ele: any) => ele.message)
          .join(';'),
      )
    }
  } catch (data: any) {
    console.error(data.message || '校验失败')
  }
}

async function ipSplit(form: any) {
  const params = {
    regionCode: baseFormProvider.value.regionCode,
    instanceId: form.instanceId2,
    subnets: form.subnetStr.split(';').map((ele: any) => {
      const splited = ele.split('/')
      return {
        prefix: splited[0],
        mask: splited[1],
        vpn: form.vpn2,
      }
    }),
  }
  try {
    await ipRangeSplit(params)
    form.splitMessage = '划分成功!'
  } catch (data: any) {
    SlMessage.error(data.message)
    form.splitMessage = '划分失败!' + data.message
  }
}

function selectIpRange({ row, formItem, level }: any) {
  formItem[`ip${level}`] = row.ip
  formItem[`type${level}`] = row.type
  formItem[`vpn${level}`] = row.vpn
  formItem[`instanceId${level}`] = row.instanceId
  formItem.resourcePoolLabel = row.relatedPool
}
function selectVlan({ row }: any) {
  // 弹窗提示资源组确认vlan是否有被使用
  if (baseFormProvider.value.catalogueDomainCode === 'cloudst_group_plf') {
    ElMessageBox.confirm('请务必确认该vlan是否被使用', '温馨提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      closeOnClickModal: false,
    }).then(() => {
      formModel.vlan = row.vlan
      formModel.vlanType = row.type
      formModel.vlanInstanceId = row.instanceId
    })
  } else {
    formModel.vlan = row.vlan
    formModel.vlanType = row.type
    formModel.vlanInstanceId = row.instanceId
  }
}
const childNetFormModel = reactive([
  {
    ip2: '',
    ip3: '',
    ipVersion: 'IPv4',
    type2: '',
    type3: '',
    vpn2: '',
    vpn3: '',
    subnetStr: '',
    resourcePoolLabel: '',
    instanceId2: '',
    instanceId3: '',
    ipCheckSuccess: false,
    splitMessage: '',
    ref: null,
    old: false,
  },
])

const watcherStops = new Map()

// 初始化时创建监听
function setupWatchers() {
  childNetFormModel.forEach((item) => {
    if (!watcherStops.has(item)) {
      const stop = watch(
        () => item.subnetStr,
        (newVal, oldVal) => {
          if (newVal !== oldVal) {
            item.ipCheckSuccess = false
            item.splitMessage = ''
          }
        },
        { deep: true }, // 根据实际数据结构决定是否需要
      )
      watcherStops.set(item, stop)
    }
  })
}

// 监听数组结构变化
const stopArrayWatch = watch(
  () => [...childNetFormModel], // 克隆数组触发引用变化
  (newArr, oldArr) => {
    // 找出被删除的元素
    const removed = oldArr.filter((item) => !newArr.includes(item))
    removed.forEach((item) => {
      if (watcherStops.has(item)) {
        watcherStops.get(item)() // 执行停止函数
        watcherStops.delete(item)
      }
    })

    // 为新元素添加监听
    setupWatchers()
  },
  { deep: true },
)

// 组件卸载时清理
onBeforeUnmount(() => {
  // 停止所有监听器
  watcherStops.forEach((stop) => stop())
  watcherStops.clear()
  stopArrayWatch()
})

// 初始化现有元素
setupWatchers()
const noUsedChildNetFormModel: any[] = []
watch(
  () => baseFormProvider.value.jsonStr,
  (newVal) => {
    if (!newVal) return
    const obj = JSON.parse(newVal)
    Object.assign(formModel, obj.formModel)
    childNetFormModel.length = 0
    childNetFormModel.push(
      ...obj.childNetFormModel.filter((item: any) => {
        const isUsed = baseFormProvider.value.uuids.includes(item.uuid) || !item.uuid
        if (isUsed) {
          item.old = true
          return true
        } else {
          noUsedChildNetFormModel.push(item)
          return false
        }
      }),
    )
  },
  { immediate: true },
)

function setChildNetRef(ref: any, model: any) {
  // vue3 先创建再销毁，所以需要判断
  if (ref) model.ref = ref
}

function removeChildNet(index: number) {
  childNetFormModel.splice(index, 1)
}

function addChildNet() {
  childNetFormModel.push({
    ip2: '',
    ip3: '',
    ipVersion: childNetFormModel[0].ipVersion === 'IPv4' ? 'IPv6' : 'IPv4',
    type2: '',
    type3: '',
    vpn2: '',
    vpn3: '',
    subnetStr: '',
    resourcePoolLabel: '',
    instanceId2: '',
    instanceId3: '',
    ipCheckSuccess: false,
    splitMessage: '',
    ref: null,
    old: false,
  })
}

const disabledIpVersion = computed(() => childNetFormModel.length === 2)
const childNetFormOptions = function (old: boolean, model: any) {
  console.log(model)

  return [
    {
      style: 'padding:4px 10px;margin:0 0 0 -12px;background:transparent;',
      gutter: 10,
      groupItems: [
        {
          span: 24,
          render() {
            return (
              <SlBlockTitle size={14} style="margin-bottom:20px" no-bar>
                网络划分
              </SlBlockTitle>
            )
          },
        },
        {
          label: 'IP版本',
          type: 'radio',
          key: 'ipVersion',
          props: {
            radio: {
              disabled: computed(() => disabledIpVersion.value || opType === 'view' || old),
            },
          },
          options: [
            {
              label: 'IPv4',
              value: 'IPv4',
            },
            {
              label: 'IPv6',
              value: 'IPv6',
            },
          ],
          span: 8,
        },
        {
          span: 16,
          render(props: any) {
            const { form } = props
            return (
              <el-button
                disabled={opType === 'view' || old}
                onClick={() => {
                  if (!form.ipVersion) {
                    SlMessage.error('请选择IP版本')
                    return
                  }
                  if (!baseFormProvider?.value?.regionCode) {
                    SlMessage.error('请选择资源池')
                    return
                  }
                  networkDialogVisible.value = true
                  netRangeListDialogProps.ipVersion = form.ipVersion
                  netRangeListDialogProps.formItem = form
                  netRangeListDialogProps.level = 2
                }}
                size="small"
                type="primary"
                style="margin-top:4px"
              >
                选择网段
              </el-button>
            )
          },
        },
        {
          label: ' IP地址',
          type: 'text',
          key: 'ip2',
          span: 6,
        },
        {
          label: '资源池',
          type: 'text',
          key: 'resourcePoolLabel',
          span: 6,
        },
        {
          label: '类型',
          type: 'text',
          key: 'type2',
          span: 6,
        },
        {
          label: 'VPN',
          type: 'text',
          key: 'vpn2',
          span: 6,
        },
        {
          label: '网络划分',
          type: 'input',
          key: 'subnetStr',
          span: 12,
          props: {
            disabled: opType === 'view' || old,
            placeholder: '请输入划分网段(先选择网段)',
          },
          rules: [
            { required: true, message: '请输入划分网段', trigger: 'change' },
            { validator: validateIpRangeRule, trigger: ['change'], form: model },
          ],
        },
        {
          span: 12,
          render(props: any) {
            const { form } = props
            return (
              <div style="margin-top:4px">
                <SlButton
                  disabled={!form.instanceId2 || form.ipCheckSuccess || opType === 'view' || old}
                  apiFunction={() => ipCheck(form)}
                  size="small"
                  type={form.ipCheckSuccess ? 'success' : 'primary'}
                  icon={form.ipCheckSuccess ? Check : ''}
                >
                  {form.ipCheckSuccess ? '校验成功' : '校验'}
                </SlButton>
                <SlButton
                  apiFunction={() => ipSplit(form)}
                  disabled={!form.ipCheckSuccess || opType === 'view' || old}
                  size="small"
                >
                  划分完成
                </SlButton>
              </div>
            )
          },
        },
        {
          span: 24,
          render(props: any) {
            const { form } = props
            return (
              form.splitMessage && (
                <div
                  class={
                    form.splitMessage.includes('划分成功') ? 'split-message' : 'split-message error'
                  }
                >
                  <el-tooltip content={form.splitMessage} placement="top">
                    {form.splitMessage}
                  </el-tooltip>
                </div>
              )
            )
          },
        },
        {
          span: 24,
          render() {
            return (
              <SlBlockTitle size={14} style="margin-bottom:20px" no-bar>
                配置信息
              </SlBlockTitle>
            )
          },
        },
        {
          span: 24,
          render(props: any) {
            const { form } = props
            return (
              <el-button
                disabled={opType === 'view' || old}
                onClick={() => {
                  if (!form.instanceId2) {
                    SlMessage.error('请选择二级网段')
                    return
                  }
                  if (!baseFormProvider?.value?.regionCode) {
                    SlMessage.error('请选择资源池')
                    return
                  }
                  networkDialogVisible.value = true
                  netRangeListDialogProps.ipVersion = form.ipVersion
                  netRangeListDialogProps.formItem = form
                  netRangeListDialogProps.level = 3
                }}
                size="small"
                type="primary"
                style="margin-bottom:20px"
                no-bar
              >
                选择网段
              </el-button>
            )
          },
        },
        {
          label: 'IP版本',
          type: 'text',
          key: 'ipVersion',
          span: 6,
        },
        {
          label: 'IP地址',
          type: 'text',
          key: 'ip3',
          span: 6,
        },

        {
          label: '类型',
          type: 'text',
          key: 'type3',
          span: 6,
        },
        {
          label: 'VPN',
          type: 'text',
          key: 'vpn3',
          span: 6,
        },
      ],
    },
  ]
}

const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;',
    gutter: 60,
    hideBlockTitle: true,
    groupItems: [
      {
        label: '网络名称',
        type: 'input',
        key: 'name',
        props: {
          disabled: ['view', 'addSubnet'].includes(opType),
          maxlength: 48,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入网络名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
        span: 12,
      },
      {
        label: '网络平面',
        type: 'text',
        key: 'plane',
        span: 12,
      },
      {
        label: '网络类型',
        type: computed(() => (baseFormProvider.value.isCxc ? 'text' : 'radio')),
        key: 'networkType',
        props: {
          group: { disabled: opType === 'view' },
        },
        options: [
          { value: 'vlan', label: 'vlan' },
          { value: 'vxlan', label: 'vxlan' },
        ],
        span: 12,
      },
      {
        label: '外部网络',
        type: 'select',
        key: 'externalNetworkId',
        props: {
          select: {
            disabled: ['view', 'addSubnet'].includes(opType),
          },
        },
        required: true,
        rules: [{ required: true, message: '请选择外部网络', trigger: ['blur', 'change'] }],
        span: 12,
        hidden: hiddenExternalNetwork,
        options: externalNetworkOptions,
      },
      {
        span: 16,
        hidden: computed(() => baseFormProvider.value.isCxc),
        render() {
          return (
            <el-button
              disabled={opType === 'view'}
              onClick={() => {
                if (!baseFormProvider?.value?.regionCode) {
                  SlMessage.error('请选择资源池')
                  return
                }
                vlanDialogVisible.value = true
              }}
              size="small"
              type="primary"
              style="margin-top:4px"
            >
              选择vlan
            </el-button>
          )
        },
      },
      {
        label: 'vlan值',
        type: 'text',
        key: 'vlan',
        hidden: computed(() => baseFormProvider.value.isCxc),
        span: 12,
      },
      {
        label: 'vlan类型',
        type: 'text',
        key: 'vlanType',
        hidden: computed(() => baseFormProvider.value.isCxc),
        span: 12,
      },

      {
        span: 24,
        render() {
          return childNetFormModel.map((model, index) => (
            <el-row class="subnet-row" style="background:#edf5ff;margin:10px 0">
              <sl-form
                key={model}
                ref={(ref: any) => setChildNetRef(ref, model)}
                options={childNetFormOptions(model.old, model)}
                modelValue={model}
              ></sl-form>
              {childNetFormModel.length > 1 && opType !== 'view' && !model.old && (
                <el-icon onClick={() => removeChildNet(index)} color="red" class="goods-del-btn">
                  <CircleCloseFilled />
                </el-icon>
              )}
            </el-row>
          ))
        },
      },
      {
        span: 24,
        render() {
          return (
            opType !== 'view' &&
            childNetFormModel.length === 1 && (
              <el-button
                onClick={() => addChildNet()}
                icon={<CirclePlus />}
                style="width:100%"
                type="primary"
                plain
              >
                增加子网
              </el-button>
            )
          )
        },
      },
    ],
  },
])
const doSubmit = async () => {
  const tempChildNetFormModel = childNetFormModel.map((item: any) => {
    return {
      ...item,
      uuid: item.uuid ? item.uuid : item.old ? '' : uuid(16),
    }
  })
  const couldSubmitChildNetFormModel = tempChildNetFormModel.filter((item: any) => !item.old)
  if (couldSubmitChildNetFormModel.length === 0) {
    return
  }
  const submitdata = {
    detail: JSON.stringify({
      formModel: { ...formModel, operationName: '查看资源申请单' },
      childNetFormModel: tempChildNetFormModel
        .concat(noUsedChildNetFormModel)
        .map((ele) => ({ ...ele, ref: null })),
      orderId,
    }),
    plane: props.plane,
    name: formModel.name,
    networkType: formModel.networkType,
    vlanId: formModel.vlanInstanceId,
    vlan: formModel.vlan,
    subnets: couldSubmitChildNetFormModel.map((ele) => ({
      cidr: ele.ip3,
      ipVersion: ele.ipVersion,
      instanceId: ele.instanceId3,
      level2InstanceId: ele.instanceId2,
      uuid: ele.uuid,
    })),
    externalNetworkId: formModel.externalNetworkId,
    externalNetworkName: formModel.externalNetworkName,
  }
  const instanceId = childNetFormModel.map((ele) => ele.instanceId3)
  const vlanInstanceId = formModel.vlanInstanceId

  return {
    submitdata,
    updateIps: instanceId,
    updateVlans: [vlanInstanceId],
  }
}
const submitfromdata = async () => {
  if (!slFormRef.value) return
  const validate = await slFormRef.value.validateField([
    'name',
    'functionalModule',
    'networkType',
    'resourcePoolId',
    'azCode',
  ])
  const subnetValidate = childNetFormModel.every((ele) => ele.instanceId3)
  if (validate) {
    if (!subnetValidate) {
      SlMessage.error('请选择三级网段')
      return
    }
    if (!baseFormProvider.value.isCxc && !formModel.vlanInstanceId) {
      SlMessage.error('请选择vlan')
      return
    }
    return doSubmit()
  }
}
defineExpose({
  submitfromdata,
})
</script>
<style scoped>
:deep(.split-message) {
  word-break: break-all;
  font-size: 14px;
  background: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  border-left: 4px solid var(--el-color-success);
  color: var(--el-color-success);
}
:deep(.split-message.error) {
  color: var(--el-color-error);
  border-left: 4px solid var(--el-color-error);
}
:deep(.subnet-row) {
  .goods-del-btn {
    position: absolute;
    top: -7px;
    right: -6px;
    z-index: 10;
    color: var(--el-color-danger);
    font-size: 1.2rem;
    cursor: pointer;
    display: none;
    display: none;
  }
}
:deep(.subnet-row:hover) {
  .goods-del-btn {
    display: block;
  }
}
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;
      .oneboxleft {
        flex-grow: 3;
        margin: 8px 0px 8px 0;
      }

      .oneboxright {
        flex-grow: 1;
        flex-basis: 0;
        background: #ffffff;
        margin: 8px 8px 8px 0;
        border-radius: 6px;
        min-width: 300px;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}
</style>
