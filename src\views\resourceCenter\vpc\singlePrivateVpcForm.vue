<template>
  <div class="resourcerequest">
    <div class="resourcerequestbox">
      <div class="onebox">
        <div class="oneboxleft">
          <sl-form
            ref="slFormRef"
            show-block-title
            :options="formOptions"
            :model-value="formModel"
            style="overflow: hidden"
          >
          </sl-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx">
import slForm from '@/components/form/SlForm.vue'
import { reactive, ref, inject, type Ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import useModel from './model'
import { validateGoodsName } from '../utils'
import type { IBaseFormProvider } from './types'
import { uuid } from '@/utils'
import { validateIpv4Range } from '@/utils'

const baseFormProvider = inject<Ref<IBaseFormProvider>>('baseFormProvider')!
const opType = baseFormProvider.value.opType
const props = defineProps<{
  plane: string
}>()

const model = useModel()
const formModel = reactive<{ [key: string]: any }>({})
Object.assign(formModel, model)

const childNetFormModel = reactive([
  {
    subnetName: '',
    startIp: '',
    netmask: '',
    ref: null,
    old: false,
    ipRangeStart: '',
    ipRangeEnd: '',
  },
])
const noUsedChildNetFormModel: any[] = []
watch(
  () => baseFormProvider.value.jsonStr,
  (newVal: string) => {
    if (!newVal) return
    const obj = JSON.parse(newVal)
    Object.assign(formModel, obj.formModel)
    Object.assign(
      childNetFormModel,
      obj.childNetFormModel.filter((item: any) => {
        const isUsed = baseFormProvider.value.uuids.includes(item.uuid) || !item.uuid
        if (isUsed) {
          item.old = true
          return true
        } else {
          noUsedChildNetFormModel.push(item)
          return false
        }
      }),
    )
  },
  { immediate: true },
)

const globalDic = useGlobalDicStore()

const slFormRef = ref()
const { getDic } = globalDic
const route = useRoute()
const orderId = route.query.orderId as string

function validateIpRangeRule(rule: any, value: any, callback: any) {
  const fn = validateIpv4Range
  if (!value) return callback()
  if (!fn(value + '/0')) {
    callback(new Error(`请输入正确的IPv4地址`))
  } else {
    callback()
  }
}
const childNetFormOptions = function (old: boolean) {
  return [
    {
      style: 'padding:0;margin:0 0 0 -12px',
      groupItems: [
        {
          label: '子网名称',
          type: 'input',
          key: 'subnetName',
          rules: [
            { required: true, message: '请输入子网名称', trigger: ['blur', 'change'] },
            { validator: validateGoodsName, trigger: ['blur', 'change'] },
          ],
          props: {
            disabled: opType === 'view' || old,
            maxlength: 48,
            showWordLimit: true,
          },
          span: 12,
        },
        {
          label: '子网IP',
          type: 'slot',
          slotName: 'ipSlot',
          key: 'startIp',
          span: 12,
          rules: [
            { required: true, message: '请输入子网IP', trigger: ['blur', 'change'] },
            { validator: validateIpRangeRule, trigger: ['change'] },
          ],
          props: {
            disabled: opType === 'view' || old,
          },
        },
        {
          label: '起始IP',
          type: 'input',
          key: 'ipRangeStart',
          options: getDic('functionalModule'),
          rules: [{ validator: validateIpRangeRule, trigger: ['change'] }],
          span: 12,
          props: {
            disabled: opType === 'view' || old,
          },
        },
        {
          label: '结束IP',
          type: 'input',
          key: 'ipRangeEnd',
          span: 12,
          rules: [{ validator: validateIpRangeRule, trigger: ['change'] }],
          props: {
            disabled: opType === 'view' || old,
          },
        },
        {
          span: 24,
          render() {
            return <el-divider />
          },
        },
      ],
    },
  ]
}
const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;',
    hideBlockTitle: true,
    groupItems: [
      {
        label: 'VPC名称',
        type: 'input',
        key: 'instanceName',
        span: 12,
        props: {
          disabled: ['view', 'addSubnet'].includes(opType),
        },
        rules: [
          { required: true, message: '请输入VPC名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '网段选择',
        type: 'select',
        key: 'netRange',
        options: getDic('netRange'),
        span: 12,
        props: {
          select: {
            disabled: ['view', 'addSubnet'].includes(opType),
          },
        },
        rules: {
          required: true,
          message: '请选择网段',
          trigger: ['blur', 'change'],
        },
      },
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px" no-bar>
              默认子网
            </SlBlockTitle>
          )
        },
      },
      {
        span: 24,
        render() {
          return childNetFormModel.map((model, index) => (
            <el-row>
              <el-col span={23}>
                <sl-form
                  key={model}
                  ref={(ref: any) => setChildNetRef(ref, model)}
                  options={childNetFormOptions(model.old)}
                  modelValue={model}
                >
                  {{
                    ipSlot: ({ item }: any) => (
                      <div style="display: flex;align-items: center;">
                        <el-input
                          style="flex:2"
                          v-model={model.startIp}
                          placeholder="请输入子网IP"
                          {...item.props}
                        />
                        <span style="width: 10px;margin: 0 10px">/</span>
                        <el-input-number
                          controls={false}
                          v-model={model.netmask}
                          {...item.props}
                          max={32}
                          min={0}
                          placeholder="请输入掩码长度"
                        />
                      </div>
                    ),
                  }}
                </sl-form>
              </el-col>
              {childNetFormModel.length > 1 && opType !== 'view' && !model.old && (
                <el-col
                  style="display: flex;align-items: center !important;margin-bottom: 70px;justify-content: center;cursor: pointer;"
                  span={1}
                  onClick={() => removeChildNet(index)}
                >
                  <el-icon color="red">
                    <Delete />
                  </el-icon>
                </el-col>
              )}
            </el-row>
          ))
        },
      },
      {
        span: 24,
        render() {
          return (
            opType !== 'view' && (
              <el-button
                onClick={() => addChildNet()}
                icon={<CirclePlus />}
                style="width:100%"
                type="primary"
                plain
              >
                增加子网
              </el-button>
            )
          )
        },
      },
    ],
  },
])
function addChildNet() {
  childNetFormModel.push({
    subnetName: '',
    startIp: '',
    netmask: '',
    ref: null,
    old: false,
    ipRangeStart: '',
    ipRangeEnd: '',
  })
}
function setChildNetRef(ref: any, model: any) {
  // vue3 先创建再销毁，所以需要判断
  if (ref) model.ref = ref
}
function removeChildNet(index: number) {
  childNetFormModel.splice(index, 1)
}

const doSubmit = async () => {
  const tempChildNetFormModel = childNetFormModel.map((item: any) => {
    return {
      ...item,
      uuid: item.uuid ? item.uuid : item.old ? '' : uuid(16),
    }
  })
  const couldSubmitChildNetFormModel = tempChildNetFormModel.filter((item: any) => !item.old)
  if (couldSubmitChildNetFormModel.length === 0) {
    return
  }
  const submitdata = {
    vpcName: formModel.instanceName,
    cidr: formModel.netRange,
    plane: props.plane,
    subnetDTOList: couldSubmitChildNetFormModel.map((ele) => {
      return {
        ...ele,
        ...(ele.ipRangeStart || ele.ipRangeEnd
          ? {
              allocationPools: [
                { startIp: ele.ipRangeStart || null, endIp: ele.ipRangeEnd || null },
              ],
            }
          : {}),
      }
    }),
    detail: JSON.stringify({
      formModel: { ...formModel, operationName: '查看资源申请单' },
      childNetFormModel: tempChildNetFormModel.concat(noUsedChildNetFormModel).map((ele) => ({
        ...ele,
        ref: null,
      })),
      orderId,
    }),
  }
  return {
    submitdata,
    updateIps: childNetFormModel.map((item: any) => item.instanceId),
  }
}
const submitfromdata = async () => {
  if (!slFormRef.value) return
  const ChildNetPromises: Promise<any>[] = childNetFormModel.map((model: any) =>
    model.ref.validate(),
  )
  ChildNetPromises.push(slFormRef.value.validate())
  const validate = await Promise.all(ChildNetPromises)
  if (!(validate.length > 0 && validate.every(Boolean))) return
  return doSubmit()
}
defineExpose({
  submitfromdata,
})
</script>
<style scoped>
.scroll-view {
  height: calc(100vh - 188px);
}

.resourcerequest {
  .resourcerequestbox {
    .onebox {
      display: flex;

      .oneboxleft {
        flex-grow: 3;
        margin: 8px 0px 8px 0;
      }

      .oneboxright {
        flex-grow: 1;
        flex-basis: 0;
        background: #ffffff;
        margin: 8px 8px 8px 0;
        border-radius: 6px;
        min-width: 300px;
      }
    }

    .onefooter {
      height: 48px;
      margin: 0 8px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 14px;
    }
  }
}
</style>
