<template>
  <Card>
    <Title title="算力利用率" />
    <div ref="gpuLineRef" style="width: 100%; margin-top: 15px"></div>
  </Card>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeMount } from 'vue'
import Card from './Card.vue'
import Title from './Title.vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

let gpuLine: ECharts | null = null
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
    required: true,
  },
  xAxisData: {
    type: Array,
    default: () => [],
    required: true,
  },
})

const gpuLineRef = ref<HTMLDivElement | null>(null)

watch(
  () => props.data,
  () => {
    setOption()
  },
)

onBeforeMount(() => {
  gpuLine?.dispose()
})

function setOption() {
  if (!gpuLine) return
  if (!props.data.length) {
    gpuLine.clear()
    gpuLine.setOption({
      graphic: {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: '暂无数据',
          fontSize: 14,
          fill: '#999',
          textAlign: 'center',
          textVerticalAlign: 'middle',
        },
      },
      xAxis: {
        show: false,
      },
      yAxis: {
        show: false,
      },
      grid: {
        show: false,
      },
    })
    return
  }

  // 生成所有城市的数据
  const allSeriesData = props.data
  gpuLine.clear()
  gpuLine.setOption({
    grid: { left: 40, right: 20, top: 20, bottom: 30 },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 10,
      },
      formatter: function (params: any) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;z-index: 1000;">${params[0].axisValue}</div>`
        // params按照value降序排序
        params.sort((a: any, b: any) => b.value - a.value)
        params.forEach((param: any) => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
            <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${param.color}; margin-right: 8px;"></span>
            <span>${param.seriesName}</span>
            <span style="margin-left: auto; font-weight: bold;">${param.value}%</span>
          </div>`
        })
        return result
      },
    },
    xAxis: {
      type: 'category',
      data: props.xAxisData.map((item: any) => item.slice(0, 5)),
      axisLine: { lineStyle: { color: 'gray' } },
      axisLabel: { color: 'black', fontSize: 10 },
    },
    yAxis: {
      type: 'value',
      min: 0,
      axisLine: { show: false },
      splitLine: { lineStyle: { color: '#eaf6ff' } },
      axisLabel: {
        color: 'black',
        fontSize: 10,
        formatter: '{value}%',
      },
    },
    series: allSeriesData.map(({ city, data }: any) => ({
      name: city.name,
      data: data,
      type: 'line',
      smooth: false,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: { color: city.color, width: 1 },
      itemStyle: { color: city.color },
    })),
  })
}

// 原始数值堆叠柱状图数据
onMounted(() => {
  // 折线图：算力利用率
  gpuLine = echarts.init(gpuLineRef.value!, null, { height: 170, renderer: 'svg' })
  setOption()
})
</script>

<style scoped>
.tip-con {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.right-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: right;
}

.power-bar-chart {
  width: 100%;
  background: transparent;
  margin-top: 0;
}
</style>
