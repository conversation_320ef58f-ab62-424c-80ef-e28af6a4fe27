<template>
  <div id="BackupDetail" class="table-box">
    <sl-page-header
      title="备份策略详情"
      :icon="{
        class: 'page_beifencelve',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="backup-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <div class="operation-buttons" v-if="!shouldHideResourceOperations">
          <el-button type="primary" @click="handleBindResource">绑定资源</el-button>
          <el-button type="danger" @click="handleUnbindResource">解绑资源</el-button>
        </div>
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
        </sl-form>
      </div>
    </el-scrollbar>

    <!-- 绑定/解绑资源弹窗 -->
    <ResourceBindDialog
      v-model:visible="bindDialogVisible"
      :backup-type="detailData.backupType"
      :resource-pool-id="dialogType === 'BIND' ? detailData.resourcePoolId : ''"
      :device-id="dialogType === 'UNBIND' ? resourceId : ''"
      :backup-id="resourceId"
      :dialog-type="dialogType"
      @refresh="refreshResourceTable"
      @selectDevice="handleDeviceSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { ProTableInstance } from '@/components/SlProTable/interface'
import SlPageHeader from '@/components/SlPageHeader/index.vue'

import ResourceBindDialog from '@/views/resourceCenter/backup/components/ResourceBindDialog.vue'
import { getResourceDetail, backupOperate } from '@/api/modules/resourecenter'
import { ElMessage } from 'element-plus'
import { useRolePermission } from '../hooks/useRolePermission'

const route = useRoute()
const router = useRouter()
const resourceId = ref<string>((route.query.id as string) || '')
const bindDialogVisible = ref(false)
const dialogType = ref<'BIND' | 'UNBIND'>('BIND')
const resourceTable = ref<ProTableInstance>()
const slFormRef = ref<any>(null)
const tableLoading = ref(false)

const { shouldHideResourceOperations } = useRolePermission()

// 备份策略详情数据
const detailData = reactive<any>({
  deviceName: '',
  backupType: '',
  backupTypeName: '',
  frequency: '',
  weekDay: '',
  weekDayName: '',
  tenantName: '',
  businessSysName: '',
  domainName: '',
  resourcePoolName: '',
  resourcePoolId: '',
  orderCode: '',
  effectiveTime: '',
  applyUserName: '',
})

// 表单配置
const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '策略名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '备份类型',
        type: 'text',
        key: 'backupTypeName',
        span: 8,
      },
      {
        label: '备份频率',
        type: 'text',
        key: 'frequencyName',
        span: 8,
      },
      {
        label: '星期',
        type: 'text',
        key: 'daysOfWeek',
        span: 8,
        hidden: () => detailData.frequency !== 'weekly',
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'domainName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'effectiveTime',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

// 初始化
onMounted(async () => {
  await fetchBackupDetail()
})

// 获取备份策略详情
const fetchBackupDetail = async () => {
  const res = await getResourceDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
  detailData.backupTypeName = detailData.backupType === 'ecs' ? '云主机' : '云硬盘'
  detailData.frequencyName = detailData.frequency === 'days' ? '每天' : '每周'
}

// 返回上一页
const handleGoBack = () => {
  router.push('/backupList')
}

// 绑定资源
const handleBindResource = () => {
  dialogType.value = 'BIND'
  bindDialogVisible.value = true
}

// 解绑资源
const handleUnbindResource = () => {
  dialogType.value = 'UNBIND'
  bindDialogVisible.value = true
}

// 处理设备选择
const handleDeviceSelect = async (devices: any[]) => {
  if (!devices || devices.length === 0) return

  try {
    tableLoading.value = true

    await backupOperate({
      type: dialogType.value,
      detailId: devices.map((item: any) => item.id),
      backupDetailId: resourceId.value,
    })

    ElMessage.success(dialogType.value === 'BIND' ? '绑定资源成功' : '解绑资源成功')
    bindDialogVisible.value = false
    refreshResourceTable()
  } catch (error) {
    console.error(error)
  } finally {
    tableLoading.value = false
  }
}

// 刷新资源表格
const refreshResourceTable = () => {
  if (resourceTable.value) {
    resourceTable.value.getTableList()
  }
}
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;

  .card-header {
    padding: 12px 20px;
    border-bottom: 1px solid #e0e0e0;
    font-weight: bold;
  }
}

.operation-buttons {
  background-color: white;
  margin: 8px 8px -8px 8px;
  padding: 8px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.resource-card {
  margin-top: 16px;

  :deep(.el-table) {
    padding: 16px;
  }
}
</style>
