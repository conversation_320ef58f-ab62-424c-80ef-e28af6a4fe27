<template>
  <div class="gpu-slice-chart">
    <!-- 顶部标签 -->
    <div class="tab-header">
      <span
        v-for="(tab, index) in tabs"
        :key="index"
        :class="{ active: currentTab === tab, 'tab-disabled': tab !== 'T4' }"
        @click="handleTabClick(tab as keyof typeof GPU_TYPES)"
      >
        {{ tab }}
      </span>
    </div>

    <div class="slice-container">
      <div class="slice-details">
        <!-- 切片数及详情 -->
        <div class="slice-info">
          <span class="slice-title">切片数</span>
          <a class="detail-link" @click="handleOpen()">详情>></a>
        </div>
        <!-- 已分配量和剩余量 -->
        <div class="usage-info">
          <div class="usage-item usage-item-text">
            <span>物理卡数:{{ ofPhysicalCards }}</span>
          </div>
          <div class="usage-item">
            <span class="dot" style="background-color: #409eff"></span>
            <span>已分配量:{{ allocated }}</span>
          </div>
          <div class="usage-item">
            <span class="dot" style="background-color: #d9d9d9"></span>
            <span>剩余量:{{ remaining }}</span>
          </div>
        </div>
      </div>
      <!-- 仪表盘容器 -->
      <div
        v-show="remaining && allocated"
        ref="gaugeChart"
        class="gauge-chart"
        style="width: 300px; height: 200px"
      ></div>
      <div v-show="!remaining || !allocated" class="empty-container">暂无数据</div>
    </div>
  </div>
  <Drawer
    v-model="drawer"
    title="I am the title"
    :direction="direction"
    :before-close="handleClose"
    size="80%"
    :model-name="modelName"
    v-if="drawer"
    type="virtual"
  >
  </Drawer>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, computed, nextTick } from 'vue'
import * as echarts from 'echarts'

import Drawer from './Drawer.vue'

const drawer = ref(false)
const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('btt')
const modelName = ref<string>('')
const handleClose = () => {
  drawer.value = false
}
const handleOpen = () => {
  modelName.value = currentTab.value
  drawer.value = true
}

// 不同类型GPU的配置
const GPU_TYPES = ref({
  T4: {
    max: 90,
    initialAllocated: 78,
    totalCount: 0,
    formatter: '13/15',
  },
  A10: {
    max: 80,
    initialAllocated: 60,
    totalCount: 0,
    formatter: '3/4',
  },
  V100: {
    max: 100,
    initialAllocated: 50,
    totalCount: 0,
    formatter: '1/2',
  },
  A40: {
    max: 120,
    initialAllocated: 90,
    totalCount: 0,
    formatter: '3/4',
  },
})

const formatter = ref('1/9')
// 状态管理
const tabs = ref(['T4', 'A10', 'V100', 'A40'])
const currentTab = ref<keyof typeof GPU_TYPES.value>('T4')

const allocated = ref(0)
const remaining = ref(0)
const ofPhysicalCards = ref(0)

const gaugeChart = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

// 计算属性：当前GPU类型的最大值
const currentMax = computed(() => GPU_TYPES.value[currentTab.value].max) ?? 0

// 初始化仪表盘
const initGaugeChart = () => {
  if (gaugeChart.value && !chartInstance) {
    chartInstance = echarts.init(gaugeChart.value, null, { renderer: 'svg' })

    // 明确指定series类型
    const option: echarts.EChartsOption = {
      grid: {
        left: '30%',
        right: '10%',
        top: '5%',
        bottom: '5%',
        // containLabel: true,
      },
      series: [
        {
          type: 'gauge',
          radius: '100%',
          center: ['50%', '55%'] as [string, string], // 明确类型为元组
          startAngle: 180,
          endAngle: 0,
          min: 0,
          max: currentMax.value,
          splitNumber: 8,
          axisLine: {
            lineStyle: {
              width: 8,
              // color: [[1, '#409eff']] as [number, string][], // 明确颜色数组类型
              color: [
                [allocated.value / currentMax.value, '#409eff'], // 已使用部分，蓝色
                [1, '#d9d9d9'], // 剩余部分，灰色
              ],
            },
          },
          pointer: { show: false },
          axisTick: { show: false },
          splitLine: {
            show: true,
            length: 6,
            distance: 2,
            lineStyle: {
              color: '#333',
              width: 1,
              opacity: 0.3,
            },
          },
          axisLabel: {
            show: true,
            color: '#333',
            distance: 10,
            fontSize: 10,
          },
          detail: {
            show: true,
            fontSize: 12,
            fontWeight: 'bold',
            offsetCenter: [0, '-20%'] as [number, string], // 明确偏移中心类型
            color: '#333',
            lineHeight: 20,
            valueAnimation: true,
            formatter: `${formatter.value}\n切分比`,
            // rich: {
            //   value: { fontSize: 28, lineHeight: 36, color: '#409eff' },
            //   formatter: { fontSize: 16, color: '#333' },
            // },
          },
          data: [
            {
              value: allocated.value,
              // name: '已分配',
              itemStyle: { color: '#409eff' },
            },
          ] as any, // 明确数据类型
          emphasis: {
            detail: {
              fontSize: 10,
              shadowBlur: 3,
              shadowColor: 'rgba(0,0,0,0.2)',
            },
          },
        } as echarts.GaugeSeriesOption, // 明确指定为仪表盘系列
      ] as echarts.SeriesOption[], // 明确指定系列数组类型
    }

    chartInstance.setOption(option)
  }
}
initGaugeChart()
// 切换标签
const handleTabClick = (tab: keyof typeof GPU_TYPES.value) => {
  if (tab !== 'T4') return
  if (tab) {
    currentTab.value = tab
    allocated.value = GPU_TYPES.value[tab].initialAllocated
    remaining.value = GPU_TYPES.value[tab].max - GPU_TYPES.value[tab].initialAllocated
    ofPhysicalCards.value = GPU_TYPES.value[tab].totalCount
    formatter.value = GPU_TYPES.value[tab].formatter
    if (chartInstance) {
      chartInstance.setOption({
        series: [
          {
            max: currentMax.value,
            data: [{ value: allocated.value }] as any, // 明确数据类型
            detail: {
              formatter: `${formatter.value}\n切分比`,
            },
            axisLine: {
              lineStyle: {
                width: 8,
                // color: [[1, '#409eff']] as [number, string][], // 明确颜色数组类型
                color: [
                  [allocated.value / currentMax.value, '#409eff'], // 已使用部分，蓝色
                  [1, '#d9d9d9'], // 剩余部分，灰色
                ],
              },
            },
          }, // 明确指定为仪表盘系列
        ], // 明确指定系列数组类型
      })
    }
  }
}

// 生命周期管理
nextTick(() => {
  initGaugeChart()
  window.addEventListener('resize', () => chartInstance?.resize())
})
// 计算最大公约数
const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b))

// 化简分数
const simplifyFraction = (numerator: number, denominator: number): string => {
  if (denominator === 0 || numerator === 0) return '0'
  const divisor = gcd(numerator, denominator)
  return `${numerator / divisor}/${denominator / divisor}`
}

const initData = (data: any) => {
  if (!data || !data.length) return

  tabs.value = data.map((item: any) => item.modelName)
  currentTab.value = tabs.value[0] as keyof typeof GPU_TYPES.value

  const gpuData: any = {}

  data.map((item: any) => {
    gpuData[item.modelName] = {
      max: item.totalCountMax ?? 0,
      initialAllocated: item.allocatedCount ?? 0,
      totalCount: item.totalCount,
      formatter:
        item.modelName === 'T4' ? '1/4' : simplifyFraction(item.allocatedCount, item.totalCountMax),
    }
  })
  GPU_TYPES.value = gpuData

  nextTick(() => {
    handleTabClick(currentTab.value)
  })
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', () => chartInstance?.resize())
  chartInstance?.dispose()
  chartInstance = null
})

defineExpose({
  initData,
})
</script>

<style scoped>
* {
  box-sizing: border-box;
}
.tab-header {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
  border-bottom: #949292 1px solid;
}
.tab-header span {
  padding: 3px 12px;
  margin-right: 10px;
  cursor: pointer;
  color: #333;
}
.tab-header span.active {
  border-bottom: 2px solid #409eff;
  color: #409eff;
  font-weight: bold;
}
.tab-header span.tab-disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}
.slice-container {
  position: relative;
  height: 110px;
  padding-left: 50px;
}

.slice-details {
  width: 130px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.slice-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
}
.slice-title {
  font-weight: bold;
  margin-right: 8px;
}
.detail-link {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;
  position: relative;
  z-index: 11;
}
.usage-info {
  margin-bottom: 15px;
  font-size: 10px;
}
.usage-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.usage-item-text {
  font-size: 10px;
  font-weight: bold;
}

.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.empty-container {
  width: 100%;
  height: 100%;
  padding-top: 50px;
  text-align: center;
  font-size: 14px;
  color: #999;
}
</style>
