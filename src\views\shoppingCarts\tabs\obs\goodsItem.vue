<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="goods.orderJson"
  >
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>
    <!-- 数据盘插槽 -->
    <template #obsSlot="{ form, item }">
      <div style="display: flex; flex-grow: 1">
        <el-select clearable :disabled="item.disabled" v-model="form[item.key][0]">
          <el-option
            :key="option.value"
            v-for="option in item.options"
            :label="option.label"
            :min="10"
            :value="option.value"
          />
        </el-select>
        <el-input-number
          :disabled="item.disabled"
          v-bind="item.props"
          v-model="form[item.key][1]"
          style="margin: 0 4px; min-width: 120px"
        />
        GB
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { IObsModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import { validateObsName } from '@/views/resourceCenter/utils'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const rouete = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<IObsModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: rouete.query.orderId ? true : false,
  })
}
const formModel = props.goods.orderJson

const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IObsModel>) {
  goods.ref = slFormRef
}

const validateobsTypeobsSize = (rule: any, value: any, callback: any) => {
  if (!formModel.obs[0]) {
    callback(new Error('请选择数据盘类型'))
  } else if (!formModel.obs[1]) {
    callback(new Error('请输入数据盘大小'))
  } else {
    callback()
  }
}

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '对象存储名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入对象存储名称', trigger: ['blur', 'change'] },
          { validator: validateObsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '功能模块',
        type: 'select',
        key: 'functionalModule',
        options: getDic('functionalModule'),
        span: 8,
        rules: {
          required: true,
          message: '请选择功能模块',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '存储',
        type: 'slot',
        slotName: 'obsSlot',
        key: 'obs',
        options: getDic('obs'),
        span: 8,
        props: {
          min: 1,
        },
        required: true,
        rules: [
          {
            validator: validateobsTypeobsSize,
            trigger: 'change',
          },
        ],
      },
      {
        label: '开通数量',
        type: 'inputNumber',
        key: 'numbers',
        props: {
          min: 1,
          max: 100,
          step: 1,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请选择开通数量', trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
