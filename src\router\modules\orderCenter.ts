// import SlLayout from '@/views/SlLayout.vue'
import type { RouteRecordRaw } from 'vue-router'

const orderCenterRouter: RouteRecordRaw[] = [
  {
    path: '/corporateOrder',
    component: () => import('@/views/orderCenter/corporateOrder/list.vue'),
    name: 'corporateOrder',
    meta: {
      title: '订单中心',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/corporateOrderDetail',
    component: () => import('@/views/orderCenter/corporateOrder/detail.vue'),
    name: 'corporateOrderDetail',
    meta: {
      title: '订单详情',
      hidden: true,
    },
  },
  {
    path: '/unsubscribeList',
    component: () => import('@/views/orderCenter/unsubscribeList/list.vue'),
    name: 'unsubscribeList',
    meta: {
      title: '退订列表',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/unsubscribeDetail',
    component: () => import('@/views/orderCenter/unsubscribeList/detail.vue'),
    name: 'unsubscribeDetail',
    meta: {
      title: '退订详情',
      hidden: true,
    },
  },
  {
    path: '/changeList',
    component: () => import('@/views/orderCenter/changeList/list.vue'),
    name: 'changeList',
    meta: {
      title: '变更列表',
      icon: 'menu_changeorderlist',
    },
  },
  {
    path: '/changeDetail',
    component: () => import('@/views/orderCenter/changeList/detail.vue'),
    name: 'changeDetail',
    meta: {
      title: '变更详情',
      hidden: true,
    },
  },
]

export default orderCenterRouter
