<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getUserListApi"
    :init-param="props.queryParams"
    hidden-table-header
  >
  </SlProTable>
  <SlDialog
    v-model="roleDialogVisible"
    title="授权角色"
    width="600px"
    destroy-on-close
    @close="handleRoleClose"
    @confirm="handleRoleConfirm"
  >
    <sl-form ref="roleFormRef" :options="roleFormOptions" v-model="roleFormModel"></sl-form>
    <template #footer>
      <el-button @click="handleRoleClose">取消</el-button>
      <sl-button type="primary" :api-function="handleRoleConfirm">提交</sl-button>
    </template>
  </SlDialog>
  <SlDialog
    v-model="tenantDialogVisible"
    title="绑定租户"
    width="600px"
    destroy-on-close
    @close="handleTenantClose"
    @confirm="handleTenantConfirm"
  >
    <sl-form ref="tenantFormRef" :options="tenantFormOptions" v-model="tenantFormModel"></sl-form>
    <template #footer>
      <el-button @click="handleTenantClose">取消</el-button>
      <sl-button type="primary" :api-function="handleTenantConfirm">提交</sl-button>
    </template>
  </SlDialog>
</template>
<script setup lang="tsx" name="DataList">
import { reactive, ref, watch, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import SlMessage from '@/components/base/SlMessage'
import {
  getUserListApi,
  freezeUserApi,
  unfreezeUserApi,
  userAuthorizationRoleApi,
  userBindTenantApi,
  userGetDetailApi,
} from '@/api/modules/managementCenter'
import { useGlobalDicStore } from '@/stores/modules/dic'
const props = defineProps<{
  queryParams: any
  roleList: any[]
  tenantList: any[]
}>()
const localRoleList = ref([])
const localTenantList = ref([])
watch(
  () => props.roleList,
  (newRoleList: any) => {
    localRoleList.value = newRoleList
  },
)
watch(
  () => props.tenantList,
  (newTenantList: any) => {
    localTenantList.value = newTenantList
  },
)
const globalDic = useGlobalDicStore()
const { getDic, getDicNumber } = globalDic
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  { prop: 'account', label: '用户账号', fixed: 'left', width: 150 },
  { prop: 'username', label: '用户姓名', width: 150 },
  { prop: 'roleNames', label: '所属角色', width: 120 },
  { prop: 'email', label: '邮箱', width: 120 },
  { prop: 'mobilephone', label: '手机号', width: 150 },
  { prop: 'tenantNames', label: '所属租户', width: 150 },
  { prop: 'userCategory', label: '用户类型', width: 150, enum: getDic('userCategory') },
  { prop: 'status', label: '状态', width: 150, enum: getDicNumber('userStatus') },
  { prop: 'createdByName', label: '创建人', width: 150 },
  { prop: 'createdTime', label: '创建时间', width: 180 },
  { prop: 'operation', label: '操作', width: 350, fixed: 'right', render: operationRender },
])

function operationRender({ row }: any): VNode {
  return (
    <>
      {row.userCategory === 'ESOP' && (
        <el-button onClick={() => handleEdit(row)} type="primary" link v-permission="Edit">
          编辑
        </el-button>
      )}
      <el-button onClick={() => handleFreezingUser(row)} type="primary" link v-permission="Freeze">
        {row.status == 1 ? '冻结' : '解冻'}
      </el-button>
      <el-button onClick={() => handleShowRoleDialog(row)} type="primary" link v-permission="Auth">
        授权角色
      </el-button>
      <el-button
        onClick={() => handleShowTenantDialog(row)}
        type="primary"
        link
        disabled
        v-permission="BindTenant"
      >
        绑定租户
      </el-button>
      <el-button onClick={() => showApiDialog(row)} type="primary" link v-permission="UserApI">
        授权API
      </el-button>
    </>
  )
}
const proTable = ref<ProTableInstance>()

const handleFreezingUser = async (row: any) => {
  const msg =
    row.status == 1
      ? '是否冻结当前用户？冻结后账号不能登录系统。'
      : '是否解冻当前用户？解冻后账号可以登录系统。'
  const statusStr = row.status == 1 ? '冻结' : '解冻'
  await ElMessageBox.confirm(msg, '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res =
        row.status == 1
          ? await freezeUserApi({
              id: row.id,
            })
          : await unfreezeUserApi({
              id: row.id,
            })
      if (res.code == 200) {
        SlMessage.success(`${statusStr}成功`)
        proTable.value?.getTableList()
      } else {
        SlMessage.error(res.message || `${statusStr}失败`)
      }
    })
    .catch(() => {
      console.log(`${statusStr}取消`)
    })
}
// 授权角色
const roleDialogVisible = ref(false)

interface User {
  id: number
}

interface RoleFormModel {
  userIds: number[]
  roleIds: number[]
}
const roleFormModel = reactive<RoleFormModel>({
  userIds: [], // 用户 IDs
  roleIds: [], // 角色 IDs
})

const roleFormOptions = reactive([
  {
    groupName: '',
    groupItems: [
      {
        label: '所属角色',
        type: 'select',
        key: 'roleIds',
        options: localRoleList,
        span: 24,
        props: {
          select: {
            multiple: true,
            collapseTags: true,
            collapseTagsTooltip: true,
          },
        },
        rules: [{ required: true, message: '请选择所属角色', trigger: ['blur', 'change'] }],
      },
    ],
  },
])
const handleShowRoleDialog = async (row: User) => {
  const res = await userGetDetailApi({
    id: row.id,
  })
  if (res.code == 200) {
    roleDialogVisible.value = true
    roleFormModel.userIds = [row.id]
    roleFormModel.roleIds = res.entity.roles.map((i: any) => i.id)
  } else {
    SlMessage.error(res.message || '获取用户信息失败')
  }
}

const roleFormRef = ref()
const handleRoleConfirm = async () => {
  if (!(await roleFormRef.value?.validate(() => true))) return
  const res = await userAuthorizationRoleApi(roleFormModel)
  if (res.code == 200) {
    proTable.value?.getTableList()
    SlMessage({
      message: '授权成功',
      type: 'success',
    })
    handleRoleClose()
  }
}

const handleRoleClose = () => {
  roleDialogVisible.value = false
  roleFormModel.userIds = []
  roleFormModel.roleIds = []
}
// 绑定租户
const tenantDialogVisible = ref(false)

interface Tenant {
  tenantId: number
}

interface TenantFormModel {
  userId: number
  accounts: Tenant[]
}
const tenantFormModel = reactive<TenantFormModel>({
  userId: 0, // 用户 IDs
  accounts: [], // 角色 IDs
})

const tenantFormOptions = reactive([
  {
    groupName: '',
    groupItems: [
      {
        label: '所属租户',
        type: 'select',
        key: 'accounts',
        options: localTenantList,
        span: 24,
        props: {
          select: {
            multiple: true,
            collapseTags: true,
            collapseTagsTooltip: true,
          },
        },
        rules: [{ required: true, message: '请选择所属租户', trigger: ['blur', 'change'] }],
      },
    ],
  },
])

const emit = defineEmits<{
  (e: 'showApiDialogFunc', row: { id: string }): void
  (e: 'editUser', userId: number): void
}>()

//展示API权限配置
const showApiDialog = (row: User) => {
  console.log(row)
  emit('showApiDialogFunc', {
    id: String(row.id),
  })
}
const handleShowTenantDialog = async (row: User) => {
  const res = await userGetDetailApi({
    id: row.id,
  })
  if (res.code == 200) {
    tenantDialogVisible.value = true
    tenantFormModel.userId = row.id
    tenantFormModel.accounts = res.entity.tenants.map((i: any) => i.id)
    console.log(tenantFormModel.accounts)
  } else {
    SlMessage.error(res.message || '获取用户信息失败')
  }
}

const tenantFormRef = ref()
const handleTenantConfirm = async () => {
  if (!(await tenantFormRef.value?.validate(() => true))) return
  const res = await userBindTenantApi({
    userId: tenantFormModel.userId,
    accounts: tenantFormModel.accounts.map((item) => {
      return { tenantId: item }
    }),
  })
  if (res.code == 200) {
    proTable.value?.getTableList()
    SlMessage({
      message: '绑定成功',
      type: 'success',
    })
    handleTenantClose()
  }
}

const handleTenantClose = () => {
  tenantDialogVisible.value = false
  tenantFormModel.userId = 0
  tenantFormModel.accounts = []
}

// 处理编辑用户
const handleEdit = (row: any) => {
  emit('editUser', row.id)
}
</script>
