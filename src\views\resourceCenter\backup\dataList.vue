<template>
  <div
    class="table-main"
    v-loading.fullscreen.lock="tableLoading"
    element-loading-text="操作中，请稍候..."
  >
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="getResourceList"
      :init-param="queryParams"
      :current-change="currentChange"
      @selection-change="handleSelectionChange"
      hidden-table-header
      row-key="id"
    >
    </SlProTable>

    <!-- 绑定/解绑资源弹窗 -->
    <ResourceBindDialog
      v-model:visible="bindDialogVisible"
      :backup-type="currentBackup.backupType?.toLowerCase()"
      :resource-pool-id="dialogType === 'BIND' ? currentBackup.resourcePoolId : ''"
      :dialog-type="dialogType"
      :device-id="dialogType === 'UNBIND' ? currentBackup.deviceId : ''"
      :backup-id="currentBackup.deviceId"
      @selectDevice="handleDeviceSelect"
      @refresh="refreshTable"
    />
  </div>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getResourceList, cycleBinCreate, backupOperate } from '@/api/modules/resourecenter'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import ResourceBindDialog from '@/views/resourceCenter/backup/components/ResourceBindDialog.vue'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const { queryParams, hideOperations } = defineProps<{
  queryParams: Record<string, any>
  hideOperations?: boolean
}>()

const emit = defineEmits(['currentChange'])
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'selection', width: 55 },
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'deviceName',
    label: '云灾备名称',
    minWidth: 150,
    fixed: 'left',
    render: ({ row }) => (
      <el-button onClick={() => handleViewDetail(row)} type="primary" link>
        {row.deviceName}
      </el-button>
    ),
  },
  {
    prop: 'backupType',
    label: '备份类型',
    minWidth: 120,
    enum: [
      { label: '云主机', value: 'ECS' },
      {
        label: '云硬盘',
        value: 'EVS',
      },
    ],
  },
  {
    prop: 'frequency',
    label: '备份频率',
    minWidth: 120,
    enum: [
      { label: '每天', value: 'days' },
      { label: '每周', value: 'weeks' },
    ],
  },
  { prop: 'daysOfWeek', label: '星期', minWidth: 100 },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'businessSysName', label: '业务系统', minWidth: 150, filter: true },
  { prop: 'domainName', label: '所属云', minWidth: 150, filter: true },
  { prop: 'resourcePoolName', label: '资源池', minWidth: 150, filter: true },
  { prop: 'orderCode', label: '工单编号', minWidth: 150 },
  { prop: 'effectiveTime', label: '开通时间', minWidth: 150 },
  { prop: 'recoveryStatusCn', label: '回收状态', width: 100 },
  { prop: 'applyUserName', label: '申请人', minWidth: 100 },
  ...(!hideOperations
    ? [
        {
          prop: 'operation',
          label: '操作',
          width: 180,
          fixed: 'right',
          render: operationRender,
        },
      ]
    : []),
])

// 表格操作渲染
function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleBindResource(row)} type="primary" link>
        绑定资源
      </el-button>
      <el-button onClick={() => handleUnbindResource(row)} type="danger" link>
        解绑资源
      </el-button>
    </>
  )
}

const proTable = ref<ProTableInstance>()
const tableLoading = ref(false)

// 多选数据
const multipleSelection = ref<any[]>([])
const bindDialogVisible = ref(false)
const currentBackup = ref<any>({})
const dialogType = ref<'BIND' | 'UNBIND'>('BIND')

// 处理选择变更
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 查看详情
const handleViewDetail = (row: any) => {
  router.push(`/backupDetail?id=${row.id}`)
}

// 绑定资源
const handleBindResource = (row: any) => {
  currentBackup.value = row
  dialogType.value = 'BIND'
  bindDialogVisible.value = true
}

// 解绑资源
const handleUnbindResource = (row: any) => {
  currentBackup.value = row
  dialogType.value = 'UNBIND'
  bindDialogVisible.value = true
}

const bindingLoading = ref('')

// 处理设备选择
const handleDeviceSelect = async (devices: any[]) => {
  if (bindingLoading.value) return
  if (!currentBackup.value.id) return
  if (!devices || devices.length === 0) return

  try {
    bindingLoading.value = currentBackup.value.id
    tableLoading.value = true

    await backupOperate({
      type: dialogType.value,
      detailId: devices.map((item: any) => item.id),
      backupDetailId: currentBackup.value.id,
    })

    ElMessage.success(dialogType.value === 'BIND' ? '绑定设备成功' : '解绑设备成功')
    proTable.value?.getTableList()
    bindDialogVisible.value = false
    currentBackup.value = {}
  } catch (error) {
    console.error(error)
  } finally {
    bindingLoading.value = ''
    tableLoading.value = false
  }
}

// 刷新表格
const refreshTable = () => {
  if (proTable.value) {
    proTable.value.getTableList()
  }
}

// 批量回收
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []
  if (selectedList.length === 0) {
    ElMessage.warning('请至少选择一个云灾备')
    return
  }
  try {
    await ElMessageBox.confirm('确定要批量回收选中的云灾备吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    tableLoading.value = true
    // 这里应该调用批量回收云灾备的接口
    const currentRecycleIdsList = selectedList.map((i) => ({ goodsId: i.id.trim() }))

    const res = await cycleBinCreate({
      goodsType: 'backup',
      goodsItems: currentRecycleIdsList,
    })
    if (res.code != 200) {
      return ElMessage.error(res.message || '接口请求失败')
    }
    ElMessage.success('已加入回收站')
    refreshTable()
    proTable.value?.clearSelection()
    eventBus.emit('cycleBins:updateCount')
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  } finally {
    tableLoading.value = false
  }
}

defineExpose({
  handleBatchRecycle,
  refreshTable,
})
</script>
<style lang="scss" scoped>
.table-main {
  margin: 8px;
}
</style>
