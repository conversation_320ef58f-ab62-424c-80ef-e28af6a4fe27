<template>
  <div :style="props.style">
    <sl-form
      class="goods-info-form"
      size="small"
      ref="slFormRef"
      :options="goodsInfoOptions"
      :model-value="goods"
    >
      <!-- 操作系统插槽 -->
      <template #imageSlot="{ form, item }">
        <template v-if="props.goods.openType === 'online'">
          <el-cascader
            v-model="form[item.key]"
            :options="item.options"
            clearable
            :props="{
              value: 'value',
              label: 'label',
            }"
          >
          </el-cascader>
        </template>
        <template v-else>
          <el-input v-model="form[item.key][0]" />
          <span style="margin: 0 6px; max-width: 2px">/</span>
          <el-input v-model="form[item.key][1]" />
        </template>
      </template>
      <!-- 实例规格插槽 -->
      <template #flavorSlot="{ form, item }">
        <template v-if="props.goods.openType === 'online'">
          <el-select filterable v-model="form[item.key]" clearable>
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </template>
        <template v-else>
          <el-input-number
            :controls="false"
            v-model="form[item.key][0]"
            style="margin-right: 4px"
          />C
          <el-input-number :controls="false" v-model="form[item.key][1]" style="margin: 0 4px" />GB
          <span style="margin: 0 6px; max-width: 2px">/</span>
          <el-input v-model="form[item.key][2]" style="margin-right: 4px" />
        </template>
      </template>
      <!-- 可用区插槽 -->
      <template #azSlot="{ form, item }">
        <el-select filterable v-model="form[item.key]" clearable>
          <el-option
            v-for="option in item.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </template>
      <!-- 资源池插槽 -->
      <template #resourcePoolSlot="{ form, item }">
        <template v-if="goods.openType === 'online'">
          <el-select
            @change="handleResourcePoolChange"
            filterable
            v-model="form[item.key]"
            clearable
          >
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </template>
        <template v-else>
          <el-input v-model="form[item.key]" />
        </template>
        <el-button
          style="max-width: 50px; margin-left: 10px"
          type="primary"
          link
          v-if="!orderId"
          @click="toggleOpenType"
        >
          {{ goods.openType === 'online' ? '线下开通' : '线上开通' }}
        </el-button>
      </template>
      <!-- 删除按钮 -->
      <template #globalFormSlot>
        <div @click="handleGoodsDelete" class="goods-del-btn">
          <el-icon><CircleCloseFilled /></el-icon>
        </div>
      </template>
      <!-- 系统盘插槽 -->
      <template #sysDiskSlot="{ form, item }">
        <div style="display: flex; flex-grow: 1">
          <template v-if="goods.openType === 'online'">
            <el-select filterable style="flex: 1" clearable v-model="form[item.key][0]">
              <el-option
                :key="option.value"
                v-for="option in item.options"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
          <template v-else>
            <el-input v-model="form[item.key][0]" />
          </template>
          <el-input-number
            v-model="form[item.key][1]"
            v-bind="item.props"
            style="margin: 0 4px; min-width: 90px"
          >
          </el-input-number>
          GB
        </div>
      </template>
      <!-- 数据盘插槽 -->
      <template #evsSlot="{ form, item }">
        <div
          style="
            flex-grow: 0.12;
            display: flex;
            align-items: flex-start;
            height: 100%;
            margin-right: 6px;
          "
        >
          <el-switch v-model="form[item.swithKey]" active-value="1" inactive-value="0" />
        </div>
        <div>
          <el-form-item
            class="evs-item"
            v-for="(evs, evsIndex) in form[item.key]"
            :key="evsIndex"
            :prop="item.key + '.' + evsIndex"
            :rules="evsRules"
          >
            <div class="evs-item-content">
              <template v-if="goods.openType === 'online'">
                <el-select
                  filterable
                  style="flex: 1"
                  clearable
                  :disabled="item.disabled"
                  v-model="form[item.key][evsIndex][0]"
                >
                  <el-option
                    :key="option.value"
                    v-for="option in item.options"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </template>
              <template v-else>
                <el-input :disabled="item.disabled" v-model="form[item.key][evsIndex][0]" />
              </template>
              <el-input-number
                :disabled="item.disabled"
                v-bind="item.props"
                v-model="form[item.key][evsIndex][1]"
                style="margin: 0 4px; min-width: 90px"
              />
              <span>GB</span>
              <div class="evs-icons" v-if="form.isMountEvs == 1">
                <el-icon
                  @click="handleEvsRemove(form[item.key], evsIndex)"
                  v-if="form[item.key].length > 1"
                >
                  <RemoveFilled />
                </el-icon>
                <el-icon
                  @click="handleEvsAdd(form[item.key])"
                  v-if="form[item.key].length == evsIndex + 1"
                >
                  <CirclePlusFilled />
                </el-icon>
              </div>
            </div>
          </el-form-item>
        </div>
      </template>
      <!-- 公网IP插槽 -->
      <template #networkIpSlot="{ form, item }">
        <el-switch
          style="flex-grow: 0.2"
          v-model="form[item.swithKey]"
          active-value="1"
          inactive-value="0"
        />
        <el-input-number v-bind="item.props || {}" v-model="form[item.key]" clearable />
      </template>
    </sl-form>
  </div>
</template>
<script setup lang="ts">
import { reactive, computed, ref, watchEffect } from 'vue'
import type { IGcsModel } from '../model'
import { CirclePlusFilled, RemoveFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import slForm from '@/components/form/SlForm.vue'
import { useSelectLabel } from '@/hooks/useSelectLabel'
import { getAzListDic, getCloudPlatformDic, getResourcePoolsDic } from '@/api/modules/dic'
import { getListFlavorApi } from '@/api/modules/approvalCenter'
import { useRoute } from 'vue-router'
import { useImageTree } from '@/views/resourceCenter/hooks/useImageTree'

const props = defineProps<{
  goods: IGcsModel
  style?: string | Record<string, string | number>
}>()
const route = useRoute()
const orderId = route.query.orderId as string | ''
const formModel = props.goods

// 初始化数据
if (formModel.openType === 'offline' && formModel.gcs === '') {
  formModel.gcs = [0, 0, '']
}

// 非标工单目前仅需支持融合边缘云VMware、华三技术栈云主机、GPU云主机、VPC网络线上开通，华为技术栈云主机云、VPC网络线上开通，其他的产品都是线下开通
watchEffect(() => {
  if (props.goods.domainCode === 'plf_prov_moc_zj_huawei') {
    formModel.openType = 'offline'
    formModel.imageOs = ['', '']
    formModel.gcs = [0, 0, '']
  }
})

const forceOffline = ref(false)
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

// select 配置项
const resourcePoolOptions = ref<any>([])
const cloudTypeOptions = ref<any>([])
const cloudPlatformOptions = ref<any>([])
const azOptions = ref<any>([])
const flavorOptions = ref<any>([])
const imageTreeOptions = ref<any>([])

const getFlavorOptions = async (regionId: string) => {
  const { entity } = await getListFlavorApi({
    type: 'gcs',
    regionId,
  })
  flavorOptions.value = entity.map((e: any) => ({
    label: e.name,
    value: e.name,
  }))
}

const toggleOpenType = () => {
  forceOffline.value = true
  const goods = props.goods
  // 组件cascader 的值 需要设置为空
  goods.imageOs = ['', '']
  goods.resourcePoolId = ''
  goods.azCode = ''
  goods.azId = ''
  goods.azName = ''
  goods.sysDisk = ['', 0]
  if (goods.openType === 'online') {
    goods.gcs = [0, 0, '']
    goods.openType = 'offline'
  } else {
    goods.gcs = ''
    goods.openType = 'online'
  }
}

const emit = defineEmits(['deleteGoods'])
function handleGoodsDelete() {
  emit('deleteGoods')
}
// 数据盘配置
const handleEvsRemove = (item: any, index: number) => {
  item.splice(index, 1)
}

const handleEvsAdd = (item: Array<any>) => {
  item.push(['', 0])
}

const evsDisabled = ref(false)
const netSizeDisabled = ref(false)
const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGcsModel) {
  goods.ref = slFormRef
}
watchEffect(() => {
  if (formModel.isMountEvs === '0') {
    evsDisabled.value = true
    if (slFormRef.value) {
      formModel.evs = [['', 0]]
      slFormRef.value.clearValidate(['evs'])
    }
  } else {
    evsDisabled.value = false
    if (formModel.evs.length) {
      formModel.evs.forEach((evs) => {
        if (evs[1] === 0) evs[1] = 20
      })
    }
  }
})
watchEffect(() => {
  if (formModel.isBindPublicNetworkIp === '0') {
    netSizeDisabled.value = true
    if (slFormRef.value) {
      formModel.eipValue = 0
      slFormRef.value.clearValidate(['eipValue'])
    }
  } else {
    netSizeDisabled.value = false
    formModel.eipValue = formModel.eipValue || 5
  }
})

const validateevsTypeevsSize = (rule: any, value: any, callback: any) => {
  if (evsDisabled.value) callback()
  let error = ''
  if (!value[0] && !value[1]) {
    error = '数据盘类型、大小不能为空'
  } else if (!value[0] && value[1]) {
    error = '数据盘类型不能为空'
  } else if (value[0] && !value[1]) {
    error = '数据盘大小不能为空'
  }
  error ? callback(new Error(error)) : callback()
}

const validateDiskTypeSiskSize = (rule: any, value: any, callback: any) => {
  if (!formModel.sysDisk[0]) {
    callback(new Error('系统盘类型不能为空'))
  } else if (!formModel.sysDisk[1]) {
    callback(new Error('系统盘大小不能为空'))
  } else {
    callback()
  }
}

const validateSysDiskRequired = (rule: any, value: any, callback: any) => {
  if (value && value.length === 2 && value[0] !== '' && value[1] !== '') {
    callback()
  } else {
    callback(new Error(rule.message))
  }
}
const validateSpecRequired = (rule: any, value: any, callback: any) => {
  if (props.goods.openType === 'offline') {
    if (value && value.length === 3 && value[0] && value[1] && value[2] !== '') {
      callback()
    } else {
      callback(new Error(rule.message))
    }
  } else {
    if (value) {
      callback()
    } else {
      callback(new Error(rule.message))
    }
  }
}

async function getAzOptions(regionId: string) {
  const { entity } = await getAzListDic({ regionId: regionId })
  if (entity) {
    azOptions.value = entity.map((e: any) => ({
      value: e.code,
      label: e.name,
    }))
  }
}
const handleResourcePoolChange = () => {
  formModel.azCode = ''
  formModel.azId = ''
  formModel.azName = ''
  azOptions.value = []
  formModel.gcs = ''
}
useSelectLabel(
  () => resourcePoolOptions,
  () => formModel.resourcePoolId,
  (option) => {
    formModel.regionCode = option.regionCode
    formModel.resourcePoolName = option.label
    if (formModel.resourcePoolId) {
      getAzOptions(formModel.resourcePoolId)
      getFlavorOptions(formModel.resourcePoolId)
      imageTreeOptions.value = useImageTree({
        regionId: formModel.resourcePoolId,
        type: 'gcs',
      }).value
    }
  },
)
useSelectLabel(
  () => azOptions,
  () => formModel.azCode,
  (option) => {
    formModel.azName = option.label
    formModel.azId = option.id
  },
)
const getResourcePools = async (domainCode: string = '') => {
  const { entity } = await getResourcePoolsDic({
    domainCode,
  })
  resourcePoolOptions.value = entity.map((e: any) => ({
    value: e.id,
    label: e.name,
    regionCode: e.code,
  }))
}
// 云类型
const getCloudType = async () => {
  cloudTypeOptions.value = [
    {
      label: '移动云',
      value: 'cloudst_group_moc',
    },
  ]
  formModel.catalogueDomainCode = 'cloudst_group_moc'
}
// 云平台
const getCloudPlatform = async (catalogueDomainCode: string = '') => {
  const { entity } = await getCloudPlatformDic({
    parentCode: catalogueDomainCode,
  })
  cloudPlatformOptions.value = entity.map((e: any) => ({
    value: e.code,
    label: e.name,
  }))
}
const initData = async () => {
  useSelectLabel(
    () => cloudTypeOptions,
    () => formModel.catalogueDomainCode,
    (option) => {
      formModel.catalogueDomainName = option.label
      if (formModel.catalogueDomainCode) {
        getCloudPlatform(option.value)
      }
    },
  )
  useSelectLabel(
    () => cloudPlatformOptions,
    () => formModel.domainCode,
    (option) => {
      formModel.domainName = option.label
      if (formModel.domainCode) {
        getResourcePools(option.value)
      }
    },
  )
  getCloudType()
}
initData()

const computedMin = computed(() => {
  if (formModel.isMountEvs === '0') return 0
  if (formModel.openType === 'offline') return 0
  return 20
})

const sysDiskMin = computed(() => {
  if (formModel.openType === 'offline') return 0
  return 40
})

const evsRules = [
  {
    validator: validateevsTypeevsSize,
    trigger: 'change',
  },
]

const hiddenAz = computed(() => {
  return props.goods.openType === 'offline'
})

const goodsInfoOptions = reactive([
  {
    style: 'margin:auto 0 0 0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        options: cloudTypeOptions,
        hidden: hiddenAz,
        props: {
          select: {
            disabled: true,
          },
        },
        span: 8,
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        options: cloudPlatformOptions,
        hidden: hiddenAz,
        onChange: () => {
          if (formModel.openType === 'offline') return
          formModel.resourcePoolId = ''
          formModel.regionCode = ''
          resourcePoolOptions.value = []
        },
        span: 8,
      },
      {
        label: '资源池',
        type: 'slot',
        key: 'resourcePoolId',
        options: resourcePoolOptions,
        span: 8,
        slotName: 'resourcePoolSlot',
        rules: {
          required: true,
          message: '资源池不能为空',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '可用区',
        type: 'slot',
        key: 'azCode',
        hidden: hiddenAz,
        slotName: 'azSlot',
        options: azOptions,
        rules: [{ required: true, message: '可用区不能为空', trigger: ['blur', 'change'] }],
        span: 8,
      },
      {
        label: 'GPU主机名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '主机名称不能为空', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '实例规格',
        type: 'slot',
        slotName: 'flavorSlot',
        key: 'gcs',
        options: flavorOptions,
        span: 8,
        required: true,
        rules: [
          {
            validator: validateSpecRequired,
            message: '实例规格不能为空',
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: '操作系统',
        type: 'slot',
        slotName: 'imageSlot',
        key: 'imageOs',
        options: imageTreeOptions,
        span: 8,
        rules: [
          {
            validator: validateSysDiskRequired,
            message: '操作系统名称和版本不能为空',
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: '系统盘',
        type: 'slot',
        slotName: 'sysDiskSlot',
        key: 'sysDisk',
        options: getDic('sysDisk'),
        props: {
          min: sysDiskMin,
          step: 1,
        },
        span: 8,
        required: true,
        rules: [{ validator: validateDiskTypeSiskSize, trigger: ['blur', 'change'] }],
      },
      {
        label: '是否绑定公网IP',
        type: 'slot',
        slotName: 'networkIpSlot',
        swithKey: 'isBindPublicNetworkIp',
        key: 'eipValue',
        suffix: 'M',
        props: {
          min: 0,
          step: 1,
          disabled: netSizeDisabled,
        },
        rules: [
          {
            validator: validateEmpty,
            disabled: netSizeDisabled,
            message: '带宽大小不能为空',
            trigger: ['blur', 'change'],
          },
        ],
        span: 8,
        required: true,
      },
      {
        label: '开通数量',
        type: 'inputNumber',
        key: 'numbers',
        props: {
          min: 1,
          step: 1,
          max: 99,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '开通数量不能为空', trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '申请时长',
        type: 'inputNumber',
        key: 'time',
        span: 8,
        props: {
          min: 1,
          step: 1,
        },
        rules: [{ required: true, message: '申请时长不能为空', trigger: ['blur', 'change'] }],
        suffix: '天',
      },
      {
        label: '是否挂载数据盘',
        type: 'slot',
        slotName: 'evsSlot',
        disabled: evsDisabled,
        swithKey: 'isMountEvs',
        key: 'evs',
        options: getDic('evs'),
        span: 8,
        required: true,
        props: {
          min: computedMin,
          step: 1,
        },
      },
    ],
  },
])
</script>
<style scoped>
.disaster-recovery-tip {
  margin-left: 8px;
  color: var(--el-color-warning);
  flex-basis: auto;
  position: absolute;
  top: 18px;
}
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0px;
}
</style>
