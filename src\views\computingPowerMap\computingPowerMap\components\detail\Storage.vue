<template>
  <div class="storage-container flx-justify-between" @click="handleClick">
    <div class="storage-chart">
      <div class="echarts-container" ref="chartContainer"></div>
    </div>

    <div class="storage-details">
      <div class="storage-total mb10 pb10">
        <span class="total-label">存储总量</span>
        <span class="total-value valueNumber ml10">
          {{ storageData.total }}{{ storageData.memoryunits }}
        </span>
      </div>
      <div class="details flx-justify-between">
        <div class="detail-item">
          <div class="detail-value valueNumber">
            {{ storageData.used }}{{ storageData.memoryunits }}
          </div>
          <div class="detail-label">存储已使用量</div>
        </div>
        <div class="detail-item">
          <div class="detail-value valueNumber">
            {{ storageData.remaining }}{{ storageData.memoryunits }}
          </div>
          <div class="detail-label">存储剩余量</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import 'echarts-liquidfill'

// 注册 ECharts 组件
echarts.use([CanvasRenderer])

// 定义 props
const props = defineProps<{
  storageData?: {
    total: number
    used: number
    remaining: number
    percentage: number
    memoryunits: string
  }
}>()

// 定义事件
const emit = defineEmits<{
  click: []
}>()

// 存储数据，使用 props 或默认值
const storageData = computed(
  () =>
    props.storageData || {
      total: 0,
      used: 0,
      remaining: 0,
      percentage: 0,
      memoryunits: 'GB',
    },
)

// ECharts 相关
const chartContainer = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

// 获取格式化的标签和数值
const getFormattedValue = () => {
  let labelText = storageData.value.percentage.toFixed(2).replace(/\.?0+$/, '')
  labelText = labelText + '%'
  labelText = labelText + '\n' + '分配率'

  let value = storageData.value.percentage / 100
  return { labelText, value }
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)

  const { labelText, value } = getFormattedValue()
  const option: echarts.EChartsCoreOption = {
    backgroundColor: 'transparent', // 背景透明
    series: [
      {
        name: '存储使用率',
        type: 'liquidFill',
        radius: '90%',
        center: ['50%', '50%'],
        backgroundStyle: {
          color: 'transparent',
        },
        data: [value, value],
        amplitude: 8, // 水波振幅
        label: {
          // 标签设置
          position: ['50%', '45%'],
          formatter: labelText, // 显示文本
          textStyle: {
            fontSize: 15,
            lineHeight: 18,
            color: '#004fb1',
          },
        },
        outline: {
          borderDistance: 3,
          itemStyle: {
            borderWidth: 2,
            borderColor: {
              type: 'linear',
              x: 1,
              y: 0,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#007DFF',
                },
                {
                  offset: 0.6,
                  color: 'rgba(45, 67, 114, 1)',
                },
                {
                  offset: 1,
                  color: 'rgba(45, 67, 114, 1)',
                },
              ],
              globalCoord: false,
            },
          },
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            {
              offset: 1,
              color: 'rgba(31, 222, 225, 1)',
            },
            {
              offset: 0.85,
              color: '#007DFF80',
            },
            {
              offset: 0.35,
              color: '#004a99',
            },
            {
              offset: 0,
              color: 'rgba(31, 222, 225, 1)',
            },
          ]),
        },
      },
    ],
  }

  chartInstance.setOption(option)
}

// 点击事件处理
const handleClick = () => {
  emit('click')
}

// 监听数据变化，更新图表
const updateChart = () => {
  if (!chartInstance) return

  const { labelText, value } = getFormattedValue()
  const option: echarts.EChartsCoreOption = {
    series: [
      {
        data: [value, value],
        label: {
          // 标签设置
          formatter: labelText, // 显示文本
        },
      },
    ],
  }

  chartInstance.setOption(option)
}

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 监听数据变化
watch(
  storageData,
  () => {
    updateChart()
  },
  { deep: true },
)

// 组件卸载前销毁图表实例
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style lang="scss" scoped>
.storage-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.valueNumber {
  font-size: 26px;
  font-weight: bold;
  color: #004fb1;
}

.storage-chart {
  width: 150px;
}

.storage-total {
  border-bottom: 10px solid transparent;
  border-image: url('/images/computingPower/comPowerSplitIconRevolve.png') 10;
}

.storage-details {
  flex: 1;

  .details {
    text-align: center;
  }
}

.echarts-container {
  width: 140px;
  height: 140px;
}

.detail-item {
  .detail-value {
    font-size: 20px;
  }
}
</style>
