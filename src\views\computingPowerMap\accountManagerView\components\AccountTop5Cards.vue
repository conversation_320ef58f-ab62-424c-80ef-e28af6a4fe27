<template>
  <el-row class="top5-cards" :gutter="24" justify="space-between">
    <el-col v-for="item in top5List" :key="item.title" :span="8" class="top5-card">
      <div class="top5-title">{{ item.title }}</div>
      <div class="top5-header">
        <span> 名称</span>
        <span>{{ item.category }}</span>
      </div>
      <div class="top5-list">
        <!-- 当列表为空时显示暂无数据 -->
        <el-empty
          v-if="!item.list || item.list.length === 0"
          description="暂无数据"
          :image-size="60"
          class="empty-container"
        />
        <!-- 当有数据时显示列表 -->
        <template v-else>
          <div
            class="top5-item"
            v-for="(detail, index) in getDisplayList(item.list)"
            :key="detail.deviceName || index"
          >
            <span class="top5-item-name">{{ detail.deviceName || '' }}</span>
            <span class="top5-item-value ml10">
              {{ detail.topPercent || '--' }}
            </span>
          </div>
        </template>
      </div>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getTop5ResourceOfCustomApi } from '@/api/modules/computingPowerMap'

interface Top5Item {
  deviceName: string
  topPercent: number
}
interface Top5Card {
  title: string
  category: string
  list: Top5Item[]
}

const route = useRoute()
const customId = (route.query.customId as string) || ''

const top5List = ref<Top5Card[]>([
  { title: 'vCPU利用率top5', category: 'vCPU利用率(%)', list: [] },
  { title: '内存利用率top5', category: '内存利用率(%)', list: [] },
  { title: 'IO读写速率top5', category: 'IO读写速率(次/秒)', list: [] },
])

// 获取显示列表，不足5条时补充占位符
const getDisplayList = (list: Top5Item[]) => {
  if (!list || list.length === 0) return []

  const displayList = [...list]
  // 如果不足5条，补充占位符
  while (displayList.length < 5) {
    displayList.push({
      deviceName: '',
      topPercent: undefined as any,
    })
  }
  return displayList
}

const fetchTop5 = async (orderType: string, idx: number) => {
  if (!customId) return
  const res = await getTop5ResourceOfCustomApi({ customId, orderType })
  top5List.value[idx].list = res.entity ?? []
}

onMounted(() => {
  fetchTop5('vCPU', 0)
  fetchTop5('MEM', 1)
  fetchTop5('IO', 2)
})
</script>

<style lang="scss" scoped>
.top5-cards {
  height: 100%;
  flex-wrap: nowrap;
  padding-left: 10px;
  padding-right: 10px;
  .top5-card {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    flex: 1;
    padding: 0 !important;
    &:nth-child(2) {
      margin-left: 10px;
      margin-right: 10px;
    }
  }

  .top5-title {
    font-size: 13px;
    font-weight: bold;
    background: linear-gradient(to right, #e7d3ff, #fefeff);
    border-radius: 8px;
    padding-left: 10px;
    line-height: 22px;
  }
  .top5-header {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    background-color: #eee;
    margin: 5px 10px 10px;
    border-radius: 8px;
    padding: 0 10px;
    line-height: 15px;
  }
  .top5-list {
    flex: 1;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 5px 10px 10px;
    gap: 6px;
  }
  .empty-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .top5-item {
    display: flex;
    justify-content: space-between;
    font-size: 14px;

    &::before {
      content: '';
      display: inline-block;
      width: 7px;
      height: 23px;
      background-color: #000;
      border-radius: 7px;
    }

    &:nth-child(1)::before {
      background-color: #cde1fd;
    }
    &:nth-child(2)::before {
      background-color: #ddebfe;
    }
    &:nth-child(3)::before {
      background-color: #ebf3fe;
    }
    &:nth-child(4)::before {
      background-color: #f8f8f8;
    }
    &:nth-child(5)::before {
      background-color: #f8f8f8;
    }
  }
  .top5-item::before {
    content: '';
    display: inline-block;
    width: 7px;
    height: 20px;
    // background-color: #000;
    border-radius: 7px;
  }
  .top5-item-value {
    width: 30%;
    text-align: right;
  }
  .top5-item-name {
    width: 70%;
    text-align: left;
    padding-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    // 当内容为占位符时居中显示
    &:empty::before {
      content: '--';
      text-align: center;
      display: block;
      width: 100%;
    }
  }
}
</style>
