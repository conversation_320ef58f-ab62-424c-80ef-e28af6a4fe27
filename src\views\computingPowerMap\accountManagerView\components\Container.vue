<template>
  <div ref="containerRef" class="account-manager-view">
    <div
      :style="noScale ? 'padding: 16px' : 'width: 1221px; padding: 16px'"
      id="account-manager-scale-content"
    >
      <slot></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
const props = defineProps<{
  noScale?: boolean
}>()
const containerRef = ref<HTMLElement | null>(null)
let resizeObserver: ResizeObserver | null = null
let debounceTimer: number | null = null
let lastWidth = 0 // 记录上次的宽度
const nativeWidth = 1221

// 缩放计算函数
const updateScale = () => {
  if (props.noScale) return
  const wrap = containerRef.value as HTMLElement
  const container = document.getElementById('account-manager-scale-content') as HTMLElement
  if (!wrap || !container) return
  const currentWidth = wrap.clientWidth
  // 如果宽度变化小于阈值（通常滚动条宽度为15-17px），忽略此次变化
  if (Math.abs(currentWidth - lastWidth) < 20 && lastWidth > 0) {
    console.log(`宽度变化过小，忽略：${Math.abs(currentWidth - lastWidth)}px`)
    return
  }
  lastWidth = currentWidth
  const scaleX = currentWidth / nativeWidth
  const transValueX = (1 - 1 / scaleX) * 50
  container.style.transform = `scale(${scaleX}, ${scaleX}) translate(${transValueX}%, ${transValueX}%)`
  console.log(`应用缩放：${scaleX.toFixed(3)}, 宽度：${currentWidth}px`)
}

// 防抖函数
const debouncedUpdateScale = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  debounceTimer = setTimeout(() => {
    console.log('Div 尺寸变化')
    updateScale()
  }, 100) // 150ms 防抖延迟
}

onMounted(() => {
  // 初始化时立即执行一次
  updateScale()

  // 1. 获取目标元素
  const targetDiv = document.querySelector('.account-manager-view') as HTMLElement

  // 2. 创建 ResizeObserver 实例
  resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      const { width } = entry.contentRect
      console.log(`监听到尺寸变化：宽度=${width}px`)
      // 检查是否是有意义的变化
      if (Math.abs(width - lastWidth) >= 20 || lastWidth === 0) {
        debouncedUpdateScale()
      } else {
        console.log(`变化太小，忽略：${Math.abs(width - lastWidth)}px`)
      }
    }
  })

  // 3. 开始监听
  if (targetDiv) {
    resizeObserver.observe(targetDiv)
  }
})

// 窗口大小变化时也使用防抖
window.addEventListener('resize', debouncedUpdateScale)

// 组件卸载时清理
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }

  if (debounceTimer) {
    clearTimeout(debounceTimer)
    debounceTimer = null
  }

  window.removeEventListener('resize', debouncedUpdateScale)
})
</script>
<style scoped lang="scss">
.account-manager-view {
  // 防止滚动条导致的布局问题
  background: url('/images/accountView/bg.jpg') no-repeat center center;
  background-size: cover;
}
#account-manager-scale-content {
  box-sizing: border-box;
  height: calc(100vh - 55px);
}
</style>
