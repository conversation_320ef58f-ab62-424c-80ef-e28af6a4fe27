<template>
  <div class="comPowerLeftDialog">
    <div class="closeIconBox" @click="close">
      <div class="closeIcon">
        <b></b>
      </div>
    </div>
    <div class="comPowerLeftDialogContent">
      <div class="comPowerLeftDialogTop">
        <div class="comPowerLeftDialogTopItem">
          <div class="comPowerLeftDialogTopItemtitle">存储</div>
          <div class="comPowerLeftDialogTopItemTotal">
            <b>存储总量</b>
            <span>{{ latest.storageTotal }}{{ latest.units }}</span>
          </div>
          <div class="comPowerLeftDialogTopItemInfo">
            <b>存储已开通</b>
            <span>{{ latest.storageUsed }}{{ latest.units }}</span>
          </div>
          <div class="comPowerLeftDialogTopItemInfo">
            <b>存储剩余量</b>
            <span>{{ latest.storageAvi }}{{ latest.units }}</span>
          </div>
          <div style="margin-top: 24px">
            <el-progress
              :percentage="percentage(latest.storageAvi, latest.storageTotal)"
              class="progress-warp m-t-6"
              :stroke-width="18"
              :show-text="true"
              :color="customColors[0]"
              :format="getLabel"
              :text-inside="true"
            />
          </div>
        </div>
        <div class="comPowerLeftDialogTopItem">
          <div class="comPowerLeftDialogTopItemtitle">内存</div>
          <div class="comPowerLeftDialogTopItemTotal">
            <b>内存总量</b>
            <span>{{ latest.memoryTotal }}{{ latest.memoryunits }}</span>
          </div>
          <div class="comPowerLeftDialogTopItemInfo">
            <b>内存已开通</b>
            <span>{{ latest.memoryUsed }}{{ latest.memoryunits }}</span>
          </div>
          <div class="comPowerLeftDialogTopItemInfo">
            <b>内存剩余量</b>
            <span>{{ latest.memoryAvi }}{{ latest.memoryunits }}</span>
          </div>
          <div style="margin-top: 24px">
            <el-progress
              :percentage="percentage(latest.memoryUsed, latest.memoryTotal)"
              class="progress-warp m-t-6"
              :stroke-width="18"
              :show-text="true"
              :color="customColors[0]"
              :format="getLabel"
              :text-inside="true"
            />
          </div>
        </div>
        <div class="comPowerLeftDialogTopItem">
          <div class="comPowerLeftDialogTopItemtitle">vCPU</div>
          <div class="comPowerLeftDialogTopItemTotal">
            <b>vCPU总量</b>
            <span> {{ latest.vcpuTotal }}核</span>
          </div>
          <div class="comPowerLeftDialogTopItemInfo">
            <b>vCPU已开通</b>
            <span> {{ latest.vcpuUsed }}核</span>
          </div>
          <div class="comPowerLeftDialogTopItemInfo">
            <b>vCPU剩余量</b>
            <span> {{ latest.vcpuAvi }}核</span>
          </div>
          <div style="margin-top: 24px">
            <el-progress
              :percentage="percentage(latest.vcpuUsed, latest.vcpuTotal)"
              class="progress-warp m-t-6"
              :stroke-width="18"
              :show-text="true"
              :color="customColors[0]"
              :format="getLabel"
              :text-inside="true"
            />
          </div>
        </div>
      </div>
      <div class="comPowerLeftDialogMiddle">
        <div class="comPowerLeftDialogChart">
          <div class="comPowerLeftDialogChartTitle">资源池利用率</div>
          <div class="">
            <div
              ref="dialogLineChartRef1"
              style="width: 100%; height: 175px; margin-top: 5px"
            ></div>
          </div>
        </div>
        <div class="comPowerLeftDialogChart">
          <div class="comPowerLeftDialogChartTitle">资源池分配率</div>
          <div class="">
            <div
              ref="dialogLineChartRef2"
              style="width: 100%; height: 175px; margin-top: 5px"
            ></div>
          </div>
        </div>
      </div>
      <div class="comPowerLeftDialogBottom">
        <div class="comPowerLeftDialogBottomTitle">主要资源概况</div>
        <div class="comPowerLeftDialogBottomList">
          <div
            :key="'comPowerLeftDialogBottomItem' + index"
            class="comPowerLeftDialogBottomItem"
            v-for="(item, index) in leftDialogResInfoList"
          >
            <div>
              <span>{{ item.name }}</span>
              <span>
                <b>{{ item.num }}</b>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'

import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import {
  getComPowerMapStatsResUsageLatest,
  getComPowerMapResourceCount,
  getComPowerMapResourceLineData,
  getComPowerMapCmMap,
} from '@/api/modules/comPowerCenter'
const emit = defineEmits(['close'])

// 定义props接收父组件传递的参数
const props = defineProps({
  baseDeviceHandResourcePoolList: {
    type: Array,
    required: false,
    default: () => [],
  },
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

//总览数据
let latest = ref({
  vcpuTotal: 0,
  vcpuUsed: 0,
  vcpuAvi: 0,
  memoryTotal: 0,
  memoryUsed: 0,
  memoryAvi: 0,
  storageTotal: 0,
  storageUsed: 0,
  storageAvi: 0,
  units: 'GB',
  memoryunits: 'GB',
})
const close = () => {
  emit('close')
}
const leftDialogResInfoList = ref([
  {
    name: '云主机',
    num: '0',
    radio: '1.63%',
    change: '89',
  },
  {
    name: 'GPU云主机',
    num: '0',
    radio: '8.63%',
    change: '9',
  },
  {
    name: '云硬盘',
    num: '0',
    radio: '2.85%',
    change: '91',
  },

  {
    name: '对象存储',
    num: '0',
    radio: '2.85%',
    change: '91',
  },
  {
    name: '负载均衡',
    num: '0',
    radio: '2.85%',
    change: '91',
  },
  {
    name: '弹性公网',
    num: '0',
    radio: '2.85%',
    change: '91',
  },
  // {
  //   name: 'NAT网关',
  //   num: '3287',
  //   radio: '2.85%',
  //   change: '91',
  // },
  // {
  //   name: 'VPC',
  //   num: '3287',
  //   radio: '2.85%',
  //   change: '91',
  // },
  // {
  //   name: '网络',
  //   num: '3287',
  //   radio: '2.85%',
  //   change: '91',
  // },
])

// 判断需要转换到的单位
let determineUnit = (gb: number) => {
  const gbToTb = 1024 // 1 TB = 1024 GB
  const tbToPb = 1024 // 1 PB = 1024 TB
  // const pbToEb = 1024 // 1 EB = 1024 PB

  // if (gb >= gbToTb * tbToPb * pbToEb) {
  //   return 'EB'
  // } else
  if (gb >= gbToTb * tbToPb) {
    return 'PB'
  } else if (gb >= gbToTb) {
    return 'TB'
  } else {
    return 'GB'
  }
}
// 执行转换
let convertGbWithUnit = (val: number, unit: string) => {
  // console.log('执行转换',val,unit)
  let value
  // if (unit === 'EB') {
  //   value = val / (1024 * 1024 * 1024) // GB to EB
  // } else
  if (unit === 'PB') {
    value = val / (1024 * 1024) // GB to PB
  } else if (unit === 'TB') {
    value = val / 1024 // GB to TB
  } else {
    value = val // 保持为GB
  }

  // 保留两位小数
  value = value.toFixed(2)
  return value
}
let getLatestApiHttp = () => {
  let parms: any = {
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
    cityCode: props.requestParams.cityCode,
    areaCode: props.requestParams.areaCode,
  }
  getComPowerMapStatsResUsageLatest(parms).then((res: any) => {
    if (res.code == 200) {
      let { entity } = res
      // 内存字段添加单位
      if (entity.memoryTotal) {
        entity['memoryunits'] = determineUnit(entity.memoryTotal)
        entity.memoryTotal = convertGbWithUnit(entity.memoryTotal, entity['memoryunits'])
        entity.memoryUsed = convertGbWithUnit(entity.memoryUsed, entity['memoryunits'])
        entity.memoryAvi = convertGbWithUnit(entity.memoryAvi, entity['memoryunits'])
      }

      if (entity.storageTotalSsd || entity.storageTotalHhd) {
        // console.log('字节转换',determineUnit(entity.storageTotal))
        if (entity.storageTotalSsd && !entity.storageTotalHhd) {
          entity['units'] = determineUnit(entity.storageTotalSsd)
        } else if (!entity.storageTotalSsd && entity.storageTotalHhd) {
          entity['units'] = determineUnit(entity.storageTotalHhd)
        } else {
          entity['units'] = determineUnit(entity.storageTotalSsd)
        }
        entity.storageTotalSsd = convertGbWithUnit(entity.storageTotalSsd, entity['units'])
        entity.storageUsedSsd = convertGbWithUnit(entity.storageUsedSsd, entity['units'])
        entity.storageAviSsd = convertGbWithUnit(entity.storageAviSsd, entity['units'])
        entity.storageTotalHhd = convertGbWithUnit(entity.storageTotalHhd, entity['units'])
        entity.storageUsedHhd = convertGbWithUnit(entity.storageUsedHhd, entity['units'])
        entity.storageAviHhd = convertGbWithUnit(entity.storageAviHhd, entity['units'])
      } else {
        entity['units'] = determineUnit(entity.storageTotal)
        //  console.log('字节转换unitType',entity['units'])
        entity.storageTotal = convertGbWithUnit(entity.storageTotal, entity['units'])
        entity.storageUsed = convertGbWithUnit(entity.storageUsed, entity['units'])
        entity.storageAvi = convertGbWithUnit(entity.storageAvi, entity['units'])
      }
      latest.value = entity
      leftDialogResInfoList.value[2].num = entity.storageTotal + latest.value.units
      leftDialogResInfoList.value[5].num = entity.eipTotal + '个'
    }
  })
}
let getResourceCountApiHttp = () => {
  let parms: any = {
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
    cityCode: props.requestParams.cityCode,
    areaCode: props.requestParams.areaCode,
  }
  getComPowerMapResourceCount(parms).then((res: any) => {
    if (res.code == 200) {
      let { entity } = res
      let bucketSumUnit: any = ''
      if (entity.bucketSum) {
        bucketSumUnit = determineUnit(entity.bucketSum)
        entity.bucketSum = convertGbWithUnit(entity.bucketSum, bucketSumUnit)
      }
      // 内存字段添加单位
      leftDialogResInfoList.value[0].num = entity.vmCount + '个'
      leftDialogResInfoList.value[1].num = entity.gpuVmCount + '个'
      leftDialogResInfoList.value[3].num = entity.bucketSum + bucketSumUnit
      leftDialogResInfoList.value[4].num = entity.lbCount + '个'
    }
  })
}

// 日期计算
let subtractDaysFromDate = (dateString: string, daysToSubtract: number): string => {
  // 创建一个Date对象，解析输入的日期字符串
  const date = new Date(dateString.replace(/-/g, '/')) // 有些浏览器可能无法正确解析YYYY-MM-DD格式的日期，所以这里用/替换-

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string')
  }

  // 使用setDate方法减去指定的天数
  date.setDate(date.getDate() - daysToSubtract)

  // 格式化日期为YYYY-MM-DD字符串
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始计数，所以需要加1
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

let getTodayFormatted = (): string => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0') // 月份从0开始，需要加1并补零
  const day = String(today.getDate()).padStart(2, '0') // 补零
  return `${year}-${month}-${day}`
}
const resourcePoolTime = ref(getTodayFormatted())

const cpuLineData = ref([])
const memoryLineData = ref([])
const xAxis = ref([])
let getResourceLineDataApiHttp = () => {
  let parms: any = {
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
    cityCode: props.requestParams.cityCode,
    areaCode: props.requestParams.areaCode,
    startTime: subtractDaysFromDate(resourcePoolTime.value, 15),
    endTime: resourcePoolTime.value,
  }
  getComPowerMapResourceLineData(parms).then((res: any) => {
    if (res.code == 200) {
      let { entity } = res
      xAxis.value = entity.cpuData.days
      ;(cpuLineData.value = entity.cpuData.dataUtil[0].performanceData.map((value: any) => {
        if (Number(value) === 0) return 0
        const percent = Number(value)
        return parseFloat(percent.toFixed(2))
      })),
        (memoryLineData.value = entity.memData.dataUtil[0].performanceData.map((value: any) => {
          if (Number(value) === 0) return 0
          const percent = Number(value)
          return parseFloat(percent.toFixed(2))
        }))
      setOption()
    }
  })
}

const cpuLineData2 = ref([])
const memoryLineData2 = ref([])
const diskLineData2 = ref([])
const xAxis2 = ref([])
let getResourceLine2DataApiHttp = () => {
  let parms: any = {
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
    cityCode: props.requestParams.cityCode,
    areaCode: props.requestParams.areaCode,
    startTime: subtractDaysFromDate(resourcePoolTime.value, 15),
    endTime: resourcePoolTime.value,
  }
  getComPowerMapCmMap(parms).then((res: any) => {
    if (res.code == 200) {
      let { entity } = res

      cpuLineData2.value = entity.cpuData.dataUtil[0].cmData.map((value: any) => {
        if (Number(value) === 0) return 0
        const percent = Number(value) * 100
        // const percent = Number(value)
        return parseFloat(percent.toFixed(2))
      })
      memoryLineData2.value = entity.memData.dataUtil[0].cmData.map((value: any) => {
        if (Number(value) === 0) return 0
        const percent = Number(value) * 100
        // const percent = Number(value)
        return parseFloat(percent.toFixed(2))
      })
      diskLineData2.value = entity.diskData.dataUtil[0].cmData.map((value: any) => {
        if (Number(value) === 0) return 0
        const percent = Number(value) * 100
        // const percent = Number(value)
        return parseFloat(percent.toFixed(2))
      })
      xAxis2.value = entity.cpuData.days
      setOption2()
    }
  })
}
let dialogLineChart1: ECharts | null = null
let dialogLineChart2: ECharts | null = null
const dialogLineChartRef1 = ref<HTMLDivElement | null>(null)
const dialogLineChartRef2 = ref<HTMLDivElement | null>(null)

function setOption() {
  // 给最小值和最大值留一些边距
  let option1 = {
    xAxis: {
      type: 'category',
      splitLine: {
        show: false,
      },
      boundaryGap: false,
      axisLabel: {
        show: true,
        interval: 0, // 强制显示所有标签
        formatter: function (value: any) {
          // 自定义格式，例如添加前缀或后缀
          return value.slice(5, 10)
        },
      },
      data: xAxis.value,
    },
    tooltip: {
      trigger: 'axis',
      valueFormatter: function (v: any) {
        return v + '%'
      },
    },
    legend: {},
    yAxis: {
      type: 'value',

      axisLabel: {
        formatter: function (value: any) {
          return value + '%' // 在这里添加单位
        },
      },
      axisTick: {
        inside: true,
      },
      splitLine: {
        show: true,
      },
      // axisLabel: {
      //   inside: true,
      //   formatter: '{value}\n'
      // },
      z: 10,
    },
    grid: {
      top: 30,
      left: 40,
      right: 30,
      bottom: 25,
      // height: 160
    },
    series: [
      {
        name: 'cpu利用率',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        sampling: 'average',
        itemStyle: {
          color: '#00ff77',
        },
        stack: 'a',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0,255,119,0.9)',
            },
            {
              offset: 1,
              color: 'rgba(187,211,247,0.9)',
            },
          ]),
        },
        emphasis: {
          disabled: true,
        },
        data: cpuLineData.value,
      },
      {
        name: '内存利用率',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        sampling: 'average',
        itemStyle: {
          color: '#00e4ff',
        },
        stack: 'a',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0,228,255,0.9)',
            },
            {
              offset: 1,
              color: 'rgba(187,211,247,0.9)',
            },
          ]),
        },
        emphasis: {
          disabled: true,
        },
        data: memoryLineData.value,
      },
    ],
  }
  if (!dialogLineChart1) return
  dialogLineChart1.setOption(option1)
}

function setOption2() {
  // 给最小值和最大值留一些边距
  let option1 = {
    xAxis: {
      type: 'category',
      boundaryGap: false,
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        interval: 0, // 强制显示所有标签
        // rotate: resourcePool.value == 1 ? 0 : 30, // 30度角倾斜显示
        formatter: function (value: any) {
          // 自定义格式，例如添加前缀或后缀
          return value.slice(5, 10)
        },
      },
      data: xAxis2.value,
    },
    tooltip: {
      trigger: 'axis',
      valueFormatter: function (v: any) {
        return v + '%'
      },
    },
    legend: {},
    yAxis: {
      type: 'value',

      axisLabel: {
        formatter: function (value: any) {
          return value + '%' // 在这里添加单位
        },
      },
      axisTick: {
        inside: true,
      },
      splitLine: {
        show: true,
      },
      // axisLabel: {
      //   inside: true,
      //   formatter: '{value}\n'
      // },
      z: 10,
    },
    grid: {
      top: 30,
      left: 40,
      right: 30,
      bottom: 25,
      // height: 160
    },
    series: [
      {
        name: 'cpu分配率',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        sampling: 'average1',
        itemStyle: {
          color: '#00ff77',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0,255,119,0.9)',
            },
            {
              offset: 1,
              color: 'rgba(187,211,247,0.9)',
            },
          ]),
        },
        emphasis: {
          disabled: true,
        },
        data: cpuLineData2.value,
      },
      {
        name: '内存分配率',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        sampling: 'average2',
        itemStyle: {
          color: '#00e4ff',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0,228,255,0.9)',
            },
            {
              offset: 1,
              color: 'rgba(187,211,247,0.9)',
            },
          ]),
        },
        emphasis: {
          disabled: true,
        },
        data: memoryLineData2.value,
      },
      {
        name: '存储分配率',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        sampling: 'average3',
        itemStyle: {
          color: '#ff5747',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(255,87,71,0.9)',
            },
            {
              offset: 1,
              color: 'rgba(187,211,247,0.9)',
            },
          ]),
        },
        emphasis: {
          disabled: true,
        },
        data: diskLineData2.value,
      },
    ],
  }
  if (!dialogLineChart2) return
  dialogLineChart2.setOption(option1)
}
let percentage = (Used: number, Total: number) => {
  let num = (Used / Total) * 100
  Math.round(num)
  return num || 0
}
let getLabel = (percent: number) => {
  let str = percent.toFixed(2).replace(/\.?0+$/, '')
  return '分配率:' + str + '%' || '0%'
}
const customColors = ref(['linear-gradient(to right, rgba(24, 82, 188, 1), rgba(61, 207, 255, 1))'])
onMounted(() => {
  getLatestApiHttp()
  getResourceCountApiHttp()
  getResourceLineDataApiHttp()
  getResourceLine2DataApiHttp()

  dialogLineChart1 = echarts.init(dialogLineChartRef1.value!, null, {})
  dialogLineChart2 = echarts.init(dialogLineChartRef2.value!, null, {})
})
</script>
<style scoped lang="scss">
.comPowerLeftDialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 790px;
  height: 100%;
  z-index: 11;
  //background-color: rgba(0, 0, 0, 0.5);
  background: rgba(255, 255, 255, 0.47);
  padding: 10px;
  border-radius: 5px;
  box-shadow: 4px 0px 5px 0px rgba(0, 0, 0, 0.25);
  .closeIconBox {
    position: relative;
    cursor: pointer;
    .closeIcon {
      position: absolute;
      right: -45px;
      top: 4px;
      width: 35px;
      height: 67px;
      background: url('/images/computingPower/comPowerLeftDialogCloseIcon.png') no-repeat 100% 100%;
      background-size: 100% 100%;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      b {
        display: inline-block;
        width: 0;
        height: 0;
        border-top: 10px solid transparent; /* 底边宽度的一半 */
        border-bottom: 10px solid transparent; /* 底边宽度的一半 */
        border-right: 11px solid #3d6eef; /* 两腰长度 */
        margin-left: 5px;
      }
    }
  }
  .comPowerLeftDialogContent {
    width: 100%;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 5px;
    background: linear-gradient(90deg, rgba(40, 68, 186, 0.9), rgba(224, 236, 255, 0.9));
    .comPowerLeftDialogTop {
      display: flex;
      margin-bottom: 18px;
      div.comPowerLeftDialogTopItem {
        width: 32.5%;
        margin: 0 1% 0 0;
        color: #ffffff;
        //background: #0f87a3;
        border-radius: 5px;
        text-align: center;
        padding: 18px 15px 20px;
        .comPowerLeftDialogTopItemtitle {
          font-family: 'ziHuiJingDianYaHei';
          font-weight: 400;
          font-size: 24px;
          color: #e3e9ff;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.17);
          margin-bottom: 20px;
        }
        .comPowerLeftDialogTopItemTotal {
          background: linear-gradient(90deg, #1059c6, #0f69d1);
          border-radius: 19px;
          padding: 9px 12px;
          font-size: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          b {
            display: inline-block;
            font-weight: normal;
            line-height: 1;
            font-size: 16px;
          }
          span {
            display: inline-block;
            font-size: 22px;
            line-height: 1;
          }
        }
        .comPowerLeftDialogTopItemInfo {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 12px;
          margin-bottom: 16px;
          b {
            display: inline-block;
            font-weight: normal;
            line-height: 1;
            font-size: 15px;
          }
          span {
            display: inline-block;
            font-size: 18px;
            line-height: 1;
          }
        }
      }
      div.comPowerLeftDialogTopItem:nth-child(1) {
        background: url('/images/computingPower/comPowerLeftDialogTopBg1.png') no-repeat 100% 100%;
        background-size: 100% 100%;
      }
      div.comPowerLeftDialogTopItem:nth-child(2) {
        background: url('/images/computingPower/comPowerLeftDialogTopBg2.png') no-repeat 0 0;
        background-size: 100% 100%;
        .comPowerLeftDialogTopItemTotal {
          background: linear-gradient(90deg, #1584c6, #23a2ce);
        }
      }
      div.comPowerLeftDialogTopItem:nth-child(3) {
        background: url('/images/computingPower/comPowerLeftDialogTopBg3.png') no-repeat 0 0;
        background-size: 100% 100%;
        margin-right: 0;
        .comPowerLeftDialogTopItemtitle {
          font-family: 'Microsoft YaHei';
          margin-bottom: 15px;
        }
        .comPowerLeftDialogTopItemTotal {
          background: linear-gradient(90deg, #11adbc, #0ca5c0);
        }
      }
    }
    .comPowerLeftDialogMiddle {
      .comPowerLeftDialogChart {
        width: 100%;
        height: 225px;
        margin-bottom: 18px;
        //background: #0f87a3;
        border-radius: 10px;
        padding: 14px 5px 14px 18px;
        box-sizing: border-box;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.7), rgba(125, 171, 244, 0.7));
        .comPowerLeftDialogChartTitle {
          font-family: 'ziHuiJingDianYaHei';
          font-weight: 400;
          font-size: 18px;
          color: #0053bf;
        }
      }
    }
    .comPowerLeftDialogBottom {
      width: 100%;
      height: 308px;
      padding: 14px 18px;
      box-sizing: border-box;
      border-radius: 10px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.7), rgba(125, 171, 244, 0.7));
      .comPowerLeftDialogBottomTitle {
        font-family: 'ziHuiJingDianYaHei';
        font-weight: 400;
        font-size: 18px;
        color: #0053bf;
      }
      .comPowerLeftDialogBottomList {
        width: 100%;
        box-sizing: border-box;
        margin-top: 13px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        //gap:16px;
        .comPowerLeftDialogBottomItem {
          width: 40%;
          height: 65px;
          box-sizing: border-box;
          display: inline-block;
          background: rgba(255, 255, 255, 0.49);
          border-radius: 5px;
          //margin-right: 1%;
          padding: 15px 15px;
          //margin-bottom: 10px;
          margin: 10px 4%;
          div {
            display: flex;
            justify-content: space-around;
            align-items: center;
            line-height: 1;
            span {
              display: inline-block;
              font-size: 15px;
              color: #0053bf;
            }
            b {
              display: inline-block;
              font-family: Microsoft YaHei;
              font-weight: normal;
              font-size: 26px;
              color: #2f2f30;
              vertical-align: middle;
            }
            i {
              display: inline-block;
              font-style: normal;
              background: #d1dcf7;
              border-radius: 8px;
              padding: 5px 8px;
              font-size: 14px;
              color: #0053bf;
              vertical-align: middle;
            }
          }
          div.increaseInfo {
            margin-top: 14px;
            font-size: 14px;
            color: #2f2f30;
            display: inline-block;
            span {
              font-size: 14px;
              color: #118ea0;
            }
          }
        }
        &.div:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
