<template>
  <div>
    <div>
      <sl-page-header title="VPN"></sl-page-header>
    </div>
    <sl-form
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formData"
      :label-width="120"
      ref="formRef"
    >
      <template #region-slot="{ form, item }">
        <region-select :form="form" :item="item" ref="regionRef" />
      </template>
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
          <el-button type="primary" plain class="cart-btn" @click="handleAddToCart">
            加入清单
          </el-button>
          <el-button type="primary" class="order-btn" @click="handleDirectOrder" v-if="vifOpened">
            直接开通
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, watch, markRaw, computed } from 'vue'
import CustomRadio from './components/CustomRadio.vue'
import RegionSelect from './components/RegionSelect.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import { useTenant } from './hooks/useTenant'
import { corporateOrderApi, corporateOrderTemSaveApi } from '@/api/modules/resourecenter'

import useGetTheParameters from './hooks/useGetTheParameters'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import SlForm from '@/components/form/SlForm.vue'
import Bandwidth from './components/Bandwidth.vue'
import eventBus from '@/utils/eventBus'
import { useAuthStore } from '@/stores/modules/auth'
import { validateGoodsName } from '../resourceCenter/utils'
import { getCorporateVpcTreeApi } from '@/api/modules/approvalCenter'
import { productSpecSupportApi } from '@/api/modules/resourecenter'
import { showTips } from '@/utils'

const authStore = useAuthStore()
const vifOpened = computed(() =>
  authStore.authButtonListGet?.viewOfPublicTenants?.includes('Opened'),
)

const formData = ref<any>({
  isBindEip: false,
  tenant: null,
  paymentType: 'month',

  region: 'placeholder',
  domain: null,
  resourcePool: null,
  catalogueDomainCode: 'cloudst_group_moc',
  catalogueDomainName: '移动云',
  az: null,
  vpc: null,
  subnet: null,
  instanceName: '',
  maxConnection: 1,
  bandwidth: 1,
})

const vpcOptions = ref<FormDataType[]>([])
const getVpcOptions = async () => {
  const { entity } = await getCorporateVpcTreeApi({
    tenantId: formData.value.tenant?.id,
    regionCode: formData.value.resourcePool?.code,
    azCode: formData.value.az?.code,
  })
  vpcOptions.value = entity.map((item: any) => {
    return {
      label: item.vpcName,
      value: item,
    }
  })
}
const subnetOptions = ref<FormDataType[]>([])
const getSubnetOptions = async () => {
  subnetOptions.value = []
  console.log(formData.value, ' vpc')

  if (formData.value.vpc)
    subnetOptions.value = formData.value.vpc.vpcSubnetOrderList.map((item: any) => ({
      label: item.subnetName,
      value: item,
    }))
}

const regionRef = ref<InstanceType<typeof RegionSelect>>()

/*
// 调用接口校验资源池 和可用区是否可用
产品[%s]在资源池[%s]中不支持
*/
const checkResourcePoolAndAz = async (flag: boolean = true) => {
  let parms: any = {
    productType: 'vpn',
  }
  let message = '产品在该资源池中不支持'

  if (!formData.value.resourcePool?.code) return

  parms.regionCode = formData.value.resourcePool.code
  if (formData.value.az && flag) {
    parms.azId = formData.value.az.id
    message = '产品在该可用区中不支持'
  }
  const { entity } = await productSpecSupportApi(parms)
  if (entity.length === 0) {
    showTips(message)
    formData.value.resourcePool = null
    formData.value.az = null
    vpcOptions.value = []
    subnetOptions.value = []
    regionRef.value?.clearAzOptions()
  }
}

watch(
  () => formData.value.resourcePool?.code,
  () => {
    // 只有当code真正改变时才执行
    checkResourcePoolAndAz(false)
  },
)

watch(
  () => formData.value.az?.code,
  (newCode, oldCode) => {
    // 只有当code真正改变时才执行
    if (newCode !== oldCode) {
      // 重置网络
      formData.value.vpc = null
      formData.value.subnet = null
      vpcOptions.value = []
      subnetOptions.value = []
      if (formData.value.az && formData.value.az.id) checkResourcePoolAndAz()

      if (formData.value.az && formData.value.az.id && formData.value.tenant?.id) {
        getVpcOptions()
      }
    }
  },
)

watch(
  () => formData.value.tenant?.id,
  (id) => {
    // 重置网络
    formData.value.vpc = null
    formData.value.subnet = null
    vpcOptions.value = []
    subnetOptions.value = []
    if (formData.value.az && formData.value.az.id && id) getVpcOptions()
  },
)

const { tenantList: tenantListOptions } = useTenant()
const router = useRouter()

const bandwidthOptions = ref<any[]>([1, 2, 3, 5, 10, 50, 100, 200])
const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '区域',
        type: 'slot',
        slotName: 'region-slot',
        key: 'region',
        span: 24,
        // component: markRaw(RegionSelect),
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formData.value.domain) {
                callback(new Error('请选择云平台'))
              }
              if (!formData.value.resourcePool) {
                callback(new Error('请选择资源池'))
              }
              if (!formData.value.az) {
                callback(new Error('请选择可用区'))
              }
              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: '名称',
        type: 'input',
        key: 'instanceName',
        required: true,
        rules: [
          { required: true, message: '请输入名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
        props: {
          maxlength: 128,
          showWordLimit: true,
        },
        span: 13,
      },
      {
        label: '租户',
        type: 'select',
        key: 'tenant',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请选择租户', trigger: ['blur', 'change'] }],
        options: tenantListOptions,
        props: {
          select: {
            valueKey: 'id',
          },
        },
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '付费类型',
        type: 'component',
        key: 'paymentType',
        span: 24,
        required: true,
        options: [
          {
            label: '按日付费',
            value: 'day',
          },
          {
            label: '按月付费',
            value: 'month',
          },
          {
            label: '按年付费',
            value: 'year',
          },
        ],
        component: markRaw(CustomRadio),
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '最大客户数',
        type: 'inputNumber',
        key: 'maxConnection',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请输入最大客户数量', trigger: ['blur', 'change'] }],
        props: {
          min: 1,
          step: 1,
        },
      },
      {
        label: 'VPC',
        type: 'select',
        key: 'vpc',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请选择VPC', trigger: ['blur', 'change'] }],
        options: vpcOptions,
        onChange: () => {
          getSubnetOptions()
        },
        props: {
          select: {
            valueKey: 'id',
          },
        },
      },
      {
        label: '',
        type: 'text',
        render: () => (
          <div
            class="tip-con"
            style="margin-left: 4px; font-size: 12px; color: var(--el-color-primary);  line-height: 25px; font-weight: normal;background: rgba(72, 127, 239, 0.1); text-align: center; border-radius: 5px;"
          >
            <span class="tip">提示：一个VPC下只能有一个VPN</span>
          </div>
        ),
        span: 8,
        props: {
          labelWidth: '0',
          text: {
            type: 'warning',
          },
        },
      },
      {
        label: '子网',
        type: 'select',
        key: 'subnet',
        span: 13,
        required: true,
        rules: [{ required: true, message: '请选择子网', trigger: ['blur', 'change'] }],
        options: subnetOptions,
        props: {
          select: {
            valueKey: 'id',
          },
        },
      },
      {
        label: '带宽值',
        type: 'component',
        key: 'bandwidth',
        span: 24,
        component: markRaw(Bandwidth),
        options: bandwidthOptions,
        rules: [{ required: true, message: '请选择带宽值', trigger: ['blur', 'change'] }],
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={16} style="margin-bottom:20px">
              配置概要
            </SlBlockTitle>
          )
        },
      },
      {
        label: '名称',
        type: 'text',
        getter: (form: any) => form.instanceName || '',
        span: 8,
      },
      {
        label: '区域',
        type: 'text',
        getter: (form: any) =>
          form.resourcePool?.name ? `${form.domain?.name} - ${form.resourcePool.name}` : '',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        getter: (form: any) => form.tenant?.name || '',
        span: 8,
      },
      {
        label: '付费类型',
        type: 'text',
        getter: (form: any) =>
          form.paymentType === 'day'
            ? '按日付费'
            : form.paymentType === 'month'
              ? '按月付费'
              : '按年付费',
        span: 8,
      },
      {
        label: '最大客户数',
        type: 'text',
        getter: (form: any) => form.maxConnection || '',
        span: 8,
      },
      {
        label: 'VPC',
        type: 'text',
        getter: (form: any) => form.vpc?.vpcName || '',
        span: 8,
      },
      {
        label: '子网',
        type: 'text',
        getter: (form: any) => form.subnet?.subnetName || '',
        span: 8,
      },
      {
        label: '带宽值',
        type: 'text',
        getter: (form: any) => (form.bandwidth ? form.bandwidth + 'M' : ''),
        span: 8,
      },
    ],
  },
])

const formRef = ref<InstanceType<typeof SlForm>>()

// 处理取消操作
const handleCancel = () => {
  router.go(-1)
  eventBus.emit('corporateShoppingList:updateCount')
}

// 处理加入清单操作
const handleAddToCart = async () => {
  try {
    // 1. 校验表单数据
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 验证每个VPN配置

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'vpn', false)

    // 3. 调用API
    await corporateOrderTemSaveApi(params)
    ElMessage.success('加入清单成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}

// 处理直接开通操作
const handleDirectOrder = async () => {
  try {
    // 1. 校验表单数据
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构建参数
    const params = useGetTheParameters(formData.value, 'vpn')

    // 3. 调用API
    await corporateOrderApi(params)
    ElMessage.success('发起开通成功')
    handleCancel()
  } catch (error: any) {
    console.error(error)
  }
}
</script>

<style scoped>
/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}

.tip-con {
  margin-left: 4px;
  font-size: 12px;
  color: var(--el-color-primary);
  line-height: 25px;
  font-weight: normal;
  background: rgba(72, 127, 239, 0.1);
  text-align: center;
  border-radius: 5px;
}

/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
