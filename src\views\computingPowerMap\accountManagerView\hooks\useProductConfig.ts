import { ref, h } from 'vue'
import { ElButton, ElIcon } from 'element-plus'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import type { ColumnProps } from '@/components/SlProTable/interface'

// 产品类型定义
export type ProductType = 'ecs' | 'gcs' | 'evs' | 'obs' | 'nat' | 'slb' | 'eip' | 'vpn' | 'rdsMysql'

// Tab配置接口
export interface ProductTab {
  label: string
  name: ProductType
  count: number
}

// 筛选条件配置接口
export interface FilterFormItem {
  label: string
  type: string
  key?: string
  span: number
  disabled: boolean
  hidden: boolean
  defaultSelect?: boolean
  options?: any[]
  labelField?: string
  valueField?: string
  props?: any
  isActions?: boolean
  render?: () => any
}

export function useProductConfig() {
  // 格式化计费类型
  const formatBillType = (type: string): string => {
    const billTypeMap: Record<string, string> = {
      day: '按天计费',
      month: '按月计费',
      year: '按年计费',
    }
    return billTypeMap[type] || type || '--'
  }

  // 格式化计费方式
  const formatChargeType = (type: string): string => {
    const chargeTypeMap: Record<string, string> = {
      quant: '按量计费',
      require: '按需计费',
    }
    return chargeTypeMap[type] || type || '--'
  }

  // 产品tabs配置
  const tabs = ref<ProductTab[]>([
    { label: '云主机', name: 'ecs', count: 0 },
    { label: 'GPU云主机', name: 'gcs', count: 0 },
    { label: '云硬盘', name: 'evs', count: 0 },
    { label: '对象存储', name: 'obs', count: 0 },
    { label: 'NAT网关', name: 'nat', count: 0 },
    { label: '负载均衡', name: 'slb', count: 0 },
    { label: '弹性公网IP', name: 'eip', count: 0 },
    { label: 'VPN', name: 'vpn', count: 0 },
    { label: '云数据库', name: 'rdsMysql', count: 0 },
  ])

  // 子网信息渲染函数
  const subnetRender = ({ row }: any) => {
    return row.subnetName ? `${row.subnetName}/${row.subnetCidr || ''}`.replace(/\/$/, '') : '--'
  }

  // 各产品的列配置
  const getProductColumns = (productType: ProductType): ColumnProps<any>[] => {
    const baseColumns = {
      index: { type: 'index' as const, label: '序号', width: 55, fixed: 'left' as const },
    }

    switch (productType) {
      case 'ecs':
        return [
          baseColumns.index,
          { prop: 'deviceName', label: '主机名称', width: 150, fixed: 'left' },
          { prop: 'deviceId', label: '资源ID', width: 200 },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          { prop: 'osVersion', label: '系统版本', width: 120 },
          { prop: 'spec', label: '实例规格', width: 120 },
          { prop: 'sysDisk', label: '系统盘', width: 150 },
          { prop: 'dataDisk', label: '数据盘', width: 150 },
          { prop: 'ip', label: 'IP', width: 150 },
          { prop: 'eip', label: '弹性公网IP', width: 150 },
          { prop: 'bandWidth', label: '带宽', width: 100 },
          { prop: 'subnet', label: '子网信息', width: 100, render: subnetRender },
          { prop: 'tenantName', label: '租户', width: 100 },
          { prop: 'cloudPlatform', label: '所属云', width: 120 },
          { prop: 'resourcePoolName', label: '资源池', width: 120 },
          { prop: 'azName', label: '可用区', width: 120 },
          { prop: 'orderCode', label: '订单编号', width: 150 },
          { prop: 'securityGroupName', label: '安全组', width: 150 },
          { prop: 'vnicName', label: '虚拟网卡', width: 150 },
          { prop: 'billId', label: '计费号', width: 150 },
          { prop: 'deviceStatusCn', label: '状态', width: 100 },
          { prop: 'createTime', label: '订购时间', width: 150 },
          { prop: 'applyUserName', label: '订购人', width: 100 },
        ]

      case 'gcs':
        return [
          baseColumns.index,
          { prop: 'deviceName', label: '主机名称', width: 150, fixed: 'left' },
          { prop: 'deviceId', label: '资源ID', width: 200 },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          { prop: 'osVersion', label: '系统版本', width: 120 },
          { prop: 'spec', label: '实例规格', width: 120 },
          { prop: 'sysDisk', label: '系统盘', width: 150 },
          { prop: 'dataDisk', label: '数据盘', width: 150 },
          { prop: 'ip', label: 'IP', width: 150 },
          { prop: 'eip', label: '弹性公网IP', width: 150 },
          { prop: 'bandWidth', label: '带宽', width: 100 },
          { prop: 'subnet', label: '子网信息', width: 100, render: subnetRender },
          { prop: 'tenantName', label: '租户', width: 100 },
          { prop: 'cloudPlatform', label: '所属云', width: 120 },
          { prop: 'resourcePoolName', label: '资源池', width: 120 },
          { prop: 'azName', label: '可用区', width: 120 },
          { prop: 'orderCode', label: '订单编号', width: 150 },
          { prop: 'securityGroupName', label: '安全组', width: 150 },
          { prop: 'vnicName', label: '虚拟网卡', width: 150 },
          { prop: 'billId', label: '计费号', width: 150 },
          { prop: 'deviceStatusCn', label: '状态', width: 100 },
          { prop: 'createTime', label: '订购时间', width: 150 },
          { prop: 'applyUserName', label: '订购人', width: 100 },
        ]

      case 'evs':
        return [
          baseColumns.index,
          { prop: 'dataDisk', label: '数据盘', minWidth: 150, fixed: 'left' },
          { prop: 'deviceId', label: '资源ID', width: 200 },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          { prop: 'mountOrNot', label: '是否挂载云主机', minWidth: 150 },
          { prop: 'ecsName', label: '云主机', minWidth: 150 },
          { prop: 'tenantName', label: '租户', minWidth: 150 },
          { prop: 'cloudPlatform', label: '所属云', minWidth: 150 },
          { prop: 'resourcePoolName', label: '资源池', minWidth: 150 },
          { prop: 'orderCode', label: '订单编号', minWidth: 150 },
          { prop: 'billId', label: '计费号', width: 150 },
          { prop: 'deviceStatusCn', label: '状态', width: 100 },
          { prop: 'effectiveTime', label: '订购时间', minWidth: 150 },
          { prop: 'applyUserName', label: '订购人', minWidth: 100 },
        ]

      case 'obs':
        return [
          baseColumns.index,
          { prop: 'deviceName', label: '对象存储名称', width: 200, fixed: 'left' },
          { prop: 'deviceId', label: '资源ID', width: 200 },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          {
            prop: 'chargeType',
            label: '计费方式',
            width: 100,
            render: ({ row }) => formatChargeType(row.chargeType),
          },
          { prop: 'storeType', label: '存储类型', width: 120 },
          { prop: 'capacity', label: '容量', width: 120 },
          { prop: 'accessKey', label: '公钥', width: 150 },
          { prop: 'secretKey', label: '私钥', width: 150 },
          { prop: 'publicAddress', label: '公网访问地址', width: 200 },
          { prop: 'internalAddress', label: '私网访问地址', width: 200 },
          { prop: 'tenantName', label: '租户', width: 100 },
          { prop: 'cloudPlatform', label: '所属云', width: 120 },
          { prop: 'resourcePoolName', label: '资源池', width: 120 },
          { prop: 'orderCode', label: '订单编号', width: 150 },
          { prop: 'billId', label: '计费号', width: 150 },
          { prop: 'deviceStatusCn', label: '状态', width: 100 },
          { prop: 'effectiveTime', label: '订购时间', width: 150 },
          { prop: 'applyUserName', label: '订购人', width: 100 },
        ]

      case 'nat':
        return [
          baseColumns.index,
          { prop: 'deviceName', label: '网关名称', width: 200, fixed: 'left' },
          { prop: 'deviceId', label: '资源ID', width: 200 },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          { prop: 'spec', label: '实例规格', width: 200 },
          { prop: 'vpcName', label: 'VPC名称', width: 150 },
          { prop: 'subnetName', label: '子网名称', width: 150 },
          { prop: 'subnetCidr', label: '子网网段', width: 150 },
          { prop: 'eip', label: '弹性公网IP', width: 150 },
          { prop: 'bandWidth', label: '带宽大小', width: 120 },
          { prop: 'tenantName', label: '租户', width: 100 },
          { prop: 'cloudPlatform', label: '所属云', width: 120 },
          { prop: 'azName', label: '可用区', width: 120 },
          { prop: 'resourcePoolName', label: '资源池', width: 120 },
          { prop: 'orderCode', label: '订单编号', width: 150 },
          { prop: 'billId', label: '计费号', width: 150 },
          { prop: 'deviceStatusCn', label: '状态', width: 100 },
          { prop: 'effectiveTime', label: '订购时间', width: 150 },
          { prop: 'applyUserName', label: '订购人', width: 100 },
        ]

      case 'slb':
        return [
          baseColumns.index,
          { prop: 'deviceName', label: '负载均衡名称', width: 200, fixed: 'left' },
          { prop: 'deviceId', label: '资源ID', width: 200 },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          { prop: 'ip', label: 'VIP', width: 150 },
          { prop: 'spec', label: '实例规格', width: 200 },
          { prop: 'eip', label: '弹性公网IP', width: 150 },
          { prop: 'bandWidth', label: '带宽', width: 100 },
          { prop: 'vpcName', label: 'VPC名称', width: 150 },
          { prop: 'subnet', label: '子网信息', width: 100, render: subnetRender },
          { prop: 'tenantName', label: '租户', width: 100 },
          { prop: 'cloudPlatform', label: '所属云', width: 120 },
          { prop: 'azName', label: '可用区', width: 120 },
          { prop: 'resourcePoolName', label: '资源池', width: 120 },
          { prop: 'orderCode', label: '订单编号', width: 150 },
          { prop: 'billId', label: '计费号', width: 150 },
          { prop: 'deviceStatusCn', label: '状态', width: 100 },
          { prop: 'effectiveTime', label: '订购时间', width: 150 },
          { prop: 'applyUserName', label: '订购人', width: 100 },
        ]

      case 'eip':
        return [
          baseColumns.index,
          { prop: 'deviceName', label: '弹性公网名称', minWidth: 200, fixed: 'left' },
          { prop: 'eip', label: '弹性公网地址', minWidth: 150 },
          { prop: 'deviceId', label: '资源ID', width: 200 },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          { prop: 'bandWidth', label: '带宽', minWidth: 100 },
          { prop: 'relatedDeviceType', label: '绑定设备类型', minWidth: 150 },
          { prop: 'relatedDeviceName', label: '绑定设备名称', minWidth: 150 },
          { prop: 'tenantName', label: '租户', minWidth: 120 },
          { prop: 'cloudPlatform', label: '所属云', minWidth: 100 },
          { prop: 'resourcePoolName', label: '资源池', minWidth: 150 },
          { prop: 'orderCode', label: '订单编号', minWidth: 150 },
          { prop: 'effectiveTime', label: '订购时间', minWidth: 150 },
          { prop: 'billId', label: '计费号', width: 150 },
          { prop: 'deviceStatusCn', label: '状态', width: 100 },
          { prop: 'applyUserName', label: '订购人', minWidth: 100 },
        ]

      case 'vpn':
        return [
          baseColumns.index,
          { prop: 'deviceName', label: 'VPN名称', width: 200, fixed: 'left' },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          { prop: 'spec', label: '最大客户端数', width: 120 },
          { prop: 'vpcName', label: 'VPC', width: 150 },
          { prop: 'subnetName', label: '子网', width: 150 },
          { prop: 'eip', label: '公网IP', width: 150 },
          { prop: 'bandWidth', label: '带宽', width: 100 },
          { prop: 'cloudPlatform', label: '云平台', width: 120 },
          { prop: 'resourcePoolName', label: '资源池', width: 120 },
          { prop: 'tenantName', label: '租户', width: 100 },
          { prop: 'orderCode', label: '订单编号', width: 150 },
          { prop: 'createTime', label: '订购时间', width: 150 },
          { prop: 'applyUserName', label: '订购人', width: 100 },
        ]

      case 'rdsMysql':
        return [
          baseColumns.index,
          { prop: 'deviceName', label: '云数据库名称', width: 150, fixed: 'left' },
          { prop: 'deviceId', label: '资源ID', width: 180 },
          {
            prop: 'billType',
            label: '计费类型',
            width: 100,
            render: ({ row }) => formatBillType(row.billType),
          },
          { prop: 'osVersion', label: '数据库版本', width: 120 },
          {
            prop: 'mountOrNot',
            label: '系列',
            width: 100,
            render: ({ row }) => {
              const seriesMap: Record<string, string> = {
                ALONE: '单机版',
                COLONY: '主备版本',
              }
              return seriesMap[row.mountOrNot] || row.mountOrNot
            },
          },
          { prop: 'spec', label: '实例规格', width: 120 },
          { prop: 'sysDisk', label: '存储大小', width: 100 },
          { prop: 'ip', label: 'IP', width: 120 },
          { prop: 'tenantName', label: '租户', width: 120 },
          { prop: 'cloudPlatform', label: '所属云', width: 100 },
          { prop: 'resourcePoolName', label: '所属资源池', width: 120 },
          { prop: 'azName', label: '可用区', width: 100 },
          { prop: 'orderCode', label: '订单编号', width: 150 },
          { prop: 'billId', label: '计费号', width: 120 },
          { prop: 'deviceStatusCn', label: '状态', width: 100 },
          { prop: 'createTime', label: '订购时间', width: 160 },
          { prop: 'applyUserName', label: '订购人', width: 100 },
        ]

      default:
        return []
    }
  }

  // 获取筛选条件配置（严格按照ecs/list的DGFormOptions模式）
  const getProductFilterOptions = (productType: ProductType): FilterFormItem[] => {
    const baseItems = {
      // 操作按钮行
      actionRow: {
        label: '',
        type: 'custom',
        isActions: true,
        span: 8,
        disabled: false,
        hidden: false,
        render: () => null, // 在组件中实现
      },
    }

    switch (productType) {
      case 'ecs':
        return [
          {
            label: '主机名称',
            type: 'input',
            key: 'deviceName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '资源ID',
            type: 'input',
            key: 'deviceId',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '系统版本',
            type: 'input',
            key: 'osVersion',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '实例规格',
            type: 'input',
            key: 'spec',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '系统盘',
            type: 'input',
            key: 'sysDisk',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '数据盘',
            type: 'input',
            key: 'dataDisk',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: 'IP',
            type: 'input',
            key: 'ip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '弹性公网IP',
            type: 'input',
            key: 'eip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'effectiveTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      case 'gcs':
        return [
          {
            label: '主机名称',
            type: 'input',
            key: 'deviceName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '资源ID',
            type: 'input',
            key: 'deviceId',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '系统版本',
            type: 'input',
            key: 'osVersion',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '实例规格',
            type: 'input',
            key: 'spec',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '系统盘',
            type: 'input',
            key: 'sysDisk',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '数据盘',
            type: 'input',
            key: 'dataDisk',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: 'IP',
            type: 'input',
            key: 'ip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '弹性公网IP',
            type: 'input',
            key: 'eip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'effectiveTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      case 'evs':
        return [
          {
            label: '云主机',
            type: 'input',
            key: 'ecsName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '资源ID',
            type: 'input',
            key: 'deviceId',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '数据盘',
            type: 'input',
            key: 'dataDisk',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'effectiveTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      case 'obs':
        return [
          {
            label: '对象存储名称',
            type: 'input',
            key: 'deviceName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '资源ID',
            type: 'input',
            key: 'deviceId',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '存储类型',
            type: 'input',
            key: 'storeType',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '容量',
            type: 'input',
            key: 'capacity',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '公钥',
            type: 'input',
            key: 'accessKey',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '私钥',
            type: 'input',
            key: 'secretKey',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '公网访问地址',
            type: 'input',
            key: 'publicAddress',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '私网访问地址',
            type: 'input',
            key: 'internalAddress',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'effectiveTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      case 'nat':
        return [
          {
            label: '网关名称',
            type: 'input',
            key: 'deviceName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '资源ID',
            type: 'input',
            key: 'deviceId',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '实例规格',
            type: 'input',
            key: 'spec',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: 'VPC名称',
            type: 'input',
            key: 'vpcName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '子网名称',
            type: 'input',
            key: 'subnetName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '弹性公网IP',
            type: 'input',
            key: 'eip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'effectiveTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      case 'slb':
        return [
          {
            label: '负载均衡名称',
            type: 'input',
            key: 'deviceName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '资源ID',
            type: 'input',
            key: 'deviceId',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: 'VIP',
            type: 'input',
            key: 'ip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '实例规格',
            type: 'input',
            key: 'spec',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '弹性公网IP',
            type: 'input',
            key: 'eip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: 'VPC名称',
            type: 'input',
            key: 'vpcName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'effectiveTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      case 'eip':
        return [
          {
            label: '弹性公网名称',
            type: 'input',
            key: 'deviceName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '资源ID',
            type: 'input',
            key: 'deviceId',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '弹性公网地址',
            type: 'input',
            key: 'eip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'effectiveTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      case 'vpn':
        return [
          {
            label: 'VPN名称',
            type: 'input',
            key: 'deviceName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '最大客户端数',
            type: 'input',
            key: 'spec',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: 'VPC',
            type: 'input',
            key: 'vpcName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '子网',
            type: 'input',
            key: 'subnetName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '公网IP',
            type: 'input',
            key: 'eip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'createTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      case 'rdsMysql':
        return [
          {
            label: '云数据库名称',
            type: 'input',
            key: 'deviceName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          {
            label: '租户',
            type: 'input',
            key: 'tenantName',
            span: 8,
            disabled: true,
            hidden: false,
            defaultSelect: true,
          },
          baseItems.actionRow,
          {
            label: '资源ID',
            type: 'input',
            key: 'deviceId',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '数据库版本',
            type: 'input',
            key: 'osVersion',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '实例规格',
            type: 'input',
            key: 'spec',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '存储大小',
            type: 'input',
            key: 'sysDisk',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: 'IP',
            type: 'input',
            key: 'ip',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'orderCode',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购人',
            type: 'input',
            key: 'applyUserName',
            span: 8,
            disabled: false,
            hidden: true,
          },
          {
            label: '订购时间',
            type: 'date',
            key: 'createTime',
            span: 8,
            disabled: false,
            hidden: true,
            props: {
              type: 'datetimerange',
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              rangeSeparator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ]

      default:
        return []
    }
  }

  // 生成表单配置选项
  const getFormOptions = (
    productType: ProductType,
    queryParams: any,
    collapsed: { value: boolean },
    onReset: () => void,
    onSearch: () => void,
    ConditionFilterComponent: any,
    customFilterItems?: any[], // 添加可选的自定义筛选条件参数
  ) => {
    // 使用自定义筛选条件或默认筛选条件
    const filterItems = customFilterItems || getProductFilterOptions(productType)

    // 找到操作按钮行的索引
    const actionRowIndex = filterItems.findIndex(
      (item) => item.span === 8 && item.isActions == true,
    )

    if (actionRowIndex !== -1) {
      // 替换操作按钮行的render函数
      filterItems[actionRowIndex] = {
        ...filterItems[actionRowIndex],
        render() {
          return h('div', { style: 'display: flex;justify-content: flex-end;' }, [
            h(
              ElButton,
              { type: 'primary', link: true },
              {
                default: () =>
                  h(ConditionFilterComponent, {
                    formModel: queryParams,
                    resourceList: filterItems, // 使用同一个filterItems引用
                  }),
              },
            ),
            h(
              ElButton,
              {
                onClick: onReset,
                icon: Delete,
              },
              { default: () => '重置' },
            ),
            h(
              ElButton,
              {
                onClick: onSearch,
                icon: Search,
                type: 'primary',
              },
              { default: () => '搜索' },
            ),
            h(
              ElButton,
              {
                type: 'primary',
                link: true,
                class: 'search-isOpen',
                onClick: () => (collapsed.value = !collapsed.value),
              },
              {
                default: () => [
                  collapsed.value ? '展开' : '折叠',
                  h(
                    ElIcon,
                    { class: 'el-icon--right' },
                    {
                      default: () => h(collapsed.value ? ArrowDown : ArrowUp),
                    },
                  ),
                ],
              },
            ),
          ])
        },
      }
    }
    console.log(filterItems)

    return [
      {
        style: 'padding: 0',
        groupItems: filterItems,
      },
    ]
  }

  // 更新tab数量
  const updateTabCount = (productType: ProductType, count: number) => {
    const tab = tabs.value.find((t) => t.name === productType)
    if (tab) {
      tab.count = count
    }
  }

  return {
    tabs,
    getProductColumns,
    getProductFilterOptions,
    getFormOptions,
    updateTabCount,
  }
}
