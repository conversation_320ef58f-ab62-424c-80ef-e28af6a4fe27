<template>
  <div id="PermissionAdd">
    <div class="box-card" v-for="(item, index) in formData" :key="'formDataKey' + index">
      <sl-form
        :ref="(el: any) => setFormRef(el, index)"
        :options="formOptions"
        v-model="formData[index]"
      >
        <template #detailListlot>
          <sl-card>
            <template #header>
              <div class="card-header">
                <span>操作权限</span>
              </div>
            </template>
            <el-row
              class="menu-item"
              v-for="(operation, operationIndex) in formData[index].detailList"
              :key="'url' + operationIndex"
            >
              <el-col :span="11">
                <el-form-item
                  class="module-item"
                  :prop="'detailList.' + operationIndex + '.urlNote'"
                  label="操作"
                  :rules="operationRules"
                >
                  <div class="module-item-content">
                    <el-input v-model="operation.urlNote" placeholder="请输入" />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item
                  class="module-item"
                  :prop="'detailList.' + operationIndex + '.url'"
                  label="路径"
                  :rules="[{ required: true, message: '请输入', trigger: ['blur', 'change'] }]"
                >
                  <div class="module-item-content">
                    <el-input v-model="operation.url" placeholder="请输入" />
                  </div>
                </el-form-item>
              </el-col>
              <el-col class="operation" :span="2">
                <el-icon
                  @click="handleOperationRemove(operation, operationIndex)"
                  v-if="formData[index].detailList?.length != 1"
                >
                  <RemoveFilled />
                </el-icon>
                <el-icon
                  @click="handleOperationAdd(formData[index].detailList || [])"
                  v-if="formData[index].detailList?.length == operationIndex + 1"
                >
                  <CirclePlusFilled />
                </el-icon>
              </el-col>
            </el-row>
          </sl-card>
        </template>
      </sl-form>
    </div>
    <div class="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitValidator">确定</el-button>
    </div>
  </div>
</template>
<script setup lang="ts" name="PermissionAdd">
import { reactive, watch, ref, onMounted } from 'vue'
import { RemoveFilled, CirclePlusFilled } from '@element-plus/icons-vue'
import type { PermissionType } from '../interface/type'
import {
  addApiFunc,
  getApiPlatformListApi,
  getApiResTypeListApi,
  updateApiFunc,
  checkApiIsUseFunc,
} from '@/api/modules/managementCenter'
import { validateNoSpecialChars } from '@/utils/validate'
import SlMessage from '@/components/base/SlMessage'

const props = defineProps<{
  currentPermission: PermissionType
}>()

const dominCodeList: any = ref([])

// 定义类型/接口
interface Option {
  label: string
  value: string
}
const categoryCodeList = ref<Option[]>([])
// 初始化表格数据
onMounted(() => {
  init()
})

async function init() {
  const res1: any = await getApiPlatformListApi({}) //查询domincode
  dominCodeList.value = res1.entity

  const res: any = await getApiResTypeListApi({})

  res.entity.forEach((item: any) => {
    categoryCodeList.value.push({
      label: item.configName,
      // label: item.resType,
      value: item.configCode,
    })
  })
}

const formData = reactive<PermissionType[]>([])
const formRefs = ref<any[]>([])
const deleteArr = ref<any[]>([])
const setFormRef = (el: any, index: number) => {
  if (el) {
    formRefs.value[index] = el
  }
}
const handleOperationAdd = (detailList: any[]) => {
  detailList.push({
    id: '',
    urlNote: '',
    url: '',
  })
}

watch(
  () => props.currentPermission,
  (newPermission) => {
    if (newPermission.id) {
      formData.length = 0
      formData.push(JSON.parse(JSON.stringify(newPermission)))
    } else {
      formData.length = 0
      const newBtn = {
        urlNote: '',
        url: '',
      }
      formData.push({
        id: undefined,
        domainName: '',
        domainCode: '',
        resTypeName: '',
        resType: '',
        detailList: [newBtn],
      })
    }

    // 检查 detailList 是否为空，如果为空则添加一个默认项
    formData.forEach((item) => {
      if (!item.detailList || item.detailList.length === 0) {
        item.detailList = []
        handleOperationAdd(item.detailList)
      }
    })
  },
  {
    immediate: true,
  },
)

const formOptions = reactive([
  {
    groupItems: [
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        labelField: 'domainName',
        valueField: 'domainCode',
        options: dominCodeList,
        isDisabled: false,
        span: 12,
        // labelWidth: '63',
        disabled: true,
        hidden: false,
        defaultSelect: true,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        onChange(option: any) {
          formData[0].domainName = option.domainName
        },
      },
      {
        label: '资源类型',
        type: 'select',
        key: 'resType',
        options: categoryCodeList,
        span: 12,
        rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        onChange(option: any) {
          formData[0].resTypeName = option.label
        },
      },
      {
        label: '',
        type: 'slot',
        slotName: 'detailListlot',
        key: 'detailList',
        hiddenLabel: true,
        span: 24,
      },
    ],
  },
])

const operationRules = [
  { required: true, message: '请输入', trigger: ['blur', 'change'] },
  { validator: validateNoSpecialChars, trigger: ['blur', 'change'] },
]

const handleOperationRemove = (operation: any, index: number) => {
  if (operation.id) {
    checkApiIsUseFunc({
      id: operation.id,
    }).then((res: any) => {
      if (res.entity) {
        formData.length > 0 &&
          formData[0] &&
          formData[0].detailList &&
          formData[0].detailList.length > 0 &&
          formData[0].detailList.splice(index, 1)
        deleteArr.value.push(operation.id)
      } else {
        SlMessage.error('删除失败')
      }
    })
  } else {
    formData.length > 0 &&
      formData[0] &&
      formData[0].detailList &&
      formData[0].detailList.length > 0 &&
      formData[0].detailList.splice(index, 1)
  }
}

const emit = defineEmits(['submit', 'close'])

// const getFormatFormData = (formData: PermissionType[]) => {
//   const newData = JSON.parse(JSON.stringify(formData))
//   const originalMenu = props.currentPermission
//   if (originalMenu.id) {
//     newData.forEach((menu: PermissionType) => {
//       // menu.operateCode = menu.resTypeName == originalMenu.resTypeName && menu.resType == originalMenu.resType ? 3 : 1
//       menu?.detailList?.forEach((operation: PermissionType) => {
//         if (operation.id ) {
//           const originalOperation = originalMenu.detailList?.find(
//             (item: PermissionType) => item.id == operation.id,
//           )
//           operation.operateCode =
//             operation.urlNote == originalOperation?.urlNote && operation.url == originalOperation?.url
//               ? 3
//               : 1
//         }
//       })
//     })
//   }
//   return newData[0]
// }
const submitValidator = async () => {
  try {
    Promise.all(formRefs.value.map((formRef) => formRef.validate()))
      .then(async (res) => {
        if (res.length > 0 && res.every(Boolean)) {
          // let updatedFormData: PermissionType[]
          if (props.currentPermission.id) {
            let params = {
              domainName: formData[0].domainName,
              domainCode: formData[0].domainCode,
              resTypeName: formData[0].resTypeName,
              resType: formData[0].resType,
              detailList: formData[0].detailList,
              delIds: deleteArr.value,
            }
            // updatedFormData = formData[0]
            await updateApiFunc(params)
            SlMessage.success('编辑成功')
          } else {
            await addApiFunc(formData[0])
            SlMessage.success('创建成功')
          }

          emit('submit')
        }
      })
      .catch((err) => {
        console.error(err)
      })
    // return
  } catch {
    SlMessage.error('操作失败')
  }
}

const handleClose = () => {
  emit('close')
}
</script>
<style lang="scss" scoped>
#PermissionAdd {
  .box-card {
    margin-bottom: 10px;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  :deep(.el-card__body) {
    padding: 10px;
  }
  .menu-item {
    padding: 10px;
  }
  .operation {
    display: flex;
    align-items: center;
    font-size: 16px;
    .el-icon {
      cursor: pointer;
      margin-left: 5px;
    }
  }
  .footer {
    height: 48px;
    margin: 0 8px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: right;
    padding: 0 14px;
  }
}
</style>
