<template>
  <div class="table-box">
    <sl-page-header
      title="运营分析"
      title-line="运营分析是通过系统化收集、处理和分析运营过程中产生的数据，评估业务表现、发现问题并指导决策的过程。"
      :icon="{
        class: 'page_yunyingfenxi',
        color: '#0052D9',
        size: '40px',
      }"
    >
    </sl-page-header>

    <!-- 图表区域 -->
    <div class="chart-container">
      <!-- 第一行：活跃人数和点击量 -->
      <div class="chart-row">
        <!-- 活跃人数图表 -->
        <div class="chart-card half-width">
          <div class="chart-header">
            <div class="chart-title">活跃人数</div>
            <div class="chart-tab">
              <el-radio-group v-model="activeUserTimeRange" size="small">
                <el-radio-button value="day">本日</el-radio-button>
                <el-radio-button value="week">本周</el-radio-button>
                <el-radio-button value="month">本月</el-radio-button>
                <el-radio-button value="year">全年</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div ref="activeUserChart" class="chart-body"></div>
        </div>

        <!-- 实时点击量图表 -->
        <div class="chart-card half-width">
          <div class="chart-header">
            <div class="chart-title">用户量</div>
            <div class="chart-tab">
              <el-radio-group v-model="userCountRange" size="small">
                <el-radio-button value="day">本日</el-radio-button>
                <el-radio-button value="week">本周</el-radio-button>
                <el-radio-button value="month">本月</el-radio-button>
                <el-radio-button value="year">全年</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div ref="userCountClickChart" class="chart-body"></div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 点击量图表 -->
        <div class="chart-card half-width">
          <div class="chart-header">
            <div class="chart-title">点击量</div>
            <div class="chart-tab">
              <el-radio-group v-model="clickTimeRange" size="small">
                <el-radio-button value="day">本日</el-radio-button>
                <el-radio-button value="week">本周</el-radio-button>
                <el-radio-button value="month">本月</el-radio-button>
                <el-radio-button value="year">全年</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div ref="clickChart" class="chart-body"></div>
        </div>

        <!-- 平台调用量图表 -->
        <div class="chart-card half-width">
          <div class="chart-header">
            <div class="chart-title">平台调用量</div>
            <div class="chart-tab">
              <el-radio-group v-model="apiCallTimeRange" size="small">
                <el-radio-button value="day">本日</el-radio-button>
                <el-radio-button value="week">本周</el-radio-button>
                <el-radio-button value="month">本月</el-radio-button>
                <el-radio-button value="year">全年</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div ref="apiCallChart" class="chart-body"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
// 运营分析接口
import { activeStatisticsAggregateApi } from '@/api/modules/managementCenter'
import dayjs from 'dayjs'

// 时间范围类型定义
type TimeRange = 'day' | 'week' | 'month' | 'year'

// 时间范围选择
const activeUserTimeRange = ref<TimeRange>('day')
const userCountRange = ref<TimeRange>('day')
const clickTimeRange = ref<TimeRange>('day')
const apiCallTimeRange = ref<TimeRange>('day')

// 图表DOM引用
const activeUserChart = ref<HTMLElement | null>(null)
const userCountClickChart = ref<HTMLElement | null>(null)
const clickChart = ref<HTMLElement | null>(null)
const apiCallChart = ref<HTMLElement | null>(null)

// 图表实例
let activeUserChartInstance: echarts.ECharts | null = null
let userCountClickChartInstance: echarts.ECharts | null = null
let clickChartInstance: echarts.ECharts | null = null
let apiCallChartInstance: echarts.ECharts | null = null

// 创建数据格式转换器 - 将API返回的数据转换为图表所需格式
interface ChartData {
  xAxis: string[]
  data: number[]
}

interface ApiResponseData {
  timePoint: string
  activeUserCount: number
  userLoginCount: number
  clickCount: number
  apiAccessCount: number
}

// 获取日期范围和聚合类型
const getDateRange = (timeRange: TimeRange) => {
  const now = dayjs()
  let startDate: string
  let endDate: string
  let aggType: 'DAY' | 'MONTH' | 'YEAR' | 'WEEK'

  if (timeRange === 'day') {
    startDate = now.startOf('day').format('YYYY-MM-DD HH:mm:ss')
    endDate = now.endOf('day').format('YYYY-MM-DD HH:mm:ss')
    aggType = 'DAY'
  } else if (timeRange === 'week') {
    startDate = now.startOf('week').add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
    endDate = now.endOf('week').add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
    aggType = 'WEEK'
  } else if (timeRange === 'month') {
    startDate = now.startOf('month').format('YYYY-MM-DD HH:mm:ss')
    endDate = now.endOf('month').format('YYYY-MM-DD HH:mm:ss')
    aggType = 'MONTH'
  } else {
    startDate = now.startOf('year').format('YYYY-MM-DD HH:mm:ss')
    endDate = now.endOf('year').format('YYYY-MM-DD HH:mm:ss')
    aggType = 'YEAR'
  }

  return { startDate, endDate, aggType }
}

// 处理API返回数据
const processApiData = (
  data: ApiResponseData[],
  timeRange: TimeRange,
  dataKey: 'activeUserCount' | 'clickCount' | 'apiAccessCount' | 'userLoginCount',
): ChartData => {
  const now = dayjs()
  let xAxis: string[] = []
  let dataArr: number[] = []

  if (timeRange === 'day') {
    // 按小时聚合，只生成到当前小时的数据点
    const currentHour = now.hour()
    xAxis = Array.from({ length: currentHour + 1 }, (_, i) => `${i}:00`)
    dataArr = Array.from({ length: currentHour + 1 }, () => 0)
  } else if (timeRange === 'week') {
    // 按天聚合，只生成到当前天的数据点
    const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    const currentDayInWeek = now.day() || 7 // 将周日的0转换为7
    xAxis = weekDays.slice(0, currentDayInWeek)
    dataArr = Array.from({ length: currentDayInWeek }, () => 0)
  } else if (timeRange === 'month') {
    // 按天聚合，只生成到当前日期的数据点
    const currentDate = now.date()
    xAxis = Array.from({ length: currentDate }, (_, i) => `${i + 1}日`)
    dataArr = Array.from({ length: currentDate }, () => 0)
  } else {
    // 按月聚合，只生成到当前月份的数据点
    const months = [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ]
    const currentMonth = now.month()
    xAxis = months.slice(0, currentMonth + 1)
    dataArr = Array.from({ length: currentMonth + 1 }, () => 0)
  }

  // 填充API返回的数据
  data.forEach((item, index) => {
    if (index < dataArr.length) {
      dataArr[index] = item[dataKey] || 0
    }
  })

  return {
    xAxis,
    data: dataArr,
  }
}

// 获取活跃人数数据
const getActiveUserData = async (timeRange: TimeRange): Promise<ChartData> => {
  try {
    const { startDate, endDate, aggType } = getDateRange(timeRange)
    const response = await activeStatisticsAggregateApi({
      startDate,
      endDate,
      aggType,
    })

    if (response.entity && Array.isArray(response.entity)) {
      return processApiData(response.entity, timeRange, 'activeUserCount')
    }
    // 如果没有数据，返回空数据
    return getEmptyChartData(timeRange)
  } catch (error) {
    console.error('获取活跃用户数据失败:', error)
    // 发生错误时返回空数据
    return getEmptyChartData(timeRange)
  }
}

const getUserLoginData = async (timeRange: TimeRange) => {
  try {
    const { startDate, endDate, aggType } = getDateRange(timeRange)
    const response = await activeStatisticsAggregateApi({
      startDate,
      endDate,
      aggType,
    })
    if (response.entity && Array.isArray(response.entity)) {
      return processApiData(response.entity, timeRange, 'userLoginCount')
    }
    return getEmptyChartData(timeRange)
  } catch (error) {
    console.error('获取用户登录数据失败:', error)
    return getEmptyChartData(timeRange)
  }
}

// 获取点击量数据
const getClickData = async (timeRange: TimeRange) => {
  try {
    const { startDate, endDate, aggType } = getDateRange(timeRange)
    const response = await activeStatisticsAggregateApi({
      startDate,
      endDate,
      aggType,
    })
    if (response.entity && Array.isArray(response.entity)) {
      const { xAxis, data } = processApiData(response.entity, timeRange, 'clickCount')
      return { xAxis, data }
    }

    return getEmptyChartData(timeRange)
  } catch (error) {
    console.error('获取点击量数据失败:', error)
    return getEmptyChartData(timeRange)
  }
}

// 获取API调用量数据
const getApiCallData = async (timeRange: TimeRange) => {
  try {
    const { startDate, endDate, aggType } = getDateRange(timeRange)
    const response = await activeStatisticsAggregateApi({
      startDate,
      endDate,
      aggType,
    })

    if (response.entity && Array.isArray(response.entity)) {
      const { xAxis, data } = processApiData(response.entity, timeRange, 'apiAccessCount')
      return {
        xAxis,
        data,
      }
    }

    return getEmptyChartData(timeRange)
  } catch (error) {
    console.error('获取API调用量数据失败:', error)
    return getEmptyChartData(timeRange)
  }
}

// 生成空数据（当API调用失败或没有数据时使用）
const getEmptyChartData = (timeRange: TimeRange): ChartData => {
  if (timeRange === 'day') {
    const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)
    return { xAxis: hours, data: hours.map(() => 0) }
  } else if (timeRange === 'week') {
    const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    return { xAxis: days, data: days.map(() => 0) }
  } else if (timeRange === 'month') {
    const daysInMonth = dayjs().daysInMonth()
    const days = Array.from({ length: daysInMonth }, (_, i) => `${i + 1}`)
    return { xAxis: days, data: days.map(() => 0) }
  } else {
    const months = [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ]
    return { xAxis: months, data: months.map(() => 0) }
  }
}

// tooltip 标题统一格式化
const formatTooltipTitle = (timeRange: TimeRange, axisValue: string): string => {
  if (timeRange === 'day') {
    return `${dayjs().format('M月D日')} ${axisValue}`
  }
  if (timeRange === 'year') {
    return `${dayjs().format('YYYY年')}${axisValue}`
  }
  if (timeRange === 'month') {
    return `${dayjs().format('M月')} ${axisValue}`
  }
  return axisValue
}

// 仅有一个有效数据点(非0/非空)时，折线图改为柱状图
const decideSeriesType = (xAxis: string[], data: number[]): 'line' | 'bar' => {
  const nonEmptyCount = data.filter((v) => v !== 0 && v !== null && v !== undefined).length
  if (xAxis.length <= 1 || nonEmptyCount <= 1) return 'bar'
  return 'line'
}

// 初始化活跃人数图表
const initActiveUserChart = async () => {
  if (activeUserChartInstance) {
    activeUserChartInstance.dispose()
  }

  const chartDom = activeUserChart.value
  if (!chartDom) return

  activeUserChartInstance = echarts.init(chartDom)
  activeUserChartInstance.showLoading()

  try {
    const { xAxis, data } = await getActiveUserData(activeUserTimeRange.value)

    const seriesType = decideSeriesType(xAxis, data)

    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const title = formatTooltipTitle(activeUserTimeRange.value, params[0].name)
          return `${title}<br/>活跃人数：${params[0].value} 人`
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: seriesType === 'bar',
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: '#E5E5E5',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
      },
      yAxis: {
        type: 'value',
        name: '人/次',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#E5E5E5',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
      },
      series: [
        {
          name: '活跃人数',
          type: seriesType,
          smooth: seriesType === 'line',
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 2,
            color: '#2B7AF5',
          },
          ...(seriesType === 'line'
            ? {
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(77, 155, 249, 0.8)' },
                    { offset: 1, color: 'rgba(236, 242, 253, 0.2)' },
                  ]),
                },
              }
            : { barWidth: '60%' }),
          itemStyle: { color: '#2B7AF5' },
          data,
        },
      ],
    }

    activeUserChartInstance.hideLoading()
    activeUserChartInstance.setOption(option)
  } catch (error) {
    console.error('初始化活跃人数图表失败:', error)
    activeUserChartInstance.hideLoading()
  }
}

// 初始化实时点击量图表
const initUserLoginChart = async () => {
  if (userCountClickChartInstance) {
    userCountClickChartInstance.dispose()
  }

  const chartDom = userCountClickChart.value
  if (!chartDom) return

  userCountClickChartInstance = echarts.init(chartDom)
  userCountClickChartInstance.showLoading()

  try {
    const { xAxis, data } = await getUserLoginData(userCountRange.value)

    const seriesType = decideSeriesType(xAxis, data)

    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const title = formatTooltipTitle(userCountRange.value, params[0].name)
          return `${title}<br/>用户量：${params[0].value} 人`
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: seriesType === 'bar',
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: '#E5E5E5',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
      },
      yAxis: {
        type: 'value',
        name: '人',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#E5E5E5',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
      },
      series: [
        {
          name: '点击量',
          type: seriesType,
          smooth: seriesType === 'line',
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 2,
            color: '#FF9300',
          },
          ...(seriesType === 'line'
            ? {
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(255, 147, 0, 0.8)' },
                    { offset: 1, color: 'rgba(255, 147, 0, 0.1)' },
                  ]),
                },
              }
            : { barWidth: '60%' }),
          itemStyle: { color: '#FF9300' },
          data,
        },
      ],
    }

    userCountClickChartInstance.hideLoading()
    userCountClickChartInstance.setOption(option)
  } catch (error) {
    console.error('初始化实时点击量图表失败:', error)
    userCountClickChartInstance.hideLoading()
  }
}

// 初始化点击量图表
const initClickChart = async () => {
  if (clickChartInstance) {
    clickChartInstance.dispose()
  }

  const chartDom = clickChart.value
  if (!chartDom) return

  clickChartInstance = echarts.init(chartDom)
  clickChartInstance.showLoading()

  try {
    const { xAxis, data } = await getClickData(clickTimeRange.value)

    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const title = formatTooltipTitle(clickTimeRange.value, params[0].name)
          return `${title}<br/>点击量：${params[0].value} 次`
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: '#E5E5E5',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
      },
      yAxis: {
        type: 'value',
        name: '次',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#E5E5E5',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
      },
      series: [
        {
          name: '点击量',
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: '#63CD8C',
          },
          data,
        },
      ],
    }

    clickChartInstance.hideLoading()
    clickChartInstance.setOption(option)
  } catch (error) {
    console.error('初始化点击量图表失败:', error)
    clickChartInstance.hideLoading()
  }
}

// 初始化API调用量图表
const initApiCallChart = async () => {
  if (apiCallChartInstance) {
    apiCallChartInstance.dispose()
  }

  const chartDom = apiCallChart.value
  if (!chartDom) return

  apiCallChartInstance = echarts.init(chartDom)
  apiCallChartInstance.showLoading()

  try {
    const { xAxis, data } = await getApiCallData(apiCallTimeRange.value)

    const seriesType = decideSeriesType(xAxis, data)

    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const title = formatTooltipTitle(apiCallTimeRange.value, params[0].name)
          return `${title}<br/>调用量：${params[0].value} 次`
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: seriesType === 'bar',
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: '#E5E5E5',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
      },
      yAxis: {
        type: 'value',
        name: '次',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#E5E5E5',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
      },
      series: [
        {
          name: '调用量',
          type: seriesType,
          smooth: seriesType === 'line',
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 2,
            color: '#50A9FE',
          },
          ...(seriesType === 'line'
            ? {
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(77, 199, 255, 0.8)' },
                    { offset: 1, color: 'rgba(236, 242, 253, 0.2)' },
                  ]),
                },
              }
            : { barWidth: '60%' }),
          itemStyle: { color: '#50A9FE' },
          data,
        },
      ],
    }

    apiCallChartInstance.hideLoading()
    apiCallChartInstance.setOption(option)
  } catch (error) {
    console.error('初始化API调用量图表失败:', error)
    apiCallChartInstance.hideLoading()
  }
}

// 监听窗口大小变化
const handleResize = () => {
  activeUserChartInstance && activeUserChartInstance.resize()
  userCountClickChartInstance && userCountClickChartInstance.resize()
  clickChartInstance && clickChartInstance.resize()
  apiCallChartInstance && apiCallChartInstance.resize()
}

// 监听时间范围变化
watch(activeUserTimeRange, () => {
  initActiveUserChart()
})

watch(userCountRange, () => {
  initUserLoginChart()
})

watch(clickTimeRange, () => {
  initClickChart()
})

watch(apiCallTimeRange, () => {
  initApiCallChart()
})

onMounted(() => {
  initActiveUserChart()
  initUserLoginChart()
  initClickChart()
  initApiCallChart()
  window.addEventListener('resize', handleResize)
})

// 组件卸载前清除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  activeUserChartInstance && activeUserChartInstance.dispose()
  userCountClickChartInstance && userCountClickChartInstance.dispose()
  clickChartInstance && clickChartInstance.dispose()
  apiCallChartInstance && apiCallChartInstance.dispose()
})
</script>

<style lang="scss" scoped>
.chart-container {
  margin: 8px;
}

.chart-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.chart-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.half-width {
  width: calc(50% - 4px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.chart-body {
  height: 300px;
  padding: 20px;
}
</style>
