import http from '@/api'
import { WOC } from '../config/servicePort'
import type {
  CardUtilizationRequest,
  CardUtilizationItem,
  BusinessSystemRankingRequest,
  BusinessSystemRankingResponse,
} from '@/types/zs2map'

/**
 * @name 智算显存、算力利用率 按照区划归类  -智算运行状态
 */
export const queryDeviceRunSortApi = (config: any) => {
  return http.post(WOC + '/zsMap/queryDeviceRunSort', config)
}
/**
 * @name 量详情按照类型分组 - 算力切片
 */
export const totalPhysicaltApi = (config: any) => {
  return http.post(WOC + '/zsMap/totalPhysical', config)
}

/**
 * @name 量详情按照类型分组 - 算力切片-切片数
 */
export const totalPhysicalSliceApi = (config: any) => {
  return http.post(WOC + '/zsMap/totalPhysicalSlice', config)
}

/**
 * @name 量详情按照类型分组 - 算力切片
 */
export const totalComputeApi = (config?: any) => {
  return http.post(WOC + '/zsMap/totalCompute', config)
}

/**
 * @name 智算显存、算力利用率 按照区划归类  -智算运行状态
 */
export const queryDevicesMetricPercentApi = (config?: any) => {
  return http.post(WOC + '/zsMap/queryDevicesMetricPercent', config)
}
/**
 * @name 物理卡分布情况
 */
export const devicePhysicalTypeAreaGroupApi = (config?: any) => {
  return http.post(WOC + '/zsMap/devicePhysicalTypeAreaGroup', config)
}

/**
 * @name 物理算力列表
 */
export const pageDeviceGpuInfoListApi = (config?: any) => {
  return http.post<any>(WOC + '/zsMap/pageDeviceGpuInfoList', config)
}

/**
 * @name 切片算力列表
 */
export const pageDeviceVirtualGpuInfoListApi = (config?: any) => {
  return http.post(WOC + '/zsMap/pageDeviceVirtualGpuInfoList', config)
}

/**
 * @name 物理卡列表
 */
export const deviceMetricsListApi = (config?: any) => {
  return http.post(WOC + '/zsMap/deviceMetricsList', config)
}

/**
 * @name 显存、算力利用率 按照ip归类
 */
export const queryDeviceMetricsByIpApi = (config?: any) => {
  return http.post(WOC + '/zsMap/queryDeviceMetricsByIp', config)
}

/**
 * @name 导出物理卡列表
 */
export const exportDeviceGpuInfoApi = (config?: any) => {
  return http.post(WOC + '/zsMap/exportDeviceGpuInfo', config, { responseType: 'blob' })
}

/**
 * @name 算力详情的字典
 * 接口deviceGpuDic，参数type，get请求
 * 算力详情的归属部门-dept
 * 算力详情的所属业务-business
 * 算力切片的所属业务-virtualBusiness
 */
export const deviceGpuDicApi = (config?: any) => {
  return http.get<any>(WOC + '/zsMap/deviceGpuDic', config)
}

/**
 * @name 导出切片卡列表
 */
export const exportDeviceVirtualGpuInfoApi = (config?: any) => {
  return http.post(WOC + '/zsMap/exportDeviceVirtualGpuInfo', config, { responseType: 'blob' })
}

/**
 * @name 获取卡类型的显存利用率和算力利用率
 */
export const cardUtilizationApi = (config: CardUtilizationRequest) => {
  return http.post<CardUtilizationItem[]>(WOC + '/zs2map/cardUtilization', config)
}

/**
 * @name 获取业务系统利用率排行
 */
export const businessSystemRankingApi = (config: BusinessSystemRankingRequest) => {
  return http.post<BusinessSystemRankingResponse>(WOC + '/zs2map/businessSystemRanking', config)
}
