<template>
  <div>
    <sl-form
      class="goods-info-form"
      size="small"
      ref="slFormRef"
      :options="goodsInfoOptions"
      :model-value="goods.orderJson"
    >
      <!-- 删除按钮 -->
      <template #globalFormSlot>
        <div @click="handleGoodsDelete" class="goods-del-btn">
          <el-icon><CircleCloseFilled /></el-icon>
        </div>
      </template>
      <template #evsSlot="{ form, item }">
        <el-form-item
          class="eip-item"
          v-for="(eip, eipIndex) in form[item.key]"
          :key="eipIndex"
          :prop="item.key + '.' + eipIndex"
          :rules="evsRules"
        >
          <div class="eip-item-content">
            <el-select
              style="flex: 1"
              clearable
              :disabled="item.disabled"
              v-model="form[item.key][eipIndex][0]"
            >
              <el-option
                :key="option.value"
                v-for="option in item.options"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <el-input-number
              :disabled="item.disabled"
              v-bind="item.props"
              v-model="form[item.key][eipIndex][1]"
              style="margin: 0 4px; min-width: 90px"
            />
            <span>GB</span>
          </div>
        </el-form-item>
      </template>
      <template #isMountEcsSlot="{ form, item }">
        <div class="sle" style="width: 120px" @click="handleEipClick">
          <el-input
            disabled
            :placeholder="item.props.placeholder"
            :value="form[item.key] || ''"
          ></el-input>
        </div>
      </template>
    </sl-form>
    <ecs-dailog
      @confirm="selectEcs"
      v-if="dialogVisible"
      title="选择云主机"
      v-model="dialogVisible"
    ></ecs-dailog>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { IEipModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import ecsDailog from '../ecsDailog.vue'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'
import { validateGoodsName } from '@/views/resourceCenter/utils'

const rouete = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const handleEipClick = () => {
  dialogVisible.value = true
}

const props = defineProps<{
  goods: IGoodsItem<IEipModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: rouete.query.orderId ? true : false,
  })
}
const formModel = props.goods.orderJson
const dialogVisible = ref(false)

const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IEipModel>) {
  goods.ref = slFormRef
}

const validateevsTypeevsSize = (rule: any, value: any, callback: any) => {
  let error = ''
  if (!value[0] && !value[1]) {
    error = '请选择数据盘类型并输入数据盘大小'
  } else if (!value[0] && value[1]) {
    error = '请选择数据盘类型'
  } else if (value[0] && !value[1]) {
    error = '请输入数据盘大小'
  }
  error ? callback(new Error(error)) : callback()
}
function selectEcs(ecsItem: any) {
  formModel.vmId = ecsItem.deviceId
  formModel.ecsName = ecsItem.deviceName
}

const evsRules = [
  {
    validator: validateevsTypeevsSize,
    trigger: 'change',
  },
]

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: '弹性IP名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入弹性IP名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '功能模块',
        type: 'select',
        key: 'functionalModule',
        options: getDic('functionalModule'),
        span: 8,
        rules: {
          required: true,
          message: '请选择功能模块',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '带宽大小',
        type: 'inputNumber',
        key: 'eipValue',
        suffix: 'M',
        props: {
          min: 0,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请输入带宽大小', trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '云主机',
        type: 'slot',
        slotName: 'isMountEcsSlot',
        key: 'ecsName',
        span: 8,
        props: {
          placeholder: '点击选择云主机',
        },
        suffixSlot: true,
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
      {
        label: '开通数量',
        type: 'inputNumber',
        key: 'numbers',
        props: {
          min: 1,
          step: 1,
          max: 100,
          disabled: true,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请选择开通数量', trigger: ['blur', 'change'] },
        ],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.eip-item {
  width: 100%;
  margin-bottom: 15px;
  .eip-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .eip-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
