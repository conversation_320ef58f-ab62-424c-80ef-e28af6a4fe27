import { ref } from 'vue'
import { queryUsersByRoleCode } from '@/api/modules/resourecenter'
export default function useLevelLeaderIdOptions() {
  const levelTwoLeaderIdOptions = ref<any>([]) //二级领导
  const levelThreeLeaderIdOptions = ref<any>([]) // 三级领导
  getLeaderIdOptions('business_depart_leader', levelTwoLeaderIdOptions) // 二级
  getLeaderIdOptions('business_depart_leader2', levelThreeLeaderIdOptions) // 三级
  return {
    levelTwoLeaderIdOptions,
    levelThreeLeaderIdOptions,
  }
}

export async function getLeaderIdOptions(roleCode: string, option: any) {
  const { entity } = await queryUsersByRoleCode({
    roleCode: roleCode,
    domainCode: '',
  })
  if (entity && Array.isArray(entity)) {
    option.value = entity.map((e: any) => ({
      value: e.id,
      label: e.userOrgName,
    }))
  }
}
