<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getVirtualIpList"
    :init-param="queryParams"
    :current-change="currentChange"
    hidden-table-header
    row-key="id"
  >
  </SlProTable>
</template>
<script setup lang="tsx" name="DataList">
import { ref, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getVirtualIpList, deleteVirtualIp } from '@/api/modules/resourecenter'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
  isSelectMode: {
    type: Boolean,
    default: false,
  },
  isBindDialog: {
    type: Boolean,
    default: false,
  },
  hideOperations: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['currentChange', 'selected', 'selectDevice'])

const currentChange = (currentRow: any, oldCurrentRow: any) => {
  emit('currentChange', currentRow, oldCurrentRow)
}

// 动态计算列配置
const columns = computed(() => {
  const baseColumns: ColumnProps<any>[] = [
    { type: 'index', label: '序号', width: 55, fixed: 'left' },
    {
      prop: 'vipName',
      label: '虚拟IP名称',
      fixed: 'left',
      render: ({ row }) => {
        return (
          <el-button onClick={() => handleViewDetail(row)} type="primary" link>
            {row.vipName}
          </el-button>
        )
      },
    },
    { prop: 'businessSystemName', label: '业务系统' },
    { prop: 'catalogueDomainName', label: '云类型' },
    { prop: 'domainName', label: '云平台' },
    { prop: 'regionName', label: '资源池' },
    { prop: 'azName', label: '可用区' },
    { prop: 'vpcName', label: 'VPC/网络' },
    { prop: 'subnetName', label: '子网' },
    { prop: 'ipAddress', label: 'IP地址' },
    { prop: 'createdTime', label: '创建时间', width: 150 },
  ]

  if (!props.hideOperations) {
    // 如果不是选择模式，添加操作列
    if (!props.isSelectMode && !props.isBindDialog) {
      baseColumns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: operationRender,
      })
    } else {
      // 选择模式下添加操作列（绑定操作）
      baseColumns.push({
        prop: 'operation',
        label: '操作',
        width: 100,
        fixed: 'right',
        render: selectOperationRender,
      })
    }
  }

  return baseColumns
})

// 普通操作列渲染函数
function operationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleDelete(row)} type="primary" link>
        删除
      </el-button>
    </>
  )
}

// 选择模式下的操作列渲染函数
function selectOperationRender({ row }: any): VNode {
  return (
    <>
      <el-button onClick={() => handleSelect(row)} type="primary" link>
        绑定
      </el-button>
    </>
  )
}

// 选择虚拟网卡
const handleSelect = (row: any) => {
  emit('selected', row)
  emit('selectDevice', row)
}

const proTable = ref<ProTableInstance>()

const router = useRouter()

// 处理查看详情
const handleViewDetail = (row: any) => {
  // 跳转到虚拟IP详情页面
  router.push({
    path: '/virtualIpDetail',
    query: {
      id: row.id,
    },
  })
}

// 删除虚拟网卡
const handleDelete = async (row: any) => {
  ElMessageBox.confirm(`确定要删除虚拟IP "${row.vipName}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const res = await deleteVirtualIp({
          id: row.id,
        })
        if (res.code == 200) {
          ElMessage.success('删除成功')
          proTable.value?.getTableList()
        }
      } catch (error) {
        console.error('删除虚拟IP失败', error)
      }
    })
    .catch(() => {
      // 取消删除
    })
}

defineExpose({
  proTable,
})
</script>
