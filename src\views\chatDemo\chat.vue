<template>
  <div class="chat-container">
    <div class="chat-header">
      <h2>AI 助手</h2>
    </div>

    <div class="chat-messages" ref="messagesContainer">
      <div
        v-for="(message, index) in messages"
        :key="`message-${index}`"
        :class="['message', message.role]"
      >
        <div class="message-avatar">
          <div v-if="message.role === 'user'" class="user-avatar">👤</div>
          <div v-else class="ai-avatar">🤖</div>
        </div>
        <div class="message-content">
          <div class="message-text" v-html="formatMessage(message.content)"></div>
          <div v-if="message.role === 'assistant' && message.isLoading" class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <div class="chat-input-container">
      <div class="input-wrapper">
        <textarea
          v-model="inputMessage"
          @keydown.enter.prevent.exact="handleEnter"
          @keydown.ctrl.enter="handleCtrlEnter"
          placeholder="输入消息，按Enter发送，Ctrl+Enter换行..."
          class="chat-input"
          :disabled="isLoading"
          ref="inputRef"
        ></textarea>
        <button
          @click="sendMessage"
          :disabled="!inputMessage.trim() || isLoading"
          class="send-button"
        >
          <span v-if="!isLoading">发送</span>
          <span v-else class="loading-spinner">⏳</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { uuid } from '@/utils'
import { marked } from 'marked'

interface Message {
  role: 'user' | 'assistant'
  content: string
  isLoading?: boolean
}

const messages = ref<Message[]>([])
const inputMessage = ref('')
const isLoading = ref(false)
const messagesContainer = ref<HTMLElement>()
const inputRef = ref<HTMLTextAreaElement>()
const memoryId = ref('')

// 生成记忆ID
const generateMemoryId = () => {
  memoryId.value = uuid()
}

// 格式化消息内容，支持markdown渲染
const formatMessage = (content: string) => {
  try {
    // 预处理 Markdown，修复代码块后缺少两个换行的问题
    const normalizedContent = smartCodeBlockFix(content)

    // 配置marked选项
    marked.setOptions({
      breaks: true, // 支持换行
      gfm: true, // 启用GitHub风格markdown
    })

    // 使用marked渲染markdown
    return marked(normalizedContent)
  } catch (error) {
    console.warn('Markdown渲染失败，使用普通文本:', error)
    // 如果markdown渲染失败，回退到普通文本处理
    return content.replace(/\n/g, '<br>')
  }
}

// 修复 Markdown 代码块后缺少两个换行的问题
const normalizeMarkdown = (content: string) => {
  if (!content) return content

  return (
    content
      // 修复代码块结束后缺少两个换行的问题
      .replace(/```(\s*?)([^\n]|$)/g, '```\n\n$2')
      // 确保代码块和下一个内容之间有两个换行
      .replace(/```\n([^#\n])/g, '```\n\n$1')
      // 修复代码块后直接跟标题的情况
      .replace(/```\n(#+\s)/g, '```\n\n$1')
      // 修复代码块后直接跟列表的情况
      .replace(/```\n([*+-]\s)/g, '```\n\n$1')
      // 修复代码块后直接跟数字列表的情况
      .replace(/```\n(\d+\.\s)/g, '```\n\n$1')
  )
}

// 智能检测和处理代码块边界
const smartCodeBlockFix = (content: string) => {
  if (!content) return content

  // 计算代码块标记的数量
  const codeBlockMatches = content.match(/```/g)
  const codeBlockCount = codeBlockMatches ? codeBlockMatches.length : 0

  // 如果代码块标记是奇数，说明代码块未闭合，暂时不处理
  if (codeBlockCount % 2 === 1) {
    return content
  }

  // 如果代码块标记是偶数，说明代码块已闭合，可以进行规范化
  return normalizeMarkdown(content)
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 监听消息变化，自动滚动
watch(
  messages,
  () => {
    scrollToBottom()
  },
  { deep: true },
)

// 处理Enter键
const handleEnter = () => {
  if (!inputMessage.value.trim() || isLoading.value) return
  sendMessage()
}

// 处理Ctrl+Enter键
const handleCtrlEnter = () => {
  inputMessage.value += '\n'
}

// 发送消息
const sendMessage = async () => {
  const message = inputMessage.value.trim()
  if (!message || isLoading.value) return

  // 添加用户消息
  messages.value.push({
    role: 'user',
    content: message,
  })

  // 添加AI消息占位符
  const aiMessageIndex = messages.value.length
  messages.value.push({
    role: 'assistant',
    content: '',
    isLoading: true,
  })

  // 清空输入框
  inputMessage.value = ''
  isLoading.value = true

  try {
    await streamChat(message)
  } catch (error) {
    console.error('发送消息失败:', error)
    messages.value[aiMessageIndex].content = '抱歉，发送消息时出现错误，请重试。'
  } finally {
    isLoading.value = false
    messages.value[aiMessageIndex].isLoading = false
  }
}

// SSE流式传输
// SSE流式传输 - 修复版本
const streamChat = async (message: string) => {
  const url = 'http://188.104.1.38:8081/api/chat/stream/chat'

  const requestBody = {
    message: message,
    memoryId: memoryId.value,
    sourceType: '新华三-S5130S-54S-HI-G',
  }

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const reader = response.body?.getReader()
  if (!reader) {
    throw new Error('无法获取响应流')
  }

  const decoder = new TextDecoder()
  let buffer = ''

  try {
    while (true) {
      const { done, value } = await reader.read()

      if (done) break

      buffer += decoder.decode(value, { stream: true })
      const lines = buffer.split('\n')
      buffer = lines.pop() || ''

      for (const line of lines) {
        // 跳过空行
        if (!line.trim()) continue

        // 处理data:格式的数据（这是您服务器的格式）
        if (line.startsWith('data:')) {
          // 提取data:后面的内容
          let content = line.slice(5) // 移除'data:'前缀

          // 跳过结束标记
          if (content === '[DONE]') {
            continue
          }

          // 如果data:后面没有数据，代表换行
          if (content === '' || content.trim() === '') {
            const lastMessage = messages.value[messages.value.length - 1]
            if (lastMessage && lastMessage.role === 'assistant') {
              lastMessage.content += '\n'
            }
            continue
          }

          // 直接将内容添加到消息中（保留所有空格）
          const lastMessage = messages.value[messages.value.length - 1]
          if (lastMessage && lastMessage.role === 'assistant') {
            // 添加内容并实时规范化 Markdown
            lastMessage.content += content
            // 实时修复代码块边界问题，使用智能检测
            lastMessage.content = smartCodeBlockFix(lastMessage.content)
          }
        }
      }
    }
  } finally {
    reader.releaseLock()
  }
}

// 组件挂载时生成记忆ID
onMounted(() => {
  generateMemoryId()
  // 聚焦输入框
  nextTick(() => {
    inputRef.value?.focus()

    // messages.value.push({
    //   role: 'assistant',
    //   content:
    //     '根据您提供的信息，要实现Secondary VLAN 4内的端口二层隔离，并且让GigabitEthernet1/0/1工作在promiscuous模式下，同时将GigabitEthernet1/0/2及GigabitEthernet1/0/3设置为host模式，可以按照以下步骤进行配置。此外，还需要建立Primary VLAN 2与Secondary VLAN 4之间的映射关系。以下是详细的配置命令：\n1. **进入系统视图并创建VLAN：**   - 创建Primary VLAN 2和Secondary VLAN 4。   ```shell\n   <Sysname> system-view   [Sysname] vlan 4\n   [Sysname-vlan4] quit\n [Sysname] vlan 2   [Sysname-vlan2] private-vlan primary   [Sysname-vlan2] private-vlan secondary 4\n   [Sysname-vlan2] quit   ```\n2. **配置GigabitEthernet1/0/1端口为promiscuous模式：**\n   - 这个端口需要能够接收所有属于Primary VLAN 2的数据包。\n ```shell   [Sysname] interface gigabitethernet 1/0/1   [Sysname-GigabitEthernet1/0/1] port private-vlan 2 promiscuous   [Sysname-GigabitEthernet1/0/1] quit\n   ```\n3. **配置GigabitEthernet1/0/2和GigabitEthernet1/0/3端口为host模式：**   - 这些端口只允许与其直接相关的Secondary VLAN通信。   ```shell   [Sysname] interface gigabitethernet 1/0/2\n   [Sysname-GigabitEthernet1/0/2] port access vlan 4   [Sysname-GigabitEthernet1/0/2] port private-vlan host\n [Sysname-GigabitEthernet1/0/2] quit\n   [Sysname] interface gigabitethernet 1/0/3   [Sysname-GigabitEthernet1/0/3] port access vlan 4   [Sysname-GigabitEthernet1/0/3] port private-vlan host\n   [Sysname-GigabitEthernet1/0/3] quit   ```\n完成上述配置后，GigabitEthernet1/0/2和GigabitEthernet1/0/3这两个端口之间将被二层隔离，即它们不能直接相互通信，但都能通过GigabitEthernet1/0/1这个promiscuous端口访问外部网络或其它位于Primary VLAN 2下的设备。\n\n如果您有更多关于此配置的问题或需要进一步的帮助，请随时告诉我！',
    //   isLoading: true,
    // })
  })
})
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 900px;
  margin: 0 auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.chat-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.chat-header {
  background: rgba(255, 255, 255, 0.9);
  padding: 24px 32px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.chat-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.message {
  display: flex;
  gap: 16px;
  max-width: 100%;
  animation: fadeInUp 0.3s ease-out;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.8);
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.message-content {
  flex: 1;
  max-width: 75%;
}

.message.user .message-content {
  text-align: right;
}

.message-text {
  background: rgba(255, 255, 255, 0.9);
  padding: 16px 20px;
  border-radius: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  line-height: 1.6;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

/* Markdown样式 */
.message-text :deep(h1),
.message-text :deep(h2),
.message-text :deep(h3),
.message-text :deep(h4),
.message-text :deep(h5),
.message-text :deep(h6) {
  margin: 16px 0 8px 0;
  color: inherit;
  font-weight: 600;
}

.message-text :deep(p) {
  margin: 8px 0;
}

.message-text :deep(code) {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  color: #e83e8c;
}

.message-text :deep(pre) {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid #e9ecef;
  border-left: 4px solid #667eea;
}

.message-text :deep(pre code) {
  background: none;
  padding: 0;
  color: #333;
  font-size: 0.9em;
  line-height: 1.4;
}

.message-text :deep(blockquote) {
  border-left: 4px solid #667eea;
  padding-left: 16px;
  margin: 12px 0;
  color: #666;
}

.message-text :deep(ul),
.message-text :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.message-text :deep(li) {
  margin: 4px 0;
}

.message-text :deep(strong) {
  font-weight: 600;
}

.message-text :deep(em) {
  font-style: italic;
}

.message-text :deep(a) {
  color: #667eea;
  text-decoration: none;
}

.message-text :deep(a:hover) {
  text-decoration: underline;
}

.message.user .message-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.message.assistant .message-text {
  background: rgba(255, 255, 255, 0.95);
  color: #2c3e50;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-indicator {
  display: flex;
  gap: 6px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  margin-top: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.typing-indicator span {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input-container {
  background: rgba(255, 255, 255, 0.9);
  padding: 24px 32px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  background: #fff;
  border-radius: 24px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.chat-input {
  flex: 1;
  min-height: 48px;
  max-height: 120px;
  padding: 14px 20px;
  border: none;
  border-radius: 20px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  outline: none;
  background: transparent;
  transition: all 0.2s ease;
}

.chat-input:focus {
  background: #f8f9fa;
}

.chat-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.send-button {
  height: 48px;
  padding: 0 24px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.send-button:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.loading-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    max-width: 100%;
  }

  .message-content {
    max-width: 85%;
  }

  .chat-input-container {
    padding: 15px;
  }
}
</style>
