import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import type { GetResourceListResType } from '@/hooks/useTreeWorkOrder'

/**
 * @name 资源列表-非标工单
 */
export interface NonStandardOrderWorkorderType {
  [key: string]: any
  activityTask: GetResourceListResType | undefined
}

/*
information_archive_h  存档 超出
information_archive_l  存档 低于
offline_open_h     离线开通 超出
offline_open_l   离线开通 低于
network_provisioning_h  开通网络 超出
network_provisioning_l 开通网络 低于
resource_creation_h    资源开通 超出
resource_creation_l   资源开通 低于

response_scheme_manager // 响应方案经理审核

*/

// 定义任务类型
/* type TaskType =
  | 'response_scheme_manager'
  | 'information_archive_h'
  | 'information_archive_l'
  | 'offline_open_h'
  | 'offline_open_l'
  | 'network_provisioning_h'
  | 'network_provisioning_l'
  | 'resource_creation_h'
  | 'resource_creation_l'
   */
type TaskType =
  | 'response_scheme_manager'
  | 'information_archive'
  | 'offline_open'
  | 'network_provisioning'
  | 'resource_creation'
  | 'end'
/**
 *  @description 权限按钮类型
 */
export type NonStandardOrderBtnsType = {
  [key in TaskType]: boolean
}

export type NonStandardOrderType = 'ecs' | 'gcs' | 'bms' | 'gms' | 'mysql' | 'postgreSql' | 'redis'

export type NonStandardOrderColumnsType<T = any> = {
  [key in NonStandardOrderType]: ColumnProps<T>[]
}

export type NonStandardOrderTabsType = {
  label: string
  name: NonStandardOrderType
  count: number
  list: FormDataType[]
} & FormDataType

export type NonStandardOrderProTableType = {
  [key in NonStandardOrderType]: ProTableInstance | null
}
