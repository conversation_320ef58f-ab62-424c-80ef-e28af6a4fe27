<template>
  <div class="table-box">
    <sl-page-header
      title="云有共享盘"
      title-line="云有共享盘（Share Elastic Volume Service）是一种支持多台云服务器并发读写访问的虚拟块存储设备，可实现跨设备文件共享与数据同步。"
      :icon="{
        class: 'pege_yunyingpan',
        color: '#0052D9',
        size: '40px',
      }"
    >
      <template #custom>
        <sl-base-tabs
          :tabs="availableTabs"
          v-model="activeTab"
          v-if="!shouldHideTabs"
        ></sl-base-tabs>
      </template>
    </sl-page-header>
    <div class="resource-tab" v-if="activeTab === 'INNER'">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleBatchRecycle" type="primary" v-permission="'BatchRecycle'">
          批量回收
        </el-button>
        <el-button @click="handleBatchChange" type="primary" v-permission="'Change'">
          资源变更
        </el-button>
        <el-button @click="handleBatchDelay" type="primary" v-permission="'Delay'">
          资源延期
        </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: collapsed }"
          ref="formRef"
          :options="formOptions"
          v-model="formModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <dataList
          ref="dataListRef"
          :query-params="queryParams"
          :hide-operations="shouldHideResourceOperations"
        ></dataList>
      </div>
    </div>
    <div v-if="activeTab === 'DG'">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleBatchUnsubscribe" type="primary" v-permission="'BatchUnsubscribe'">
          批量退订
        </el-button>
        <el-button @click="handleDgBatchChange" type="primary" v-permission="'DgChange'">
          资源变更
        </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: dgCollapsed }"
          ref="dgFormRef"
          :options="DGFormOptions"
          v-model="dgFormModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <PublicDataList
          ref="PublicDataListRef"
          :query-params="dgQueryParams"
          :hide-operations="shouldHideResourceOperations"
        ></PublicDataList>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { Delete, Search, ArrowDown, ArrowUp, Upload } from '@element-plus/icons-vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import dataList from './dataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { useDichooks } from '../hooks/useDichooks'
import { normalizeExportArgs } from '@/views/resourceCenter/utils'
import { useDownload } from '@/hooks/useDownload'
import { resourceExport } from '@/api/modules/resourecenter'
import { useRolePermission } from '../hooks/useRolePermission'
import PublicDataList from './PublicDataList.vue'
import { useResourceTabs } from '../hooks/useResourceTabs'

// 获取路由信息
const route = useRoute()

const { shouldHideResourceOperations } = useRolePermission()

// 使用统一的tabs管理hook
const { availableTabs, activeTab } = useResourceTabs()
const shouldHideTabs = ref(true)
// 筛选条件下拉列表
const { busiSystemOptions } = useBusiSystemOptions()
const { resourcePoolsDic } = useDichooks()
const { resourcePoolsDic: dgResourcePoolsDic } = useDichooks({
  resourcePools: {
    realmType: 'iaas',
    domainCodes: [
      'plf_prov_moc_zj_vmware',
      'plf_prov_moc_zj_h3c',
      'plf_prov_moc_zj_huawei',
      'plf_prov_moc_zj_inspur',
    ],
  },
})
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const formRef = ref<any>(null)

// 从query参数中获取业务系统的初始值
const getBusinessSysIdsFromQuery = () => {
  const businessSysIds = route.query.businessSysIds
  if (businessSysIds && typeof businessSysIds === 'string') {
    // 字符串格式，用逗号分割
    return businessSysIds.split(',').filter((id: string) => id.trim())
  }
  return []
}

const formModel = reactive<any>({
  businessSysIds: getBusinessSysIdsFromQuery(),
})

const queryParams = ref<any>({ type: 'shareEvs', ...formModel })
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}
function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '云主机',
        type: 'input',
        key: 'ecsName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSysIds',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
        props: {
          select: {
            multiple: true,
            filterable: true,
            clearable: true,
            collapseTags: true,
          },
        },
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'shareEvs', '云有共享盘.xlsx', {
                      ...formModel,
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '数据盘',
        type: 'input',
        key: 'dataDisk',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '是否挂载云主机',
        type: 'select',
        key: 'mountOrNot',
        options: [
          { label: '是', value: '是' },
          { label: '否', value: '否' },
        ],
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'applyTime',
        options: getDic('time'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '工单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '开通时间',
        type: 'date',
        key: 'effectiveTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        label: '到期时间',
        type: 'date',
        key: 'expireTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        label: '申请人',
        type: 'input',
        key: 'applyUserName',
        span: 8,
        disabled: false,
        hidden: true,
      },
    ],
  },
])

// 批量回收
const dataListRef = ref()
const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}

// 资源变更
const handleBatchChange = () => {
  dataListRef.value?.handleResourceChange()
}

// 资源延期
const handleBatchDelay = () => {
  dataListRef.value?.handleResourceDelay()
}

// 对公列表筛选条件及配置
const dgFormRef = ref<any>(null)
const dgQueryParams = ref<any>({ type: 'shareEvs', sourceType: 'DG' })

const dgFormModel = reactive({})
function resetDgSearch() {
  dgFormRef.value!.resetFields()
  dgQueryParams.value = { ...dgQueryParams.value, ...dgFormModel }
}
function doDgSearch() {
  dgQueryParams.value = { ...dgQueryParams.value, ...dgFormModel }
}

// 是否默认折叠搜索项
const dgCollapsed = ref(true)
const DGFormOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '云主机',
        type: 'input',
        key: 'ecsName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={dgFormModel}
                  resourceList={DGFormOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={resetDgSearch} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={doDgSearch} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'shareEvs', '云有共享盘.xlsx', {
                      ...dgFormModel,
                      sourceType: 'DG',
                      disOrderStatus: 0,
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (dgCollapsed.value = !dgCollapsed.value)}
              >
                {dgCollapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {dgCollapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '数据盘',
        type: 'input',
        key: 'dataDisk',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '是否挂载云主机',
        type: 'select',
        key: 'mountOrNot',
        options: [
          { label: '是', value: '是' },
          { label: '否', value: '否' },
        ],
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: dgResourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订购时间',
        type: 'date',
        key: 'effectiveTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        label: '订购人',
        type: 'input',
        key: 'applyUserName',
        span: 8,
        disabled: false,
        hidden: true,
      },
    ],
  },
])

const PublicDataListRef = ref()
// 批量退订
const handleBatchUnsubscribe = () => {
  PublicDataListRef.value?.handleBatchUnsubscribe()
}

// 对公资源变更
const handleDgBatchChange = () => {
  PublicDataListRef.value?.handleResourceChange(true)
}
</script>
<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
