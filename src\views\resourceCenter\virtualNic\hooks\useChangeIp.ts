import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getUnusedVirtualIpList, changeVirtualNicIp } from '@/api/modules/resourecenter'

export function useChangeIp() {
  // 变更IP相关变量
  const changeIpDialogVisible = ref(false)
  const changeIpForm = ref({
    ip: '',
    vnicOrderId: '',
  })
  const ipOptions = ref<string[]>([])
  const submitLoading = ref(false)
  const currentRow = ref<any>(null)

  // 处理变更IP
  const handleChangeIp = async (row: any) => {
    currentRow.value = row
    changeIpForm.value.vnicOrderId = row.id
    changeIpForm.value.ip = ''

    try {
      // 获取未使用的IP列表
      const response = await getUnusedVirtualIpList({
        subnetId: row.subnetId,
      })

      if (response.code === 200 && response.entity) {
        ipOptions.value = response.entity
        changeIpDialogVisible.value = true
      } else {
        ElMessage.error('获取IP列表失败')
      }
    } catch (error) {
      console.error('获取未使用IP列表失败:', error)
      ElMessage.error('获取IP列表失败')
    }
  }

  // 提交变更IP
  const handleSubmitChangeIp = async (refreshCallback?: () => void) => {
    if (!changeIpForm.value.ip) {
      ElMessage.warning('请选择IP地址')
      return
    }

    submitLoading.value = true

    try {
      const response = await changeVirtualNicIp({
        vnicOrderId: changeIpForm.value.vnicOrderId,
        ip: changeIpForm.value.ip,
      })

      if (response.code === 200) {
        ElMessage.success('IP变更成功')
        changeIpDialogVisible.value = false
        // 调用刷新回调函数
        if (refreshCallback) {
          refreshCallback()
        }
      } else {
        ElMessage.error(response.message || 'IP变更失败')
      }
    } catch (error) {
      console.error('变更IP失败:', error)
      ElMessage.error('IP变更失败')
    } finally {
      submitLoading.value = false
    }
  }

  // 关闭弹窗
  const closeChangeIpDialog = () => {
    changeIpDialogVisible.value = false
    changeIpForm.value.ip = ''
    changeIpForm.value.vnicOrderId = ''
    currentRow.value = null
  }

  return {
    // 响应式变量
    changeIpDialogVisible,
    changeIpForm,
    ipOptions,
    submitLoading,
    currentRow,

    // 方法
    handleChangeIp,
    handleSubmitChangeIp,
    closeChangeIpDialog,
  }
}
