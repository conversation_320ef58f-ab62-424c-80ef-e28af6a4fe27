<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { useGlobalStore } from './stores/modules/global'
import { useWorkOrderStore } from './stores/modules/workOrder'
import { computed, reactive, onMounted, onUnmounted } from 'vue'
import { shouldUpdate } from './utils'
import { ElMessageBox } from 'element-plus'
import useKeepAlive from '@/hooks/useKeepAlive'

// 引入工单store
const workOrderStore = useWorkOrderStore()

// 初始化工单轮询
onMounted(() => {
  workOrderStore.initOrderCountPolling()
  console.log('onMounted')
})

// 在组件卸载时停止轮询
onUnmounted(() => {
  workOrderStore.stopPolling()
})

let modaled = false
setInterval(
  () => {
    shouldUpdate().then((res) => {
      if (res && !modaled) {
        modaled = true
        ElMessageBox.confirm(
          '🎉我们刚刚发布了功能更完善的版本~，为保证您流畅使用所有新特性，建议您刷新页面',
          {
            confirmButtonText: '刷新',
            showCancelButton: true,
            cancelButtonText: '稍后',
            closeOnClickModal: false,
            showClose: false,
            customClass: 'update-modal',
          },
        ).then(() => {
          modaled = false
          window.location.reload()
        })
      }
    })
  },
  1000 * 60 * 30,
)

const globalStore = useGlobalStore()
// element assemblySize
const assemblySize = computed(() => globalStore.assemblySize)

// element button config
const buttonConfig = reactive({ autoInsertSpace: false })

//缓存组件
useKeepAlive()
</script>

<template>
  <el-config-provider :locale="zhCn" :size="assemblySize" :button="buttonConfig">
    <router-view></router-view>
  </el-config-provider>
</template>

<style scoped></style>
