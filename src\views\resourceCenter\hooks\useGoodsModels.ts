import { useUserStore } from '@/stores/modules/user'
import { type Ref } from 'vue'

export interface IEcsModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  functionMedelLabel: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  evsLabel: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  timeLabel: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'ecs'

  // @ productType 对应的产品名称
  goodsName: '云主机'
  goodsId: string

  // 表单实例 提交需删
  ref?: Ref<any> | null
}
export interface IMysqlModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  functionMedelLabel: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  evsLabel: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  timeLabel: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'mysql'
  deployType: 'ALONE' | 'COLONY'
  // @ productType 对应的产品名称
  goodsName: 'MySQL云数据库'
  goodsId: string
  // 表单实例 提交需删
  ref?: Ref<any> | null
}

export interface IPostgreSqlModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  functionMedelLabel: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  evsLabel: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  timeLabel: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'postgreSql'
  deployType: 'ALONE' | 'COLONY'
  // @ productType 对应的产品名称
  goodsName: 'PostgreSQL云数据库'
  goodsId: string
  // 表单实例 提交需删
  ref?: Ref<any> | null
}

export interface IRedisModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  functionMedelLabel: string
  // @实例规格
  ecs: any[]
  ecsLabel: string
  // @系统
  imageOs: any[]
  imageOsLabel: string
  // @系统盘
  sysDisk: any[]
  sysDiskLabel: string
  // @是否容灾
  disasterRecovery: string
  disasterRecoveryLabel: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  evsLabel: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  planeLabel: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  timeLabel: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'redis'

  // @ productType 对应的产品名称
  goodsName: '通用Redis'
  goodsId: string
  // 表单实例 提交需删
  ref?: Ref<any> | null
}

export interface IGcsModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @实例规格
  gcs: any[]
  // @系统
  imageOs: any[]
  // @系统盘
  sysDisk: any[]
  // @是否容灾
  disasterRecovery: string
  // @是否挂载数据盘
  isMountEvs: '0' | '1'
  // @数据盘
  evs: any[][]
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @网络平面
  planeValue: any[]
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'gcs'
  goodsId: string
  // @ productType 对应的产品名称
  goodsName: 'GPU云主机'
}
export interface IEvsModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @数据盘
  evs: any[][]
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'evs'

  isMountEcs: '0' | '1'

  // @ productType 对应的产品名称
  goodsName: '云硬盘'
  goodsId: string
  ecsName: string
  vmId: string
}
export interface IShareEvsModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @数据盘
  evs: any[][]
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'shareEvs'

  isMountEcs: '0' | '1'

  // @ productType 对应的产品名称
  goodsName: '共享数据盘'
  goodsId: string
}
export interface IEipModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @产品类型
  productType: 'eip'

  // @ productType 对应的产品名称
  goodsName: '弹性IP'
  goodsId: string
  ecsName: string
  vmId: string
  eipValue: number
  ipVersion: 'IPv4' | 'IPv6'
}
export interface IObsModel {
  goodsId: string
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @是否挂载数据盘
  isMountobs: '1' | '0'
  // @数据盘
  obs: [string, number]
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'obs'

  // @ productType 对应的产品名称
  goodsName: '对象存储'
}
export interface ISlbModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @实例规格
  slb: string
  desc: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'slb'
  // @ productType 对应的产品名称
  goodsName: '负载均衡'
  goodsId: string
}
export interface INatModel {
  // @产品名称
  instanceName: string
  // @功能模块
  functionalModule: string
  // @实例规格
  nat: string
  desc: string
  // @是否绑定公网IP
  isBindPublicNetworkIp: '0' | '1'
  // @带宽
  eipAttrId: number
  eipValue: number
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @备注
  remarks: string
  // @产品类型
  productType: 'nat'

  // @ productType 对应的产品名称
  goodsName: 'NAT网关'
  goodsId: string
}
export interface IKafkaModel {
  // @产品名称
  instanceName: string
  // 数据流量
  dataFlow: number
  // 分区数
  partition: number
  // 副本数
  replication: number
  // 保留时间
  retentionTime: number
  // 数据存储总量
  dataStorage: number
  // @申请时长
  time: string
  // @产品类型
  productType: 'kafka'

  // @ productType 对应的产品名称
  goodsName: 'Kafka'
  goodsId: string
}
export interface IEsModel {
  // @产品名称
  instanceName: string
  // 日均增量数据
  dailyIncrementData: number
  // 副本数
  replication: number
  // 保留时间
  retentionTime: number
  // 磁盘大小
  diskSize: number
  // @申请时长
  time: string
  // 模板
  template: string
  // @产品类型
  productType: 'es'

  // @ productType 对应的产品名称
  goodsName: 'ElasticSearch'
  goodsId: string
}
export interface IFlinkModel {
  // @产品名称
  instanceName: string
  // vCPU
  vCPU: number
  // 内存
  memory: number
  // @申请时长
  time: string
  // @产品类型
  productType: 'flink'

  // @ productType 对应的产品名称
  goodsName: 'Flink'
  goodsId: string
}
export interface IBldRedisModel {
  // @产品名称
  instanceName: string
  // @实例IP
  ip: string
  // 申请时长
  time: string
  // @产品类型
  productType: 'bldRedis'
  cpuArch: string
}
export interface IPmModel {
  // @产品名称
  instanceName: string
  // 物理CPU
  cpu: number
  // 物理内存
  memory: number
  // 物理硬盘
  disk: number
  // 是否使用GPU
  isUseGpu: '0' | '1'
  // GPU型号
  gpuType: string
  // 卡类型
  gpuCardType: string
  // GPU数量
  gpuCount: number
  // 申请时长
  time: string
  // @产品类型
  productType: 'pm'

  // @ productType 对应的产品名称
  goodsName: '裸金属'
  goodsId: string
  placeholder: string
}
export interface INasModel {
  // @产品名称
  instanceName: string
  storagePath: string
  storageSize: number
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @产品类型
  productType: 'nas'

  // @ productType 对应的产品名称
  goodsName: 'NAS'
  goodsId: string
}
export interface IVpnModel {
  // @产品名称
  instanceName: string
  maxClient: number
  bandwidth: number
  // @开通数量
  numbers: number
  // @申请时长
  time: string
  // @产品类型
  productType: 'vpn'

  // @ productType 对应的产品名称
  goodsName: 'VPN'
  goodsId: string
}
export interface ICqModel {
  // 产品名称
  instanceName: string
  gpu: [number, number, number]
  cpu: [number, number]
  // 4A账号数
  a4Accoun: string
  // 4A手机号
  a4Phone: string
  // 是否使用GPU
  isUseGpu: '0' | '1'
  // 申请时长
  time: string
}
export interface IBaseModel {
  applicant: string
  department: string
  workOrdertype: string
  orderTitle: string
  // @工单id
  id: string
  // @业务系统id
  busiSystemId: string
  // 局方负责人
  applyUserName: string
  // @厂家
  manufacturer: string
  // @厂家负责人
  manufacturerContacts: string
  // @厂家电话
  manufacturerMobile: string
  // @所属业务模块
  moduleId: string
  // @二级部门领导
  busiDepartLeaderId: string
  // @三级部门领导
  levelThreeLeaderId: string
  // @资源申请说明
  orderDesc: string
  // @资源上云说明书
  files: any[]

  // @操作名称
  operationName: string
  moduleName: string
  busiDepartLeaderLabel: string
  levelThreeLeaderLabel: string
  busiSystemName: string
  // 4A账号数
  a4Account?: string
  // 4A手机号
  a4Phone?: string
}

export interface IBackupModel {
  // 产品名称
  jobName: string
  // 备份类型
  backupType: string
  // 备份频率
  frequency: string
  // 星期
  daysOfWeek: number | undefined
  // 需要备份的云硬盘/云硬盘id
  objectIdList: string[]
}

export function useKafkaModel() {
  const model: IKafkaModel = {
    // @产品名称
    instanceName: '',
    // 数据流量
    dataFlow: 0,
    // 分区数
    partition: 0,
    // 副本数
    replication: 0,
    // 保留时间
    retentionTime: 0,
    // 数据存储总量
    dataStorage: 0,
    // @申请时长
    time: '',
    // @产品类型
    productType: 'kafka',
    // @ productType 对应的产品名称
    goodsName: 'Kafka',
    goodsId: '',
  }
  return model
}

export function useEsModel() {
  const model: IEsModel = {
    // @产品名称
    instanceName: '',
    // 日均增量数据
    dailyIncrementData: 0,
    // 副本数
    replication: 0,
    // 保留时间
    retentionTime: 0,
    // 磁盘大小
    diskSize: 0,
    // @申请时长
    time: '',
    // 模板
    template: '',
    // @产品类型
    productType: 'es',
    // @ productType 对应的产品名称
    goodsName: 'ElasticSearch',
    goodsId: '',
  }
  return model
}

export function useFlinkModel() {
  const model: IFlinkModel = {
    // @产品名称
    instanceName: '',
    // vCPU
    vCPU: 0,
    // 内存
    memory: 0,
    // @申请时长
    time: '',
    // @产品类型
    productType: 'flink',
    // @ productType 对应的产品名称
    goodsName: 'Flink',
    goodsId: '',
  }
  return model
}

export function useBldRedisModel() {
  const model: IBldRedisModel = {
    // @产品名称
    instanceName: '',
    // @实例IP
    ip: '',
    // @申请时长
    time: '',
    // @产品类型
    productType: 'bldRedis',
    cpuArch: '',
  }
  return model
}

export function usePmModel() {
  const model: IPmModel = {
    // @产品名称
    instanceName: '',
    // 物理CPU
    cpu: 0,
    // 物理内存
    memory: 0,
    // 物理硬盘
    disk: 0,
    // 是否使用GPU
    isUseGpu: '0',
    // GPU型号
    gpuType: '',
    // 卡类型
    gpuCardType: '',
    // GPU数量
    gpuCount: 0,
    // @申请时长
    time: '',
    // @产品类型
    productType: 'pm',
    // @ productType 对应的产品名称
    goodsName: '裸金属',
    goodsId: '',
    placeholder: 'placeholder',
  }
  return model
}

export function useBackupModel() {
  const model: IBackupModel = {
    // 产品名称
    jobName: '',
    // 备份类型
    backupType: '',
    // 备份频率
    frequency: '',
    // 星期
    daysOfWeek: undefined,
    // 需要备份的云硬盘/云硬盘id
    objectIdList: [],
  }
  return model
}

export function useEcsModel() {
  const model: IEcsModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    functionMedelLabel: '',
    // @实例规格
    ecs: [],
    ecsLabel: '',
    // @系统
    imageOs: [],
    imageOsLabel: '',
    // @系统盘
    sysDisk: ['', 40],
    sysDiskLabel: '',
    // @是否容灾
    disasterRecovery: '-1',
    disasterRecoveryLabel: '',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    evsLabel: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    planeLabel: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    timeLabel: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'ecs',

    // @ productType 对应的产品名称
    goodsName: '云主机',
    goodsId: '',
  }
  return model
}
export function useRedisModel() {
  const model: IRedisModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    functionMedelLabel: '',
    // @实例规格
    ecs: [],
    ecsLabel: '',
    // @系统
    imageOs: [],
    imageOsLabel: '',
    // @系统盘
    sysDisk: ['', 40],
    sysDiskLabel: '',
    // @是否容灾
    disasterRecovery: '-1',
    disasterRecoveryLabel: '',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    evsLabel: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    planeLabel: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    timeLabel: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'redis',

    // @ productType 对应的产品名称
    goodsName: '通用Redis',
    goodsId: '',
  }
  return model
}
export function useMysqlModel() {
  const model: IMysqlModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    functionMedelLabel: '',
    // @实例规格
    ecs: [],
    ecsLabel: '',
    // @系统
    imageOs: [],
    imageOsLabel: '',
    // @系统盘
    sysDisk: ['', 40],
    sysDiskLabel: '',
    // @是否容灾
    disasterRecovery: '-1',
    disasterRecoveryLabel: '',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    evsLabel: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    planeLabel: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    timeLabel: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'mysql',
    deployType: 'ALONE',
    // @ productType 对应的产品名称
    goodsName: 'MySQL云数据库',
    goodsId: '',
  }
  return model
}
export function usePostgreSqlModel() {
  const model: IPostgreSqlModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    functionMedelLabel: '',
    // @实例规格
    ecs: [],
    ecsLabel: '',
    // @系统
    imageOs: [],
    imageOsLabel: '',
    // @系统盘
    sysDisk: ['', 40],
    sysDiskLabel: '',
    // @是否容灾
    disasterRecovery: '-1',
    disasterRecoveryLabel: '',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    evsLabel: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    planeLabel: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    timeLabel: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'postgreSql',
    deployType: 'ALONE',
    // @ productType 对应的产品名称
    goodsName: 'PostgreSQL云数据库',
    goodsId: '',
  }
  return model
}
export function useGcsModel() {
  const model: IGcsModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @实例规格
    gcs: [],
    // @系统
    imageOs: [],
    // @系统盘
    sysDisk: ['', 40],
    // @是否容灾
    disasterRecovery: '-1',
    // @是否挂载数据盘
    isMountEvs: '0',
    // @数据盘
    evs: [[]],
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @网络平面
    planeValue: [],
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'gcs',
    goodsId: '',

    // @ productType 对应的产品名称
    goodsName: 'GPU云主机',
  }
  return model
}
export function useEvsModel() {
  const model: IEvsModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @数据盘
    evs: [['', 20]],
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'evs',

    isMountEcs: '0',

    // @ productType 对应的产品名称
    goodsName: '云硬盘',
    goodsId: '',
    ecsName: '',
    vmId: '',
  }
  return model
}
export function useShareEvsModel() {
  const model: IShareEvsModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @数据盘
    evs: [['', 20]],
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'shareEvs',

    isMountEcs: '0',

    // @ productType 对应的产品名称
    goodsName: '共享数据盘',
    goodsId: '',
  }
  return model
}
export function useEipModel() {
  const model: IEipModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @产品类型
    productType: 'eip',

    // @ productType 对应的产品名称
    goodsName: '弹性IP',
    goodsId: '',
    ecsName: '',
    vmId: '',
    eipValue: 1,
    ipVersion: 'IPv4',
  }
  return model
}

export function useSlbModel() {
  const model: ISlbModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @实例规格
    slb: '',
    desc: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'slb',

    // @ productType 对应的产品名称
    goodsName: '负载均衡',
    goodsId: '',
  }
  return model
}
export function useCqModel() {
  const model: ICqModel = {
    // 产品名称
    instanceName: '',
    cpu: [0, 0],
    gpu: [0, 0, 0],
    // 4A账号数
    a4Accoun: '',
    // 4A手机号
    a4Phone: '',
    // 是否使用GPU
    isUseGpu: '0',
    // 申请时长
    time: '',
  }
  return model
}
export function useNatModel() {
  const model: INatModel = {
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @实例规格
    nat: '',
    desc: '',
    // @是否绑定公网IP
    isBindPublicNetworkIp: '0',
    // @带宽
    eipAttrId: 0,
    eipValue: 5,
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'nat',

    // @ productType 对应的产品名称
    goodsName: 'NAT网关',
    goodsId: '',
  }
  return model
}

export function useNasModel() {
  const model: INasModel = {
    goodsId: '',
    instanceName: '',
    storagePath: '',
    storageSize: 0,
    numbers: 1,
    time: '',
    productType: 'nas',
    goodsName: 'NAS',
  }
  return model
}
export function useVpnModel() {
  const model: IVpnModel = {
    goodsId: '',
    instanceName: '',
    maxClient: 0,
    bandwidth: 0,
    numbers: 1,
    time: '',
    productType: 'vpn',
    goodsName: 'VPN',
  }
  return model
}
export function useObsModel() {
  const model: IObsModel = {
    goodsId: '',
    // @产品名称
    instanceName: '',
    // @功能模块
    functionalModule: '',
    // @是否挂载数据盘
    isMountobs: '1',
    // @数据盘
    obs: ['', 1],
    // @开通数量
    numbers: 1,
    // @申请时长
    time: '',
    // @备注
    remarks: '',
    // @产品类型
    productType: 'obs',

    // @ productType 对应的产品名称
    goodsName: '对象存储',
  }
  return model
}
export function useBaseModel() {
  const userStore = useUserStore()
  const model: IBaseModel = {
    applicant: userStore.userInfo.userName,
    department: userStore.userInfo.sysDeptName,
    workOrdertype: '开通申请',
    orderTitle: '',
    // @工单id
    id: '',
    // @业务系统id
    busiSystemId: '',
    // 局方负责人
    applyUserName: '',
    // @厂家
    manufacturer: '',
    // @厂家负责人
    manufacturerContacts: '',
    // @厂家电话
    manufacturerMobile: '',
    // @所属业务模块
    moduleId: '',
    // @二级部门领导
    busiDepartLeaderId: '',
    // @三级部门领导
    levelThreeLeaderId: '',
    // @资源申请说明
    orderDesc: '',
    // @资源上云说明书
    files: [],

    // @操作名称
    operationName: '开通资源申请单',
    moduleName: '',
    busiDepartLeaderLabel: '',
    levelThreeLeaderLabel: '',
    busiSystemName: '',
    a4Account: '',
    a4Phone: '',
  }
  return model
}

export interface ICycleBinBaseModel {
  applicant: string
  department: string
  workOrdertype: string
  orderTitle: string
  // @工单id
  id: string
  // @业务系统id
  busiSystemId: string
  // 局方负责人
  applyUserName: string
  // @厂家
  manufacturer: string
  // @厂家负责人
  manufacturerContacts: string
  // @厂家电话
  manufacturerMobile: string
  // @三级部门领导
  levelThreeLeaderId: string
  // @资源申请说明
  orderDesc: string
  // @资源上云说明书
  files: any[]

  // @操作名称
  operationName: string
  levelThreeLeaderLabel: string
  busiSystemName: string
}

export function useCycleBaseModel() {
  const userStore = useUserStore()
  const model: ICycleBinBaseModel = {
    applicant: userStore.userInfo.userName,
    department: userStore.userInfo.sysDeptName,
    workOrdertype: '回收申请',
    orderTitle: '',
    // @工单id
    id: '',
    // @业务系统id
    busiSystemId: '',
    // 局方负责人
    applyUserName: '',
    // @厂家
    manufacturer: '',
    // @厂家负责人
    manufacturerContacts: '',
    // @厂家电话
    manufacturerMobile: '',
    // @三级部门领导
    levelThreeLeaderId: '',
    // @资源申请说明
    orderDesc: '',
    // @资源上云说明书
    files: [],

    // @操作名称
    operationName: '回收资源申请单',
    levelThreeLeaderLabel: '',
    busiSystemName: '',
  }
  return model
}
