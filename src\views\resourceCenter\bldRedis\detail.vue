<template>
  <div id="BldRedisDetail" class="table-box">
    <sl-page-header
      title="国产Redis详情"
      :icon="{
        class: 'page_baolande',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="bldredis-detail-scroll-view" class="scroll-view" v-loading="loading">
      <div class="sl-card">
        <sl-form
          v-if="!loading"
          :show-block-title="false"
          :label-width="160"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
        </sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getResourceDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  deviceName: '',
  ip: '',
  cpuArchitecture: '',
  businessSysName: '',
  username: '',
  cloudPlatform: '',
  resourcePoolName: '',
  orderCode: '',
  effectiveTime: '',
  expireTime: '',
  billId: '',
  deviceStatusCn: '',
  applyUserName: '',
  spec: '',
  frequency: '',
  recoveryStatusCn: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '实例名称',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: 'Redis实例IP',
        type: 'text',
        key: 'ip',
        span: 8,
      },
      {
        label: 'CPU架构',
        type: 'text',
        key: 'spec',
        span: 8,
      },
      // {
      //   label: '宝兰德业务系统名称',
      //   type: 'text',
      //   key: 'frequency',
      //   span: 8,
      // },
      // {
      //   label: '宝兰德用户名',
      //   type: 'text',
      //   key: 'username',
      //   span: 8,
      // },
      {
        label: '业务系统名称',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '申请人',
        type: 'input',
        key: 'applyUserName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'effectiveTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
      {
        label: '回收状态',
        type: 'text',
        key: 'recoveryStatusCn',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

const loading = ref(false)
const fetchResourceDetail = async () => {
  loading.value = true
  try {
    const res = await getResourceDetail({
      id: resourceId.value,
      type: 'bldRedis',
    })
    if (res && res.entity) {
      for (const key in detailData) {
        detailData[key] = res.entity[key]
      }
    }
  } finally {
    loading.value = false
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/bldRedisList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: 0;
}

.bldredis-detail-scroll-view {
  height: 100%;
}

.sl-card {
  margin: 8px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}
</style>
