
8 算力地图
8.1 平台类型
接口描述

接口类型	HTTP GET
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/cloud_group
功能描述	平台类型
接口入参

序号	元素名称	约束	类型	描述	取值说明
/v1/cloud/resourcecenter/console/compute_power_map/stats/cloud_group


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	1	String	平台类型	
2	hardwarePoolNum	1	Int	硬件资源池数量	
3	baseDeviceNum	1	Int	设备数量	
5	platformTypeNames	?	List<String>	云平台列表	
　
8.2 弹出页查询条件
8.2.1 物理资源池（CMDB）列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/HARDWARE_RESOURCE_POOL
功能描述	查询物理资源池（CMDB）列表信息，使用场景：国产化、重保业务、绿色节能、资产分析、基础设施、DCN
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10，查询全部数据用大一些的分页数
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/HARDWARE_RESOURCE_POOL
{
    "pageNum": 1,
    "pageSize": 9999,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	ID	
5.2	name	1	String	名称	

8.2.2 虚拟资源池（CMDB）列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/VIRTUAL_RESOURCE_POOL
功能描述	查询虚拟资源池（CMDB）列表信息，使用场景：虚拟机
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	pageNum	?	Int	当前页	默认1
6	pageSize	?	int	分页大小	默认10，查询全部数据用大一些的分页数
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/VIRTUAL_RESOURCE_POOL
{
    "pageNum": 1,
    "pageSize": 9999,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	ID	
5.2	name	1	String	名称	

8.2.3 资源池列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/regions
功能描述	查询资源池列表信息，使用场景：存储、公网
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
/v1/cloud/resourcecenter/console/compute_power_map/regions
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	List<T>	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	id	1	long	ID	
2	name	1	String	名称	
3	code	1	String	编码	


8.3 国产化
8.3.1 国产化-数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/database_node
功能描述	查询国产化-数据信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
/v1/cloud/resourcecenter/console/compute_power_map/stats/database_node
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	List<T>	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	dbCategory	1	String	数据库分类	domesticDB：国产化数据库；
foreignDB：非国产化数据库；
total：总和；
2	nodeNum	1	int	数据库节点数量	
3	rate	1	double	占比	如0.12，表示12%

8.3.2 国产化-弹窗-数据库服务列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/database_service
功能描述	查询数据库服务弹窗列表信息，分为国产化、非国产化
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	dbCategory	1	String	分类	domesticDB: 国产化数据库;
foreignDB:非国产化数据库; 
/v1/cloud/resourcecenter/console/compute_power_map/page/database_service
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
　　"platformTypeName":"融合边缘云-VMWARE",
　　"dbCategory":"domesticDB",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	serviceName	1	String	数据库服务名称	
5.3	databaseName	1	String	数据库名称	
5.4	instanceName	1	String	数据库实例名称	
5.5	databaseIp	?	String	数据库IP	
5.6	listenerPort	?	String	端口	
5.7	databaseType	1	String	数据库类型	
5.8	platformTypeName	?	String	所属云平台	
5.9	relatedPool	?	String	所属物理资源池	
5.10	bsName	?	String	关联业务系统	


8.4 重保业务
8.4.1 重保业务-数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/critical_business
功能描述	查询重保业务-数据信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
/v1/cloud/resourcecenter/console/compute_power_map/stats/critical_business
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	List<T>	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	orderNum	1	int	序号	
2	bsNameType	1	String	业务分类	
3	vmNum	1	int	虚拟机数量	
4	cpuRate	1	double	CPU负荷	如0.12，表示12%
5	memoryRate	1	double	内存负荷	

8.4.2 重保业务-弹窗-虚拟机列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/critical_business
功能描述	查询重保业务虚拟机列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	pageNum	?	Int	当前页	默认1
6	pageSize	?	int	分页大小	默认10 
7	relatedPool	?	String	物理资源池名称	
8	bsNameType	1	String	业务分类	移动看家、家客系统
/v1/cloud/resourcecenter/console/compute_power_map/page/critical_business
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "bsNameType":"移动看家"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	虚拟机名称	
5.3	osVersion	?	String	操作系统版本	
5.4	platformTypeName	?	String	所属云平台	
5.5	relatedPool	?	String	所属物理资源池	
5.6	mgmtIpv4	?	String	管理网IPv4地址	
5.7	mgmtIpv6	?	String	管理网IPv6地址	
5.8	memTotalCapacity	?	Double	内存大小	GB
5.9	vdisksInfo	?	Double	存储大小	GB
5.10	vcpuInfo	?	Integer	vCPU核数	
5.11	bsName	?	String	关联业务系统	


8.4.3 重保业务-弹窗-物理机列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/critical_business
功能描述	查询重保业务物理机列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	pageNum	?	Int	当前页	默认1
6	pageSize	?	int	分页大小	默认10 
7	relatedPool	?	String	物理资源池名称	
8	bsNameType	1	String	业务分类	智家云电脑
/v1/cloud/resourcecenter/console/compute_power_map/page/critical_business
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "bsNameType":"智家云电脑"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	计算服务器名称	
5.3	osVersion	?	String	操作系统版本	
5.4	model	?	String	设备型号	
5.5	idcRoom	?	String	所属机房	
5.6	idcRack	?	String	所属机架	
5.7	platformTypeName	?	String	所属云平台	
5.8	relatedPool	?	String	所属物理资源池	
5.9	mgmtIpv4	?	String	管理网IPv4地址	
5.10	mgmtIpv6	?	String	管理网IPv6地址	
5.11	memTotalCapacity	?	Float	内存大小	GB
5.12	localStorageCapacity	?	Float	存储大小	GB
5.13	cpuCoresCount	?	Integer	CPU核数	



8.8.3 重保业务-弹窗-告警数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/alarm_stats/critical_business
功能描述	查询重保业务的告警信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	relatedPool	?	String	物理资源池名称	
6	bsNameType	1	String	业务分类	移动看家、家客系统—虚拟机告警；
智家云电脑—物理机告警
/v1/cloud/resourcecenter/console/compute_power_map/alarm_stats/critical_business
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "bsNameType":"移动看家"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	criticalCount	?	int	严重级别告警数量	
2	majorCount	?	int	主要级别告警数量	
3	minorCount	?	int	次要级别告警数量	
4	warningCount	?	int	警告级别告警数量	
5	infoCount	?	int	提示级别告警数量	
6	indeterminateCount	?	int	未知级别告警数量	


8.5 绿色节能
8.5.1 绿色节能-数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/green_energy_saved
功能描述	查询绿色节能-数据信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
/v1/cloud/resourcecenter/console/compute_power_map/stats/green_energy_saved
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	savedEnergy	1	String	节约能耗	
2	savedElectricity	1	String	节约电量	单位：Kw/h
3	states	1	List<T>	不同生命周期数量列表	
3.1	lifeCycleState	1	String	生命周期状态	工程、在网
3.2	greenEnergyNum	1	int	关机服务器数量	
4	report	1	List<T>	趋势折线图	
4.1	standbyMonth	1	String	待机月份	
4.2	savedElectricity	1	String	节约电量	单位：Kw/h

8.5.2 绿色节能-弹窗-物理机列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/green_energy_saved
功能描述	查询绿色节能物理机列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	lifeCycleState	?	String	生命周期状态	在网、工程
/v1/cloud/resourcecenter/console/compute_power_map/page/green_energy_saved
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	计算服务器名称	
5.3	osVersion	?	String	操作系统版本	
5.4	model	?	String	设备型号	
5.5	idcRoom	?	String	所属机房	
5.6	idcRack	?	String	所属机架	
5.7	platformTypeName	?	String	所属云平台	
5.8	relatedPool	?	String	所属物理资源池	
5.9	mgmtIpv4	?	String	管理网IPv4地址	
5.10	mgmtIpv6	?	String	管理网IPv6地址	
5.11	memTotalCapacity	?	Float	内存大小	GB
5.12	localStorageCapacity	?	Float	存储大小	GB
5.13	cpuCoresCount	?	Integer	CPU核数	
5.14	vmCount	?	String	关联虚拟机个数	


8.6 资产分析
8.6.1 资产分析-数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/asset_analysis
功能描述	查询资产分析-数据信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
/v1/cloud/resourcecenter/console/compute_power_map/stats/asset_analysis
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	List<T>	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	type	1	String	类型	INEFFICIENT_ASSET：低效资产；
OWNERLESS_ASSET：无主资产
SILENT_ASSET：静默资产
2	vmNum	1	int	虚拟机数量	
3	rate	1	double	占比	如0.12，表示12%


8.6.2 资产分析-弹窗-虚拟机列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/asset_analysis/{type}
功能描述	查询低效资产、无主资产、静默资产虚拟机列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	pageNum	?	Int	当前页	默认1
6	pageSize	?	int	分页大小	默认10 
7	relatedPool	?	String	物理资源池名称	
url	type	1	String	类型	INEFFICIENT_ASSET：低效资产；
OWNERLESS_ASSET：无主资产；
SILENT_ASSET：静默资产；
/v1/cloud/resourcecenter/console/compute_power_map/page/asset_analysis/INEFFICIENT_ASSET
/v1/cloud/resourcecenter/console/compute_power_map/page/asset_analysis/OWNERLESS_ASSET
/v1/cloud/resourcecenter/console/compute_power_map/page/asset_analysis/SILENT_ASSET
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	虚拟机名称	
5.3	osVersion	?	String	操作系统版本	
5.4	platformTypeName	?	String	所属云平台	
5.5	relatedPool	?	String	所属物理资源池	
5.6	mgmtIpv4	?	String	管理网IPv4地址	
5.7	mgmtIpv6	?	String	管理网IPv6地址	
5.8	memTotalCapacity	?	Double	内存大小	GB
5.9	vdisksInfo	?	Double	存储大小	GB
5.10	vcpuInfo	?	Integer	vCPU核数	
5.11	bsName	?	String	关联业务系统	



8.7 基础设施
8.7.1 基础设施-数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/base_device
功能描述	查询基础设施-数据信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
/v1/cloud/resourcecenter/console/compute_power_map/stats/base_device
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	List<T>	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	lifeCycleState	1	String	生命周期状态	工程、在网
2	baseDeviceNum	1	int	硬件设施数量	
3	rate	1	double	占比	如0.12，表示12%

8.7.2 基础设施-弹窗-刀框列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/BLAD_BLOCK
功能描述	查询基础设施-刀框列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	lifeCycleState	1	String	生命周期状态	在网、工程
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/BLAD_BLOCK
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "lifeCycleState":"在网"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	bmcIp	?	String	带外管理地址	
5.3	idcRoom	?	String	所属机房	
5.4	idcRack	?	String	所属机架	
5.5	platformTypeName	?	String	所属云平台	
5.6	relatedPool	?	String	所属物理资源池	
5.7	model	?	String	设备型号	


8.7.3 基础设施-弹窗-刀片服务器列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/BLADE_SERVER
功能描述	查询基础设施-刀片服务器列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	lifeCycleState	1	String	生命周期状态	在网、工程
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/BLADE_SERVER
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "lifeCycleState":"在网"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	设备名称	
5.3	idcRoom	?	String	所属机房	
5.4	idcRack	?	String	所属机架	
5.5	platformTypeName	?	String	所属云平台	
5.6	relatedPool	?	String	所属物理资源池	
5.7	model	?	String	设备型号	
5.8	mgmtIpv4	?	String	管理网IPV4地址	
5.9	mgmtIpv6	?	String	管理网IPV6地址	



8.7.4 基础设施-弹窗-物理机列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/PHYSICAL_MACHINE
功能描述	查询基础设施-物理机列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	lifeCycleState	1	String	生命周期状态	在网、工程
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/PHYSICAL_MACHINE
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "lifeCycleState":"在网"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	计算服务器名称	
5.3	osVersion	?	String	操作系统版本	
5.4	model	?	String	设备型号	
5.5	idcRoom	?	String	所属机房	
5.6	idcRack	?	String	所属机架	
5.7	platformTypeName	?	String	所属云平台	
5.8	relatedPool	?	String	所属物理资源池	
5.9	mgmtIpv4	?	String	管理网IPv4地址	
5.10	mgmtIpv6	?	String	管理网IPv6地址	
5.11	memTotalCapacity	?	Float	内存大小	GB
5.12	localStorageCapacity	?	Float	存储大小	GB
5.13	cpuCoresCount	?	Integer	CPU核数	


8.7.5 基础设施-弹窗-交换机列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/SWITCH
功能描述	查询基础设施-交换机列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	lifeCycleState	1	String	生命周期状态	在网、工程
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/SWITCH
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "lifeCycleState":"在网"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	设备名称	
5.3	idcRoom	?	String	所属机房	
5.4	idcRack	?	String	所属机架	
5.5	platformTypeName	?	String	所属云平台	
5.6	relatedPool	?	String	所属物理资源池	
5.7	model	?	String	设备型号	
5.8	mgmtIpv4	?	String	管理网IPV4地址	
5.9	mgmtIpv6	?	String	管理网IPV6地址	

8.7.6 基础设施-弹窗-路由器列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/ROUTER
功能描述	查询基础设施-路由器列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	lifeCycleState	1	String	生命周期状态	在网、工程
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/ROUTER
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "lifeCycleState":"在网"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	设备名称	
5.3	idcRoom	?	String	所属机房	
5.4	idcRack	?	String	所属机架	
5.5	platformTypeName	?	String	所属云平台	
5.6	relatedPool	?	String	所属物理资源池	
5.7	model	?	String	设备型号	
5.8	mgmtIpv4	?	String	管理网IPV4地址	
5.9	mgmtIpv6	?	String	管理网IPV6地址	

8.7.7 基础设施-弹窗-防火墙列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/FIREWALL
功能描述	查询基础设施-防火墙列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	lifeCycleState	1	String	生命周期状态	在网、工程
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/FIREWALL
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "lifeCycleState":"在网"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	设备名称	
5.3	idcRoom	?	String	所属机房	
5.4	idcRack	?	String	所属机架	
5.5	platformTypeName	?	String	所属云平台	
5.6	relatedPool	?	String	所属物理资源池	
5.7	hardwarePartition	?	String	所属硬件分区	
5.8	virtualResourcePool	?	String	所属虚拟资源池	
5.9	model	?	String	设备型号	
5.10	mgmtIpv4	?	String	管理网IPV4地址	
5.11	mgmtIpv6	?	String	管理网IPV6地址	

8.7.8 基础设施-弹窗-负载均衡器列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/LOAD_BALANCER
功能描述	查询基础设施-负载均衡器列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
7	lifeCycleState	1	String	生命周期状态	在网、工程
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/LOAD_BALANCER
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577",
    "lifeCycleState":"在网"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	设备名称	
5.3	idcRoom	?	String	所属机房	
5.4	idcRack	?	String	所属机架	
5.5	platformTypeName	?	String	所属云平台	
5.6	relatedPool	?	String	所属物理资源池	
5.7	hardwarePartition	?	String	所属硬件分区	
5.8	virtualResourcePool	?	String	所属虚拟资源池	
5.9	model	?	String	设备型号	
5.10	mgmtIpv4	?	String	管理网IPV4地址	
5.11	mgmtIpv6	?	String	管理网IPV6地址	


8.8 虚拟机
8.8.1 虚拟机-数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/vm_count
功能描述	查询基础设施-数据信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
/v1/cloud/resourcecenter/console/compute_power_map/stats/vm_count
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
　　"cityCode":"577"
　　"areaCode":"5771"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1		1	Int	虚拟机数量	


8.8.2 虚拟机-弹窗-列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/VIRTUAL_MACHINE
功能描述	查询虚拟机列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	pageNum	?	Int	当前页	默认1
6	pageSize	?	int	分页大小	默认10 
7	vrpName	?	String	虚拟资源池名称	
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/VIRTUAL_MACHINE
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	pages	1	long	总页数	
2	total	1	long	总数量	
3	current	1	long	当前页码	
4	size	1	long	每页大小	
5	records	?	List<T>	返回实体	
5.1	instanceId	1	String	配置项编号	
5.2	name	1	String	虚拟机名称	
5.3	osVersion	?	String	操作系统版本	
5.4	platformTypeName	?	String	所属云平台	
5.5	relatedPool	?	String	所属物理资源池	
5.6	mgmtIpv4	?	String	管理网IPv4地址	
5.7	mgmtIpv6	?	String	管理网IPv6地址	
5.8	memTotalCapacity	?	Double	内存大小	GB
5.9	vdisksInfo	?	Double	存储大小	GB
5.10	vcpuInfo	?	Integer	vCPU核数	
5.11	bsName	?	String	关联业务系统	


8.8.3 虚拟机-弹窗-告警数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/alarm_stats/vm
功能描述	查询虚拟机的告警信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	vrpName	?	String	虚拟资源池名称	
/v1/cloud/resourcecenter/console/compute_power_map/alarm_stats/vm
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	criticalCount	?	int	严重级别告警数量	
2	majorCount	?	int	主要级别告警数量	
3	minorCount	?	int	次要级别告警数量	
4	warningCount	?	int	警告级别告警数量	
5	infoCount	?	int	提示级别告警数量	
6	indeterminateCount	?	int	未知级别告警数量	



8.9 用量信息
8.9.1 存储/vCpu/内存/网络用量&资源概况-数据
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/res_usage/latest
功能描述	查询存储、vCpu、内存、网络用量-数据信息
资源概况查看中，查询存储、内存、vCpu用量信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
/v1/cloud/resourcecenter/console/compute_power_map/stats/res_usage/latest
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
　　"cityCode":"577"
　　"areaCode":"5771"
} 

接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	vcpuTotal	1	Long	vCPU总量	
2	vcpuUsed	1	Long	vCPU已用量	
3	vcpuAvi	1	Long	vCPU剩余量	
4	memoryTotal	1	Double	内存总量	单位：GB
5	memoryUsed	1	Double	内存已用量	单位：GB
6	memoryAvi	1	Double	内存剩余量	单位：GB
7	storageTotal	1	Double	存储总量	单位：GB
8	storageUsed	1	Double	存储已用量	单位：GB
9	storageAvi	1	Double	存储剩余量	单位：GB
10	eipTotal	1	Long	公网IP总量	
11	eipUsed	1	Long	公网IP已用量	
12	eipAvi	1	Long	公网IP剩余量	
13	dcnTotal	1	Long	dcn总量	
14	dcnUsed	1	Long	dcn已用量	
15	dcnAvi	1	Long	dcn剩余量	

















8.9.2 网络-弹窗-公网IP列表




接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/res/EIP
功能描述	查询公网IP列表接口
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	pageNum	1	Integer	分页数	
6	pageSize	1	Integer	分页大小	
7	regionId	?	String	资源池ID	
{
    "cloudName": "移动云",
    "pageNum": 1,
    "pageSize": 10,
　　"platformTypeName": "融合边缘云-VMWARE",
    "cityCode": "571",
     "areaCode": "5719",
      "regionId": 5719

}


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	类型	描述	取值说明
1	records	Array	 	
1.1	id	String	主键id	
1.2	name	Long	公网Ip名称	
1.3	domainName	String	所属云平台	
1.4	appName	String	业务系统名称	
1.5	regionName	String	所属资源池	
1.6	ip	String	公网IP	
1.7	bandwidth	Integer	带宽大小	
1.8	resType	String	资源类型	
1.9	resName	String	资源名称	
2	size	Integer		
3	current	Integer	当前页	
4	total	Integer	总数	















 
{
    "success": "1",
    "code": 200,
    "message": "",
    "entity": {
        "records": [
            {
                "id": "a0512d3814fa41488fc4d225b1902e8f",
                "name": "eip-dr-test-0303-1",
                "domainName": "VMWARE",
                "domainCode": "VMWARE",
                "cmpTenantId": 100000194041,
                "appName": "zyf测试0213",
                "regionName": "浙江-衢州-地市-威睿-paas",
                "ip": "************",
                "bandwidth": 5,
                "resType": "负载均衡",
                "resName": "slb_zyf_0303-1"
            }
        ],
        "size": 10,
        "current": 1,
        "total": 2
    }
}


8.9.3 网络-弹窗-公网ip下载
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/res/eip/download
功能描述	公网IP下载
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	regionId	?	String	资源池ID	
{
    "cloudName": "移动云",
　　"platformTypeName": "融合边缘云-VMWARE",
    "cityCode": "571",
     "areaCode": "5719",

}


8.9.4 存储-弹窗-云硬盘列表




接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/res/STORAGE
功能描述	查询对象存储列表
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	pageNum	1	Integer	分页数	
6	pageSize	1	Integer	分页大小	
7	regionId	?	String	资源池ID	
{
    "cloudName": "移动云",
    "pageNum": 1,
    "pageSize": 10,
　　"platformTypeName": "融合边缘云-VMWARE",
    "cityCode": "571",
     "areaCode": "5719",
      "regionId": 5719
}


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	类型	描述	取值说明
1	records	Array	 	
1.1	id	String	主键id	
1.2	name	Long	公网Ip名称	
1.3	domainName	String	所属云平台	
1.4	appName	String	业务系统名称	
1.5	regionName	String	所属资源池	
1.6	volumeStatus	String	状态	
1.7	vmName	String	主机名称	
1.8	isAttached	String	是否挂载云主机	
2	size	Integer		
3	current	Integer	当前页	
4	total	Integer	总数	















 
{
    "success": "1",
    "code": 200,
    "message": "",
    "entity": {
        "records": [
            {
                "id": "a0512d3814fa41488fc4d225b1902e8f",
                "name": "SSD 15",
                "domainName": "VMWARE",
                "domainCode": "VMWARE",
                "cmpTenantId": 100000194041,
                "appName": "zyf测试0213",
                "regionName": "浙江-衢州-地市-威睿-paas",
                "isAttached ": "是",
                "vmName": "vm_zyf_0303-1",
                  “volumeStatus”:”已挂载”
            }
        ],
        "size": 10,
        "current": 1,
        "total": 2
    }
}

 

8.9.5 存储-弹窗-云硬盘下载
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/res/evs/download
功能描述	云硬盘下载
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	regionId	?	String	资源池ID	
{
    "cloudName": "移动云",
　　"platformTypeName": "融合边缘云-VMWARE",
    "cityCode": "571",
     "areaCode": "5719",

}


8.9.6 网络-弹窗-DCN列表
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/DCN
功能描述	查询网络-DCN列表信息
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	pageNum	?	Int	当前页	默认1
5	pageSize	?	int	分页大小	默认10 
6	relatedPool	?	String	物理资源池名称	
/v1/cloud/resourcecenter/console/compute_power_map/page/base_device/DCN
{
    "pageNum": 1,
    "pageSize": 10,
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
    "cityCode":"577"
} 

8.9.7 vCpu/内存-弹窗-性能趋势&资源利用率
接口描述

接口类型	HTTP GET
接口URL	/性能中心/view/pm/map
功能描述	查询vCpu、内存性能趋势信息
资源概况查看中，查询资源池利用率
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	startTime	1	String	开始日期	格式：YYYY-MM_DD
如：2024-11-01
6	endTime	1	String	截至日期	格式：YYYY-MM_DD
如：2024-11-01
/性能中心/view/pm/top?startTime=2025-07-12&endTime=2025-07-17&cloudName=网络云&platformTypeName=平台云&cityCode=571&areaCode=5712  


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
5	cpuData	?	T	CPU资源使用数据	
5.1	days	?	List<String>	时间维度	格式为 "YYYY-MM-DD" 的日期数组
5.2	dataUtil	?	List<T>	各区域性能数据集合	
5.2.1	performanceData	?	List<Double>	性能指标数据数组	每个元素表示某一天的使用率百分比（如 20.8 表示 20.8%）
6	memData	?	T	内存资源使用数据	包含内存使用率相关的区域、日期及性能数据
6.1	days	?	List<String>	时间维度	格式为 "YYYY-MM-DD" 的日期数组
6.2	dataUtil	?	List<T>	各区域性能数据集合	
6.2.1	performanceData	?	List<Double>	性能指标数据数组	每个元素表示某一天的使用率百分比（如 20.8 表示 20.8%）

8.10 高亮地市及区县
接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/stats/cloud_city_areas
功能描述	高亮地市及区县
接口入参

序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
/v1/cloud/resourcecenter/console/compute_power_map/stats/cloud_city_areas
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	List<T>	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	cityCode	1	String	地市编码	
2	areaCodes	?	List<String>	区县编码集合	
　

8.11 资源概况查看
8.11.1 资源池分配律
接口描述

接口类型	HTTP GET
接口URL	/v1/cloud/resourcecenter/console/compute_power_map/cm/map
功能描述	查询资源池分配律
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
5	startTime	1	String	开始日期	格式：YYYY-MM_DD
如：2024-11-01
6	endTime	1	String	截至日期	格式：YYYY-MM_DD
如：2024-11-01
/v1/cloud/resourcecenter/console/compute_power_map/cm/map?startTime=2025-07-12&endTime=2025-07-17&cloudName=网络云&platformTypeName=平台云&cityCode=571&areaCode=5712  


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
5	cpuData	?	T	CPU资源使用数据	包含vCpu使用率相关的日期及用量数据
5.1	days	?	List<String>	时间维度	格式为 "YYYY-MM-DD" 的日期数组
5.2	dataUtil	?	List<T>	各区域用量数据集合	
5.2.1	cmData	?	List<Double>	用量指标数据数组	每个元素表示某一天的使用率百分比（如 20.8 表示 20.8%）
6	memData	?	T	内存资源使用数据	包含内存使用率相关的日期及用量数据
6.1	days	?	List<String>	时间维度	格式为 "YYYY-MM-DD" 的日期数组
6.2	dataUtil	?	List<T>	各区域用量数据集合	
6.2.1	cmData	?	List<Double>	用量指标数据数组	每个元素表示某一天的使用率百分比（如 20.8 表示 20.8%）
7	diskData	?	T	存储资源使用数据	包含存储使用率相关的日期及用量数据
7.1	days	?	List<String>	时间维度	格式为 "YYYY-MM-DD" 的日期数组
7.2	dataUtil	?	List<T>	各区域用量数据集合	
7.2.1	cmData	?	List<Double>	用量指标数据数组	每个元素表示某一天的使用率百分比（如 20.8 表示 20.8%）


8.11.2 主要资源慨况

接口描述

接口类型	HTTP POST
接口URL	/v1/cloud/resourcecenter/console/compute_power_map /resource/count
功能描述	主要资源慨况，包括：云主机、GPU云主机、对象存储、负载均衡数量
接口入参
序号	元素名称	约束	类型	描述	取值说明
1	cloudName	?	String	云平台类型	
2	platformTypeName	?	String	云平台	
3	cityCode	?	String	地市编码	
4	areaCode	?	String	区县编码	
/v1/cloud/resourcecenter/console/compute_power_map/resource/count
{
    "cloudName":"移动云",
    "platformTypeName":"融合边缘云-VMWARE",
　　"cityCode":"577"
　　"areaCode":"5771"
} 


接口出参

序号	元素名称	约束	类型	描述	取值说明
1	success	1	String	判断请求成功还是失败	0: 失败
1: 成功
2	code	1	String	状态码	
3	message	1	String	返回信息	
4	entity	1	T	返回实体	

　entity字段说明
序号	元素名称	约束	类型	描述	取值说明
1	vmCount	1	int	云主机总数	
2	gpuVmCount	1	int	GPU云主机总数	
3	bucketSum	1	int	对象存储总容量	单位：GB
4	lbCount	1	int	负载均衡总数	
　

