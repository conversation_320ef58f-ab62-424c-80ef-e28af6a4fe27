import type { RouteRecordRaw } from 'vue-router'
const orderCenterRouter: RouteRecordRaw[] = [
  {
    path: '/netWork/pcapStrategy',
    component: () => import('@/components/MicroPage.vue'),
    name: 'pcapStrategy',
    props: {
      moduleType: 'network',
      subAppUrl: '/microApp/monitor/netWork/pcapStrategy',
    },
    meta: {
      title: '抓包策略',
      icon: 'icon-gongdan',
    },
  },
  {
    path: '/netWork/flowLog',
    component: () => import('@/components/MicroPage.vue'),
    name: 'flowLog',
    props: () => ({
      moduleType: 'network',
      subAppUrl: '/microApp/monitor/netWork/flowLog',
    }),
    meta: {
      title: '流量日志',
      icon: 'icon-rizhi',
    },
  },
  {
    path: '/netWork/pcapDownLoad',
    component: () => import('@/components/MicroPage.vue'),
    name: 'pcapDownLoad',
    props: () => ({
      moduleType: 'network',
      subAppUrl: '/microApp/monitor/netWork/pcapDownLoad',
    }),
    meta: {
      title: '抓包下载',
      icon: 'icon-xiazai',
    },
  },
  {
    path: '/alarmModule/alarmList',
    component: () => import('@/components/MicroPage.vue'),
    name: 'alarmList',
    props: {
      moduleType: 'alarm',
      subAppUrl: '/microApp/monitor/alarmModule/alarmList',
    },
    meta: {
      title: '告警列表',
      icon: 'icon-gaojing',
    },
  },
  {
    path: '/alarmModule/alarmRules',
    component: () => import('@/components/MicroPage.vue'),
    name: 'alarmRules',
    props: {
      moduleType: 'alarm',
      subAppUrl: '/microApp/monitor/alarmModule/alarmRules',
    },
    meta: {
      title: '告警规则',
      icon: 'icon-guize',
    },
  },
  {
    path: '/alarmModule/alarmControll',
    component: () => import('@/components/MicroPage.vue'),
    name: 'alarmControll',
    props: {
      moduleType: 'alarm',
      subAppUrl: '/microApp/monitor/alarmModule/alarmControll',
    },
    meta: {
      title: '告警抑制',
      icon: 'icon-kongzhi',
    },
  },
  {
    path: '/selfMonitor/collector',
    component: () => import('@/components/MicroPage.vue'),
    name: 'collector',
    props: {
      moduleType: 'selfMonitor',
      subAppUrl: '/microApp/monitor/selfMonitor/collector',
    },
    meta: {
      title: '采集器',
      icon: 'icon-caiji',
    },
  },
  {
    path: '/selfMonitor/component',
    component: () => import('@/components/MicroPage.vue'),
    name: 'component',
    props: {
      moduleType: 'selfMonitor',
      subAppUrl: '/microApp/monitor/selfMonitor/component',
    },
    meta: {
      title: '组件监控',
      icon: 'icon-zujian',
    },
  },
  {
    path: '/selfMonitor/diskManagement',
    component: () => import('@/components/MicroPage.vue'),
    name: 'diskManagement',
    props: {
      moduleType: 'selfMonitor',
      subAppUrl: '/microApp/monitor/selfMonitor/diskManagement',
    },
    meta: {
      title: '磁盘管理',
      icon: 'icon-cipan',
    },
  },
  {
    path: '/topologyView',
    component: () => import('@/components/MicroPage.vue'),
    name: 'topologyView',
    props: {
      moduleType: 'topology',
      subAppUrl: '/microApp/monitor/topologyView',
    },
    meta: {
      title: '拓扑视图',
      icon: 'icon-tuopu',
    },
  },
  {
    path: '/topologyView/create',
    component: () => import('@/components/MicroPage.vue'),
    name: 'topologyCreate',
    props: {
      moduleType: 'topology',
      subAppUrl: '/microApp/monitor/topologyView/create',
    },
    meta: {
      title: '创建拓扑',
      icon: 'icon-chuangjian',
    },
  },
  {
    path: '/topologyView/detail',
    component: () => import('@/components/MicroPage.vue'),
    name: 'topologyDetail',
    props: () => ({
      moduleType: 'topology',
      subAppUrl: '/microApp/monitor/topologyView/detail',
    }),
    meta: {
      title: '拓扑详情',
      icon: 'icon-xiangqing',
    },
  },
  {
    path: '/businessList',
    component: () => import('@/components/MicroPage.vue'),
    name: 'businessList',
    props: {
      moduleType: 'business',
      subAppUrl: '/microApp/monitor/businessList',
    },
    meta: {
      title: '业务列表',
      icon: 'icon-yewu',
    },
  },
  {
    path: '/businessList/detail',
    component: () => import('@/components/MicroPage.vue'),
    name: 'businessDetail',
    props: () => ({
      moduleType: 'business',
      subAppUrl: '/microApp/monitor/businessList/detail',
    }),
    meta: {
      title: '业务详情',
      icon: 'icon-xiangqing',
    },
  },
  {
    path: '/reSource/networkResource',
    component: () => import('@/components/MicroPage.vue'),
    name: 'networkResource',
    props: {
      moduleType: 'resource',
      subAppUrl: '/microApp/monitor/reSource/networkResource',
    },
    meta: {
      title: '网络资源',
      icon: 'icon-ziyuan',
    },
  },
  {
    path: '/configurationManagement/BusinessReport',
    component: () => import('@/components/MicroPage.vue'),
    name: 'businessReport',
    props: {
      moduleType: 'configuration',
      subAppUrl: '/microApp/monitor/configurationManagement/BusinessReport',
    },
    meta: {
      title: '业务报表',
      icon: 'icon-baobiao',
    },
  },
  {
    path: '/selfMonitor/notificationMethod',
    component: () => import('@/components/MicroPage.vue'),
    name: 'notificationMethod',
    props: {
      moduleType: 'selfMonitor',
      subAppUrl: '/microApp/monitor/selfMonitor/notificationMethod',
    },
    meta: {
      title: '通知方式',
      icon: 'icon-tongzhi',
    },
  },
  {
    path: '/dialingTest/testTask',
    component: () => import('@/components/MicroPage.vue'),
    name: 'testTask',
    props: {
      moduleType: 'dialingTest',
      subAppUrl: '/microApp/monitor/dialingTest/testTask',
    },
    meta: {
      title: '测试任务',
      icon: 'icon-ceshi',
    },
  },
  {
    path: '/dialingTest/meshpingDetail',
    component: () => import('@/components/MicroPage.vue'),
    name: 'meshpingDetail',
    props: () => ({
      moduleType: 'dialingTest',
      subAppUrl: '/microApp/monitor/dialingTest/meshpingDetail',
    }),
    meta: {
      title: 'Meshping详情',
      icon: 'icon-xiangqing',
    },
  },
  // demo
  {
    path: '/network2',
    component: () => import('@/components/MicroPage.vue'),
    name: 'network2',
    props: () => ({
      moduleType: 'dialingTest',
      subAppUrl: '/mric-cloudPods/network2',
    }),
    meta: {
      title: 'Meshping详情',
      icon: 'icon-xiangqing',
    },
  },
]

export default orderCenterRouter
