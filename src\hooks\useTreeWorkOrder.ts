import { ref } from 'vue'
import { configData, type IDataItem } from '@/components/base/SlBaseSteps/help'

export interface TreeNode {
  title: string
  id: string
  branch?: TreeNode[]
}

/**
 * @name 流程节点
 */
export interface GetResourceListResType {
  currentTask: string
  currentTaskName: string
  root: ActiviteDetailVoListType
}

interface ActiviteDetailVoListType {
  task: string
  taskName: string
  children: ActiviteDetailVoListType[]
}

const useTreeWorkOrder = () => {
  const allTasks = ref<IDataItem[]>([])
  const currentTask = ref<string>('')
  const currentTaskName = ref<string>('')
  // const currentSort = ref<number>(-1)
  const historyTasks = ref<ActiviteDetailVoListType[]>([])

  const getWorkOrderNode = async (entity: GetResourceListResType) => {
    currentTask.value = entity?.currentTask || ''
    currentTaskName.value = entity?.currentTaskName || ''

    const convertedResult = convertEntityToResult(entity.root)
    const { data, activeChain } = configData(convertedResult, {
      branchSpace: 10, // 上下间距
      activeId: entity?.currentTask, //活跃的节点id
    })

    historyTasks.value = activeChain
      .map((item: TreeNode) => {
        return {
          ...item,
          taskName: item.title,
          task: item.id,
        }
      })
      .slice(0, -1)

    allTasks.value = data
  }
  return {
    allTasks, // 流程节点集合
    currentTask, // 当前节点code
    currentTaskName, // 当前节点名称
    // currentSort, // 当前节点排序
    historyTasks, // 当前节点之前的节点集合
    getWorkOrderNode, // 获取当前流程步骤
  }
}

export function convertEntityToResult(node: any, result: TreeNode[] = []): TreeNode[] {
  result.push({
    title: node.taskName,
    id: node.task,
  })
  let mainIdx = 0
  for (let index = 0; index < node.children.length; index++) {
    const element = node.children[index]
    if (index === 0) {
      mainIdx = result.length - 1
      convertEntityToResult(element, result)
    } else {
      const thatNode = result[mainIdx]
      if (thatNode) {
        thatNode.branch = convertEntityToResult(element)
      }
    }
  }

  return result
}

export default useTreeWorkOrder
