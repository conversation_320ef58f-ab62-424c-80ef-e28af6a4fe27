<template>
  <div class="table-box">
    <sl-page-header
      title="裸金属"
      title-line="裸金属服务器 BMS (Bare Metal Server)是一种可弹性扩展的专属计算服务，提供与传统物理机无差异的计算性能和安全物理隔离的特点"
      :icon="{
        class: 'page_luojinsu',
        color: '#0052D9',
        size: '40px',
      }"
    >
      <!-- <template #custom>
        <sl-base-tabs
          :tabs="availableTabs"
          v-model="activeTab"
          v-if="!shouldHideTabs"
        ></sl-base-tabs>
      </template> -->
    </sl-page-header>
    <div class="resource-tab">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleBatchRecycle" type="primary" v-permission="'BatchRecycle'">
          批量回收
        </el-button>
        <!-- v-permission="'Import'" -->
        <el-button
          v-permission="'Import'"
          @click="() => (importDialogVisible = true)"
          type="primary"
        >
          导入
        </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: collapsed }"
          ref="formRef"
          :options="formOptions"
          v-model="formModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <DataList
          ref="dataListRef"
          :query-params="queryParams"
          :hide-operations="shouldHideResourceOperations"
        ></DataList>
      </div>
    </div>
    <SlImportDialog
      v-model="importDialogVisible"
      title="导入裸金属"
      file-label="裸金属文件:"
      :import-api="importPmApi"
      :download-template-api="downloadPmTemplateApi"
      template-btn-text="模板下载"
      template-file-name="裸金属导入模板.xlsx"
      :on-success="() => dataListRef.value?.reloadTableList()"
    />
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp, Upload } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import DataList from './components/DataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { normalizeExportArgs } from '@/views/resourceCenter/utils'
import { useDownload } from '@/hooks/useDownload'
import { resourceExport, downloadPmTemplateApi, importPmApi } from '@/api/modules/resourecenter'
import { useRolePermission } from '../hooks/useRolePermission'
import SlImportDialog from '@/components/base/SlImportDialog.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
// import { useResourceTabs } from '../hooks/useResourceTabs'

const { shouldHideResourceOperations } = useRolePermission()

// const { shouldHideTabs } = useResourceTabs()
// const availableTabs = ref<any[]>([{ label: '线下资源', name: 'XX' }])

// const activeTab = ref<string>('XX')
const { busiSystemOptions } = useBusiSystemOptions()
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const formRef = ref<any>(null)
const queryParams = ref<any>({ type: 'pm', sourceType: 'XX' })

const formModel = reactive({})

function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}

function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '裸金属名称',
        type: 'input',
        key: 'deviceName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSysId',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'pm', '裸金属.xlsx', {
                      ...formModel,
                      sourceType: 'XX',
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '交维状态',
        type: 'input',
        key: 'handoverStatus',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '系统版本',
        type: 'input',
        key: 'osVersion',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '规格',
        type: 'input',
        key: 'spec',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: 'IP',
        type: 'input',
        key: 'ip',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'applyTime',
        options: getDic('time'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '开通时间',
        type: 'date',
        key: 'effectiveTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
      {
        label: '到期时间',
        type: 'date',
        key: 'expireTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 暴露给外部调用的批量回收功能
const dataListRef = ref<any>(null)

const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}

// 导入功能相关变量和方法
const importDialogVisible = ref(false)
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
