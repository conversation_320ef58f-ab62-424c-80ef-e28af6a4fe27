<template>
  <div class="container" ref="contentRef">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-content">
        <input
          v-model="templateName"
          :disabled="!!isView"
          class="toolbar-title-input"
          placeholder="产品组合编排模板名称"
        />

        <el-collapse v-model="activeTools">
          <el-collapse-item
            v-for="(item, key) in toolTreeItems"
            :key="key"
            :title="item.label"
            :name="key"
          >
            <el-row>
              <el-col :span="8" v-for="(child, key) in item.children" :key="key">
                <div
                  class="toolbar-item"
                  @click="handleAdd(child.type as IProductType)"
                  title="点击添加到画布"
                >
                  <div class="toolbar-icon">
                    <img :src="images[child.icon]" :alt="child.name" />
                  </div>
                  <div class="toolbar-text">{{ child.name }}</div>
                </div>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
      <el-button type="primary" size="large" class="toolbar-submit-btn" @click="handleCanvasSubmit">
        提 交
      </el-button>
    </div>

    <!-- 右键菜单 -->
    <div v-show="contextMenu.show" :style="contextMenuStyle" class="context-menu">
      <div class="context-menu-item" @click="handleContextDelete">
        <el-icon><Delete /></el-icon>
        <span>删除节点</span>
      </div>
      <div class="context-menu-item" @click="handleCopy">
        <el-icon><CopyDocument /></el-icon>
        <span>复制节点</span>
      </div>
    </div>

    <!-- 画布 -->
    <div
      @click="hideContextMenu"
      class="canvas-container"
      :class="{ 'linking-cursor': linking.isLinking }"
    >
      <v-network-graph
        ref="graph"
        :nodes="nodes"
        :edges="edges"
        :layouts="layouts"
        :configs="configs"
        :layers="layers"
        :event-handlers="eventHandlers"
        v-model:selected-nodes="selectedNodes"
        v-model:selected-edges="selectedEdges"
        class="network-graph"
        @mousemove="handleMouseMove"
      >
        <template #images="{ scale }">
          <image
            v-for="(pos, nodeId) in layouts.nodes"
            :key="nodeId"
            :x="pos.x - 20 * scale"
            :y="pos.y - 20 * scale"
            :width="40 * scale"
            :height="40 * scale"
            :href="images[nodes[nodeId].icon]"
            style="pointer-events: none"
          />
        </template>
        <template #custom-layers>
          <!-- 临时连线 -->
          <path
            v-if="linking.isLinking"
            :d="temporaryLinkPath"
            stroke="#de8802"
            stroke-width="3"
            stroke-dasharray="3,3"
            fill="none"
            pointer-events="none"
          />
        </template>
      </v-network-graph>

      <!-- 悬浮提交按钮 -->
      <!-- <div v-if="!isView" class="floating-submit-btn">
        <el-button type="primary" size="large" style="width: 100px" @click="handleCanvasSubmit">
          提 交
        </el-button>
      </div> -->
    </div>
    <el-drawer
      size="450px"
      v-model="drawer"
      title="配置信息"
      :direction="direction"
      :before-close="handleClose"
    >
      <template v-if="drawer">
        <div style="padding-bottom: 60px">
          <ecs v-if="goods.productType === 'ecs'" :goods="goods" ref="formRefs" />
          <gcs v-if="goods.productType === 'gcs'" :goods="goods" ref="formRefs" />
          <eip v-if="goods.productType === 'eip'" :goods="goods" ref="formRefs" />
          <network v-if="goods.productType === 'network'" :goods="goods" ref="formRefs" />
          <vpc v-if="goods.productType === 'vpc'" :goods="goods" ref="formRefs" />
          <nat v-if="goods.productType === 'nat'" :goods="goods" ref="formRefs" />
          <slb v-if="goods.productType === 'slb'" :goods="goods" ref="formRefs" />
          <evs v-if="goods.productType === 'evs'" :goods="goods" ref="formRefs" />
          <obs v-if="goods.productType === 'obs'" :goods="goods" ref="formRefs" />
          <cloudPort v-if="goods.productType === 'cloudPort'" :goods="goods" ref="formRefs" />
          <mysql v-if="goods.productType === 'mysql'" :goods="goods" ref="formRefs" />
          <redis v-if="goods.productType === 'redis'" :goods="goods" ref="formRefs" />
          <pm v-if="goods.productType === 'pm'" :goods="goods" ref="formRefs" />
          <securityGroup
            v-if="goods.productType === 'securityGroup'"
            :goods="goods"
            ref="formRefs"
          />
        </div>
      </template>
      <div class="drawer-footer">
        <el-button @click="closeDrawer">取消</el-button>
        <el-button v-if="!isView" type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import * as vNG from 'v-network-graph'
import { useGraph, type IProductType } from './useGraph'
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { Delete, CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { DrawerProps } from 'element-plus'
import ecs from './forms/ecs.vue'
import gcs from './forms/gcs.vue'
import eip from './forms/eip.vue'
import network from './forms/network.vue'
import vpc from './forms/vpc.vue'
import nat from './forms/nat.vue'
import slb from './forms/slb.vue'
import evs from './forms/evs.vue'
import obs from './forms/obs.vue'
import cloudPort from './forms/cloudPort.vue'
import mysql from './forms/mysql.vue'
import redis from './forms/redis.vue'
import pm from './forms/pm.vue'
import securityGroup from './forms/securityGroup.vue'
import { useGlobalDicStore } from '@/stores/modules/dic'
import {
  createDagTemplate,
  getDagTemplateDetail,
  updateDagTemplate,
} from '@/api/modules/resourecenter'
import { useRoute, useRouter } from 'vue-router'
import type {
  IEcsModel,
  IGcsModel,
  IEvsModel,
  IObsModel,
  IVpcModel,
  INetworkModel,
  ISlbModel,
  INatModel,
  ICloudPortModel,
  IMysqlModel,
  IRedisModel,
  IEipModel,
  // IPmModel,
  // ISecurityGroupModel,
} from './model'
import { uuid } from '@/utils'

const contentRef = ref()
const route = useRoute()
const name = route.query.name as string
const router = useRouter()
const description = route.query.description as string
const isView = route.query.isView
const id = route.query.id as string
const {
  nodes,
  edges,
  layouts,
  configs,
  addNode,
  copyNode,
  deleteNode,
  deleteEdge,
  selectedNodes,
  selectedEdges,
  templateName,
  viewInfo,
} = useGraph()

templateName.value = name || '产品组合编排模板名称'

if (id) {
  getDagTemplate(id)
}
async function getDagTemplate(id: string) {
  const { entity } = await getDagTemplateDetail({ id })
  const dagCanvas = JSON.parse(entity.dagCanvas)
  Object.assign(nodes, dagCanvas.nodes)
  Object.assign(edges, dagCanvas.edges)
  Object.assign(layouts, dagCanvas.layouts)

  // 恢复视图信息
  if (dagCanvas.viewInfo) {
    Object.assign(viewInfo, dagCanvas.viewInfo)
    // 等待下一帧后应用视图变换
    await nextTick()
    if (graph.value) {
      // 使用setViewBox方法恢复视图
      graph.value.setViewBox({
        left: viewInfo.left,
        top: viewInfo.top,
        right: viewInfo.right,
        bottom: viewInfo.bottom,
      })
    }
  }
}

const formRefs = ref()
const globalDic = useGlobalDicStore()
globalDic.initDic()

const goods: any = ref({})

const graph = ref<vNG.Instance>()

const drawer = ref(false)
const direction = ref<DrawerProps['direction']>('rtl')

function handleClose() {
  drawer.value = false
}

// 右键菜单状态
const contextMenu = ref({
  show: false,
  x: 0,
  y: 0,
  nodeId: '',
})
// 计算右键菜单位置样式
const contextMenuStyle = computed(() => ({
  left: contextMenu.value.x + 'px',
  top: contextMenu.value.y + 'px',
}))

async function handleSubmit() {
  const valid = await formRefs.value.validateForm()
  if (valid) {
    console.log('校验通过')
    formRefs.value.submitForm()
    console.log(goods.value)

    closeDrawer()
  } else {
    console.log('校验不通过')
  }
}

function closeDrawer() {
  drawer.value = false
}

function handleKeyDown(e: KeyboardEvent) {
  // 检查当前焦点是否在可编辑元素上
  const activeElement = document.activeElement
  const isInEditableElement =
    activeElement instanceof HTMLInputElement ||
    activeElement instanceof HTMLTextAreaElement ||
    activeElement instanceof HTMLSelectElement ||
    (activeElement as HTMLElement)?.contentEditable === 'true' ||
    (activeElement as HTMLElement)?.isContentEditable === true

  // 如果当前焦点在可编辑元素上，不执行删除操作
  if (isInEditableElement) {
    return
  }

  if (e.key === 'Backspace') {
    for (const nodeId of selectedNodes.value) {
      handleDelNode(nodeId)
    }
    for (const edgeId of selectedEdges.value) {
      handleDelEdge(edgeId)
    }
  }
}
// 按backspace删除节点
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

const activeTools = ref([0, 1, 2, 3, 4])
const toolTreeItems = [
  {
    label: '计算能力',
    children: [
      { name: '云主机', icon: 'ecs.png', type: 'ecs' },
      { name: 'GPU云主机', icon: 'gcs.png', type: 'gcs' },
      { name: '裸金属', icon: 'pm.png', type: 'pm' },
    ],
  },
  {
    label: '存储能力',
    children: [
      { name: '云硬盘', icon: 'evs.png', type: 'evs' },
      { name: '对象存储', icon: 'obs.png', type: 'obs' },
    ],
  },
  {
    label: '网络能力',
    children: [
      { name: 'VPC', icon: 'vpc.png', type: 'vpc' },
      { name: '网络', icon: 'network.png', type: 'network' },
      { name: '负载均衡', icon: 'slb.png', type: 'slb' },
      { name: '弹性公网', icon: 'eip.png', type: 'eip' },
      { name: '防火墙', icon: 'securityGroup.png', type: 'securityGroup' },
      { name: 'NAT', icon: 'nat.png', type: 'nat' },
      { name: '云端口', icon: 'cloudPort.png', type: 'cloudPort' },
    ],
  },
  {
    label: '中间件能力',
    children: [{ name: '通用Redis', icon: 'redis.png', type: 'redis' }],
  },
  {
    label: '数据库能力',
    children: [{ name: 'MySQL云数据库', icon: 'mysql.png', type: 'mysql' }],
  },
]

// 导入所有图片
const images: Record<string, string> = {
  'ecs.png': new URL('./imgs/ecs.png', import.meta.url).href,
  'gcs.png': new URL('./imgs/gcs.png', import.meta.url).href,
  'eip.png': new URL('./imgs/eip.png', import.meta.url).href,
  'evs.png': new URL('./imgs/evs.png', import.meta.url).href,
  'nat.png': new URL('./imgs/nat.png', import.meta.url).href,
  'slb.png': new URL('./imgs/slb.png', import.meta.url).href,
  'vpc.png': new URL('./imgs/vpc.png', import.meta.url).href,
  'network.png': new URL('./imgs/network.png', import.meta.url).href,
  'obs.png': new URL('./imgs/obs.png', import.meta.url).href,
  'mysql.png': new URL('./imgs/mysql.png', import.meta.url).href,
  'redis.png': new URL('./imgs/redis.png', import.meta.url).href,
  'cloudPort.png': new URL('./imgs/cloudPort.png', import.meta.url).href,
  'securityGroup.png': new URL('./imgs/securityGroup.png', import.meta.url).href,
  'pm.png': new URL('./imgs/pm.png', import.meta.url).href,
}

const layers = {
  images: 'nodes', // 将图片图层放在节点位置
}

// 删除节点
const handleDelNode = (nodeId: string) => {
  if (nodeId) {
    resetLinking()
    deleteNode(nodeId)
  }
}

// 删除边
const handleDelEdge = (edgeId: string) => {
  resetLinking()
  deleteEdge(edgeId)
}

// 复制节点
const handleCopy = () => {
  resetLinking()
  copyNode(contextMenu.value.nodeId)
  hideContextMenu()
}
// 添加节点
const handleAdd = (type: IProductType) => {
  resetLinking()
  addNode(type)
}

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.value.show = false
}

// 连线状态
const linking = ref({
  isLinking: false,
  sourceNodeId: '',
  mouseX: 0,
  mouseY: 0,
})

// 获取节点在画布中的实际位置
const getNodePosition = (nodeId: string) => {
  const pos = layouts.nodes[nodeId]
  return pos ? { x: pos.x, y: pos.y } : null
}

// 计算临时连线的路径
const temporaryLinkPath = computed(() => {
  const sourcePosOnView = getPosOnView(linking.value.sourceNodeId)
  if (!sourcePosOnView) return ''

  // 如果还没有鼠标移动（mouseX和mouseY还是初始位置），使用起始节点位置
  const { offsetTop, offsetLeft } = contentRef.value
  let targetX = linking.value.mouseX - offsetLeft
  let targetY = linking.value.mouseY - offsetTop

  // 计算从起始节点到鼠标位置的路径
  return `M ${sourcePosOnView.x} ${sourcePosOnView.y} L ${targetX} ${targetY}`
})

// 处理鼠标移动
const handleMouseMove = (event: MouseEvent) => {
  if (linking.value.isLinking) {
    linking.value.mouseX = event.clientX
    linking.value.mouseY = event.clientY
  }
}

const connectRules: Record<string, string[]> = {
  // 这些类型不能作为源节点连接到其他节点
  vpc: [],
  network: [],
  obs: [],
  pm: [],

  // 这些类型只能连接到vpc
  nat: ['vpc'],
  slb: ['vpc'],
  cloudPort: ['vpc'],

  // 这些类型可以连接到vpc和network
  ecs: ['vpc', 'network'],
  gcs: ['vpc', 'network'],
  mysql: ['vpc', 'network'],
  redis: ['vpc', 'network'],

  // eip可以连接到这些计算和网络服务类型
  eip: ['nat', 'slb', 'ecs', 'gcs', 'mysql', 'redis'],

  // evs只能连接到计算类型的节点
  evs: ['ecs', 'gcs', 'mysql', 'redis'],
  securityGroup: ['ecs'],
}

// 获取连接到指定节点的所有节点（用于校验）
function getConnectedNodesForValidation(nodeId: string, targetType?: string) {
  const connectedNodes = []
  for (const edgeId in edges) {
    const edge = edges[edgeId]
    if (edge.source === nodeId) {
      const targetNode = nodes[edge.target]
      if (!targetType || targetNode.type === targetType) {
        connectedNodes.push(targetNode)
      }
    }
  }
  return connectedNodes
}

//-----------校验逻辑开始-----------
const couldConnect = (sourceId: string, targetId: string) => {
  const source = nodes[sourceId]
  const target = nodes[targetId]

  // 使用connectRules来判断是否可以连接
  const allowedTargets = connectRules[source.type] || []
  return allowedTargets.includes(target.type)
}

// 连接校验：检查两个节点之间是否已经存在连接
const hasConnection = (sourceId: string, targetId: string) => {
  return Object.values(edges).some(
    (edge) =>
      (edge.source === sourceId && edge.target === targetId) ||
      (edge.source === targetId && edge.target === sourceId),
  )
}

const connectValid = (sourceId: string, targetId: string) => {
  if (hasConnection(sourceId, targetId)) {
    ElMessage.warning('这两个节点之间已经存在连接')
    return false
  }
  if (!couldConnect(sourceId, targetId)) {
    const source = nodes[sourceId]
    const target = nodes[targetId]
    ElMessage.warning(`【${source.name}】节点不支持连接到【${target.name}】节点`)
    return false
  }

  // 校验EVS和EIP一对一连线规则
  const sourceNode = nodes[sourceId]
  const targetNode = nodes[targetId]
  const onlyOnes = ['evs', 'eip']
  // 检查EVS、EIP节点是否已经有连接
  if (onlyOnes.includes(sourceNode.type)) {
    const existingConnections = getConnectedNodesForValidation(sourceId)
    if (existingConnections.length > 0) {
      ElMessage.warning(`【${sourceNode.name}】节点已经连接到其他节点，只能一对一连线`)
      return false
    }
  }

  if (onlyOnes.includes(targetNode.type)) {
    const existingConnections = getConnectedNodesForValidation(targetId)
    if (existingConnections.length > 0) {
      ElMessage.warning(`【${targetNode.name}】节点已经连接到其他节点，只能一对一连线`)
      return false
    }
  }

  // 校验gcs、ecs、nat、slb、redis、mysql只能被一个eip连接
  const singleEipTargetTypes = ['gcs', 'ecs', 'nat', 'slb', 'redis', 'mysql']
  if (sourceNode.type === 'eip' && singleEipTargetTypes.includes(targetNode.type)) {
    // 检查目标节点是否已经被其他EIP连接
    const existingEipConnections = Object.values(edges).filter((edge) => {
      const connectedSourceNode = nodes[edge.source]
      return edge.target === targetId && connectedSourceNode?.type === 'eip'
    })
    if (existingEipConnections.length > 0) {
      ElMessage.warning(`【${targetNode.name}】节点已经被EIP连接，只能连接一个EIP`)
      return false
    }
  }

  // 校验slb、nat、cloudPort只能连接一个vpc
  const singleVpcTypes = ['slb', 'nat', 'cloudPort']
  if (singleVpcTypes.includes(sourceNode.type) && targetNode.type === 'vpc') {
    const existingVpcConnections = getConnectedNodesForValidation(sourceId, 'vpc')
    if (existingVpcConnections.length > 0) {
      ElMessage.warning(`【${sourceNode.name}】节点已经连接到VPC，只能连接一个VPC`)
      return false
    }
  }

  if (singleVpcTypes.includes(targetNode.type) && sourceNode.type === 'vpc') {
    const existingVpcConnections = getConnectedNodesForValidation(targetId, 'vpc')
    if (existingVpcConnections.length > 0) {
      ElMessage.warning(`【${targetNode.name}】节点已经连接到VPC，只能连接一个VPC`)
      return false
    }
  }

  // 5. 校验gcs、ecs、nat、slb、redis、mysql只能被一个eip连接
  const limitedEipTargetTypes = ['gcs', 'ecs', 'nat', 'slb', 'redis', 'mysql']
  for (const nodeId in nodes) {
    const node = nodes[nodeId]
    if (limitedEipTargetTypes.includes(node.type)) {
      // 检查这个节点被多少个EIP连接
      const connectedEips = Object.values(edges).filter((edge) => {
        const sourceNode = nodes[edge.source]
        return edge.target === nodeId && sourceNode?.type === 'eip'
      })

      if (connectedEips.length > 1) {
        return {
          isValid: false,
          message: `【${node.name}】节点只能被一个EIP连接，当前被${connectedEips.length}个EIP连接`,
        }
      }
    }
  }

  return true
}
//-----------校验逻辑结束-----------

const eventHandlers: vNG.EventHandlers = {
  'node:dblclick': ({ node }) => {
    drawer.value = true
    goods.value = nodes[node].model
  },
  'node:click': ({ node, event }) => {
    if (linking.value.isLinking) {
      if (linking.value.sourceNodeId !== node) {
        if (connectValid(linking.value.sourceNodeId, node)) {
          const edgeId = `edge${uuid(16)}`
          edges[edgeId] = {
            source: linking.value.sourceNodeId,
            target: node,
          }
        }
      }
      resetLinking()
    } else if (!event.shiftKey) {
      linking.value.isLinking = true
      linking.value.sourceNodeId = node

      const posOnView = getPosOnView(node)
      // 初始化target和起点坐标一致
      if (posOnView) {
        const { offsetTop, offsetLeft } = contentRef.value
        linking.value.mouseX = posOnView.x + offsetLeft
        linking.value.mouseY = posOnView.y + offsetTop
      }
    }
    nodes[node].active = !nodes[node].active
  },
  'node:contextmenu': ({ node, event }) => {
    event.preventDefault()
    contextMenu.value = {
      show: true,
      x: event.clientX,
      y: event.clientY,
      nodeId: node,
    }
  },
  'view:click': () => {
    resetLinking()
    hideContextMenu()
  },
  'view:zoom': () => {
    // 获取当前视图框信息
    if (graph.value) {
      const viewBox = graph.value.getViewBox()
      Object.assign(viewInfo, viewBox)
    }

    // 如果正在连线，更新鼠标位置为起始节点位置
    if (linking.value.isLinking) {
      const posOnView = getPosOnView(linking.value.sourceNodeId)
      if (posOnView) {
        linking.value.mouseX = posOnView.x
        linking.value.mouseY = posOnView.y
      }
    }
  },
  'view:pan': () => {
    // 获取当前视图框信息
    if (graph.value) {
      const viewBox = graph.value.getViewBox()
      Object.assign(viewInfo, viewBox)
    }

    // 如果正在连线，更新鼠标位置为起始节点位置
    if (linking.value.isLinking) {
      const posOnView = getPosOnView(linking.value.sourceNodeId)
      if (posOnView) {
        linking.value.mouseX = posOnView.x
        linking.value.mouseY = posOnView.y
      }
    }
  },
} as vNG.EventHandlers

function resetLinking() {
  linking.value.isLinking = false
  linking.value.sourceNodeId = ''
}
function getPosOnView(nodeId: string) {
  if (!graph.value) return ''
  const pos = getNodePosition(nodeId)
  if (!pos) return ''
  const svgPoint = graph.value.translateFromSvgToDomCoordinates({
    x: pos.x,
    y: pos.y,
  })
  return { x: svgPoint.x, y: svgPoint.y }
}
function handleContextDelete() {
  handleDelNode(contextMenu.value.nodeId)
  hideContextMenu()
}

// 参数组装
const prepareParams = () => {
  const params = {
    id,
    description,
    name: templateName.value,
    dagCanvas: JSON.stringify({
      nodes: nodes,
      edges: edges,
      layouts: layouts,
      viewInfo: viewInfo, // 添加视图信息
    }),
    ecsModelList: [] as any[],
    gcsModelList: [] as any[],
    evsModelList: [] as any[],
    obsModelList: [] as any[],
    vpcModelList: [] as any[],
    networkModelList: [] as any[],
    slbModelList: [] as any[],
    natModelList: [] as any[],
    cloudPortModelList: [] as any[],
    redisModelList: [] as any[],
    mysqlModelList: [] as any[],
    eipModelList: [] as any[],
  }

  // 获取节点间的连接关系
  const getConnectedNodes = (nodeId: string, targetType?: string) => {
    const connectedNodes = []
    for (const edgeId in edges) {
      const edge = edges[edgeId]
      if (edge.source === nodeId) {
        const targetNode = nodes[edge.target]
        if (!targetType || targetNode.type === targetType) {
          connectedNodes.push(targetNode)
        }
      }
    }
    return connectedNodes
  }

  // 获取连接到指定节点的EIP节点dagId列表
  const getConnectedEipDagIds = (nodeId: string) => {
    return getConnectedNodes(nodeId, 'eip').map((eipNode) => eipNode.model.uid)
  }

  // 获取连接到指定节点的EVS节点dagId列表
  const getConnectedEvsDagIds = (nodeId: string) => {
    return getConnectedNodes(nodeId, 'evs').map((evsNode) => evsNode.model.uid)
  }

  // 组装各类产品参数
  for (const nodeId in nodes) {
    const node = nodes[nodeId]
    const model = node.model
    const relDagIds = connectRules[node.type].flatMap((type) =>
      getConnectedNodes(nodeId, type).map((node) => node.model.uid),
    )
    const subnetDagIds = connectRules[node.type]
      .filter((type) => type === 'vpc' || type === 'network')
      .flatMap((type) =>
        getConnectedNodes(nodeId, type).flatMap(
          (node) => node.model.childNet?.map((subnet: any) => subnet.uid) || [],
        ),
      )
    switch (node.type) {
      case 'ecs': {
        const ecsModel = model as IEcsModel
        const ecsItem = {
          dagId: ecsModel.uid,
          relDagIds,
          subnetDagIds,
          productType: ecsModel.productType,
          vmName: ecsModel.instanceName,
          imageOs: ecsModel.imageOs[0],
          flavorName: ecsModel.ecs[1],
          flavorType: ecsModel.ecs[0],
          sysDiskSize: ecsModel.sysDisk[1],
          sysDiskType: ecsModel.sysDisk[0],
          openNum: ecsModel.numbers,
          applyTime: ecsModel.time,
          disasterRecovery: ecsModel.disasterRecovery === '1',
          plane: ecsModel.planeValue?.join(',') || '',
          mountDataDisk: false,
          bindPublicIp: false,
          // 关联的EIP和EVS
          eipDagIds: getConnectedEipDagIds(nodeId),
          evsDagIds: getConnectedEvsDagIds(nodeId),
        }
        params.ecsModelList.push(ecsItem)
        break
      }

      case 'gcs': {
        const gcsModel = model as IGcsModel
        const gcsItem = {
          dagId: gcsModel.uid,
          relDagIds,
          subnetDagIds,
          productType: gcsModel.productType,
          vmName: gcsModel.instanceName,
          imageOs: gcsModel.imageOs[0],
          imageVersion: gcsModel.imageOs[1],
          flavorName: gcsModel.gcs[1],
          flavorType: gcsModel.gcs[0],
          sysDiskSize: gcsModel.sysDisk[1],
          sysDiskType: gcsModel.sysDisk[0],
          openNum: gcsModel.numbers,
          applyTime: gcsModel.time,
          disasterRecovery: gcsModel.disasterRecovery === '1',
          plane: gcsModel.planeValue?.join(',') || '',
          mountDataDisk: false,
          bindPublicIp: false,
          // 关联的EIP和EVS
          eipDagIds: getConnectedEipDagIds(nodeId),
          evsDagIds: getConnectedEvsDagIds(nodeId),
        }
        params.gcsModelList.push(gcsItem)
        break
      }

      case 'mysql': {
        const mysqlModel = model as IMysqlModel
        const mysqlItem = {
          dagId: mysqlModel.uid,
          relDagIds,
          subnetDagIds,
          productType: mysqlModel.productType,
          vmName: mysqlModel.instanceName,
          imageOs: mysqlModel.imageOs[0],
          imageVersion: mysqlModel.imageOs[1],
          flavorCode: null,
          flavorName: mysqlModel.ecs[1],
          flavorType: mysqlModel.ecs[0],
          sysDiskSize: mysqlModel.sysDisk[1],
          sysDiskType: mysqlModel.sysDisk[0],
          openNum: mysqlModel.numbers,
          applyTime: mysqlModel.time,
          disasterRecovery: mysqlModel.disasterRecovery === '1',
          plane: mysqlModel.planeValue?.join(',') || '',
          mountDataDisk: false,
          bindPublicIp: false,
          // 关联的EIP和EVS
          eipDagIds: getConnectedEipDagIds(nodeId),
          evsDagIds: getConnectedEvsDagIds(nodeId),
        }
        params.mysqlModelList.push(mysqlItem)
        break
      }

      case 'redis': {
        const redisModel = model as IRedisModel
        const redisItem = {
          dagId: redisModel.uid,
          relDagIds,
          subnetDagIds,
          productType: redisModel.productType,
          vmName: redisModel.instanceName,
          imageOs: redisModel.imageOs[0],
          imageVersion: redisModel.imageOs[1],
          flavorCode: null,
          flavorName: redisModel.ecs[1],
          flavorType: redisModel.ecs[0],
          sysDiskSize: redisModel.sysDisk[1],
          sysDiskType: redisModel.sysDisk[0],
          openNum: redisModel.numbers,
          applyTime: redisModel.time,
          disasterRecovery: redisModel.disasterRecovery === '1',
          plane: redisModel.planeValue?.join(',') || '',
          mountDataDisk: false,
          bindPublicIp: false,
          // 关联的EIP和EVS
          eipDagIds: getConnectedEipDagIds(nodeId),
          evsDagIds: getConnectedEvsDagIds(nodeId),
        }
        params.redisModelList.push(redisItem)
        break
      }

      case 'evs': {
        const evsModel = model as IEvsModel
        const evsItem = {
          relDagIds,
          subnetDagIds,
          dagId: evsModel.uid,
          productType: evsModel.productType,
          evsName: evsModel.instanceName,
          openNum: evsModel.numbers,
          applyTime: evsModel.time,
          sysDiskSize: evsModel.evs[0]?.[1] || 0,
          sysDiskType: evsModel.evs[0]?.[0] || '',
        }
        params.evsModelList.push(evsItem)
        break
      }

      case 'eip': {
        const eipModel = model as IEipModel
        const eipItem = {
          relDagIds,
          subnetDagIds,
          dagId: eipModel.uid,
          productType: eipModel.productType,
          eipName: eipModel.instanceName,
          openNum: eipModel.numbers,
          applyTime: eipModel.time,
          bandwidth: eipModel.eipValue,
        }
        params.eipModelList.push(eipItem)
        break
      }

      case 'obs': {
        const obsModel = model as IObsModel
        const obsItem = {
          relDagIds,
          subnetDagIds,
          dagId: obsModel.uid,
          productType: obsModel.productType,
          obsName: obsModel.instanceName,
          openNum: obsModel.numbers,
          applyTime: obsModel.time,
          storageDiskSize: obsModel.obs[1],
          storageDiskType: obsModel.obs[0],
          remarks: obsModel.remarks,
        }
        params.obsModelList.push(obsItem)
        break
      }

      case 'vpc': {
        const vpcModel = model as IVpcModel
        const vpcItem = {
          relDagIds,
          subnetDagIds,
          dagId: vpcModel.uid,
          productType: vpcModel.productType,
          vpcName: vpcModel.instanceName,
          cidr: vpcModel.netRange,
          plane: vpcModel.plane?.join(',') || '',
          subnetDTOList:
            vpcModel.childNet?.map((subnet: any) => ({
              dagId: subnet.uid,
              subnetName: subnet.subnetName,
              startIp: subnet.startIp,
              netmask: subnet.netmask,
            })) || [],
        }
        params.vpcModelList.push(vpcItem)
        break
      }

      case 'network': {
        const networkModel = model as INetworkModel
        const networkItem = {
          relDagIds,
          subnetDagIds,
          dagId: networkModel.uid,
          productType: networkModel.productType,
          networkType: networkModel.networkType,
          plane: networkModel.plane,
          systemSource: 'NFVO',
          name: networkModel.instanceName,
          subnets:
            networkModel.childNet?.map((subnet: any) => ({
              dagId: subnet.uid,
              ipVersion: subnet.ipVersion,
              cidr: subnet.cidr,
            })) || [],
        }
        params.networkModelList.push(networkItem)
        break
      }

      case 'slb': {
        const slbModel = model as ISlbModel
        const slbItem = {
          relDagIds,
          subnetDagIds,
          dagId: slbModel.uid,
          productType: slbModel.productType,
          slbName: slbModel.instanceName,
          openNum: slbModel.numbers,
          applyTime: slbModel.time,
          flavorCode: null,
          flavorName: slbModel.slb,
          flavorType: slbModel.desc,
          remarks: slbModel.remarks,
          // 关联的EIP
          eipDagIds: getConnectedEipDagIds(nodeId),
          bindPublicIp: false,
        }
        params.slbModelList.push(slbItem)
        break
      }

      case 'nat': {
        const natModel = model as INatModel
        const natItem = {
          relDagIds,
          subnetDagIds,
          dagId: natModel.uid,
          productType: natModel.productType,
          natName: natModel.instanceName,
          openNum: natModel.numbers,
          applyTime: natModel.time,
          flavorCode: null,
          flavorName: natModel.nat,
          flavorType: '',
          // 关联的EIP
          eipDagIds: getConnectedEipDagIds(nodeId),
          bindPublicIp: false,
        }
        params.natModelList.push(natItem)

        break
      }

      case 'cloudPort': {
        const cloudPortModel = model as ICloudPortModel
        const cloudPortItem = {
          relDagIds,
          subnetDagIds,
          dagId: cloudPortModel.uid,
          productType: cloudPortModel.productType,
          cloudPortName: cloudPortModel.instanceName,
          openNum: 1,
          applyTime: cloudPortModel.time,
          vlanId: cloudPortModel.vlanId,
          srcIp: cloudPortModel.srcIp,
          peerIp: cloudPortModel.peerIp,
          peerPassword: cloudPortModel.peerPassword,
        }
        params.cloudPortModelList.push(cloudPortItem)
        break
      }
    }
  }

  return params
}

async function handleCanvasSubmit() {
  // 实现提交逻辑
  console.log('提交画布数据')
  console.log('节点数据:', nodes)
  console.log('连线数据:', edges)

  // 校验逻辑
  const validateResult = validateBeforeSubmit()
  if (!validateResult.isValid) {
    ElMessage.error(validateResult.message)
    return
  }

  const params = prepareParams()
  id ? await updateDagTemplate(params) : await createDagTemplate(params)
  console.log('提交参数:', params)
  ElMessage.success('提交成功')
  router.push({
    path: '/resArrangementList',
  })
}

// 提交前校验函数
function validateBeforeSubmit() {
  const requiredConnectionTypes = ['ecs', 'gcs', 'redis', 'mysql', 'slb']

  // 1. 校验ecs、gcs、redis、mysql、slb的relDagIds不能为空
  for (const nodeId in nodes) {
    const node = nodes[nodeId]
    if (requiredConnectionTypes.includes(node.type)) {
      const connectedNodes = getConnectedNodesForValidation(nodeId)
      if (connectedNodes.length === 0) {
        return {
          isValid: false,
          message: `【${node.name}】节点必须连接到其他节点，不能为空`,
        }
      }
    }
  }

  // 2. 校验evs只能一对一连线
  const evsConnectionCount = new Map<string, number>()
  for (const edgeId in edges) {
    const edge = edges[edgeId]
    const sourceNode = nodes[edge.source]
    const targetNode = nodes[edge.target]

    // 检查EVS节点的连接数量
    if (sourceNode.type === 'evs') {
      const count = evsConnectionCount.get(edge.source) || 0
      evsConnectionCount.set(edge.source, count + 1)
    }
    if (targetNode.type === 'evs') {
      const count = evsConnectionCount.get(edge.target) || 0
      evsConnectionCount.set(edge.target, count + 1)
    }
  }

  // 检查是否有EVS节点连接超过1个
  for (const [evsNodeId, connectionCount] of evsConnectionCount) {
    if (connectionCount > 1) {
      const evsNode = nodes[evsNodeId]
      return {
        isValid: false,
        message: `【${evsNode.name}】EVS节点只能一对一连线，不能连接多个节点`,
      }
    }
  }

  // 3. 校验slb、nat、cloudPort必须连接vpc
  const mustConnectVpcTypes = ['slb', 'nat', 'cloudPort']
  for (const nodeId in nodes) {
    const node = nodes[nodeId]
    if (mustConnectVpcTypes.includes(node.type)) {
      const connectedVpcs = getConnectedNodesForValidation(nodeId, 'vpc')
      if (connectedVpcs.length === 0) {
        return {
          isValid: false,
          message: `【${node.name}】节点必须连接到VPC`,
        }
      }
    }
  }

  // 4. 校验slb、nat、cloudPort只能连接一个vpc
  const singleVpcTypes = ['slb', 'nat', 'cloudPort']
  for (const nodeId in nodes) {
    const node = nodes[nodeId]
    if (singleVpcTypes.includes(node.type)) {
      const connectedVpcs = getConnectedNodesForValidation(nodeId, 'vpc')
      if (connectedVpcs.length > 1) {
        return {
          isValid: false,
          message: `【${node.name}】节点只能连接一个VPC，当前连接了${connectedVpcs.length}个VPC`,
        }
      }
    }
  }

  // 5. 校验gcs、ecs、nat、slb、redis、mysql只能被一个eip连接
  const limitedEipTargetTypes = ['gcs', 'ecs', 'nat', 'slb', 'redis', 'mysql']
  for (const nodeId in nodes) {
    const node = nodes[nodeId]
    if (limitedEipTargetTypes.includes(node.type)) {
      // 检查这个节点被多少个EIP连接
      const connectedEips = Object.values(edges).filter((edge) => {
        const sourceNode = nodes[edge.source]
        return edge.target === nodeId && sourceNode?.type === 'eip'
      })

      if (connectedEips.length > 1) {
        return {
          isValid: false,
          message: `【${node.name}】节点只能被一个EIP连接，当前被${connectedEips.length}个EIP连接`,
        }
      }
    }
  }

  return { isValid: true, message: '' }
}
</script>

<style lang="scss" scoped>
.container {
  position: relative;
}
.canvas-container {
  width: 100vw;
  height: calc(100vh - 55px);
  background-color: #edfffb;
  overflow: hidden;
}
.toolbar-submit-btn {
  border-radius: 0;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 40px;
}

.toolbar {
  position: absolute;
  right: 0px;
  top: 0px;
  width: 300px;
  box-sizing: border-box;
  height: calc(100vh - 55px);
  overflow-y: hidden;
  background: white;
  // padding: 0px 14px 0 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  // 美化滚动条
  &::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.25);
    border-radius: 3px !important;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.48);
  }
  .toolbar-content {
    padding: 14px;
    padding-top: 0px;
    height: calc(100vh - 85px);
    overflow-y: auto;
  }

  .toolbar-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
    padding: 10px 0;
  }

  .toolbar-title-input {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    border: none;
    outline: none;
    background: transparent;
    border-bottom: 1px solid #eee;
    padding: 10px 0;
    width: 100%;
    color: #333;
    text-align: center;

    &::placeholder {
      color: #999;
    }

    &:focus {
      border-bottom-color: #409eff;
    }
  }

  .toolbar-item {
    width: 65px;
    height: 55px;
    padding: 4px;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: #f5f5f5;
      transform: translateX(2px);
    }

    .toolbar-icon {
      img {
        width: 25px;
        height: 25px;
      }
    }

    .toolbar-text {
      font-size: 12px;
      color: #333;
      text-align: center;
    }
  }
  :deep(.el-collapse) {
    --el-collapse-border-color: transparent !important;
    .el-collapse-item__content {
      padding-bottom: 5px;
    }
  }
}

.context-menu {
  position: fixed;
  background: white;
  border-radius: 4px;
  padding: 4px 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 120px;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
  font-size: 12px;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item .el-icon {
  margin-right: 8px;
  font-size: 14px;
}

.network-graph :deep(.v-network-graph-node-label) {
  font-weight: bold !important;
}

.network-graph :deep(.v-network-graph) {
  position: relative;
}

.network-graph :deep(.v-network-graph-nodes) {
  z-index: 2;
}

.network-graph :deep(.v-network-graph-edges) {
  z-index: 1;
}

.network-graph :deep(.v-network-graph-custom-layers) {
  z-index: 2;
}

.linking-cursor {
  cursor:
    url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'><line x1='16' y1='8' x2='16' y2='24' stroke='%23666' stroke-width='2'/><line x1='8' y1='16' x2='24' y2='16' stroke='%23666' stroke-width='2'/></svg>")
      16 16,
    crosshair !important;
}

.network-graph :deep(.v-network-graph-node) {
  cursor: pointer;
}

.drawer-footer {
  position: absolute;
  z-index: 100;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background: #fff;
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.floating-submit-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}
</style>
