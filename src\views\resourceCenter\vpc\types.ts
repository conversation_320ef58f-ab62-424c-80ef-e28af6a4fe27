import type { Ref } from 'vue'

interface IBaseFormProvider {
  regionCode: string
  collectSubmits: ICollectSubmits
  opType: 'createWithOrder' | 'addSubnet' | 'view' | 'create'
  jsonStr: string
  uuids: string[]
}
type ICollectSubmits = (fn: IFormRefs) => void

type IFormRefs = Ref<{ id: number; ref: any }[]>

interface ISubmitData {
  detail: string
  vpcName: string
  cidr: string
  plane: string
  subnetDTOList: {
    startIp: string
    netmask: string
    instanceId?: string
    level2InstanceId?: string
    subnetName: string
    allocationPools: {
      startIp: string
      endIp: string
    }[]
  }[]
}

interface SubNetFormModel {
  ip: string
  type: string
  vpn: string
  instanceId: string
  subnetName: string
  ref: null
}
interface ChildNetFormModel {
  ip2: string
  ipVersion: string
  type2: string
  vpn2: string
  subnetStr: string
  resourcePoolLabel: string
  instanceId2: string
  ipCheckSuccess: boolean
  splitMessage: string
  ref: null
}
export type {
  IBaseFormProvider,
  ICollectSubmits,
  IFormRefs,
  ISubmitData,
  SubNetFormModel,
  ChildNetFormModel,
}
