<template>
  <div id="tenantView">
    <SlPageHeader title="租户视图" detail="实时计算资源状态" :icon="{ hidden: true }">
      <!-- 使用插槽自定义返回按钮 -->
      <template #header-right-custom>
        <ResourceFilter :resource-list="resourceList"></ResourceFilter>
        <el-button type="primary" :icon="Plus" @click="handleShowBusinessSystemCreated">
          创建业务
        </el-button>
      </template>
    </SlPageHeader>
    <!-- 业务系统列表及选择 -->
    <BusinessSystemList
      :system-list="businessSystemList"
      :selected-ids="selectedSystemIds"
      @systemSelected="handleBusinessSystemSeleted"
      @showSystemDetails="handleShowBusisnessSystemDetail"
      @systemDeselected="handleBusinessSystemDeselected"
    ></BusinessSystemList>
    <!-- 新增/编辑业务系统 -->
    <el-drawer
      v-model="showBusinessSystemCreated"
      :with-header="false"
      :before-close="handleBusinessSystemCreatedClose"
      size="600"
      destroy-on-close
    >
      <BusinessSystemCreated
        :business-system-id="currentBusinessSystemId"
        @submit="handleBusinessSystemSubmit"
        @close="handleBusinessSystemCreatedClose"
      ></BusinessSystemCreated>
    </el-drawer>
    <ResourceNumberList
      :resource-list="resourceList"
      :selected-system-ids="selectedSystemIds"
      v-loading="loading"
    />
    <div class="upper">
      <SystemUsageChart
        class="upper-left"
        :system-list="businessSystemList"
        :selected-system-ids="selectedSystemIds"
      />
      <div class="upper-right">
        <WorkOrder></WorkOrder>
        <SystemNotice></SystemNotice>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="tenantView">
import { Plus } from '@element-plus/icons-vue'
import BusinessSystemCreated from './components/BusinessSystemCreated.vue'
import BusinessSystemList from './components/BusinessSystemList.vue'
import ResourceNumberList from './components/ResourceNumberList.vue'
import ResourceFilter from './components/ResourceFilter.vue'
import SystemUsageChart from './components/SystemUsageChart.vue'
import WorkOrder from './components/WorkOrder.vue'
import SystemNotice from './components/SystemNotice.vue'
import { onMounted, ref } from 'vue'
import { getBusinessSystemListApi, getResourceNumberListApi } from '@/api/modules/computingPowerMap'
import type { BusinessSystemListType } from './interface/type'
import { ElMessageBox } from 'element-plus'

// 业务系统列表相关逻辑
const businessSystemList = ref<BusinessSystemListType[]>([])

// 业务系统新增/编辑相关逻辑
const showBusinessSystemCreated = ref(false)
const currentBusinessSystemId = ref<number>()
const handleShowBusinessSystemCreated = () => {
  currentBusinessSystemId.value = undefined
  showBusinessSystemCreated.value = true
}
const handleBusinessSystemCreatedClose = () => {
  ElMessageBox.confirm('请确认是否放弃当前操作')
    .then(() => {
      showBusinessSystemCreated.value = false
      currentBusinessSystemId.value = undefined
    })
    .catch(() => {
      // catch error
    })
}
const handleBusinessSystemSubmit = () => {
  getBusinessSystemList()
  showBusinessSystemCreated.value = false
  currentBusinessSystemId.value = undefined
}

// 业务系统详情查看相关逻辑
const handleShowBusisnessSystemDetail = (id: number) => {
  currentBusinessSystemId.value = id
  showBusinessSystemCreated.value = true
}

/**
 * 获取业务系统列表及选择列表
 */
const getBusinessSystemList = async () => {
  const { entity } = await getBusinessSystemListApi()
  businessSystemList.value = entity.map((item: any) => {
    return {
      busiSystemCode: item.systemCode,
      busiSystemId: item.systemId,
      busiSystemName: item.systemName,
      tenantId: item.tenantId,
    }
  })
}
const selectedSystemIds = ref<number[]>([])
const handleBusinessSystemSeleted = (ids: number[]) => {
  selectedSystemIds.value = ids
  getResourceNumberList()
}

const handleBusinessSystemDeselected = (index: number) => {
  selectedSystemIds.value.splice(index, 1)
  getResourceNumberList()
}

// 资源数量列表关逻辑
const resourceList = ref([
  {
    key: 'ecs',
    name: '云主机',
    show: true,
    data: {},
  },
  {
    key: 'gcs',
    name: 'GPU云主机',
    show: true,
    data: {},
  },
  {
    key: 'evs',
    name: '云硬盘',
    show: true,
    data: {},
  },
  {
    key: 'obs',
    name: '对象存储',
    show: true,
    unit: 'GB',
    data: {},
  },
  {
    key: 'nat',
    name: 'NAT网关',
    show: true,
    data: {},
  },
  {
    key: 'slb',
    name: '负载均衡',
    show: true,
    data: {},
  },
  {
    key: 'eip',
    name: '弹性公网',
    show: true,
    data: {},
  },
  {
    key: 'vpc',
    name: 'VPC',
    show: true,
    data: {},
  },
  {
    key: 'network',
    name: '网络',
    show: true,
    data: {},
  },
  {
    key: 'cq',
    name: '容器配额',
    show: true,
    data: {},
  },
  {
    key: 'mysql',
    name: 'MySQL云数据库',
    show: true,
    data: {},
  },
  {
    key: 'postgreSql',
    name: 'PostgreSQL云数据库',
    show: true,
    data: {},
  },
  {
    key: 'redis',
    name: '通用Redis',
    show: true,
    data: {},
  },
  {
    key: 'cloudport',
    name: '云端口',
    show: true,
    data: {},
  },
  {
    key: 'backup',
    name: '云灾备',
    show: true,
    data: {},
  },
  {
    key: 'vpn',
    name: 'VPN',
    show: true,
    data: {},
  },
  {
    key: 'nas',
    name: 'NAS',
    show: true,
    data: {},
  },
  {
    key: 'pm',
    name: '裸金属',
    show: true,
    data: {},
  },
  {
    key: 'kafka',
    name: 'Kafka',
    show: true,
    data: {},
  },
  {
    key: 'es',
    name: 'ElasticSearch',
    show: true,
    data: {},
  },
  {
    key: 'flink',
    name: 'Flink',
    show: true,
    data: {},
  },
  {
    key: 'bldRedis',
    name: ' 国产Redis',
    show: true,
    data: {},
  },
])
const loading = ref(true)
const getResourceNumberList = async () => {
  loading.value = true
  const { entity } = await getResourceNumberListApi({
    busiSystemIds: selectedSystemIds.value,
  })
  const initEntityItem = { goodType: '', amount: 0, diffAmount: 0, lastMount: 0, ratio: '0%' }
  resourceList.value.forEach((item: any) => {
    const entityItem = entity.find((entityItem: any) => entityItem.goodType === item.key)
    item.data = entityItem || JSON.parse(JSON.stringify(initEntityItem))
    if (item.key === 'obs') {
      if (item.data.amount > 1024) {
        item.data.amount = (item.data.amount / 1024).toFixed(2)
        item.data.diffAmount = (item.data.diffAmount / 1024).toFixed(2)
        item.data.unit = 'TB'
      }
    }
  })
  loading.value = false
}

onMounted(() => {
  getBusinessSystemList().then(() => {
    selectedSystemIds.value = businessSystemList.value.map((item) => item.busiSystemId)
    getResourceNumberList()
  })
})
</script>
<style scoped lang="scss">
#tenantView {
  background-color: white;
  padding: 0 20px;
  .upper {
    display: flex;
    border-top: 1px solid #eee;
    padding: 30px 0px 30px 10px;
    .upper-left {
      width: 70%;
      padding-right: 20px;
      border-right: 1px solid #eee;
    }
    .upper-right {
      width: 500px;
    }
  }
}
</style>
<style lang="scss">
#tenantView {
  .el-drawer__body {
    padding: 0;
  }
}
</style>
