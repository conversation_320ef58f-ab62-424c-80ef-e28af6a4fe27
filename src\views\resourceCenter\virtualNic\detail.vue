<template>
  <div id="VirtualNicDetail" class="table-box">
    <sl-page-header
      title="虚拟网卡详情"
      :icon="{
        class: 'page_xuniwangka',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
        </sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive, computed } from 'vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getVirtualNicDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  vnicName: '',
  type: '',
  typeName: '',
  businessSystemName: '',
  catalogueDomainName: '',
  domainName: '',
  regionName: '',
  azName: '',
  vpcName: '',
  subnetName: '',
  netName: '',
  ipAddress: '',
  vmName: '',
  createTime: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '虚拟网卡名称',
        type: 'text',
        key: 'vnicName',
        span: 8,
      },
      {
        label: '类型',
        type: 'text',
        key: 'typeName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSystemName',
        span: 8,
        hidden: computed(() => route.query.sourceType === 'DG'),
      },
      {
        label: '云类型',
        type: 'text',
        key: 'catalogueDomainName',
        span: 8,
      },
      {
        label: '云平台',
        type: 'text',
        key: 'domainName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'regionName',
        span: 8,
      },
      {
        label: '可用区',
        type: 'text',
        key: 'azName',
        span: 8,
      },
      {
        label: 'VPC/网络',
        type: 'text',
        key: 'vpcName',
        span: 8,
        hidden: computed(() => detailData.type === '3'),
      },
      {
        label: '子网',
        type: 'text',
        key: 'subnetName',
        span: 8,
        hidden: computed(() => detailData.type === '3'),
      },
      {
        label: '管理网段',
        type: 'text',
        key: 'netName',
        span: 8,
        hidden: computed(() => detailData.type !== '3'),
      },
      {
        label: 'IP地址',
        type: 'text',
        key: 'ipAddress',
        span: 8,
      },
      {
        label: '云主机',
        type: 'text',
        key: 'vmName',
        span: 8,
      },
      {
        label: '创建时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getVirtualNicDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
    // 处理类型名称
    if (detailData.type === '2') {
      detailData.typeName = '业务网卡'
    } else if (detailData.type === '3') {
      detailData.typeName = '管理网卡'
    } else {
      detailData.typeName = detailData.type || ''
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.go(-1)
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}
</style>
