import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/modules/user'

/**
 * 资源中心tabs管理hooks
 * 根据用户角色和类型决定显示的tabs
 */
export function useResourceTabs() {
  const userStore = useUserStore()

  // 获取用户信息
  const userInfo = computed(() => userStore.getUserInfo)

  // 获取用户角色代码列表
  const userRoleCodes = computed(() => {
    return userInfo.value.oacRoles?.map((role) => role.roleCode) || []
  })

  // 获取用户类型
  const userType = computed(() => userInfo.value.userType)

  // 判断是否为管理员角色
  const isAdminRole = computed(() => {
    const adminRoles = ['super_admin', 'general_admin', 'operation_group', 'operation_group_host']
    return userRoleCodes.value.some((roleCode) => adminRoles.includes(roleCode))
  })

  // 获取可用的tabs
  const availableTabs = computed(() => {
    // 如果是管理员角色，显示对内资源和对公资源两个tab
    if (isAdminRole.value) {
      return [
        { label: '对内资源', name: 'INNER' },
        { label: '对公资源', name: 'DG' },
      ]
    }

    // 非管理员角色，根据userType判断
    if (userType.value === '2') {
      // userType为2时，只显示对公资源
      return [{ label: '对公资源', name: 'DG' }]
    } else {
      // 其他情况显示对内资源
      return [{ label: '对内资源', name: 'INNER' }]
    }
  })

  // 默认选中的tab
  const defaultActiveTab = computed(() => {
    return availableTabs.value[0]?.name || 'INNER'
  })

  // 是否隐藏tabs组件（当只有一个tab时隐藏）
  const shouldHideTabs = computed(() => {
    return availableTabs.value.length <= 1
  })

  // 当前激活的tab
  const activeTab = ref(defaultActiveTab.value)

  // 切换tab
  const switchTab = (tabName: string) => {
    activeTab.value = tabName
  }

  return {
    availableTabs,
    activeTab,
    defaultActiveTab,
    shouldHideTabs,
    switchTab,
    isAdminRole,
    userType,
  }
}
