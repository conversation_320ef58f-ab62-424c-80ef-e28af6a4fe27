<template>
  <header class="dashboard-header">
    <div class="dashboard-title-area">
      <div class="dashboard-title">
        <span style="margin-left: 6px">智算地图</span>
      </div>
    </div>
  </header>
</template>
<style scoped>
@font-face {
  font-family: 'PangMenZhengDao';
  src: url('/fonts/PangMenZhengDao.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 60px 0 60px;
  z-index: 2;
  background: url('/images/dashboard/top.png') no-repeat center center;
  background-size: cover;
  justify-content: center;
}
.dashboard-title-area {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.dashboard-title {
  font-size: 30px;
  color: #fff;
  font-weight: 10;
  letter-spacing: 12px;
  text-shadow: 0 2px 8px rgba(42, 107, 255, 0.1);
  height: 56px;
  line-height: 38px;
  font-family: 'PangMenZhengDao', 'Microsoft YaHei', Arial, sans-serif;
}
</style>
