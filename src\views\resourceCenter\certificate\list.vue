<template>
  <div class="table-box">
    <sl-page-header
      title="证书管理"
      title-line="证书管理主要用于管理服务器证书和客户端证书，用于HTTPS协议监听和双向认证场景。"
      :icon="{
        class: 'page_zhengshuguanli',
        color: '#0052D9',
        size: '40px',
      }"
    >
      <template #custom>
        <sl-base-tabs
          :tabs="availableTabs"
          v-model="activeTab"
          v-if="!shouldHideTabs"
        ></sl-base-tabs>
      </template>
    </sl-page-header>
    <div class="resource-tab" v-if="activeTab === 'INNER'">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleOpenDrawer" type="primary"> 创建证书 </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: collapsed }"
          ref="formRef"
          :options="formOptions"
          v-model="formModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <DataList
          ref="dataListRef"
          :query-params="queryParams"
          :hide-operations="shouldHideResourceOperations"
        ></DataList>
      </div>
    </div>
    <div v-if="activeTab === 'DG'">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleOpenPublicDrawer" type="primary"> 创建证书 </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: dgCollapsed }"
          ref="dgFormRef"
          :options="DGFormOptions"
          v-model="dgFormModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <PublicDataList
          ref="PubRef"
          :query-params="dgQueryParams"
          :hide-operations="shouldHideResourceOperations"
        ></PublicDataList>
      </div>
    </div>

    <!-- 证书创建抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      title="创建证书"
      size="50%"
      :destroy-on-close="true"
      :before-close="handleCloseDrawer"
    >
      <sl-form
        ref="certificateFormRef"
        v-model="certificateForm"
        :options="certificateFormOptions"
        label-width="120px"
        class="certificate-form"
      >
      </sl-form>
      <div class="drawer-footer">
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCloseDrawer">取消</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import DataList from '@/views/resourceCenter/certificate/components/DataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { useDichooks } from '../hooks/useDichooks'
import { createCertificate } from '@/api/modules/resourecenter'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { getCloudTypeDic, getCloudPlatformDic, getResourcePoolsDic } from '@/api/modules/dic'
import { useRolePermission } from '../hooks/useRolePermission'
import { useResourceTabs } from '../hooks/useResourceTabs'
import PublicDataList from './components/PublicDataList.vue'

const { busiSystemOptions } = useBusiSystemOptions()
const { busiSystemOptions: dgBusiSystemOptions } = useBusiSystemOptions({ sourceType: 'DG' })

const { resourcePoolsDic } = useDichooks()
const { resourcePoolsDic: dgResourcePoolsDic } = useDichooks({
  resourcePools: {
    realmType: 'iaas',
    domainCodes: [
      'plf_prov_moc_zj_vmware',
      'plf_prov_moc_zj_h3c',
      'plf_prov_moc_zj_huawei',
      'plf_prov_moc_zj_inspur',
    ],
  },
})

// 使用统一的tabs管理hook
const { availableTabs, activeTab, shouldHideTabs } = useResourceTabs()

const formRef = ref<any>(null)
const queryParams = ref<any>({})

const formModel = reactive({})

const dataListRef = ref<any>(null)

function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}

function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)

// 字典数据
const cloudTypeOptions = ref<any[]>([])
const cloudPlatformOptions = ref<any[]>([])
const resourcePoolOptions = ref<any[]>([])

// 抽屉相关
const drawerVisible = ref(false)
const certificateFormRef = ref<FormInstance>()
const submitLoading = ref(false)

// 表单数据
const certificateForm = reactive({
  certificateName: '', // 证书名称
  businessSystemId: '', // 业务系统编码
  businessSystemName: '', // 业务系统名称
  catalogueDomainCode: '', // 云类型编码
  catalogueDomainName: '', // 云类型名称
  domainCode: '', // 云平台编码
  domainName: '', // 云平台名称
  regionId: '', // 资源池ID
  regionName: '', // 资源池名称
  regionCode: '', // 资源池编码
  certificateType: '', // 证书类型
  publicKeyContent: '', // 公钥证书内容
  privateKeyContent: '', // 私钥证书内容
  sourceType: '', // 来源类型
})

// 表单配置
const certificateFormOptions = reactive([
  {
    groupItems: [
      {
        label: computed(() => (drawerType.value === 'public' ? '租户' : '业务系统')),
        type: 'select',
        key: 'businessSystemId',
        span: 24,
        options: computed(() =>
          drawerType.value === 'public' ? dgBusiSystemOptions.value : busiSystemOptions.value,
        ),
        rules: [{ required: true, message: '请选择业务系统', trigger: 'change' }],
        onChange: function (selectedOption: any) {
          certificateForm.businessSystemName = selectedOption?.label || ''
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        span: 24,
        options: cloudTypeOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择云类型', trigger: 'change' }],
        onChange: function (selectedOption: any) {
          certificateForm.catalogueDomainName = selectedOption?.name || ''
          // 重置关联字段
          certificateForm.domainCode = ''
          certificateForm.domainName = ''
          certificateForm.regionId = ''
          certificateForm.regionName = ''
          certificateForm.regionCode = ''
          // 清空选项
          cloudPlatformOptions.value = []
          resourcePoolOptions.value = []
          // 加载云平台
          if (certificateForm.catalogueDomainCode) {
            getCloudPlatformList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
            disabled: computed(() => drawerType.value === 'public'),
          },
        },
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        span: 24,
        options: cloudPlatformOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择云平台', trigger: 'change' }],
        onChange: function (selectedOption: any) {
          certificateForm.domainName = selectedOption?.name || ''
          // 重置关联字段
          certificateForm.regionId = ''
          certificateForm.regionName = ''
          certificateForm.regionCode = ''
          // 清空选项
          resourcePoolOptions.value = []
          // 加载资源池
          if (certificateForm.domainCode) {
            getResourcePools()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        span: 24,
        options: resourcePoolOptions,
        labelField: 'name',
        valueField: 'id',
        rules: [{ required: true, message: '请选择资源池', trigger: 'change' }],
        onChange: function (selectedOption: any) {
          certificateForm.regionName = selectedOption?.name || ''
          certificateForm.regionCode = selectedOption?.code || ''
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '证书名称',
        type: 'input',
        key: 'certificateName',
        span: 24,
        rules: [{ required: true, message: '请输入证书名称', trigger: 'blur' }],
      },
      {
        label: '证书类型',
        type: 'select',
        key: 'certificateType',
        span: 24,
        options: [
          { label: '服务器证书', value: 'server_certificate' },
          { label: 'CA证书', value: 'client_certificate' },
        ],
        rules: [{ required: true, message: '请选择证书类型', trigger: 'change' }],
        props: {
          select: {
            clearable: true,
          },
        },
      },
      {
        label: '公钥证书内容',
        type: 'input',
        key: 'publicKeyContent',
        span: 24,
        rules: [{ required: true, message: '请输入公钥证书内容', trigger: 'blur' }],
        props: {
          type: 'textarea',
          rows: 6,
        },
      },
      {
        label: '私钥证书内容',
        type: 'input',
        key: 'privateKeyContent',
        span: 24,
        rules: [{ required: true, message: '请输入私钥证书内容', trigger: 'blur' }],
        props: {
          type: 'textarea',
          rows: 6,
        },
      },
    ],
  },
])

// 表单配置
const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '证书名称',
        type: 'input',
        key: 'certificateName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSystemId',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '证书类型',
        type: 'select',
        key: 'certificateType',
        span: 8,
        options: [
          { label: '服务器证书', value: 'server_certificate' },
          { label: 'CA证书', value: 'client_certificate' },
        ],
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        options: resourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 对公列表筛选条件及配置
const dgFormRef = ref<any>(null)
const dgQueryParams = ref<any>({ type: 'certificate', sourceType: 'DG' })

const dgFormModel = reactive<any>({})
function resetDgSearch() {
  dgFormRef.value!.resetFields()
  dgQueryParams.value = { ...dgQueryParams.value, ...dgFormModel }
}
function doDgSearch() {
  dgQueryParams.value = { ...dgQueryParams.value, ...dgFormModel }
}

// 是否默认折叠搜索项
const dgCollapsed = ref(true)

// 对公资源搜索表单配置
const DGFormOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: '证书名称',
        type: 'input',
        key: 'certificateName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={dgFormModel}
                  resourceList={DGFormOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={resetDgSearch} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={doDgSearch} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (dgCollapsed.value = !dgCollapsed.value)}
              >
                {dgCollapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {dgCollapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '资源ID',
        type: 'input',
        key: 'deviceId',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '资源池',
        type: 'select',
        key: 'resourcePoolId',
        options: dgResourcePoolsDic,
        labelField: 'name',
        valueField: 'id',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '证书类型',
        type: 'select',
        key: 'certificateType',
        span: 8,
        options: [
          { label: '服务器证书', value: 'server_certificate' },
          { label: 'CA证书', value: 'client_certificate' },
        ],
        disabled: false,
        hidden: true,
      },
      {
        label: '订单编号',
        type: 'input',
        key: 'orderCode',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '状态',
        type: 'input',
        key: 'deviceStatus',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订购人',
        type: 'input',
        key: 'applyUserName',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '订购时间',
        type: 'date',
        key: 'effectiveTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 打开抽屉
const handleOpenDrawer = () => {
  drawerVisible.value = true
  drawerType.value = 'inner'
  certificateForm.catalogueDomainCode = ''
  getCloudPlatformList()
}
const drawerType = ref('inner')
// 打开对公抽屉
const handleOpenPublicDrawer = () => {
  drawerVisible.value = true
  drawerType.value = 'public'
  certificateForm.catalogueDomainCode = 'cloudst_group_moc'
  getCloudPlatformList()
}

// 关闭抽屉
const handleCloseDrawer = () => {
  drawerVisible.value = false
  // 重置表单
  certificateForm.certificateName = ''
  certificateForm.businessSystemId = ''
  certificateForm.businessSystemName = ''
  certificateForm.catalogueDomainCode = ''
  certificateForm.catalogueDomainName = ''
  certificateForm.domainCode = ''
  certificateForm.domainName = ''
  certificateForm.regionId = ''
  certificateForm.regionName = ''
  certificateForm.regionCode = ''
  certificateForm.certificateType = ''
  certificateForm.publicKeyContent = ''
  certificateForm.privateKeyContent = ''
  // 清空选项
  cloudPlatformOptions.value = []
  resourcePoolOptions.value = []
  // 重置表单校验状态
  certificateFormRef.value?.resetFields()
}

// 获取云类型列表
const getCloudTypeList = async () => {
  try {
    const { entity } = await getCloudTypeDic(null)
    cloudTypeOptions.value = entity || []
  } catch (error) {
    console.error('获取云类型失败', error)
  }
}

// 获取云平台列表
const getCloudPlatformList = async () => {
  try {
    const params: any = {
      parentCode: certificateForm.catalogueDomainCode,
      businessCode: 'certificate',
    }

    const { entity } = await getCloudPlatformDic(params)
    cloudPlatformOptions.value = entity || []
  } catch (error) {
    console.error('获取云平台失败', error)
  }
}

// 获取资源池列表
const getResourcePools = async () => {
  try {
    const { entity } = await getResourcePoolsDic({
      domainCode: certificateForm.domainCode,
      realmType: activeTab.value == 'DG' ? 'iaas' : '',
    })
    resourcePoolOptions.value = entity || []
  } catch (error) {
    console.error('获取资源池失败', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!certificateFormRef.value) return
  if (drawerType.value === 'public') {
    certificateForm.sourceType = 'DG'
  }
  try {
    const valid = await certificateFormRef.value.validate()
    if (valid) {
      submitLoading.value = true
      await createCertificate(certificateForm)
      ElMessage.success('创建证书成功')
      // 关闭抽屉
      handleCloseDrawer()
      // 刷新列表
      if (dataListRef.value?.proTable) {
        dataListRef.value.proTable.getTableList()
      }
    }
  } catch (error) {
    console.error('创建证书失败', error)
  } finally {
    submitLoading.value = false
  }
}

const { shouldHideResourceOperations } = useRolePermission()

// 初始化数据
onMounted(async () => {
  // 获取云类型数据
  await getCloudTypeList()
})
</script>

<style lang="scss" scoped>
.sl-table-content {
  overflow: auto !important;
}
.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
.certificate-form {
  padding: 20px;
}
.drawer-footer {
  padding: 0 20px 20px 20px;
  text-align: right;
}
</style>
