<template>
  <div :style="style" :class="containerKls">
    <!-- icon & line -->
    <div :class="[ns.e('head'), ns.is(currentStatus)]">
      <div v-if="!isSimple" :class="ns.e('line')">
        <i :class="ns.e('line-inner')" :style="lineStyle" />
      </div>

      <div :class="[ns.e('icon'), ns.is(icon || $slots.icon ? 'icon' : 'text')]">
        <slot name="icon">
          <el-icon v-if="icon" :class="ns.e('icon-inner')">
            <component :is="icon" />
          </el-icon>
          <el-icon
            v-else-if="currentStatus === 'success'"
            :class="[ns.e('icon-inner'), ns.is('status')]"
          >
            <Check />
          </el-icon>
          <el-icon
            v-else-if="currentStatus === 'error'"
            :class="[ns.e('icon-inner'), ns.is('status')]"
          >
            <Close />
          </el-icon>
          <div v-else-if="!isSimple" :class="ns.e('icon-inner')">
            {{ indexFinal }}
          </div>
        </slot>
      </div>
    </div>
    <!-- title & description -->
    <div :class="ns.e('main')">
      <div :style="{ fontSize: props.fontSize }" :class="[ns.e('title'), ns.is(currentStatus)]">
        <slot name="title">{{ title }}</slot>
      </div>
      <div v-if="isSimple" :class="ns.e('arrow')" />
      <div v-else :class="[ns.e('description'), ns.is(currentStatus)]">
        <slot name="description">{{ description }}</slot>
      </div>
    </div>
    <div v-if="slots.branch" :class="[ns.e('head'), ns.is(currentStatus)]">
      <div :class="ns.e('line')" :style="branchLineStyle">
        <i :class="ns.e('line-inner')" :style="lineStyleVertical" />
      </div>
      <sl-base-steps
        is-branch
        :to-be-render="toBeRender"
        :style="branchStyle"
        :space="parent.props.space"
        :process-status="parent.props.processStatus"
        :finish-status="parent.props.finishStatus"
        :active="branchActive"
        :align-center="true"
        :gray-mode="parent.props.grayMode"
      >
        <slot name="branch"></slot>
      </sl-base-steps>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  inject,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  watch,
  provide,
} from 'vue'
import { useNamespace } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { isNumber } from '@/utils/index'
import { stepProps } from './item'
import SlBaseSteps from './steps.vue'

import type { CSSProperties, Ref } from 'vue'
import { useSlots } from 'vue'

const slots = useSlots()
type IStatus = 'wait' | 'process' | 'finish' | 'error' | 'success'
export interface IStepsProps {
  space: number | string
  isBranch: boolean
  active: number | string
  direction: string
  alignCenter: boolean
  simple: boolean
  finishStatus: IStatus
  processStatus: IStatus
  toBeRender: boolean
  grayMode: boolean
}

export interface StepItemState {
  uid: number
  currentStatus: string
  setIndex: (val: number) => void
  setOffset: (val: number) => void
  calcProgress: (status: string) => void
}

export interface IStepsInject {
  props: IStepsProps
  steps: Ref<StepItemState[]>
  branches: Ref<Record<number, StepItemState[]>>
  addStep: (item: StepItemState) => void
  removeStep: (uid: number) => void
  addBranch: (parentIndex: number, step: StepItemState) => void
  removeBranch: (parentIndex: number, uid: number) => void
}

defineOptions({
  name: 'SlBaseStep',
})

const toBeRender = ref(true)
const props = defineProps(stepProps)
const ns = useNamespace('step')
const index = ref(-1)
const offset = ref(1)
const lineStyle = ref({})
const parent = inject('SlBaseSteps') as IStepsInject

function getNextStatus(branchActive: string | number) {
  const strVal = String(branchActive)
  if (strVal === '0') return parent.props.processStatus
  if (toBeRender.value) return parent.props.finishStatus
  return 'await'
}
const lineStyleVertical = computed(() => {
  const nextStatus = getNextStatus(branchActive.value)
  const step = nextStatus === parent.props.processStatus || nextStatus === 'await' ? 0 : 100
  return {
    // 下一个节点状态是parent.props.processStatus 或者 下一个节点是wait
    borderWidth: step && !isSimple.value ? '1px' : 0,
    height: `${step}%`,
  }
})
const internalStatus = ref('')
const currentInstance = getCurrentInstance()
const indexFinal = computed(() => index.value + offset.value)
const branchActive = ref<number | string>(-1)

provide('parentStep', { index: indexFinal })

onMounted(() => {
  watch(
    [() => parent.props.active, () => parent.props.processStatus, () => parent.props.finishStatus],
    ([active]) => {
      const activeArr = String(active).split('-')
      let activeIndex = -1
      // 判断是否是叶子分支
      let isLeafBranch = true

      if (active !== -1) {
        if (activeArr.length > 1) {
          branchActive.value = String(active).slice(2)
          isLeafBranch = false
        }
        activeIndex = Number(activeArr[0])
      }
      toBeRender.value =
        parent.props.toBeRender && activeIndex === index.value && activeArr.length > 1
      // console.log(
      //   props.title,
      //   parent.props.toBeRender,
      //   activeIndex,
      //   index.value,
      //   'parent.props.toBeRender:',
      //   parent.props.toBeRender,
      //   'isBranch:',
      //   parent.props.isBranch,
      // )

      updateStatus(activeIndex, isLeafBranch)
    },
    { immediate: true },
  )
})

onBeforeUnmount(() => {
  parent.removeStep(stepItemState.uid)
})

const currentStatus = computed(() => {
  return props.status || internalStatus.value
})

const prevStatus = computed(() => {
  const prevStep = parent.steps.value[index.value - 1]
  return prevStep ? prevStep.currentStatus : 'wait'
})

const isCenter = computed(() => {
  return parent.props.alignCenter
})

const isVertical = computed(() => {
  return parent.props.direction === 'vertical'
})

const isSimple = computed(() => {
  return parent.props.simple
})

const stepsCount = computed(() => {
  return parent.steps.value.length
})

const isLast = computed(() => {
  return parent.steps.value[stepsCount.value - 1]?.uid === currentInstance?.uid
})

const space = computed(() => {
  return isSimple.value ? '' : parent.props.space
})

const containerKls = computed(() => {
  return [
    ns.b(),
    ns.is(isSimple.value ? 'simple' : parent.props.direction),
    ns.is('flex', isLast.value && !space.value && !isCenter.value),
    ns.is('center', isCenter.value && !isVertical.value && !isSimple.value),
    { 'gray-mode': parent.props.grayMode },
  ]
})

const branchLineStyle = computed(() => {
  const style: CSSProperties = {
    width: '2px',
    height: `${props.branchSpace}px`,
    position: 'absolute',
    top: 0,
    left: 'calc(50% - 1px)',
  }
  return style
})

const branchStyle = computed(() => {
  const numSpace = isNumber(space.value)
    ? Number(space.value)
    : Number(space.value.replace('px', ''))
  const style: CSSProperties = {
    width: `${props.branchLength * numSpace}px`,
    position: 'absolute',
    top: `${props.branchSpace}px`,
  }
  return style
})

const style = computed(() => {
  const style: CSSProperties = {
    flexBasis: isNumber(space.value)
      ? `${space.value}px`
      : space.value
        ? space.value
        : `${100 / (stepsCount.value - (isCenter.value ? 0 : 1))}%`,
  }
  if (isVertical.value) return style
  if (isLast.value) {
    style.maxWidth = `${100 / stepsCount.value}%`
  }
  return style
})

const setIndex = (val: number) => {
  index.value = val
}
const setOffset = (val: number) => {
  offset.value = val
}

const calcProgress = (status: string) => {
  const isWait = status === 'wait'
  const style: CSSProperties = {
    transitionDelay: `${isWait ? '-' : ''}${150 * index.value}ms`,
  }
  const step = status === parent.props.processStatus || isWait ? 0 : 100

  style.borderWidth = step && !isSimple.value ? '1px' : 0
  style[parent.props.direction === 'vertical' ? 'height' : 'width'] = `${step}%`

  lineStyle.value = style
}

const updateStatus = (activeIndex: number, isLeafBranch: boolean) => {
  if (parent.props.isBranch && !parent.props.toBeRender) {
    internalStatus.value = 'wait'
  } else {
    if (activeIndex > index.value) {
      internalStatus.value = parent.props.finishStatus
    } else if (activeIndex === index.value && prevStatus.value !== 'error') {
      if (!isLeafBranch) {
        internalStatus.value = parent.props.finishStatus
      } else {
        internalStatus.value = parent.props.processStatus
      }
    } else {
      internalStatus.value = 'wait'
    }
  }

  const prevChild = parent.steps.value[index.value - 1]
  if (prevChild) prevChild.calcProgress(internalStatus.value)
}

const stepItemState = reactive({
  uid: currentInstance!.uid,
  currentStatus,
  setIndex,
  calcProgress,
  setOffset,
})

parent.addStep(stepItemState)
</script>

<style>
/* 灰色模式样式 */
.gray-mode .el-step__head,
.gray-mode .el-step__main,
.gray-mode .el-step__title,
.gray-mode .el-step__description {
  color: var(--el-text-color-placeholder) !important;
}

.gray-mode .el-step__head.is-process,
.gray-mode .el-step__head.is-finish,
.gray-mode .el-step__head.is-success,
.gray-mode .el-step__head.is-wait,
.gray-mode .el-step__head.is-error {
  color: var(--el-text-color-placeholder) !important;
}

.gray-mode .el-step__title.is-process,
.gray-mode .el-step__title.is-finish,
.gray-mode .el-step__title.is-success,
.gray-mode .el-step__title.is-wait,
.gray-mode .el-step__title.is-error {
  color: var(--el-text-color-placeholder) !important;
}

.gray-mode .el-step__description.is-process,
.gray-mode .el-step__description.is-finish,
.gray-mode .el-step__description.is-success,
.gray-mode .el-step__description.is-wait,
.gray-mode .el-step__description.is-error {
  color: var(--el-text-color-placeholder) !important;
}

.gray-mode .el-step__icon,
.gray-mode .el-step__icon-inner,
.gray-mode .el-step__head.is-process .el-step__icon,
.gray-mode .el-step__head.is-finish .el-step__icon,
.gray-mode .el-step__head.is-success .el-step__icon {
  background-color: var(--el-text-color-placeholder) !important;
  color: #ffffff !important;
  border-color: var(--el-text-color-placeholder) !important;
}

.gray-mode .el-step__line,
.gray-mode .el-step__line-inner {
  background-color: var(--el-text-color-placeholder) !important;
}

/* 确保激活状态的图标也是灰色 */
.gray-mode .el-step__head.is-process .el-step__icon.is-text,
.gray-mode .el-step__head.is-finish .el-step__icon.is-text,
.gray-mode .el-step__head.is-success .el-step__icon.is-text,
.gray-mode .el-step__head.is-process .el-step__icon-inner,
.gray-mode .el-step__head.is-finish .el-step__icon-inner,
.gray-mode .el-step__head.is-success .el-step__icon-inner {
  background-color: var(--el-text-color-placeholder) !important;
  color: #ffffff !important;
}

/* 确保激活状态的线条也是灰色 */
.gray-mode .el-step__head.is-process + .el-step__main .el-step__line,
.gray-mode .el-step__head.is-finish + .el-step__main .el-step__line,
.gray-mode .el-step__head.is-success + .el-step__main .el-step__line {
  background-color: var(--el-text-color-placeholder) !important;
}
</style>
