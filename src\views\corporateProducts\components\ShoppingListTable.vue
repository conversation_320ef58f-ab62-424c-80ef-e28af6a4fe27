<template>
  <div class="shopping-list-table">
    <el-tabs v-model="activeShoppingListTab" @tab-change="handleTabChange">
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        v-for="item in shoppingListTabs"
        :key="item.name"
      >
        <template #label>
          <span class="tab-label">{{ item.label }}</span>
          <span class="tab-count">（{{ item.count }}）</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 表格部分 -->
    <SlProTable
      ref="proTable"
      :data="currentTabData"
      :columns="currentColumns"
      :pagination="false"
      v-if="currentTabData.length > 0 && currentColumns.length > 0"
    >
    </SlProTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

const props = defineProps({
  // 当前租户数据
  currentTenant: {
    type: Object,
    default: () => ({}),
  },
  // 所有可能的标签页配置
  allTabs: {
    type: Array,
    default: () => [
      { label: '云主机', name: 'ecs', count: 0 },
      { label: 'GPU云主机', name: 'gcs', count: 0 },
      { label: '云硬盘', name: 'evs', count: 0 },
      { label: '对象存储', name: 'obs', count: 0 },
      { label: '负载均衡', name: 'slb', count: 0 },
      { label: 'NAT网关', name: 'nat', count: 0 },
      { label: 'VPN', name: 'vpn', count: 0 },
      { label: 'MySQL云数据库', name: 'rdsMysql', count: 0 },
    ],
  },
  // 列配置函数
  getColumnsByResourceType: {
    type: Function,
    required: true,
  },
  // 删除项目的处理函数
  handleDeleteItem: {
    type: Function,
    required: false,
  },
  // 取消退订的处理函数
  onCancelUnsubscribe: {
    type: Function,
    required: false,
  },
})

const emit = defineEmits(['update:activeTab'])

const activeShoppingListTab = ref('')

// 动态生成shoppingListTabs
const shoppingListTabs = computed(() => {
  const tabs: any = []
  props.allTabs.forEach((item: any) => {
    const orderJson = props.currentTenant?.orderJson
    if (orderJson && orderJson[item.name + 'List'] && orderJson[item.name + 'List'].length > 0) {
      tabs.push({
        label: item.label,
        name: item.name,
        count: orderJson[item.name + 'List'].length,
      })
    }
  })
  return tabs
})

// 计算当前要显示的表格数据
const currentTabData = computed(() => {
  if (!props.currentTenant || !activeShoppingListTab.value) return []

  const listKey = activeShoppingListTab.value + 'List'
  return props.currentTenant.orderJson?.[listKey] || []
})

// 使用ref而不是computed，以便可以修改
const currentColumns = ref<any[]>([])

// 更新列配置的函数
const updateColumns = () => {
  currentColumns.value = []
  setTimeout(() => {
    if (activeShoppingListTab.value) {
      // 优先使用onCancelUnsubscribe，如果没有则使用handleDeleteItem
      const deleteHandler = props.onCancelUnsubscribe || props.handleDeleteItem
      currentColumns.value = props.getColumnsByResourceType(
        activeShoppingListTab.value,
        deleteHandler,
      )
    } else {
      currentColumns.value = []
    }
  }, 200)
}

// 标签页变化处理
const handleTabChange = () => {
  emit('update:activeTab', activeShoppingListTab.value)
  updateColumns()
}

// 监听activeShoppingListTab变化，强制刷新列配置
watch(activeShoppingListTab, () => {
  updateColumns()
})

// 初始更新列配置
watch(
  [shoppingListTabs],
  ([newTabs]) => {
    if (newTabs.length > 0) {
      // 如果当前没有选中的标签页，或者当前选中的标签页不在新的标签页列表中，则选择第一个
      if (
        !activeShoppingListTab.value ||
        !newTabs.find((t: any) => t.name === activeShoppingListTab.value)
      ) {
        activeShoppingListTab.value = newTabs[0].name
      }
    }
    // 更新列配置
    handleTabChange()
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.shopping-list-table {
  width: 100%;

  :deep(.el-tabs__item) {
    min-width: 120px;
  }
}

.tab-label {
  margin-right: 4px;
}
</style>
