<template>
  <div class="order-detail-page">
    <sl-page-header
      title="订单详情"
      :icon="{
        class: 'page_orderlist',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>

    <div class="detail-content" v-loading="loading">
      <!-- 资源详情 -->
      <div class="resource-detail-card">
        <div class="card-header">
          <h3>资源详情</h3>
        </div>
        <div class="card-content">
          <ShoppingListTable
            :current-tenant="orderDetail"
            :all-tabs="allTabs"
            :get-columns-by-resource-type="getColumnsByResourceType"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { ElMessage } from 'element-plus'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import ShoppingListTable from '@/views/corporateProducts/components/ShoppingListTable.vue'
import { useShoppingListColumns } from '@/views/corporateProducts/hooks/useShoppingListColumns'
import { corporateOrderDetail } from '@/api/modules/orderCenter'

const route = useRoute()
const router = useRouter()

// 页面状态
const loading = ref(false)
const orderInfo = ref<any>({})
const orderDetail = ref<any>({})

// tab栏配置
const allTabs = ref([
  { label: '云主机', name: 'ecs', count: 0 },
  { label: 'GPU云主机', name: 'gcs', count: 0 },
  { label: '云硬盘', name: 'evs', count: 0 },
  { label: '对象存储', name: 'obs', count: 0 },
  { label: '负载均衡', name: 'slb', count: 0 },
  { label: 'NAT网关', name: 'nat', count: 0 },
  { label: '弹性公网', name: 'eip', count: 0 },
  { label: 'VPN', name: 'vpn', count: 0 },
  { label: 'MySQL云数据库', name: 'rdsMysql', count: 0 },
  { label: '云灾备', name: 'backup', count: 0 },
])

// 获取表格列配置
const { getColumnsByResourceType } = useShoppingListColumns({
  allowDelete: false,
  showStatusAndMessage: true,
  hideQuantity: true,
})

// 获取订单详情
const fetchOrderDetail = async () => {
  const orderId = route.query.id
  if (!orderId) {
    ElMessage.error('订单ID不能为空')
    handleGoBack()
    return
  }

  try {
    loading.value = true
    const res = await corporateOrderDetail({ id: orderId })
    // 设置订单基本信息
    orderInfo.value = {
      orderCode: res.entity.orderCode || '',
      tenantName: res.entity.tenantName || '',
      createByName: res.entity.createByName || '',
      createTime: res.entity.createTime || '',
      status: res.entity.status || '',
    }

    // 处理资源详情数据
    const orderJson: any = {}
    for (let key in res.entity) {
      // 取res.entity中的xxxModelList为orderJson的xxxList，使用正则表达式
      if (key.endsWith('ModelList')) {
        orderJson[key.replace('ModelList', 'List')] = res.entity[key]
      }
    }

    orderDetail.value = {
      orderJson,
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 返回上一页
const handleGoBack = () => {
  router.back()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOrderDetail()
})
</script>

<style lang="scss" scoped>
.order-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.order-info-card,
.resource-detail-card {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.card-content {
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .label {
    font-weight: 500;
    color: #606266;
    min-width: 80px;
  }

  .value {
    color: #303133;
    word-break: break-all;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .detail-content {
    padding: 8px;
  }

  .card-content {
    padding: 16px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;

    .label {
      margin-bottom: 4px;
    }
  }
}
</style>
