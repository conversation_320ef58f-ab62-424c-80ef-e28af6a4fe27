<template>
  <div class="no-card">
    <sl-pro-table
      :columns="tableColumns()"
      :data="goodsAllDetails"
      :pagination="false"
      :is-show-search="false"
      row-key="ids"
      ref="proTable"
    >
    </sl-pro-table>
    <!-- 弹窗 -->
    <el-dialog
      title="资源详情"
      v-model="dialogVisible"
      width="80%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <sl-pro-table
        :columns="tableColumns(false)"
        :data="goodsDetails"
        :pagination="false"
        :is-show-search="false"
      >
        <template #originName="{ row }">
          {{ row.goodsName }}
        </template>
      </sl-pro-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button :disabled="disCancel" @click="dialogVisible = false">
            {{ isCanOperation ? '取 消' : '关 闭' }}
            <el-tooltip
              v-if="disCancel && dialogVisible"
              class="box-item"
              effect="dark"
              content="当前有资源状态发生改变，请点击“提交”按钮合并工单。"
              placement="top-start"
            >
              <el-icon class="ml5">
                <el-icon><Warning /></el-icon>
              </el-icon>
            </el-tooltip>
          </el-button>
          <el-button v-if="isCanOperation" type="primary" @click="submit">提 交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { computed, inject, ref, toRefs } from 'vue'
import type { NonStandardOrderBtnsType, NonStandardOrderType } from '../../../interface/type'
import useModelList, {
  type goodsDetailsType,
} from '@/views/approvalCenter/workOrder/orderApproval/components/ResourceInformation/useModelList'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import nonStandardOrderAllColumns from './goodsColumns'
import { useVModel } from '@vueuse/core'
import { Warning } from '@element-plus/icons-vue'
import { nonStanderWorkOrderResOpenApi } from '@/api/modules/approvalCenter'
import { showTips } from '@/utils'
type PropsType = {
  goodsList: FormDataType[]
  networkDic: FormDataType
  goodsType: NonStandardOrderType | 'unknown' //资源类型
  orderDesc: FormDataType //工单详情信息
}
const props = withDefaults(defineProps<PropsType>(), {})
const { goodsType, networkDic, orderDesc } = toRefs(props)

const emit = defineEmits(['update:goodsList'])

const goodsList = useVModel(props, 'goodsList', emit)

const pageStatus = inject('pageStatus', ref(true))
const btnAuth = inject('btnAuth', ref<NonStandardOrderBtnsType>())

const workOrderId = inject('workOrderId', '')

/**
 * 生成查重的keys
 */
const getKeys = () => {
  const keys: string[] = ['idHash', 'status']
  // 资源开通
  if (btnAuth.value?.resource_creation || (btnAuth.value?.end && orderDesc.value?.offlineOpen))
    keys.push('azId', 'vpcId', 'subnetId')

  //信息归档
  if (btnAuth.value?.information_archive || (btnAuth.value?.end && orderDesc.value?.offlineOpen))
    keys.push('archivedIp')

  return keys
}

const { insertAtIndexAndClear, mergeParent, uniqueByPropertiesWithCustomKey, createObjectsArray } =
  useModelList(getKeys)

const goodsAllDetails = computed({
  get() {
    if (!goodsList.value?.length) return []

    const newGoods = JSON.parse(JSON.stringify(goodsList.value))
    return uniqueByPropertiesWithCustomKey<goodsDetailsType>(newGoods, getKeys(), 'openNum')
  },
  set(val) {
    const arrall: any[] = []
    val.map((row: any) => {
      arrall.push(...createObjectsArray(row))
    })

    goodsList.value = arrall
  },
})

const undateGoodsAllDetails = () => {
  if (!dialogVisible.value) goodsAllDetails.value = mergeParent(goodsAllDetails.value)
}
// -----------------------弹窗数据---------------------

const goodsDetails = ref<goodsDetailsType[]>([])
const goodIndex = ref<number>(-1)
const dialogVisible = ref(false)

// 是否禁用取消
const disCancel = ref<boolean>(false)

const close = () => {
  goodIndex.value = -1
  goodsDetails.value = []
  disCancel.value = false
}

const submit = () => {
  const newGoods = JSON.parse(JSON.stringify(goodsDetails.value))
  const items = uniqueByPropertiesWithCustomKey(newGoods, getKeys(), 'openNum').map((item) => {
    return { ...item }
  })
  // 合并父级
  const goods = JSON.parse(JSON.stringify(goodsAllDetails.value))

  insertAtIndexAndClear(goods, goodIndex.value, items)

  goodsAllDetails.value = mergeParent(goods)
  dialogVisible.value = false
}

// -----------------------获取字典---------------------

// 私网字典
const getPrivateNetworkDic = async (value: number, row: any) => {
  row['subnetId'] = ''
  row['subnetName'] = ''
  row['vpcName'] = ''
  row['vpcCode'] = ''
  row['subnets'] = []
  if (!value) row['privateNetworkDic'] = []
  else {
    const obj = networkDic.value[`${row.regionCode}-${row.azCode}`].find(
      (item: any) => item.id === value,
    )
    row['vpcName'] = obj.name
    row['vpcCode'] = obj.code
    row['privateNetworkDic'] = obj?.subnetOrderList ?? []
  }
}

/**
 * 当维开通中 和 开通成功时 禁用
 * @param status 状态
 */
const rowStatus = (status: string) => {
  return status && ['opening', 'open_success'].includes(status)
}

// 开通
// 网络开通函数
const networkOpen = async (row: any) => {
  // 1.组合数据 校验
  let obj: any = {
    standardWorkOrderId: workOrderId,
    openResType: goodsType.value,
  }
  // 详情内 id 与外层的id参数不同
  if (dialogVisible.value) obj['openResIds'] = [row.id]
  else obj['openResIds'] = row.ids

  let flag = true
  let goodsParams = ['vpcId', 'subnetId']

  goodsParams.forEach((item) => {
    flag = flag && row[item]
  })
  if (!flag) {
    return showTips('请先选择要提交的数据！')
  }

  // 重新开通先做个假的
  if (row.status === 'open_fail') {
    row.status = 'opening'
    row.statusCn = '开通中'
    disCancel.value = true
    return
  }

  const setVlueKey = ['azCode', 'azId', 'azName']
  setVlueKey.map((item) => {
    if (row[item]) obj[item] = row[item]
  })
  obj['planeNetworkModelList'] = [
    {
      type: row.vpcType,
      id: row.vpcId,
      name: row.vpcName,
      plane: row.plane,
      subnets: row.subnets,
      sort: 0,
    },
  ]

  try {
    await nonStanderWorkOrderResOpenApi(obj)
    row.status = 'opening'
    row.statusCn = '开通中'
    disCancel.value = true
  } catch (e) {
    console.error(e)
    disCancel.value = false
  }
}

// 禁用网络
const disabledNetwork = (row: goodsDetailsType) => {
  if (!pageStatus.value || !btnAuth.value?.resource_creation) return true

  if (rowStatus(row.status)) return true

  return false
}
// 是否可操作合并拆分 且 添加开通数据以及分配资源池
const isCanOperation = computed(() => {
  return (
    pageStatus.value && (btnAuth.value?.resource_creation || btnAuth.value?.information_archive)
  )
})

const editFn = (row: any, index: number) => {
  goodsDetails.value = createObjectsArray(row)
  goodIndex.value = index
  dialogVisible.value = true
}

const columns = ref<ColumnProps<goodsDetailsType>[]>([
  { prop: 'openNum', label: '开通数量', width: 150 },
  {
    prop: 'message',
    label: '失败原因',
    align: 'center',
    width: '300px',
    isShow: btnAuth.value?.resource_creation,
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    width: '100px',
    fixed: 'right',
    link: true,
    enum: [
      { label: '待开通', value: 'wait_open', elType: 'primary' },
      { label: '开通中', value: 'opening', elType: 'warning' },
      { label: '开通成功', value: 'open_success', elType: 'success' },
      { label: '开通失败', value: 'open_fail', elType: 'danger' },
      { label: '开通成功', value: 'offline_open', elType: 'success' },
    ],

    isShow: btnAuth.value?.resource_creation || btnAuth.value?.information_archive,
  },
])

const networkColumns = ref<ColumnProps<goodsDetailsType>>({
  prop: 'network_provisioning',
  label: '网络资源',
  align: 'center',
  width: '480px',
  render: ({ row }: { row: any }) => {
    return (
      <>
        {disabledNetwork(row) ? (
          row.vpcName ? (
            row.vpcName + ' / ' + row.subnetName
          ) : (
            '--'
          )
        ) : (
          <div class="flx-center">
            <el-select
              class="mr10"
              v-model={row.vpcId}
              placeholder="请选择网络"
              clearable
              onChange={(value: number) => getPrivateNetworkDic(value, row)}
            >
              {networkDic.value[`${row.regionCode}-${row.azCode}`]?.map((option: any) => (
                <el-option key={option.id} label={option.name} value={option.id} />
              ))}
              {!networkDic.value[`${row.regionCode}-${row.azCode}`]?.length && (
                <el-option label="网络不存在" value="1" disabled={true} />
              )}
            </el-select>
            <el-select
              v-model={row.subnetId}
              placeholder="请选择子网"
              clearable
              onChange={(value: string[]) => {
                row.subnetName = ''
                row['subnets'] = []

                if (!value) return

                let obj = row.privateNetworkDic?.find((item: any) => value == item.id)

                row['subnets'] = [
                  {
                    subnetId: obj.id,
                    subnetName: obj.subnetName,
                    subnetIp: obj.cidr,
                  },
                ]
                row.subnetName = obj.subnetName

                undateGoodsAllDetails()
              }}
            >
              {row.privateNetworkDic?.map((option: any) => (
                <el-option key={option.id} label={option.subnetName} value={option.id} />
              ))}
              {!row.privateNetworkDic?.length && (
                <el-option label="请先选择网络,若不存在请删除重选" value="1" disabled={true} />
              )}
            </el-select>
          </div>
        )}
      </>
    )
  },
})

// ip
const IPInputColumn = ref<ColumnProps<goodsDetailsType>>({
  prop: 'archivedIp',
  label: 'IP',
  width: '200px',
  render: ({ row }: { row: any }) => {
    return (
      <>
        {pageStatus.value ? (
          <el-input
            v-model={row.archivedIp}
            placeholder="请输入"
            type="text"
            onBlur={undateGoodsAllDetails}
            clearable
          ></el-input>
        ) : (
          (row.archivedIp ?? '--')
        )}
      </>
    )
  },
})

const operationColumns = (falg: boolean = true) => {
  const width = falg ? '140px' : '80px'

  return {
    prop: 'operation',
    label: '操作',
    align: 'center',
    fixed: 'right',
    width,
    render: ({ row, $index }: { row: any; $index: number }) => {
      return (
        <>
          {btnAuth.value?.resource_creation && pageStatus.value && !rowStatus(row.status) && (
            <el-button type="primary" onClick={() => networkOpen(row)} link>
              {row.status === 'open_fail' ? '重新开通' : '开通'}
            </el-button>
          )}
          {falg && (
            <el-button type="primary" onClick={() => editFn(row, $index)} link>
              详情
            </el-button>
          )}
        </>
      )
    },
  }
}

const proTable = ref<ProTableInstance>()

const tableColumns = computed(() => (falg: boolean = true) => {
  if (!goodsType.value || goodsType.value === 'unknown') return []

  const filterColumns = columns.value.filter((item) => {
    if (item.prop === 'message') return !falg

    return true
  })
  let indexColumn: ColumnProps = { type: 'index', label: '序号', width: 55 }

  const eumenType = ['ecs', 'gcs']

  // 当为 'ecs', 'gcs' 类型时候且 offlineOpen为false是 显示 azName
  const AllColumns =
    eumenType.includes(goodsType.value) && !orderDesc.value?.offlineOpen
      ? nonStandardOrderAllColumns[goodsType.value]
      : nonStandardOrderAllColumns[goodsType.value].filter(
          (item) => !['catalogueDomainName', 'domainName', 'azName'].includes(item.prop!),
        )

  const newColumns = [indexColumn, ...AllColumns]

  if (btnAuth.value?.information_archive || btnAuth.value?.end) {
    orderDesc.value?.offlineOpen && newColumns.push(IPInputColumn.value)
  }

  if (btnAuth.value?.resource_creation || btnAuth.value?.end) {
    !orderDesc.value?.offlineOpen && newColumns.push(networkColumns.value)
  }

  newColumns.push(...filterColumns)

  if (falg || btnAuth.value?.resource_creation) {
    newColumns.push(operationColumns(falg))
  }

  return newColumns
})
</script>

<style></style>
