<template>
  <sl-form size="small" ref="slFormRef" :options="formOptions" :model-value="basicModel">
    <template #billNo>
      <el-input
        style="max-width: 24%"
        :model-value="basicModel.billNo"
        @update:model-value="updateBillNo"
        placeholder="请输入e55计费号"
      />
      <el-button style="margin-left: 10px; max-width: 80px" type="primary" @click="handleBillNo">
        搜索
      </el-button>
    </template>
    <template #contractCost>
      <div style="display: flex; align-items: center">
        <span style="color: red; margin-right: 5px">¥</span>
        <span>{{ basicModel.contractCost }}</span>
        <span style="margin-left: 5px">元</span>
      </div>
    </template>
  </sl-form>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import SlForm from '@/components/form/SlForm.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import { type IModel } from '../model'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { tenantGetByBillId } from '@/api/modules/resourecenter'
import { getLeaderIdOptions } from '@/views/resourceCenter/hooks/useLevelLeaderIdOptions'

const { basicModel } = defineProps<{
  basicModel: IModel['basicModel']
}>()

const workOrderTitle = ref('')
const resourceDesc = ref('')
const branchLeaderOptions = ref<any>([])

getLeaderIdOptions('branch_leader', branchLeaderOptions)

const handleBillNo = async () => {
  try {
    const { entity } = await tenantGetByBillId({ billId: basicModel.billNo })
    const temp = basicModel
    temp.customerName = entity.customerName
    temp.contactPerson = entity.contactPerson
    temp.contactPhone = entity.contactPhone
    temp.customerManager = entity.customerManager
    temp.customerCode = entity.customerCode
    temp.managerPhone = entity.managerPhone
    temp.customerId = entity.customerId
  } catch (error) {
    console.log(error)
  }
}
const updateBillNo = (val: string) => {
  setValue(basicModel, 'billNo', val)
}
const setValue = (obj: any, key: string, val: any) => {
  obj[key] = val
}

const slFormRef = ref<InstanceType<typeof SlForm>>()
setRef(basicModel)
function setRef(goods: IModel['basicModel']) {
  goods.ref = slFormRef
}

const formOptions = ref([
  {
    style: 'margin: 0;',
    hideBlockTitle: true,
    gutter: 20,
    groupItems: [
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px">
              工单信息
            </SlBlockTitle>
          )
        },
      },
      {
        label: 'e55计费号',
        type: 'slot',
        key: 'billNo',
        slotName: 'billNo',
        span: 24,
        required: true,
        rules: [{ required: true, message: '请输入e55计费号', trigger: ['blur', 'change'] }],
      },
      {
        label: '客户名称',
        type: 'text',
        key: 'customerName',
        span: 8,
      },
      {
        label: '客户联系人',
        type: 'text',
        key: 'contactPerson',
        span: 8,
        disabled: true,
      },
      {
        label: '客户经理',
        type: 'text',
        key: 'customerManager',
        span: 8,
        disabled: true,
      },
      {
        label: '客户编码',
        type: 'text',
        key: 'customerCode',
        span: 8,
        disabled: true,
      },
      {
        label: '客户联系方式',
        type: 'text',
        key: 'contactPhone',
        span: 8,
        disabled: true,
      },
      {
        label: '客户经理联系方式',
        type: 'text',
        key: 'managerPhone',
        span: 8,
        disabled: true,
      },
      {
        label: '工单标题',
        type: 'input',
        key: 'workOrderTitle',
        span: 8,
        modelValue: workOrderTitle,
        required: true,
        placeholder: '请输入内容',
        props: {
          maxlength: 50,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '分公司领导',
        type: 'select',
        key: 'branchLeader',
        required: true,
        rules: [{ required: true, message: '请选择分公司领导', trigger: ['blur', 'change'] }],
        onChange: (val: any) => {
          const temp = basicModel
          temp.branchLeaderName = val.label
        },
        span: 8,
        options: branchLeaderOptions,
      },
      {
        label: '资源申请说明',
        type: 'input',
        key: 'orderDesc',
        props: {
          type: 'textarea',
          autosize: { minRows: 2, maxRows: 4 },
          row: 4,
          maxlength: 200,
          showWordLimit: true,
        },
        span: 24,
        modelValue: resourceDesc,
        placeholder: '请输入内容',
      },
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px">
              工单附件信息
            </SlBlockTitle>
          )
        },
      },
      {
        label: '资源上云说明书',
        type: 'upload',
        key: 'files',
        rules: [{ required: true, message: '请上传资源上云说明书', trigger: ['blur', 'change'] }],
        span: 24,
        props: {
          upload: {
            fileMimeType: ['.doc', '.docx'],
            fileSize: 100,
            fileType: 'RESOURCE_EXPLAIN',
            tip: '请上传word格式文件，大小在 100M 以内',
            fileTemplate: {
              name: '模板下载',
              uid: 'XXXX上云设计说明书.docx',
              fileName: 'XXXX上云设计说明书.docx',
            },
          },
        },
      },
      {
        span: 24,
        render() {
          return (
            <SlBlockTitle size={14} style="margin-bottom:20px">
              工单费用信息
            </SlBlockTitle>
          )
        },
      },
      {
        label: '合同费用',
        type: 'slot',
        key: 'contractCost',
        slotName: 'contractCost',
        span: 24,
      },
    ],
  },
])
</script>

<style scoped lang="scss"></style>
