<template>
  <div class="order-detail-page">
    <sl-page-header
      title="变更详情"
      :icon="{
        class: 'menu_changeorderlist',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>

    <div class="detail-content" v-loading="loading">
      <!-- 资源详情 -->
      <div class="resource-detail-card no-card">
        <div class="card-header">
          <h3>资源详情</h3>
        </div>
        <div class="card-content">
          <div class="orderDetailTab">
            <sl-tabs class="mb10" show-count :tabs="changesTabs" v-model="changeType"></sl-tabs>
          </div>

          <template v-for="item in changesTabs" :key="item.label">
            <template v-if="item.name === changeType">
              <SlProTable
                :key="item.name"
                :data="item.list"
                :columns="resourcesColumns(item.name)"
                :pagination="false"
                row-key="id"
              >
              </SlProTable>
            </template>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { ElMessage } from 'element-plus'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { changeWorkOrderDetailApi } from '@/api/modules/orderCenter'
import type { ChangeType, ChangeTabsType } from './interface'
import changeAllColumns, {
  changeStatusColumn,
  changeTypeEnum,
  changeValueEnum,
  goodsNameKey,
  indexColumn,
  messageColumn,
} from './hooks/goodsColumns'
import type { ColumnProps } from '@/components/SlProTable/interface'

const route = useRoute()
const router = useRouter()

const changeType = ref<ChangeType>('ecs')
const changesTabs = ref<ChangeTabsType[]>([])

// 页面状态
const loading = ref(false)

const initData = (data: any, flag: boolean = true) => {
  const tabs: ChangeTabsType[] = []
  changeValueEnum.forEach((item) => {
    if (data[item.goodsList] && data[item.goodsList].length) {
      tabs.push({
        label: item.desc,
        name: item.code as ChangeType,
        count: data[item.goodsList].length,
        list: setGoodsList(data[item.goodsList], item.code),
      })
    }
  })
  changesTabs.value = tabs
  flag && (changeType.value = tabs[0]?.name || 'ecs')
}

const setGoodsList = (list: any[], type: string) => {
  return list.map((item) => {
    let changeTypeList: any[] = changeTypeEnum.filter((obj) => item.changeType.includes(obj.value))
    return {
      ...item,
      originName: item[goodsNameKey[type as ChangeType]],
      changeTypeList: changeTypeList.map((obj) => setOldAndNewValue(item, obj, type)),
    }
  })
}

/*
  { value: 'instance_spec_change', label: '实例规格更变' },
  { value: 'storage_expand', label: '存储扩容' },
  { value: 'bandwidth_expand', label: '带宽扩容' },
  { value: 'delay', label: '延期' },
*/
/*
ecs gcs 支持 实例规格、存储扩容、带宽扩容、延期
evs obs 支持 存储扩容、延期
slb 支持 实例规格、带宽扩容、延期
eip 支持 带宽扩容、延期
nat 支持 延期

*/
const setOldAndNewValue = (item: any, obj: any, type: string) => {
  if (obj.value === 'instance_spec_change') {
    let oldValue = item.vmSpec

    if (type === 'slb') {
      oldValue = item.slbSpec
    }
    if (type === 'rdsMysql') {
      oldValue = item.spec
    }
    return {
      ...obj,
      oldValue,
      newValue: item.changeFlavorName,
    }
  }

  if (obj.value === 'storage_expand') {
    let oldValue = item.sysDiskSize
    let newValue = item.changeVolumeSize
    if (['gcs', 'ecs', 'mysql', 'redis', 'rdsMysql'].includes(type)) {
      oldValue = item.evsModelList?.map((item: any) => item.spec).join(',') ?? ''
      newValue =
        item.evsModelList
          ?.map((item: any) => {
            let arr = item.spec.split(' ')

            return arr[0] + ' ' + item.changeVolumeSize + 'GB'
          })
          .join(',') ?? ''
    }
    if (type === 'obs') {
      oldValue = item.obsSpec
      let arr = item.obsSpec.split(' ')

      newValue = arr[0] + ' ' + item.changeVolumeSize + 'GB'
    }

    if (type === 'evs') {
      oldValue = item.spec
      let arr = item.spec.split(' ')

      newValue = arr[0] + ' ' + item.changeVolumeSize + 'GB'
    }
    return {
      ...obj,
      oldValue, //改
      newValue,
    }
  }

  if (obj.value === 'bandwidth_expand') {
    let oldValue = item.bandwidth
    let newValue = item.ChangeBandwidth
    if (['gcs', 'ecs', 'slb', 'eip', 'mysql', 'redis', 'rdsMysql'].includes(type)) {
      oldValue = item.eipModel.eipBandwidth + 'M'
      newValue = item.eipModel.changeBandwidth + 'M'
    }
    return {
      ...obj,
      oldValue, //改
      newValue,
    }
  }

  if (obj.value === 'delay') {
    return {
      ...obj,
      oldValue: item.expireTime + ' 到期',
      newValue: item.newExpireTime + ' 到期',
    }
  }

  return {
    ...obj,
  }
}

// 获取订单详情
const fetchOrderDetail = async () => {
  const orderId = route.query.id
  if (!orderId) {
    ElMessage.error('订单ID不能为空')
    handleGoBack()
    return
  }

  try {
    loading.value = true
    const { entity } = await changeWorkOrderDetailApi({ workOrderId: orderId })
    initData(entity)
  } catch (error) {
    console.error('获取订单详情失败:', error)
  } finally {
    loading.value = false
  }
}

const resourcesColumns = (name: ChangeType) => {
  const columns: ColumnProps[] = [
    indexColumn,
    ...changeAllColumns[`${name}`],
    changeStatusColumn,
    messageColumn,
  ]

  return columns
}

// 返回上一页
const handleGoBack = () => {
  router.back()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOrderDetail()
})
</script>

<style lang="scss" scoped>
.order-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.order-info-card,
.resource-detail-card {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.card-content {
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .label {
    font-weight: 500;
    color: #606266;
    min-width: 80px;
  }

  .value {
    color: #303133;
    word-break: break-all;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .detail-content {
    padding: 8px;
  }

  .card-content {
    padding: 16px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;

    .label {
      margin-bottom: 4px;
    }
  }
}
</style>
