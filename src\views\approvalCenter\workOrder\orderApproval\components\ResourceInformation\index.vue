<template>
  <div>
    <!-- 资源申请概览 -->
    <OrderOverviewList v-if="orderOverView" :form-data="orderOverView" :goods-type="goodsType" />
    <ResourceCapacityList v-if="btnAuth.schema_administrator && pageStatus"></ResourceCapacityList>
    <div class="sl-card mb8">
      <sl-block-title>资源开通建议</sl-block-title>
      <sl-form
        :show-block-title="false"
        ref="slFormRef"
        v-model="form"
        :options="visibleFields"
        :dic-collection="dicCollection"
        :gutter="20"
        label-width="175px"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="form" :fields="item" :dic-collection="dicCollection" />
        </template>
      </sl-form>
    </div>

    <!-- 产品列表 -->

    <div class="sl-card mb8" v-if="goodsTabs.length">
      <div class="orderDetailTab">
        <sl-tabs class="mb10" show-count :tabs="goodsTabs" v-model="goodsType"></sl-tabs>
      </div>

      <el-row class="mb10 ml30" v-show="'cq' === goodsType">
        <el-col :span="6">
          <p>
            <span class="label">4A账号 :</span>
            <span class="value ml10">{{ cqGoods?.a4Account ?? '' }}</span>
          </p>
        </el-col>
        <el-col :span="10">
          <p>
            <span class="label">4A账号绑定的手机号 :</span>
            <span class="value ml10">{{ cqGoods?.a4Phone ?? '' }}</span>
          </p>
        </el-col>
      </el-row>
      <div v-if="btnAuth.schema_administrator" class="tip-con">
        <span class="tip"> 提示：请选择租户申请资源可分配的具体资源池！</span>
      </div>
      <div v-if="btnAuth.resource_creation && 'vpn' === goodsType" class="tip-con">
        <span class="tip"> 提示：一个vpc下只能有一个vpn！</span>
      </div>
      <template v-for="item in goodsTabs" :key="item.label">
        <SpecialModelList
          v-if="isshowSpecialModelList(item.name)"
          v-show="goodsType === item.name"
          v-model:goods-list="item.goodsList"
          :goods-type="item.name"
          ref="cloudHostsListRef"
          :btn-auth="btnAuth"
          :resource-pools-dic="dicCollection.resourcePoolsDic"
          :order-id="item.orderDetailId"
          :az-list="azList"
          :order-desc="orderDesc"
          :domain-code-status="domainCodeStatus"
        />
        <ModelList
          v-else
          v-show="goodsType === item.name"
          v-model:goods-list="item.goodsList"
          :goods-type="item.name"
          ref="cloudHostsListRef"
          :btn-auth="btnAuth"
          :resource-pools-dic="dicCollection.resourcePoolsDic"
          :order-id="item.orderDetailId"
          :az-list="azList"
          :order-desc="orderDesc"
          :domain-code-status="domainCodeStatus"
        >
        </ModelList>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, provide, ref, watchEffect, nextTick } from 'vue'
import { useResourceForm } from './useForm'
import { goodsValueEnum, type goodsTypeCodeEnum } from '../../../interface/type'
import ModelList from './ModelList.vue'
import SpecialModelList from './SpecialModelList.vue'
import EnumText from '../../../../components/EnumText.vue'
import ResourceCapacityList from './ResourceCapacityList.vue'
import OrderOverviewList from './OrderOverviewList.vue'
import { areAllValuesNotEmpty, showTips, uniqueByKeys } from '@/utils'
import { getAzListBatchDic } from '@/api/modules/dic'
import { getNetworkTreeApi, standardWorkOrderCapacityCheckApi } from '@/api/modules/approvalCenter'
import { useRoute } from 'vue-router'
import { goodsNameKey } from './goodsColumns'

const props = withDefaults(
  defineProps<{
    orderDesc: FormDataType
    btnAuth: { [key: string]: boolean }
  }>(),
  {
    orderDesc: () => ({}),
  },
)

// const currentTask = inject('currentTask', ref(''))
const pageStatus = inject('pageStatus', ref(true))

const form = ref<FormDataType>({
  business_planning: true,
  business_architecture: true,
  cloud_architecture: true,
  cloud_resources: true,
})

const emit = defineEmits(['update:disabledBtn'])
watchEffect(() => {
  if (props.btnAuth.schema_administrator) {
    const falg =
      form.value.cloud_resources == 'false' ||
      form.value.cloud_architecture == 'false' ||
      form.value.business_architecture == 'false' ||
      form.value.business_planning == 'false'
    emit('update:disabledBtn', falg)
  }
})

/**
 * @description: 根据云平台-与资源池判断特殊处理
 */
const domainCodeStatus = computed(() => {
  return ['plf_prov_nwc_zj_nfvo', 'plf_prov_nwc_zj_plf'].includes(form.value.domainCode)
})

provide('domainCodeStatus', domainCodeStatus)

const isshowSpecialModelList = (goodsType: string) => {
  return (
    ['ecs', 'gcs', 'mysql', 'redis'].includes(goodsType) &&
    domainCodeStatus.value &&
    (props.btnAuth.resource_creation || props.btnAuth.autodit_end)
  )
}

/**
 * @description: 获取子网内使用IP集合
 * 根据子网的分配模式，获取子网内可用的IP集合
 */

const getSubnetIp = computed(() => {
  const goodsList: { [key: string]: string[] } = {}
  goodsTabs.value.map((item) => {
    if (['ecs', 'gcs', 'mysql', 'redis'].includes(item.name)) {
      item.goodsList.map((good) => {
        if (good.planeNetworkModel && Array.isArray(good.planeNetworkModel)) {
          good.planeNetworkModel.map((planeNetwork: any) => {
            planeNetwork.subnets?.map((subnet: any) => {
              goodsList[subnet.subnetId]
                ? goodsList[subnet.subnetId].push(...subnet.ipAddress)
                : (goodsList[subnet.subnetId] = subnet.ipAddress)
            })
          })
        }
      })
    }
  })
  return goodsList
})

provide('getSubnetIp', getSubnetIp)
/**
 * @description: 是否为创新池类型
 */
const isInnovationPool = computed(() => {
  return form.value.domainCode === 'plf_prov_nwc_zj_nfvo'
})
provide('isInnovationPool', isInnovationPool)

//云主机列表备份
let goodsTabsBackup: goodsTabsType[]

const goodsType = ref<goodsTypeCodeEnum>('ecs')
type goodsTabsType = {
  label: string
  name: goodsTypeCodeEnum
  count: number
  goodsList: any[]
  orderOverView: any
  orderDetailId: string
}
const goodsTabs = ref<goodsTabsType[]>([])

const { cloudTypeDic, cloudPlatformDic, resourcePoolsDic } = useResourceForm(form.value, true)

const dicCollection = computed<{ [key: string]: any }>(() => {
  return {
    cloudTypeDic: cloudTypeDic.value,
    cloudPlatformDic: cloudPlatformDic.value,
    resourcePoolsDic: resourcePoolsDic.value,
  }
})

/**
 * 之前是模板的模式给的 直接页面写死的
 * @name 资源申请概览
 */
const showInfo = ref<{ [key in goodsTypeCodeEnum]: any }>()
const orderOverView = computed(() => {
  if (!goodsType.value || !showInfo.value) return null

  return showInfo.value[goodsType.value] ?? null
})

const cqGoods = computed(() => {
  if (!goodsType.value || !goodsTabs.value) return null
  return goodsTabs.value.find((item) => item.name === 'cq')?.goodsList?.[0] ?? null
})

/**
 * 初始化数据/回显数据
 */
const initData = (data: any) => {
  // 1..资源列表
  setGoodsTabs(data)
  // 2.其他信息
  cahgeFormData(data)
  changeFields(data)
}
// 初始化数据
const setGoodsTabs = (data: any) => {
  goodsTabs.value = []
  const tabs: goodsTabsType[] = []
  goodsValueEnum.forEach((item) => {
    if (data[item.goodsList] && data[item.goodsList].length > 0) {
      const goodsList = data[item.goodsList].map((good: any) => {
        const originName =
          good[goodsNameKey[item.code as keyof typeof goodsNameKey] as keyof typeof good] ??
          undefined
        if (['ecs', 'gcs', 'mysql', 'redis'].includes(item.code))
          return {
            ...good,
            goodsName: originName,
            originName: originName,
            planeNetworkModel: !good.planeNetworkModel
              ? []
              : good.planeNetworkModel.map((item: any) => {
                  const subnets =
                    item?.subnets?.map((subnet: any) => {
                      delete subnet.ipAddresses
                      return {
                        ...subnet,
                        ipAddress: subnet?.ipAddress ? [subnet?.ipAddress] : [],
                      }
                    }) ?? []
                  return {
                    ...item,
                    subnets,
                  }
                }),
          }
        else {
          return {
            ...good,
            goodsName: originName,
            originName: originName,
            vpcName: good.planeNetworkModel ? (good.planeNetworkModel.name ?? '') : '',
            subnetNames: good.planeNetworkModel
              ? (good.planeNetworkModel?.subnets
                  ?.map((subnet: any) => subnet.subnetName)
                  .join(',') ?? '')
              : '',
          }
        }
      })

      tabs.push({
        label: item.desc,
        name: item.code as goodsTypeCodeEnum,
        count: data[item.goodsList].length,
        goodsList,
        orderOverView: data.orderOverView,
        orderDetailId: data.id,
      })
    }
  })
  goodsType.value = tabs[0].name
  goodsTabs.value = JSON.parse(JSON.stringify(tabs))
  goodsTabsBackup = tabs.map((item) => {
    return {
      ...item,
      goodsList: item.goodsList.map((good) => {
        let obj: any = {}
        if (['evs', 'eip'].includes(item.name) && good['vmName']) {
        } else {
          obj = {
            regionCode: '',
            regionName: '',
            regionId: '',
          }
        }
        return {
          ...good,
          ...obj,
        }
      }),
    }
  })
  // 资源开通 节点获取
  nextTick(() => {
    props.btnAuth.resource_creation && getAzList(tabs)
    props.btnAuth.resource_creation && getInnovationWorkList(tabs)
    props.btnAuth.resource_creation && disabledResource(tabs)
  })
}
// 判断是否禁用
// 资源开通权限判断
const disabledResource = (tabs: goodsTabsType[]) => {
  if (!tabs.length) return true
  let falg = tabs.every((goodItem) => {
    return goodItem.goodsList.every((item) => item.status == 'open_success')
  })

  emit('update:disabledBtn', falg)
}

// 获取可用区字典
const azList = ref<FormDataType>([])
const getAzList = async (tabs: goodsTabsType[]) => {
  const poolList: string[] = []
  tabs.map(async (goodItem) => {
    goodItem.goodsList.map((good) => {
      poolList.push(good.regionId)
    })
  })
  const ids = [...new Set(poolList)]

  if (!ids.length) return
  const { entity } = await getAzListBatchDic({
    regionIds: ids,
  })
  azList.value = entity
}

// 获取创新池子的网络列表集合
const route = useRoute()
const innovationWorkList = ref<FormDataType>([])
provide('innovationWorkList', innovationWorkList)
const getInnovationWorkList = async (tabs: goodsTabsType[]) => {
  if (!isInnovationPool.value) return

  let poolList: any[] = []
  tabs.map(async (goodItem) => {
    goodItem.goodsList.map((good) => {
      poolList.push({
        regionCode: good.regionCode,
        orderId: route.query?.workOrderId,
      })
    })
  })
  poolList = uniqueByKeys(poolList, ['regionCode', 'orderId'])
  Promise.all(poolList.map(async (item) => getNetworkDic(item, `${item.regionCode}`)))
}

const getNetworkDic = async (params: any, key: string) => {
  if (innovationWorkList.value[key]) return
  // 配置网络 清空

  const { entity } = await getNetworkTreeApi(params)

  let arr = entity.map((item: any) => {
    return {
      ...item,
      name: item.vpcName ? item.vpcName : item.name,
      subnetOrderList: item.subnetOrderList ? item.subnetOrderList : item.vpcSubnetOrderList,
    }
  })
  innovationWorkList.value[key] = arr
}
//
const cahgeFormData = (data: any) => {
  if (data.catalogueDomainCode) form.value.catalogueDomainCode = data.catalogueDomainCode
  if (data.catalogueDomainName) form.value.catalogueDomainName = data.catalogueDomainName
  if (data.domainCode) form.value.domainCode = data.domainCode
  if (data.domainName) form.value.domainName = data.domainName
  if (data.securityDomain) form.value.securityDomain = data.securityDomain
  if (data.securityDomainName) form.value.securityDomainName = data.securityDomainName
  if (data.auditLogList && data.auditLogList.length) {
    data.auditLogList.map((item: any) => {
      form.value[item.auditType] = item.auditResult
    })
  } else {
    form.value.cloud_resources = 'true'
    form.value.cloud_architecture = 'true'
    form.value.business_architecture = 'true'
    form.value.business_planning = 'true'
  }
  showInfo.value = data.showInfo
}

function changeFields(data: any) {
  if (pageStatus.value && props.btnAuth.schema_administrator) {
    //禁用云平台,云类型

    const newModelList = [...(data.evsModelList ?? []), ...(data.eipModelList ?? [])]
    if (
      !newModelList?.length ||
      !newModelList.some((item: any) => item.vmName && item.vmName !== '')
    )
      return

    visibleFields.value = visibleFields.value.map((field: any) => {
      field.groupItems = field.groupItems.map((item: any) => {
        let obj = {
          ...item,
        }
        if (['catalogueDomainCode', 'domainCode'].includes(item.key)) {
          obj = {
            ...item,
            type: 'slot',
            slotName: 'enumText',
            key: item.keyName ? item.keyName : item.key,
          }
        }
        return obj
      })
      return field
    })
    return
  }

  visibleFields.value = visibleFields.value.map((field: any) => {
    field.groupItems = field.groupItems.map((item: any) => {
      return {
        ...item,
        type: 'slot',
        slotName: 'enumText',
        key: item.keyName ? item.keyName : item.key,
      }
    })
    return field
  })
}

const optionsEume = [
  {
    value: 'true',
    label: '是',
  },
  {
    value: 'false',
    label: '否',
  },
]
const visibleFields = ref([
  {
    groupName: '资源开通建议',
    groupItems: [
      {
        label: '云资源是否满足要求',
        type: 'radio',
        key: 'cloud_resources',
        rules: [{ required: true, message: '请选择云资源是否满足要求', trigger: 'change' }],
        props: {},
        options: optionsEume,
        span: 8,
      },
      {
        label: '云化架构是否满足要求',
        type: 'radio',
        key: 'cloud_architecture',
        rules: [{ required: true, message: '请选择云化架构是否满足要求', trigger: 'change' }],
        props: {},
        options: optionsEume,
        span: 8,
      },
      {
        label: '业务架构是否合理',
        type: 'radio',
        key: 'business_architecture',
        rules: [{ required: true, message: '请选择业务架构是否合理', trigger: 'change' }],
        props: {},
        options: optionsEume,
        span: 8,
      },
      {
        label: '业务是否有规划设计',
        type: 'radio',
        key: 'business_planning',
        rules: [{ required: true, message: '请选择业务是否有规划设计', trigger: 'change' }],
        props: {},
        options: optionsEume,
        span: 8,
      },
      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        keyName: 'catalogueDomainName',
        span: 8,
        rules: [{ required: true, message: '云类型不能为空', trigger: 'change' }],
        dicKey: 'cloudTypeDic',
        labelField: 'name',
        valueField: 'code',
        onChange: function () {
          form.value.catalogueDomainName = ''
          if (form.value.catalogueDomainCode) {
            let obj = cloudTypeDic.value.find(
              (item: any) => item.code === form.value.catalogueDomainCode,
            )
            form.value.catalogueDomainName = obj?.name
          }
          form.value.securityDomain = ''
          form.value.domainCode = ''
          cloudPlatformDic.value = []
          resourcePoolsDic.value = []
          goodsTabs.value = JSON.parse(JSON.stringify(goodsTabsBackup))
        },
        props: {
          select: {
            clearable: true,
            filterable: true,
          },
        },
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        keyName: 'domainName',
        span: 8,
        rules: [{ required: true, message: '云平台不能为空', trigger: 'change' }],
        dicKey: 'cloudPlatformDic',
        labelField: 'name',
        valueField: 'code',
        onChange: function () {
          // 赋值name
          form.value.domainName = ''
          if (form.value.domainCode) {
            let obj = cloudPlatformDic.value.find((itme) => itme.code === form.value.domainCode)
            form.value.domainName = obj?.name
          }
          resourcePoolsDic.value = []
          goodsTabs.value = JSON.parse(JSON.stringify(goodsTabsBackup))
        },
        props: {
          select: {
            clearable: true,
            filterable: true,
          },
        },
      },
    ],
  },
])

const slFormRef = ref()

//校验表单
const validateCloudHost = async () => {
  const falg = await slFormRef.value.validate(() => true)
  if (!falg) {
    showTips('请完善资源开通建议表单信息')
    return true
  }

  // 云主机 只有 架构审核人节点 和 资源审核人节点 需要校验

  if (!goodsTabs.value.length) return false
  let flagList: string[] = []
  const goodsModelList: {
    id: string
    regionId: string
    regionCode: string
    regionName: string
  }[] = []
  goodsTabs.value.map((goodItem) => {
    if (!areAllValuesNotEmpty<any>(goodItem.goodsList, ['regionId'])) {
      flagList.push(goodItem.label)
    }
    goodItem.goodsList.map((good) => {
      goodsModelList.push({
        id: good.id,
        regionId: good.regionId,
        regionCode: good.regionCode,
        regionName: good.regionName,
      })
    })
  })

  // 判断是否有空的资源池
  if (flagList.length) {
    showTips(`请选择${flagList.join('、')}的资源池`)
    return true
  }

  // 调用接口判断资源池是否可用

  const { entity } = await standardWorkOrderCapacityCheckApi({
    goodsModelList,
    orderId: route.query?.workOrderId as string,
    ...form.value,
  })
  if (entity && entity.length) {
    try {
      await ElMessageBox.confirm(entity.join('<br />'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      })
      return false
    } catch (error) {
      console.log(error)
      return true
    }
  }

  return false
}
// 校验表单函数
const validateForm = async () => {
  //架构审核校验
  if (props.btnAuth.schema_administrator) return await validateCloudHost()
}

const submitForm = async () => {
  let params: any = {}
  //  2.1 架构审核节点
  if (props.btnAuth.schema_administrator) {
    const goodsModelList: {
      id: string
      regionId: string
      regionCode: string
      regionName: string
    }[] = []
    goodsTabs.value.map((goodItem) => {
      goodItem.goodsList.map((good) => {
        goodsModelList.push({
          id: good.id,
          regionId: good.regionId,
          regionCode: good.regionCode,
          regionName: good.regionName,
        })
      })
    })
    params = {
      ...params,
      ...form.value,
      goodsModelList,
      auditAdvice: `1、云资源${form.value.cloud_resources == 'false' ? '不' : ''}满足要求 ; 2、${form.value.cloud_architecture == 'false' ? '不' : ''}满足云化架构要求 ; 3、业务架构${form.value.business_architecture == 'false' ? '不' : ''}合理 ; 4、业务${form.value.business_planning == 'false' ? '没' : ''}有规划设计 ;`,
    }
    return params
  }
}

defineExpose({
  form,
  validateForm,
  submitForm,
  initData,
  domainCodeStatus,
})
</script>

<style lang="scss" scoped>
.tip-con {
  font-size: 12px;
  color: var(--el-color-primary);
  line-height: 25px;
  margin-left: 4px;
  font-weight: normal;
  .tip {
    padding: 2px 8px;
    background: rgba(72, 127, 239, 0.1);
    border-radius: 5px;
  }
  .danger {
    background: rgba(242, 124, 159, 0.1);
    color: var(--el-color-danger);
  }
}
.resource-provision .page-footer {
  display: flex;
  justify-content: flex-end;
  background: #fff;
}
.orderDetailTab {
  background-color: #f2f3f5;
  position: relative;
}
</style>
