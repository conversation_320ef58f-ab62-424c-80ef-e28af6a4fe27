<template>
  <div id="shopping-carts" class="table-box">
    <sl-page-header
      :title="isEdit ? '编辑' : '变更区'"
      :icon="{
        Vnode: EditPen,
        color: '#0052D9',
        size: '30px',
      }"
    ></sl-page-header>
    <el-scrollbar wrap-class="shopping-carts-scroll-view" class="scroll-view">
      <div v-if="catrsModel.basePropertyList.length" style="background: #fff; overflow: hidden">
        <sl-form
          :key="catrsModel.basePropertyList[0].id"
          ref="slFormRef"
          show-block-title
          :options="formOptions"
          :model-value="catrsModel.basePropertyList[0]"
          size="small"
        >
        </sl-form>
      </div>
      <sl-tabs show-count :tabs="tabs" v-model="activeTab" @update:modelValue="tabClick"> </sl-tabs>
      <ecs-tab
        :goods-list="catrsModel.ecsPropertyList"
        class="goods-tab"
        v-show="activeTab === 'ecs'"
      ></ecs-tab>
      <nat-tab
        :goods-list="catrsModel.natPropertyList"
        class="goods-tab"
        v-show="activeTab === 'nat'"
      ></nat-tab>
      <evs-tab
        :goods-list="catrsModel.evsPropertyList"
        class="goods-tab"
        v-show="activeTab === 'evs'"
      ></evs-tab>
      <eip-tab
        :goods-list="catrsModel.eipPropertyList"
        class="goods-tab"
        v-show="activeTab === 'eip'"
      ></eip-tab>
      <gcs-tab
        :goods-list="catrsModel.gcsPropertyList"
        class="goods-tab"
        v-show="activeTab === 'gcs'"
      ></gcs-tab>
      <obs-tab
        :goods-list="catrsModel.obsPropertyList"
        class="goods-tab"
        v-show="activeTab === 'obs'"
      ></obs-tab>
      <slb-tab
        :goods-list="catrsModel.slbPropertyList"
        class="goods-tab"
        v-show="activeTab === 'slb'"
      ></slb-tab>
      <mysql-tab
        :goods-list="catrsModel.mysqlPropertyList"
        class="goods-tab"
        v-show="activeTab === 'mysql'"
      ></mysql-tab>
      <postgreSql-tab
        :goods-list="catrsModel.postgreSqlPropertyList"
        class="goods-tab"
        v-show="activeTab === 'postgreSql'"
      ></postgreSql-tab>
      <redis-tab
        :goods-list="catrsModel.redisPropertyList"
        class="goods-tab"
        v-show="activeTab === 'redis'"
      ></redis-tab>
    </el-scrollbar>
    <div class="footer">
      <div class="button-group">
        <el-button @click="cancel">取消</el-button>
        <template v-if="isEdit">
          <sl-button :api-function="submit" :style="{ width: '100px' }" type="primary">
            确定修改
          </sl-button>
        </template>
        <template v-else>
          <el-button
            :style="{ width: '60px' }"
            v-permission="'Storage'"
            @click="saveDraft"
            type="primary"
          >
            暂存
          </el-button>
          <sl-button
            v-if="!isEdit"
            :style="{ width: '60px' }"
            v-permission="'Draft'"
            :api-function="submitDraftOrder"
            type="primary"
          >
            草稿
          </sl-button>
          <sl-button
            :api-function="submit"
            v-permission="'Submit'"
            :style="{ width: '100px' }"
            type="primary"
          >
            提交申请
          </sl-button>
        </template>
      </div>
    </div>
    <el-drawer
      v-model="showBusinessSystemCreated"
      :with-header="false"
      :before-close="handleBusinessSystemCreatedClose"
      size="600"
      destroy-on-close
    >
      <BusinessSystemCreated
        @submit="handleBusinessSystemSubmit"
        @close="handleBusinessSystemCreatedClose"
      ></BusinessSystemCreated>
    </el-drawer>
  </div>
</template>

<script setup lang="tsx" name="workOrder">
import { ref, reactive, computed } from 'vue'
import ecsTab from './tabs/ecs/ecs.vue'
import evsTab from './tabs/evs/evs.vue'
import obsTab from './tabs/obs/obs.vue'
import slbTab from './tabs/slb/slb.vue'
import gcsTab from './tabs/gcs/gcs.vue'
import natTab from './tabs/nat/nat.vue'
import eipTab from './tabs/eip/eip.vue'
import mysqlTab from './tabs/mysql/mysql.vue'
import postgreSqlTab from './tabs/postgreSql/postgreSql.vue'
import redisTab from './tabs/redis/redis.vue'
import { useRoute, useRouter } from 'vue-router'
import SlForm from '@/components/form/SlForm.vue'
import { useChangeFormOptions } from '@/views/resourceCenter/hooks/useChangeFormOptions'
import { usePropertyChange, type ICartsModel } from '@/views/resourceCenter/hooks/usePropertyChange'
import SlMessage from '@/components/base/SlMessage'
import eventBus from '@/utils/eventBus'
import { changeWorkOrderCreate, changeWorkOrderDraft } from '@/api/modules/resourecenter'
import { getBusinessSystemListApi } from '@/api/modules/computingPowerMap'
import BusinessSystemCreated from '@/views/computingPowerMap/tenantView/components/BusinessSystemCreated.vue'
import SlButton from '@/components/base/SlButton.vue'
import { EditPen } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const isEdit = computed(() => route.query.orderId !== undefined)
const showBusinessSystemCreated = ref(false)

// 业务系统列表相关逻辑
const handleBusinessSystemCreatedClose = () => {
  ElMessageBox.confirm('请确认是否放弃新增业务系统')
    .then(() => {
      showBusinessSystemCreated.value = false
    })
    .catch(() => {
      // catch error
    })
}

const catrsModel = usePropertyChange()
let temp: any = null
const orderFormOptions = useChangeFormOptions(
  catrsModel.basePropertyList[0],
  ({ busiSystemOptions }) => {
    temp = busiSystemOptions
  },
)
const handleBusinessSystemSubmit = async () => {
  if (temp) {
    const { entity: records } = await getBusinessSystemListApi()
    if (records && Array.isArray(records)) {
      temp.value = records?.map((e: any) => ({
        value: e.systemId,
        label: e.systemName,
      }))
    }
  }
  showBusinessSystemCreated.value = false
}
const formOptions = reactive([orderFormOptions])
const goodsListkeys = [
  'slbPropertyList',
  'ecsPropertyList',
  'mysqlPropertyList',
  'postgreSqlPropertyList',
  'redisPropertyList',
  'evsPropertyList',
  'obsPropertyList',
  'gcsPropertyList',
  'natPropertyList',
  'eipPropertyList',
] as const

const slFormRef = ref()
setRef(catrsModel.basePropertyList[0])

function setRef(goods: any) {
  goods.ref = slFormRef
}
const orderId = (route.query.orderId as string) || ''

function cancel() {
  router.go(-1)
}
function composeArguments(catrsModel: ICartsModel, diff: boolean = false) {
  const orederItem = catrsModel.basePropertyList[0]
  // 工单信息
  const order = {
    id: orderId,
    businessSystemId: orederItem.busiSystemId,
    orderTitle: orederItem.orderTitle,
    businessDepartLeaderId: orederItem.busiDepartLeaderId,
    levelThreeLeaderId: orederItem.levelThreeLeaderId,
    orderDesc: orederItem.orderDesc,
    manufacturer: orederItem.manufacturer,
    manufacturerContacts: orederItem.manufacturerContacts,
    manufacturerMobile: orederItem.manufacturerMobile,
    moduleId: orederItem.moduleId,
    bureauUserName: orederItem.applyUserName,
    departmentName: orederItem.department,
    moduleName: orederItem.moduleName,
    businessDepartLeaderName: orederItem.busiDepartLeaderLabel,
    levelThreeLeaderName: orederItem.levelThreeLeaderLabel,
    businessSystemName: orederItem.busiSystemName,
    resourceApplyFiles: orederItem.files.map((e: any) => {
      return {
        orderFileType: e.orderFileType,
        fileId: e.id,
        ...e,
      }
    }),
    // 产品列表
    ecsPropertyList: [],
    gcsPropertyList: [],
    evsPropertyList: [],
    obsPropertyList: [],
    slbPropertyList: [],
    natPropertyList: [],
    eipPropertyList: [],
    mysqlPropertyList: [],
    postgreSqlPropertyList: [],
    redisPropertyList: [],
    shareEvsPropertyList: [],
  }
  // 添加产品信息
  addGoodsParams(order, catrsModel, diff)
  return order
}

function addGoodsParams(order: any, goodsItem: ICartsModel, diff: boolean = false) {
  const keys = Object.keys(goodsItem) as (keyof ICartsModel)[]
  keys.forEach((key) => {
    if (key === 'basePropertyList') return
    const goods = goodsItem[key]
    order[key] = goods
      .map((item) => {
        const parmas = {
          resourceDetailId: item.resourceDetailId,
          resourceType: item.resourceType,
          changeType: [] as string[],
        } as any
        item.props.forEach((prop) => {
          switch (prop.changeType) {
            case 'instance_spec_change':
              if (prop.resourceType === 'ecs' && item.domainCode === 'plf_prov_nwc_zj_nfvo') {
                parmas.templateCode = prop.templateCode
              }
              if (!diff) {
                if (prop.resourceType === 'slb') {
                  parmas.flavorName = prop.after
                } else {
                  parmas.flavorType = prop.after[0]
                  parmas.flavorName = prop.after[1]
                }
                parmas.changeType.push(prop.changeType)
                return
              }
              if (prop.resourceType === 'slb') {
                if (prop.after && prop.after !== prop.before) {
                  parmas.flavorName = prop.after
                  parmas.changeType.push(prop.changeType)
                }
                return
              }
              if (prop.after[1] && prop.after[1] !== prop.before) {
                parmas.flavorType = prop.after[0]
                parmas.flavorName = prop.after[1]
                parmas.changeType.push(prop.changeType)
              }
              break
            case 'storage_expand':
              const changed = prop.after
                .map((e, index) => {
                  if (!diff) {
                    return {
                      volumeType: e[0],
                      volumeSize: e[1],
                      id: e[2],
                    }
                  }
                  if (e[1] && e[1] === prop.before[index][1]) return null
                  return {
                    volumeType: e[0],
                    volumeSize: e[1],
                    id: e[2],
                  }
                })
                .filter(Boolean)
              if (changed.length > 0) {
                parmas.volumeChangeReqModels = changed
                parmas.changeType.push(prop.changeType)
              }
              break
            case 'bandwidth_expand':
              if (!diff) {
                parmas.eipBandwidth = prop.after
                parmas.eipId = prop.eipId
                parmas.changeType.push(prop.changeType)
                return
              }
              if (prop.after && prop.after !== prop.before) {
                parmas.eipBandwidth = prop.after
                parmas.eipId = prop.eipId
                parmas.changeType.push(prop.changeType)
              }
              break
            case 'delay':
              parmas.changeTime = prop.after
              parmas.changeType.push(prop.changeType)
              break
          }
        })
        if (parmas.changeType.length > 0) return parmas
      })
      .filter(Boolean)
  })
}
function getFormRefs() {
  return goodsListkeys.reduce(
    (acc: any[], key) => {
      return acc.concat(...catrsModel[key].map((ele) => ele.props.map((e) => e.ref)))
    },
    [slFormRef.value],
  )
}
async function submitDraftOrder() {
  const validate = await slFormRef.value.validateField(['orderTitle'])
  if (!validate) return
  const order = composeArguments(catrsModel)
  const { code } = await changeWorkOrderDraft(order)
  if (code === 200) {
    SlMessage.success('提交成功')
    eventBus.emit('propertyChange:updateCount')
    router.push({
      path: '/changeWorkOrder',
    })
    slFormRef.value.resetFields()
  }
}

function isEmptyChange(order: any, catrsModel: ICartsModel) {
  const keys = Object.keys(catrsModel) as (keyof ICartsModel)[]
  return keys.every((key) => {
    if (key === 'basePropertyList') return true
    return order[key].length === 0
  })
}
function submit() {
  return new Promise((resolve, reject) => {
    const formRefs = getFormRefs()
    if (formRefs.length < 2) {
      SlMessage.error('请添加产品')
      return reject(new Error('请添加产品'))
    }
    const formPromises = formRefs.map((ele) => ele.validate())
    Promise.all(formPromises)
      .then(async (res) => {
        if (res.length > 0 && res.every(Boolean)) {
          // 组装入参
          const order = composeArguments(catrsModel, true)
          const isEmpty = isEmptyChange(order, catrsModel)
          if (isEmpty) {
            SlMessage.warning('无变更（变更前后不一致才视为变更）')
            return reject(new Error('无变更'))
          }
          const { code } = await changeWorkOrderCreate(order)
          resolve(true)
          if (code === 200) {
            SlMessage.success('提交成功')
            if (isEdit.value) {
              cancel()
            } else {
              eventBus.emit('propertyChange:updateCount')
              router.push({
                path: '/changeWorkOrder',
              })
            }
          }
        } else {
          SlMessage.error('请填写完整信息')
          reject(new Error('请填写完整信息'))
        }
      })
      .catch((err) => {
        reject(err)
      })
  })
}
function saveDraft() {
  eventBus.emit('propertyChange:updateGoods', { refresh: true, showMessage: true })
}
const activeTab = ref('ecs')
activeTab.value = (route.query.activeTab as string) || 'ecs'

const ecsCount = computed(() => {
  return catrsModel?.ecsPropertyList?.length || 0
})
const gcsCount = computed(() => {
  return catrsModel?.gcsPropertyList?.length || 0
})
const evsCount = computed(() => {
  return catrsModel?.evsPropertyList?.length || 0
})
const obsCount = computed(() => {
  return catrsModel?.obsPropertyList?.length || 0
})
const slbCount = computed(() => {
  return catrsModel?.slbPropertyList?.length || 0
})
const natCount = computed(() => {
  return catrsModel?.natPropertyList?.length || 0
})
const eipCount = computed(() => {
  return catrsModel?.eipPropertyList?.length || 0
})
const mysqlCount = computed(() => {
  return catrsModel?.mysqlPropertyList?.length || 0
})
const postgreSqlCount = computed(() => {
  return catrsModel?.postgreSqlPropertyList?.length || 0
})
const redisCount = computed(() => {
  return catrsModel?.redisPropertyList?.length || 0
})
// tab栏切换
const tabs: any = ref([
  { label: '云主机', name: 'ecs', count: ecsCount },
  { label: 'GPU云主机', name: 'gcs', count: gcsCount },
  { label: '云硬盘', name: 'evs', count: evsCount },
  { label: '弹性公网', name: 'eip', count: eipCount },
  { label: '对象存储', name: 'obs', count: obsCount },
  { label: '负载均衡', name: 'slb', count: slbCount },
  { label: 'NAT网关', name: 'nat', count: natCount },
  { label: 'MySQL云数据库', name: 'mysql', count: mysqlCount },
  { label: 'PostgreSQL云数据库', name: 'postgreSql', count: postgreSqlCount },
  { label: '通用Redis', name: 'redis', count: redisCount },
])
const tabClick = (name: string) => {
  router.replace({
    query: {
      ...route.query,
      activeTab: name,
    },
  })
}
</script>

<style scoped lang="scss">
.scroll-view {
  margin-top: 2px;
}
.footer {
  height: 48px;
  padding: 8px 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  .button-group {
    display: flex;
    align-items: center;
    justify-content: right;
    flex-grow: 1;
  }
}
.add-busisystem-btn {
  display: flex;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
}
#shopping-carts {
  :deep(.el-drawer__body) {
    padding: 0;
  }
}
</style>
