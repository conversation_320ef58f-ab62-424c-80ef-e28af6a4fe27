{"name": "vue-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint ./src --fix", "format": "prettier --write ./src", "prepare": "husky", "commit": "cz", "lint-staged": "lint-staged"}, "dependencies": {"@guolao/vue-monaco-editor": "1.5.5", "@types/echarts": "4.9.22", "@vueuse/core": "12.0.0", "axios": "1.7.7", "echarts": "5.6.0", "echarts-gl": "2.0.8", "echarts-liquidfill": "3.1.0", "element-plus": "2.9.3", "js-cookie": "3.0.5", "lodash": "4.17.21", "marked": "16.1.2", "mitt": "3.0.1", "pinia": "2.2.6", "pinia-plugin-persistedstate": "4.1.3", "screenfull": "6.0.2", "spark-md5": "3.0.2", "v-network-graph": "0.9.21", "vue": "3.5.13", "vue-router": "4.4.5", "wujie-vue3": "1.0.28", "xlsx": "0.18.5"}, "devDependencies": {"@commitlint/config-conventional": "19.5.0", "@tsconfig/node22": "22.0.0", "@types/js-cookie": "3.0.6", "@types/node": "22.10.2", "@types/spark-md5": "3.0.5", "@vitejs/plugin-vue": "5.1.4", "@vitejs/plugin-vue-jsx": "4.0.1", "@vue/eslint-config-prettier": "10.1.0", "@vue/eslint-config-typescript": "14.3.0", "@vue/tsconfig": "0.5.1", "commitizen": "4.3.1", "commitlint": "19.5.0", "commitlint-config-cz": "0.13.3", "cross-env": "7.0.3", "cz-conventional-changelog": "3.3.0", "cz-customizable": "7.2.1", "eslint": "9.14.0", "eslint-plugin-vue": "9.30.0", "husky": "9.1.7", "lint-staged": "15.2.10", "npm-run-all2": "7.0.1", "prettier": "3.3.3", "sass": "1.80.7", "typescript": "5.6.3", "unplugin-auto-import": "0.18.3", "unplugin-vue-components": "0.27.4", "vite": "5.4.10", "vite-plugin-compression": "0.5.1", "vite-plugin-html": "3.2.2", "vite-plugin-vue-devtools": "7.5.4", "vue-tsc": "2.1.10"}, "config": {"commitizen": {"path": "node_modules/cz-customizable", "defaultScope": ""}, "cz-customizable": {"config": "./.cz-config.cjs"}}, "lint-staged": {"test/**/*.{ts,tsx,js,vue}": ["eslint"]}}