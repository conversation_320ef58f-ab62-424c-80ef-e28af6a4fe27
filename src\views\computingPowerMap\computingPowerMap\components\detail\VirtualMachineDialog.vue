<template>
  <div class="bottom-dialog-container">
    <img @click="closeBottomDialog" src="/images/computingPower/comPowerIconClose.png" alt="" />
    <div class="bottom-dialog-content">
      <div class="bottom-dialog-main">
        <div class="bottom-dialog-title">
          <div>虚拟机</div>
          <div>
            <img src="/images/computingPower/comPowerDialogTitleBg.png" alt="" />
          </div>
        </div>
        <div class="bottom-dialog-tooltip">
          <div class="bottom-dialog-infrastructure">
            <div
              v-for="item in warningList"
              :key="item.value"
              class="bottom-dialog-warning-item"
              :style="{ color: item.color }"
              @click="changeWarningItem(item)"
            >
              <i class="iconfont sub-icon icon_warning" :style="{ color: item.color }"></i>
              <span>{{ item.label }} {{ item.count }}</span>
            </div>
          </div>
          <div class="bottom-dialog-search">
            <el-select
              v-model="selectedCloudName"
              placeholder="请选择云类型"
              :suffix-icon="CaretBottom"
              style="width: 130px"
              clearable
              @change="handleCloudNameChange"
            >
              <el-option
                v-for="item in cloudOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="selectedPlatformTypeName"
              placeholder="请选择云平台"
              :suffix-icon="CaretBottom"
              style="width: 130px"
              clearable
              @change="handlePlatformTypeChange"
            >
              <el-option
                v-for="item in platformTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="selectedVrpName"
              placeholder="请选择资源池"
              :suffix-icon="CaretBottom"
              style="width: 130px"
              @change="handleVrpChange"
            >
              <el-option
                v-for="item in vrpOptions"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
            <!-- <el-button class="exportBtn" @click="handleExport" :loading="exportLoading">
              <img src="/images/computingPower/comPowerIconExport.png" alt="" />
              导出
            </el-button> -->
          </div>
        </div>
        <div class="bottom-dialog-table">
          <el-table
            ref="tableRef"
            class="comPowerTable"
            :data="tableData"
            height="250"
            style="width: 100%"
          >
            <el-table-column prop="date" label="序号" width="60" />
            <el-table-column prop="name" label="虚拟机名称" />
            <el-table-column prop="osVersion" label="操作系统版本" />
            <el-table-column prop="platformTypeName" label="所属云平台" />
            <el-table-column show-overflow-tooltip prop="relatedPool" label="所属物理资源池">
              <template #default="scope">
                <span class="c-comPower-table-cell-blue-theme">{{ scope.row.relatedPool }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="mgmtIpv4" label="管理网IPv4地址" />
            <el-table-column prop="mgmtIpv6" label="管理网IPv6地址" />
            <el-table-column prop="memTotalCapacity" label="内存大小(GB)" />
            <el-table-column prop="vdisksInfo" label="存储大小(GB)" />
            <el-table-column prop="vcpuInfo" label="vCPU核数" />
            <el-table-column show-overflow-tooltip prop="bsName" label="关联业务系统">
              <template #default="scope">
                <span class="c-comPower-table-cell-blue-theme">{{ scope.row.bsName }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 10px">
            <Pagination
              :pageable="pageable"
              :handle-size-change="handleSizeChange"
              :handle-current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CaretBottom } from '@element-plus/icons-vue'
import { reactive, ref, watch, onMounted } from 'vue'
import {
  getVirtualMachineListApi,
  getVirtualMachineAlarmStatsApi,
  getVirtualResourcePoolListApi,
  getCloudGroupStatsApi,
} from '@/api/modules/computingPowerMap'
import { ElTable } from 'element-plus'
import Pagination from '../pagination.vue'

// 定义接口请求参数类型
interface VirtualMachineRequestParams {
  cloudName?: string
  platformTypeName?: string
  cityCode?: string
  areaCode?: string
  pageNum?: number
  pageSize?: number
  vrpName?: string
}

// 定义虚拟机响应数据类型（根据8.8.2接口文档）
interface VirtualMachineRecord {
  instanceId: string
  name: string
  osVersion?: string
  platformTypeName?: string
  relatedPool?: string
  mgmtIpv4?: string
  mgmtIpv6?: string
  memTotalCapacity?: number
  vdisksInfo?: number
  vcpuInfo?: number
  bsName?: string
}

interface VirtualMachineResponse {
  records: VirtualMachineRecord[]
  size: number
  current: number
  total: number
}

// 定义告警数据类型（根据8.8.3接口文档）
interface AlarmStatsResponse {
  criticalCount?: number
  majorCount?: number
  minorCount?: number
  warningCount?: number
  infoCount?: number
  indeterminateCount?: number
}

// 定义虚拟机数据项类型
interface VirtualMachineItem {
  date: number
  instanceId: string
  name: string
  osVersion: string
  platformTypeName: string
  relatedPool: string
  mgmtIpv4: string
  mgmtIpv6: string
  memTotalCapacity: string
  vdisksInfo: string
  vcpuInfo: string
  bsName: string
}

// table 实例
const tableRef = ref<InstanceType<typeof ElTable>>()

// 定义props接收父组件传递的参数
const props = defineProps({
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

// 定义需要发出的事件类型
const emit = defineEmits(['close'])

const pageable = reactive({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: 10,
  // 总条数
  total: 0,
})

const handleSizeChange = (val: number) => {
  pageable.pageNum = 1
  pageable.pageSize = val
  fetchVirtualMachineData()
}

const handleCurrentChange = (val: number) => {
  pageable.pageNum = val
  fetchVirtualMachineData()
}

// 筛选条件
const selectedCloudName = ref<string>('')
const selectedPlatformTypeName = ref<string>('')
// 虚拟资源池选择
const selectedVrpName = ref<string>('')

// 选项列表
const cloudOptions = ref<Array<{ label: string; value: string }>>([])
const platformTypeOptions = ref<Array<{ label: string; value: string }>>([])

// 原始云平台数据
const originalCloudData = ref<Array<any>>([])

// 虚拟资源池选项列表
const vrpOptions = ref<Array<{ name: string }>>([])

const tableData = ref<VirtualMachineItem[]>([])

// 获取云平台数据
const fetchCloudPlatformData = async () => {
  try {
    console.log('获取云平台数据')

    const response: { entity: Array<any> | null; code?: number; message?: string } =
      await getCloudGroupStatsApi()

    if (response.code === 200 && response.entity && Array.isArray(response.entity)) {
      // 保存原始数据
      originalCloudData.value = response.entity

      // 提取云类型选项
      const cloudNames = [...new Set(response.entity.map((item) => item.cloudName))].filter(Boolean)
      cloudOptions.value = cloudNames.map((name) => ({ label: name, value: name }))

      // 根据当前选中的云类型更新平台类型选项
      updatePlatformTypeOptions()

      console.log('云平台数据获取成功:', {
        cloudTypes: cloudOptions.value.length,
        platformTypes: platformTypeOptions.value.length,
      })
    } else {
      console.warn('云平台数据接口返回异常:', response)
      // 返回为null或异常时清空数据
      cloudOptions.value = []
      platformTypeOptions.value = []
    }
  } catch (error) {
    console.error('获取云平台数据失败:', error)
    // 异常时清空数据
    cloudOptions.value = []
    platformTypeOptions.value = []
  }
}

// 根据选中的云类型更新平台类型选项
const updatePlatformTypeOptions = () => {
  if (!selectedCloudName.value || !originalCloudData.value.length) {
    platformTypeOptions.value = []
    return
  }

  // 查找选中云类型对应的数据
  const selectedCloudData = originalCloudData.value.find(
    (item) => item.cloudName === selectedCloudName.value,
  )

  if (
    selectedCloudData &&
    selectedCloudData.platformTypeNames &&
    Array.isArray(selectedCloudData.platformTypeNames)
  ) {
    platformTypeOptions.value = selectedCloudData.platformTypeNames.map((name: string) => ({
      label: name,
      value: name,
    }))
  } else {
    platformTypeOptions.value = []
  }

  console.log('更新平台类型选项:', {
    cloudName: selectedCloudName.value,
    platformTypes: platformTypeOptions.value.length,
  })
}

// 筛选条件变化处理
const handleCloudNameChange = () => {
  // 重置下级选择
  selectedPlatformTypeName.value = ''
  selectedVrpName.value = ''
  // 重置分页
  pageable.pageNum = 1
  // 更新平台类型选项
  updatePlatformTypeOptions()
  // 重新获取数据
  fetchVrpOptions()
  fetchVirtualMachineData()
  fetchAlarmStats()
}

const handlePlatformTypeChange = () => {
  // 重置资源池选择
  selectedVrpName.value = ''
  // 重置分页
  pageable.pageNum = 1
  // 重新获取数据
  fetchVrpOptions()
  fetchVirtualMachineData()
  fetchAlarmStats()
}

const warningList = ref([
  {
    value: 'critical',
    label: '严重',
    count: 0,
    color: '#FF4D4F',
  },
  {
    value: 'major',
    label: '主要',
    count: 0,
    color: '#FF9F00',
  },
  {
    value: 'minor',
    label: '次要',
    count: 0,
    color: '#FFEB3B',
  },
  {
    value: 'warning',
    label: '警告',
    count: 0,
    color: '#FFEB3B',
  },
  {
    value: 'info',
    label: '提示',
    count: 0,
    color: '#00D4FF',
  },
  {
    value: 'indeterminate',
    label: '未知',
    count: 0,
    color: '#2196F3',
  },
])

const activeWarningItem = ref('')
const changeWarningItem = (item: any) => {
  activeWarningItem.value = item.value
  // 根据告警级别筛选可以在这里添加逻辑
  console.log('选择告警级别:', item)
}

// 虚拟资源池选择变化处理
const handleVrpChange = () => {
  // 重置分页
  pageable.pageNum = 1
  // 重新获取数据
  fetchVirtualMachineData()
}

// 获取虚拟机列表数据
const fetchVirtualMachineData = async () => {
  try {
    const requestParams: VirtualMachineRequestParams = {
      cloudName: selectedCloudName.value,
      platformTypeName: selectedPlatformTypeName.value,
      cityCode: props.requestParams?.cityCode,
      areaCode: props.requestParams?.areaCode,
      pageNum: pageable.pageNum,
      pageSize: pageable.pageSize,
    }

    // 如果选择了虚拟资源池，添加到请求参数
    if (selectedVrpName.value) {
      requestParams.vrpName = selectedVrpName.value
    }

    console.log('请求虚拟机数据:', requestParams)

    const response: { entity: VirtualMachineResponse | null; code?: number; message?: string } =
      await getVirtualMachineListApi(requestParams)

    if (
      response.code === 200 &&
      response.entity &&
      response.entity.records &&
      Array.isArray(response.entity.records)
    ) {
      // 转换数据格式
      tableData.value = response.entity.records.map(
        (item: VirtualMachineRecord, index: number) => ({
          date: (pageable.pageNum - 1) * pageable.pageSize + index + 1,
          instanceId: item.instanceId || '-',
          name: item.name || '-',
          osVersion: item.osVersion || '-',
          platformTypeName: item.platformTypeName || '-',
          relatedPool: item.relatedPool || '-',
          mgmtIpv4: item.mgmtIpv4 || '-',
          mgmtIpv6: item.mgmtIpv6 || '-',
          memTotalCapacity: item.memTotalCapacity ? `${item.memTotalCapacity}` : '-',
          vdisksInfo: item.vdisksInfo ? `${item.vdisksInfo}` : '-',
          vcpuInfo: item.vcpuInfo ? `${item.vcpuInfo}` : '-',
          bsName: item.bsName || '-',
        }),
      )

      // 更新分页信息
      pageable.total = response.entity.total || 0
      console.log('虚拟机数据获取成功:', tableData.value.length, '条')
    } else {
      console.warn('虚拟机接口返回异常:', response)
      tableData.value = []
      pageable.total = 0
    }
  } catch (error) {
    console.error('获取虚拟机数据失败:', error)
    tableData.value = []
    pageable.total = 0
  }
}

// 获取虚拟机告警数据
const fetchAlarmStats = async () => {
  try {
    const requestParams: VirtualMachineRequestParams = {
      cloudName: selectedCloudName.value,
      platformTypeName: selectedPlatformTypeName.value,
      cityCode: props.requestParams?.cityCode,
      areaCode: props.requestParams?.areaCode,
    }

    // 如果选择了虚拟资源池，添加到请求参数
    if (selectedVrpName.value) {
      requestParams.vrpName = selectedVrpName.value
    }

    console.log('请求虚拟机告警数据:', requestParams)

    const response: { entity: AlarmStatsResponse | null; code?: number; message?: string } =
      await getVirtualMachineAlarmStatsApi(requestParams)

    if (response.code === 200 && response.entity) {
      // 更新告警数量
      warningList.value[0].count = response.entity.criticalCount || 0
      warningList.value[1].count = response.entity.majorCount || 0
      warningList.value[2].count = response.entity.minorCount || 0
      warningList.value[3].count = response.entity.warningCount || 0
      warningList.value[4].count = response.entity.infoCount || 0
      warningList.value[5].count = response.entity.indeterminateCount || 0

      console.log('虚拟机告警数据获取成功')
    } else {
      console.warn('虚拟机告警接口返回异常:', response)
      // 返回为null时不设置默认值，保持原有值
    }
  } catch (error) {
    console.error('获取虚拟机告警数据失败:', error)
    // 异常时不设置默认值，保持原有值
  }
}

// 监听参数变化自动重新请求
watch(
  () => props.requestParams,
  async (newParams: any) => {
    console.log('父组件参数变化:', newParams)

    // 先获取云平台数据（确保选项列表是最新的）
    await fetchCloudPlatformData()

    // 更新筛选条件
    selectedCloudName.value = newParams?.cloudName || ''
    selectedPlatformTypeName.value = newParams?.platformTypeName || ''

    // 根据新的云类型更新平台类型选项
    updatePlatformTypeOptions()

    // 重置虚拟资源池选择
    selectedVrpName.value = ''
    // 重新获取虚拟资源池选项、虚拟机数据和告警数据
    fetchVrpOptions()
    fetchVirtualMachineData()
    fetchAlarmStats()
  },
  { deep: true },
)

// 组件挂载时初始化数据
onMounted(async () => {
  // 先获取云平台数据
  await fetchCloudPlatformData()

  // 然后设置初始值为父组件传入的参数
  selectedCloudName.value = props.requestParams?.cloudName || ''
  selectedPlatformTypeName.value = props.requestParams?.platformTypeName || ''

  // 根据初始云类型更新平台类型选项
  updatePlatformTypeOptions()

  fetchVrpOptions()
  fetchVirtualMachineData()
  fetchAlarmStats()
})

// 关闭当前弹窗
const closeBottomDialog = () => {
  emit('close')
}

// 导出虚拟机列表
// const exportLoading = ref(false)
// const handleExport = async () => {
//   try {
//     exportLoading.value = true

//     const requestParams: VirtualMachineRequestParams = {
//       cloudName: props.requestParams?.cloudName,
//       platformTypeName: props.requestParams?.platformTypeName,
//       cityCode: props.requestParams?.cityCode,
//       areaCode: props.requestParams?.areaCode,
//       pageNum: 1,
//       pageSize: 9999, // 导出全部数据
//     }

//     // 如果选择了虚拟资源池，添加到请求参数
//     if (selectedVrpName.value) {
//       requestParams.vrpName = selectedVrpName.value
//     }

//     console.log('导出虚拟机数据:', requestParams)

//     // const response = await downloadVirtualMachineListApi(requestParams)

//     // 创建下载链接
//     // const blob = new Blob([response as any], {
//     //   type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//     // })
//     // const url = window.URL.createObjectURL(blob)
//     // const link = document.createElement('a')
//     // link.href = url
//     // link.download = `虚拟机列表_${new Date().toISOString().slice(0, 10)}.xlsx`
//     // document.body.appendChild(link)
//     // link.click()
//     // document.body.removeChild(link)
//     // window.URL.revokeObjectURL(url)

//     // ElMessage.success('导出成功')
//     console.log('虚拟机数据导出成功')
//   } catch (error) {
//     console.error('导出虚拟机数据失败:', error)
//     // ElMessage.error('导出失败，请稍后重试')
//   } finally {
//     exportLoading.value = false
//   }
// }

// 获取虚拟资源池列表
const fetchVrpOptions = async () => {
  try {
    const requestParams = {
      cloudName: selectedCloudName.value,
      platformTypeName: selectedPlatformTypeName.value,
      cityCode: props.requestParams?.cityCode,
      areaCode: props.requestParams?.areaCode,
      pageNum: 1,
      pageSize: 9999,
    }

    console.log('请求虚拟资源池列表:', requestParams)

    const response: {
      entity: { records: { name: string }[] } | null
      code?: number
      message?: string
    } = await getVirtualResourcePoolListApi(requestParams)

    if (
      response.code === 200 &&
      response.entity &&
      response.entity.records &&
      Array.isArray(response.entity.records)
    ) {
      vrpOptions.value = response.entity.records.map((item) => ({ name: item.name }))
      console.log('虚拟资源池列表获取成功:', vrpOptions.value.length, '条')
    } else {
      console.warn('虚拟资源池接口返回异常:', response)
      vrpOptions.value = []
    }
  } catch (error) {
    console.error('获取虚拟资源池列表失败:', error)
    vrpOptions.value = []
  }
}
</script>
<style scoped lang="scss">
.bottom-dialog-container {
  position: fixed;
  left: 10px;
  right: 10px;
  bottom: 10px;
  width: calc(100% - 20px);
  height: 436px;
  z-index: 20;
  box-sizing: border-box;
  //height: 367px;
  & > img {
    position: absolute;
    top: 0;
    right: 10px;
    cursor: pointer;
  }
  .bottom-dialog-content {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 400px;
    padding: 12px 9px 11px 10px;
    box-shadow: 0px 0px 5px 0px #09155c;
    border-radius: 1px;
    box-sizing: border-box;
    background-color: rgba(170, 198, 245, 0.3);
    //background: url('/images/computingPower/comPowerBottomDialogBg.png') no-repeat 0 0;
    //background-size: 100% 100%;
    .bottom-dialog-main {
      width: 100%;
      height: 100%;
      padding: 8px 15px;
      border-radius: 1px;
      box-sizing: border-box;
      background: linear-gradient(0deg, rgba(124, 166, 238, 0.92), rgba(10, 52, 103, 0.92));
    }
    //opacity: 0.92;
    .bottom-dialog-title {
      //display: flex;
      div {
        display: inline-block;
        font-size: 18px;
        color: #ffffff;
        width: calc(100% - 100px);
        vertical-align: top;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei';
        img {
          width: 100%;
        }
      }
      & > div:nth-child(1) {
        width: 70px;
        margin-right: 12px;
      }
    }
    .bottom-dialog-tooltip {
      height: 41px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border-bottom: 2px solid #3161b4;
      padding-top: 4px;
      position: relative;
      display: flex;
      align-items: end;
      justify-content: center;
      .bottom-dialog-infrastructure {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 10px;
        font-size: 16px;
        .bottom-dialog-warning-item {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 50px;
          cursor: pointer;
          .icon_warning {
            font-size: 24px;
            margin-right: 4px;
          }
        }
      }
      .bottom-dialog-search {
        display: inline-block;
        vertical-align: top;
        margin-right: 60px;
        position: absolute;
        right: 0;
        top: 0;
        .el-select {
          background: transparent;
          font-size: 15px;
          color: #ffffff;
          margin-right: 13px;
        }
        .exportBtn {
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          img {
            margin-right: 8px;
          }
        }
      }
    }
    .bottom-dialog-table {
      .el-table {
        background: transparent;
        th.el-table__cell {
          background: transparent;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.comPowerPage.el-pagination {
  .el-select__wrapper.el-tooltip__trigger {
    background: #3f6eb8;
    color: #fff;
    box-shadow: none;
    border-radius: 1px;
    border: 1px solid #42659e;
    .el-select__input.is-default,
    .el-select__selected-item.el-select__placeholder {
      color: #fff;
    }
  }
  .el-pagination__total.is-first {
    color: #ffffff;
    font-size: 15px;
  }
  .el-pagination__goto {
    color: #ffffff;
  }
  .el-input__wrapper {
    background: #3f6eb8;
    border: none;
    box-shadow: none;
    .el-input__inner {
      color: #ffffff;
    }
  }
  .el-pagination__classifier {
    color: #ffffff;
  }
}
.comPowerPage.el-pagination.is-background .btn-next:disabled,
.comPowerPage.el-pagination.is-background .btn-prev:disabled {
  background: transparent;
  color: #3f5d78;
}
.comPowerPage.el-pagination.is-background .btn-next,
.comPowerPage.el-pagination.is-background .btn-prev {
  background: transparent;
  color: #0787de;
}
.comPowerPage.el-pagination.is-background .el-pager li {
  background: #3f6eb8;
  padding: 2px 16px;
}
.comPowerPage.el-pagination.is-background .el-pager li.is-active {
  background: #317ced;
}
.comPowerPage.el-pagination .btn-next .el-icon,
.comPowerPage.el-pagination .btn-prev .el-icon {
  font-size: 24px;
}
</style>
