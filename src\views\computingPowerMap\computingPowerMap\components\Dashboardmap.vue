<template>
  <div class="dashboard-cards">
    <div
      :key="'topNavItem' + index"
      v-for="(item, index) in topNavList"
      :class="{
        active: showTopType == index,
        disabled: item.cloudName === 'IT云',
      }"
      @click="item.cloudName !== 'IT云' ? changeTopType(index, item) : null"
    >
      <div>
        <img src="/images/computingPower/comPowerYidongyun.png" alt="" />
      </div>
      <div class="active">
        <span>{{ item.cloudName }}</span>
        <p>
          <b>硬件资源池数量</b>
          <i>{{ item.hardwarePoolNum }} </i>
        </p>
        <p>
          <b>设备数量</b>
          <i>{{ item.baseDeviceNum }}</i>
        </p>
      </div>
    </div>
  </div>
  <section class="panel dashboard-map">
    <div
      ref="mapRef"
      style="width: 720px; height: 710px"
      @contextmenu.prevent="handleContextMenu"
    ></div>
  </section>
  <!-- 右键菜单 -->
  <div
    v-if="contextMenu.visible"
    class="context-menu"
    :style="{
      left: `${contextMenu.x}px`,
      top: `${contextMenu.y}px`,
    }"
  >
    <div class="menu-header" @click="showLeftDialog">
      <span>{{ contextMenu.name || '位置' }}</span>
    </div>
  </div>
  <div v-if="props.city != 'zhejiang'" class="returnBtn">
    <el-button type="text" @click="returnHome">返回上级</el-button>
  </div>
  <div v-if="showBottomCloudInfo" class="dashboard-bottom-dialog">
    <div class="dashboard-bottom-dialog-box">
      <div>
        <template v-if="platformTypeNames.length > 0">
          <div
            :key="'platformTypeNamesItem' + index"
            v-for="(item, index) in platformTypeNames"
            @click="changeBottomTootip(index, item)"
            class="item"
            :class="{ active: showBottomTootipType == index }"
          >
            <i class="imgIcon"></i>
            <span v-if="item == '融合边缘云-VMWARE'"> 融合边缘云- <b>vmware</b> </span>
            <span v-else>{{ item }}</span>
          </div>
        </template>
        <template v-else> 暂无数据 </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, toRef } from 'vue'
import * as echarts from 'echarts/core'
import { type ECharts } from 'echarts/core'
import {
  getComPowerStatsCloudGroup,
  getComPowerMapStatsCloudCityAreas,
} from '@/api/modules/comPowerCenter'

let chart: ECharts | null = null
const emit = defineEmits(['skipCityPage', 'showLeftDialog', 'transferData', 'transferCloudData'])

const mapData = ref<any>(null)

const selectedCityCode = ref('')
import areaNameCodeModuleJson from '../json/cityNameCode.json'
const areaCodeModule: any = ref({})
areaCodeModule.value = areaNameCodeModuleJson
// 动态加载地图数据
const loadMapData = async () => {
  try {
    let mapDataModule
    if (props.city === 'zhejiang') {
      mapDataModule = await import('../json/zhejiang.json')
    } else {
      selectedCityCode.value = areaCodeModule.value[props.city]
      mapDataModule = await import(`../json/cities/${props.city}.json`)
    }
    mapData.value = mapDataModule.default
    return mapData.value
  } catch (error) {
    // 如果加载失败，回退到浙江省地图
    if (props.city !== 'zhejiang') {
      const fallbackModule = await import('../json/zhejiang.json')
      mapData.value = fallbackModule.default
      return mapData.value
    }
    throw error
  }
}

// 城市名称映射
const cityNameMap: Record<string, string> = {
  hangzhou: '杭州',
  ningbo: '宁波',
  wenzhou: '温州',
  jiaxing: '嘉兴',
  huzhou: '湖州',
  shaoxing: '绍兴',
  jinhua: '金华',
  quzhou: '衢州',
  zhoushan: '舟山',
  taizhou: '台州',
  lishui: '丽水',
  zhejiang: '浙江',
}

// 计算当前显示的地图名称
const currentMapName = computed(() => {
  return cityNameMap[props.city] || '浙江'
})

// 定义props
interface Props {
  city?: string
}

const props = withDefaults(defineProps<Props>(), {
  city: 'zhejiang',
})

watch(toRef(props, 'city'), async (newCity, oldCity) => {
  if (newCity !== oldCity && chart) {
    try {
      // 重新加载地图数据
      const geojson = await loadMapData()
      // 重新注册地图
      echarts.registerMap(currentMapName.value, geojson)
      // 重新设置地图选项
      setMapOption()
    } catch (error) {
      console.error('地图切换失败:', error)
    }
  }
})

// 单击事件定时器
const timerId: any = ref(null)
// 记录事件日志
const mapEvent = (type: 'click' | 'dblclick', param: any) => {
  if (type == 'click' && timerId.value) {
    return
  } else {
    if (type == 'click') {
      if (!chart) return
      contextMenu.value.visible = false
      chart.dispatchAction({
        type: 'downplay',
        // 用 index 或 id 或 name 来指定系列。
        // seriesName?: string | string[],
        seriesId: 'test1',
        // 可以是一个数组指定多个名称。
        name: allMapCityData.value,
      })
      // 判断是选中，还是取消选中
      if (transferParams.value.cityCode == param.data.areaCode) {
        if (props.city == 'zhejiang') {
          transferParams.value.cityCode = ''
        } else {
          transferParams.value.cityCode = selectedCityCode.value
        }
      } else {
        transferParams.value.cityCode = param.data.areaCode
      }
      emit('transferData', transferParams.value)
    } else {
      contextMenu.value.visible = false
      if (param.seriesName == '浙江数据') {
        transferParams.value.city = param.data.pinyin
        transferParams.value.cityCode = param.data.areaCode
        emit('transferData', transferParams.value)
      } else {
        transferParams.value.areaCode = param.data.areaCode
        emit('transferData', transferParams.value)
      }
    }
  }
}

// const powerList = ref<any>([])
onMounted(async () => {
  getComPowerStatsCloudGroup({}).then((res: any) => {
    if (res.code === 200) {
      let data = res.entity
      if (data.length == 2) {
        data.push({
          baseDeviceNum: 0,
          cloudName: 'IT云',
          hardwarePoolNum: 0,
          platformTypeNames: [],
        })
      }
      topNavList.value = res.entity
      emit('transferCloudData', topNavList.value)
    }
  })
  // 地图
  const geojson = await loadMapData()
  // echarts.registerMap('浙江', geojson)
  echarts.registerMap(currentMapName.value, geojson)
  chart = echarts.init(mapRef.value!, 'default', {
    width: 720,
    height: 681,
    renderer: 'svg', // 明确指定使用SVG渲染器
  })
  // 初始化地图
  setMapOption()

  // 添加点击事件监听
  chart.on('click', (params: any) => {
    timerId.value = setTimeout(() => {
      if (timerId.value == null) {
        return
      }
      clearTimeout(timerId.value)
      timerId.value = null
      mapEvent('click', params)
      // 这里可以执行实际的单击操作
    }, 300)
  })
  chart.on('dblclick', (params: any) => {
    if (timerId.value) {
      clearTimeout(timerId.value)
      timerId.value = null
    }
    mapEvent('dblclick', params)
  })
  chart.on('contextmenu', (params: any) => {
    activeInfo.value = params
    const event = params.event.event
    const scaleX = window.innerWidth / 1920
    const scaleY = window.innerHeight / 1080
    const x = event && event.clientX / scaleX
    const y = event && event.clientY / scaleY
    // 显示右键菜单
    contextMenu.value = {
      visible: true,
      x,
      y,
      name: '资源概况查看',
    }
  })
})

//平台类型信息
const topNavList = ref<
  { baseDeviceNum: number; cloudName: string; hardwarePoolNum: number; platformTypeNames: any }[]
>([])
const platformTypeNames = ref<string[]>([])

const showTopType = ref(4)
const showBottomTootipType = ref(5)
const showBottomCloudInfo = ref(false)
const transferParams = ref({
  cloudName: '',
  platformTypeName: '',
  cityCode: '',
  areaCode: '',
  city: '',
})
const changeTopType = (type: any, item: any) => {
  platformTypeNames.value = item.platformTypeNames
  if (showTopType.value == type) {
    showTopType.value = 4
    showBottomCloudInfo.value = false
    showBottomTootipType.value = 5
    transferParams.value.cloudName = ''
    transferParams.value.platformTypeName = ''
    if (!chart) return
    chart.dispatchAction({
      type: 'downplay',
      seriesId: 'test1',
      name: allMapCityData.value,
    })
  } else {
    showTopType.value = type
    showBottomCloudInfo.value = true
    showBottomTootipType.value = 5
    transferParams.value.cloudName = ''
    transferParams.value.platformTypeName = ''
    if (item.cloudName == 'IT云') {
      return
    }
    transferParams.value.cloudName = item.cloudName
    if (!chart) return
    chart.dispatchAction({
      type: 'downplay',
      seriesId: 'test1',
      name: allMapCityData.value,
    })
  }

  emit('transferData', transferParams.value)
}
// 查找方法
function findCitiesByCodes(codes: string[], cityMap: CityMap): string[] {
  // 创建映射字典：键为区号，值为城市名
  const codeToCityMap: Record<string, string> = {}

  for (const [cityName, cityInfo] of Object.entries(cityMap)) {
    codeToCityMap[cityInfo.areaCode] = cityName
  }

  // 将区号转换为对应城市名
  return codes.map((code) => codeToCityMap[code] || '')
}
//点击底部 根据云联动地图高亮
const changeBottomTootip = (type: any, item: any) => {
  transferParams.value.platformTypeName = item
  emit('transferData', transferParams.value)
  showBottomTootipType.value = type
  let selectCity: any = []
  let selectArea: any = []
  let needHighLightData: any = []
  getComPowerMapStatsCloudCityAreas({
    cloudName: transferParams.value.cloudName,
    platformTypeName: transferParams.value.platformTypeName,
  }).then((res) => {
    if (res.code === 200) {
      let selectData: any = res.entity
      selectData.forEach((item: any) => {
        selectCity.push(item.cityCode)
        item.areaCodes.forEach((area: any) => {
          selectArea.push(area)
        })
      })
      if (props.city === 'zhejiang') {
        needHighLightData = findCitiesByCodes(selectCity, cityMap)
      } else {
        needHighLightData = findCitiesByCodes(selectArea, cityMap)
      }
      if (!chart) return
      // 全部取消高亮
      chart.dispatchAction({
        type: 'downplay',
        // 用 index 或 id 或 name 来指定系列。
        // seriesName?: string | string[],
        seriesId: 'test1',
        // 可以是一个数组指定多个名称。
        name: allMapCityData.value,
      })
      chart.dispatchAction({
        type: 'highlight',
        // seriesName?: string | string[],
        seriesId: 'test1',
        // 可以是一个数组指定多个名称。
        name: needHighLightData,
      })
    }
  })
}
// 定义类型
interface ContextMenu {
  visible: boolean
  x: number
  y: number
  name: string | null
}

// let myChart: ECharts | null = null;
// 右键菜单状态
const contextMenu = ref<ContextMenu>({
  visible: false,
  x: 0,
  y: 0,
  name: null,
})
// 处理右键菜单事件
const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault() //
}

const mapRef = ref<HTMLDivElement | null>(null)

// 定义城市数据类型
interface CityData {
  areaCode: string
  name: string
}

// 定义城市映射类型
type CityMap = Record<string, CityData>
const cityMap: CityMap = {
  杭州市: {
    areaCode: '571',
    name: 'hangzhou',
  },
  宁波市: {
    areaCode: '574',
    name: 'nongbo',
  },
  温州市: {
    areaCode: '577',
    name: 'wenzhou',
  },
  嘉兴市: {
    areaCode: '573',
    name: 'jiaxing',
  },
  湖州市: {
    areaCode: '572',
    name: 'huzhou',
  },
  绍兴市: {
    areaCode: '575',
    name: 'shaoxing',
  },
  金华市: {
    areaCode: '579',
    name: 'jinhua',
  },
  衢州市: {
    areaCode: '570',
    name: 'quzhou',
  },
  舟山市: {
    areaCode: '580',
    name: 'zhoushan',
  },
  台州市: {
    areaCode: '576',
    name: 'taizhou',
  },
  丽水市: {
    areaCode: '578',
    name: 'lishui',
  },
  柯城区: { areaCode: '5701', name: 'quzhou' },
  江山市: { areaCode: '5702', name: 'quzhou' },
  常山县: { areaCode: '5703', name: 'quzhou' },
  开化县: { areaCode: '5704', name: 'quzhou' },
  龙游县: { areaCode: '5705', name: 'quzhou' },
  衢江区: { areaCode: '5706', name: 'quzhou' },
  上城区: { areaCode: '5711', name: 'hangzhou' },
  萧山区: { areaCode: '5712', name: 'hangzhou' },
  富阳区: { areaCode: '5713', name: 'hangzhou' },
  余杭区: { areaCode: '5714', name: 'hangzhou' },
  建德市: { areaCode: '5715', name: 'hangzhou' },
  淳安县: { areaCode: '5716', name: 'hangzhou' },
  桐庐县: { areaCode: '5717', name: 'hangzhou' },
  临安区: { areaCode: '5718', name: 'hangzhou' },
  滨江区: { areaCode: '5719', name: 'hangzhou' },
  钱塘区: { areaCode: '571A', name: 'hangzhou' },
  西湖区: { areaCode: '571B', name: 'hangzhou' },
  临平区: { areaCode: '571C', name: 'hangzhou' },
  拱墅区: { areaCode: '571D', name: 'hangzhou' },
  销售部: { areaCode: '571F', name: 'hangzhou' },
  吴兴区: { areaCode: '5721', name: 'huzhou' },
  长兴县: { areaCode: '5722', name: 'huzhou' },
  德清县: { areaCode: '5723', name: 'huzhou' },
  安吉县: { areaCode: '5724', name: 'huzhou' },
  南浔区: { areaCode: '5725', name: 'huzhou' },
  秀洲区: { areaCode: '5731', name: 'jiaxing' },
  桐乡市: { areaCode: '5732', name: 'jiaxing' },
  海宁市: { areaCode: '5733', name: 'jiaxing' },
  嘉善县: { areaCode: '5734', name: 'jiaxing' },
  平湖市: { areaCode: '5735', name: 'jiaxing' },
  海盐县: { areaCode: '5736', name: 'jiaxing' },
  南湖区: { areaCode: '5737', name: 'jiaxing' },
  海曙区: { areaCode: '5741', name: 'ningbo' },
  余姚市: { areaCode: '5742', name: 'ningbo' },
  慈溪市: { areaCode: '5743', name: 'ningbo' },
  象山县: { areaCode: '5744', name: 'ningbo' },
  北仑区: { areaCode: '5745', name: 'ningbo' },
  宁海县: { areaCode: '5746', name: 'ningbo' },
  奉化区: { areaCode: '5747', name: 'ningbo' },
  镇海区: { areaCode: '5748', name: 'ningbo' },
  鄞州区: { areaCode: '5749', name: 'ningbo' },
  江北区: { areaCode: '574C', name: 'ningbo' },
  越城区: { areaCode: '5751', name: 'shaoxing' },
  诸暨市: { areaCode: '5752', name: 'shaoxing' },
  上虞区: { areaCode: '5753', name: 'shaoxing' },
  嵊州市: { areaCode: '5754', name: 'shaoxing' },
  新昌县: { areaCode: '5755', name: 'shaoxing' },
  柯桥区: { areaCode: '5756', name: 'shaoxing' },
  椒江区: { areaCode: '5761', name: 'taizhou' },
  黄岩区: { areaCode: '5762', name: 'taizhou' },
  路桥区: { areaCode: '5763', name: 'taizhou' },
  临海市: { areaCode: '5764', name: 'taizhou' },
  温岭市: { areaCode: '5765', name: 'taizhou' },
  仙居县: { areaCode: '5766', name: 'taizhou' },
  天台县: { areaCode: '5767', name: 'taizhou' },
  三门县: { areaCode: '5768', name: 'taizhou' },
  玉环市: { areaCode: '5769', name: 'taizhou' },
  鹿城区: { areaCode: '5771', name: 'wenzhou' },
  乐清市: { areaCode: '5772', name: 'wenzhou' },
  瑞安市: { areaCode: '5773', name: 'wenzhou' },
  平阳县: { areaCode: '5774', name: 'wenzhou' },
  永嘉县: { areaCode: '5775', name: 'wenzhou' },
  洞头区: { areaCode: '5776', name: 'wenzhou' },
  文成县: { areaCode: '5777', name: 'wenzhou' },
  泰顺县: { areaCode: '5778', name: 'wenzhou' },
  苍南县: { areaCode: '5779', name: 'wenzhou' },
  瓯海区: { areaCode: '577A', name: 'wenzhou' },
  龙湾区: { areaCode: '577B', name: 'wenzhou' },
  龙港市: { areaCode: '577D', name: 'wenzhou' },
  景宁畲族自治县: { areaCode: '5789', name: 'wenzhou' },
  南城区: { areaCode: '578B', name: 'wenzhou' },
  婺城区: { areaCode: '5791', name: 'lishui' },
  义乌市: { areaCode: '5792', name: 'lishui' },
  磐安县: { areaCode: '5793', name: 'lishui' },
  兰溪市: { areaCode: '5794', name: 'lishui' },
  东阳市: { areaCode: '5795', name: 'lishui' },
  永康市: { areaCode: '5796', name: 'lishui' },
  浦江县: { areaCode: '5797', name: 'lishui' },
  武义县: { areaCode: '5798', name: 'lishui' },
  金东区: { areaCode: '5799', name: 'lishui' },
  莲都区: { areaCode: '5781', name: 'lishui' },
  缙云县: { areaCode: '5782', name: 'lishui' },
  青田县: { areaCode: '5783', name: 'lishui' },
  云和县: { areaCode: '5784', name: 'lishui' },
  庆元县: { areaCode: '5785', name: 'lishui' },
  龙泉市: { areaCode: '5786', name: 'lishui' },
  遂昌县: { areaCode: '5787', name: 'lishui' },
  松阳县: { areaCode: '5788', name: 'lishui' },
  定海区: { areaCode: '5801', name: 'jinhua' },
  普陀区: { areaCode: '5802', name: 'jinhua' },
  岱山县: { areaCode: '5803', name: 'jinhua' },
  嵊泗县: { areaCode: '5804', name: 'jinhua' },
}
const allMapCityData = ref([])
function setMapOption() {
  if (!chart || !mapData.value) return
  allMapCityData.value = []
  let seriesData = mapData.value.features.map((item: any) => {
    let areaCode = (cityMap[item.properties.name] && cityMap[item.properties.name].areaCode) || ''
    let pinyin = (cityMap[item.properties.name] && cityMap[item.properties.name].name) || ''
    return {
      name: item.properties.name,
      areaCode: areaCode,
      pinyin: pinyin,
    }
  })
  allMapCityData.value = seriesData.map((item: any) => {
    return item.name
  })
  chart.setOption({
    backgroundColor: '',
    geo: [
      {
        map: currentMapName.value,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '50%'],
        layoutSize: '180%',
        show: true,
        roam: false,
        label: {
          emphasis: {
            show: false,
          },
        },
        itemStyle: {
          normal: {
            borderColor: '#c0f3fb',
            borderWidth: 1,
            shadowColor: '#8cd3ef',
            shadowOffsetY: 10,
            shadowBlur: 120,
            areaColor: 'transparent',
          },
        },
      },
      // 重影效果
      {
        type: 'map',
        map: currentMapName.value,
        zlevel: -1,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '51%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.8)',
            shadowColor: 'rgba(172, 122, 255,0.5)',
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: 'rgba(5,21,35,0.1)',
          },
        },
      },
      {
        type: 'map',
        map: currentMapName.value,
        zlevel: -2,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '52%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.6)',
            shadowColor: 'rgba(65, 214, 255,1)',
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: 'transpercent',
          },
        },
      },
      {
        type: 'map',
        map: currentMapName.value,
        zlevel: -3,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '53%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.4)',
            shadowColor: 'rgba(58,149,253,1)',
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: 'transpercent',
          },
        },
      },
      {
        type: 'map',
        map: currentMapName.value,
        zlevel: -4,
        aspectScale: 1,
        zoom: 0.58,
        layoutCenter: ['50%', '54%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 5,
            borderColor: 'rgba(5,9,57,0.8)',
            shadowColor: 'rgba(29, 111, 165,0.8)',
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: 'rgba(5,21,35,0.1)',
          },
        },
      },
    ],
    series: [
      {
        name: `${currentMapName.value}数据`,
        type: 'map',
        map: currentMapName.value,
        aspectScale: 1,
        id: 'test1',
        zoom: 0.58,
        showLegendSymbol: true,
        label: {
          normal: {
            show: true,
            textStyle: { color: '#fff', fontSize: '120%' },
          },
          emphasis: {},
        },
        itemStyle: {
          normal: {
            areaColor: {
              type: 'linear',
              x: 1200,
              y: 0,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(3,27,78,0.75)',
                },
                {
                  offset: 1,
                  color: 'rgba(58,149,253,0.75)',
                },
              ],
              global: true,
            },
            borderColor: '#fff',
            borderWidth: 0.2,
          },
          emphasis: {
            show: false,
            color: '#fff',
            areaColor: 'rgba(0,254,233,0.6)',
          },
        },
        select: {
          itemStyle: {
            areaColor: 'rgba(0,254,233,0.6)',
          },
        },
        layoutCenter: ['50%', '50%'],
        layoutSize: '180%',
        markPoint: {
          symbol: 'none',
        },
        data: seriesData,
      },
    ],
  })
}
const activeInfo: any = ref(null)
//展示左侧总览详情信息
const showLeftDialog = () => {
  let param = activeInfo.value
  if (param.seriesName == '浙江数据') {
    transferParams.value.city = param.data.pinyin
    transferParams.value.cityCode = param.data.areaCode
    emit('showLeftDialog', transferParams.value)
  } else {
    transferParams.value.cityCode = ''
    transferParams.value.areaCode = param.data.areaCode
    emit('showLeftDialog', transferParams.value)
  }
  contextMenu.value.visible = false
}
const returnHome = () => {
  transferParams.value.city = 'zhejiang'
  transferParams.value.cityCode = ''
  transferParams.value.areaCode = ''
  emit('transferData', transferParams.value)
}
</script>

<style scoped>
@font-face {
  font-family: 'ziHuiJingDianYaHei';
  src: url('/fonts/zhihui.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.dashboard-map {
  height: 720px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #b3cfff;
  background: transparent;
}

.dashboard-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
  width: 100%;
  padding: 0 4%;
  justify-content: center;
}

.dashboard-cards > div {
  display: flex;
  padding: 12px 20px;
  cursor: pointer;
}

.dashboard-cards > div.active {
  background: url('/images/computingPower/comPowerTopBg.png') no-repeat 100% 100%;
  background-size: 100% 100%;
}
.dashboard-cards > div.disabled {
  cursor: not-allowed;
}

.dashboard-cards > div > div {
  margin-right: 10px;
}

.dashboard-cards > div > div > p {
  font-size: 15px;
  color: #080c13;
}

.dashboard-cards > div > div > p > b {
  font-weight: normal;
  display: inline-block;
  width: 120px;
}

.dashboard-cards > div > div > p > i {
  font-style: normal;
  color: #004fb1;
}

.dashboard-cards > div > div > span {
  display: inline-block;
  margin-top: 6px;
  font-size: 24px;
  font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei', Arial, sans-serif;
  color: #033176;
}

.returnBtn {
  position: absolute;
  bottom: 180px;
  right: 60px;
  .el-button {
    font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei', Arial, sans-serif;
    color: #033176;
    font-size: 18px;
  }
}
.dashboard-bottom-dialog {
  position: fixed;
  bottom: 10px;
  width: 635px;
  left: 50%;
  margin-left: -317px;
  height: 107px;
  background: url('/images/computingPower/comPowerTopDialogBg.png') no-repeat 100% 100%;
  background-size: 100% 100%;

  .dashboard-bottom-dialog-box {
    display: flex;
    align-content: center;
    justify-content: center;
    padding: 10px 20px;

    div {
      width: 100%;
      text-align: center;
      margin: 0 1%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      align-items: flex-start;

      .item {
        width: 46%;
        height: 43px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 18px;
        color: #033176;
        padding: 8px 12px;
        cursor: pointer;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei', Arial, sans-serif;

        i.imgIcon {
          width: 29px;
          height: 20px;
          display: inline-block;
          vertical-align: middle;
          margin-right: 10px;
          font-style: normal;
          background: url('/images/computingPower/comPowerIconCloud.png') no-repeat 100% 100%;
          background-size: 100% 100%;
        }

        img {
          display: inline-block;
        }

        span {
          display: inline-block;
          vertical-align: middle;
        }

        &.active {
          background: #1f62cc;
          color: #ffffff;
          border-radius: 5px;

          i.imgIcon {
            background: url('/images/computingPower/comPowerIconCloudActive.png') no-repeat 100%
              100%;
            background-size: 100% 100%;
          }
        }

        b {
          font-weight: normal;
          font-family: 'Microsoft YaHei';
        }
      }

      span.active {
      }
    }
  }
}

.context-menu {
  position: fixed;
  background: linear-gradient(90deg, rgba(93, 188, 254, 0.78), rgba(255, 255, 254, 0.78));
  border-radius: 5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;

  .menu-header {
    padding: 10px 15px;
    cursor: pointer;
    font-size: 16px;
  }
}
</style>
