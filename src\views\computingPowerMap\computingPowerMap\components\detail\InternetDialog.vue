<template>
  <div class="bottom-dialog-container">
    <!-- <img @click="closeBottomDialog" src="/images/computingPower/comPowerIconClose.png" alt="" /> -->
    <div class="bottom-dialog-content">
      <div class="bottom-dialog-main">
        <div class="bottom-dialog-title">
          <div>网络</div>
          <div>
            <img src="/images/computingPower/comPowerDialogTitleBg.png" alt="" />
          </div>
        </div>
        <div class="bottom-dialog-tooltip">
          <div class="bottom-dialog-infrastructure">
            <div
              @click="changeInfrastructureTabValue('1')"
              class="bottom-dialog-infrastructure-item"
              :class="{ active: infrastructureTabValue == '1' }"
            >
              公网
            </div>
            <div
              @click="changeInfrastructureTabValue('2')"
              class="bottom-dialog-infrastructure-item"
              :class="{ active: infrastructureTabValue == '2' }"
            >
              DCN
            </div>
          </div>
          <div class="bottom-dialog-search">
            <el-select
              v-model="selectedCloudName"
              placeholder="请选择云类型"
              :suffix-icon="CaretBottom"
              style="width: 130px"
              clearable
              @change="handleCloudNameChange"
            >
              <el-option
                v-for="item in cloudOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="selectedPlatformTypeName"
              placeholder="请选择云平台"
              :suffix-icon="CaretBottom"
              style="width: 130px"
              clearable
              @change="handlePlatformTypeChange"
            >
              <el-option
                v-for="item in platformTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-if="infrastructureTabValue === '1'"
              v-model="selectedRegionId"
              placeholder="请选择资源池"
              :suffix-icon="CaretBottom"
              style="width: 200px"
              @change="handleResourcePoolChange"
            >
              <el-option
                v-for="item in regionOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-select
              v-if="infrastructureTabValue === '2'"
              v-model="selectedRelatedPool"
              placeholder="请选择资源池"
              :suffix-icon="CaretBottom"
              style="width: 200px"
              @change="handleResourcePoolChange"
            >
              <el-option
                v-for="item in relatedPoolOptions"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
            <!--            判断是公网Ip还是dcn-->
            <template v-if="infrastructureTabValue === '1'">
              <!--              判断云类型-->
              <template v-if="selectedCloudName == '网络云'">
                <el-input
                  v-model="searchForm.ip"
                  placeholder="请输入子网前缀"
                  clearable
                  style="width: 160px"
                ></el-input>
                <el-input
                  v-model="searchForm.mask"
                  placeholder="请输入掩码"
                  clearable
                  style="width: 160px"
                ></el-input>
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入名称"
                  clearable
                  style="width: 160px"
                ></el-input>

                <el-select
                  v-model="searchForm.bsName"
                  clearable
                  filterable
                  placeholder="请选择业务系统"
                  :suffix-icon="CaretBottom"
                  style="width: 160px"
                >
                  <el-option v-for="item in bsNameList" :key="item" :label="item" :value="item" />
                </el-select>
              </template>
              <template v-else-if="selectedCloudName == '移动云'">
                <el-input
                  v-model="searchForm.ip"
                  placeholder="请输入IP"
                  clearable
                  style="width: 160px"
                ></el-input>
                <el-select
                  v-model="searchForm.cmpTenantId"
                  clearable
                  filterable
                  placeholder="请选择业务系统"
                  :suffix-icon="CaretBottom"
                  style="width: 160px"
                >
                  <el-option
                    v-for="item in tenantIdList"
                    :key="item.name"
                    :label="item.name"
                    :value="item.tenantId"
                  />
                </el-select>
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入名称"
                  clearable
                  style="width: 160px"
                ></el-input>
              </template>
              <template v-else>
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入名称"
                  clearable
                  style="width: 160px"
                ></el-input>
              </template>
            </template>
            <template v-else-if="infrastructureTabValue === '2'">
              <el-input
                v-model="searchForm.prefix"
                placeholder="请输入子网前缀"
                clearable
                style="width: 160px"
              ></el-input>
              <el-input
                v-model="searchForm.mask"
                placeholder="请输入掩码"
                clearable
                style="width: 160px"
              ></el-input>
            </template>

            <el-button type="primary" class="searchBtn" :icon="Search" @click="searchFunc">
              搜索
            </el-button>
            <el-button class="exportBtn" @click="onExport" :loading="exportLoading">
              <img src="/images/computingPower/comPowerIconExport.png" alt="" />
              导出
            </el-button>
          </div>
        </div>
        <div class="bottom-dialog-table">
          <el-auto-resizer>
            <template #default="{ height }">
              <el-table
                ref="tableRef"
                class="comPowerTable"
                :data="tableData"
                :height="height"
                style="width: 100%"
              >
                <el-table-column prop="date" label="序号" width="60" />

                <!-- 公网类型的列 -->
                <template v-if="infrastructureTabValue === '1'">
                  <template v-if="selectedCloudName == '网络云'">
                    <el-table-column
                      show-overflow-tooltip
                      :key="'网络云' + item.prop"
                      v-for="item in globalNetworkCloudColumns"
                      :prop="item.prop"
                      :label="item.label"
                    >
                      <template #default="scope">
                        <div v-if="item.prop == 'resourcePool'">
                          <span class="c-comPower-table-cell-blue-theme">{{
                            scope.row.resourcePool
                          }}</span>
                        </div>

                        <div v-else-if="item.prop == 'businessSystem'">
                          <span class="c-comPower-table-cell-blue-theme">{{
                            scope.row.businessSystem
                          }}</span>
                        </div>
                        <div v-else>
                          <span>
                            {{ scope.row[item.prop] }}
                          </span>
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else-if="selectedCloudName == '移动云'">
                    <el-table-column
                      show-overflow-tooltip
                      :key="'移动云' + item.prop"
                      v-for="item in globalMobileCloudColumns"
                      :prop="item.prop"
                      :label="item.label"
                    >
                      <template #default="scope">
                        <div v-if="item.prop == 'resourcePool'">
                          <span class="c-comPower-table-cell-blue-theme">{{
                            scope.row.resourcePool
                          }}</span>
                        </div>

                        <div v-else-if="item.prop == 'businessSystem'">
                          <span class="c-comPower-table-cell-blue-theme">{{
                            scope.row.businessSystem
                          }}</span>
                        </div>
                        <div v-else>
                          <span>
                            {{ scope.row[item.prop] }}
                          </span>
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      show-overflow-tooltip
                      :key="'其他云' + item.prop"
                      v-for="item in globalDefaultCloudColumns"
                      :prop="item.prop"
                      :label="item.label"
                    >
                      <template #default="scope">
                        <div v-if="item.prop == 'resourcePool'">
                          <span class="c-comPower-table-cell-blue-theme">{{
                            scope.row.resourcePool
                          }}</span>
                        </div>

                        <div v-else-if="item.prop == 'businessSystem'">
                          <span class="c-comPower-table-cell-blue-theme">{{
                            scope.row.businessSystem
                          }}</span>
                        </div>
                        <div v-else>
                          <span>
                            {{ scope.row[item.prop] }}
                          </span>
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <!--                  <el-table-column show-overflow-tooltip prop="name" label="弹性公网名称" />-->
                  <!--                  <el-table-column show-overflow-tooltip prop="address" label="弹性公网地址" />-->
                  <!--                  <el-table-column show-overflow-tooltip prop="bandwidth" label="带宽(Mbps)" />-->
                  <!--                  <el-table-column show-overflow-tooltip prop="cloudPlatform" label="所属云平台" />-->
                  <!--                  <el-table-column show-overflow-tooltip prop="resourcePool" label="所属资源池">-->
                  <!--                    <template #default="scope">-->
                  <!--                      <span class="c-comPower-table-cell-blue-theme">{{-->
                  <!--                        scope.row.resourcePool-->
                  <!--                      }}</span>-->
                  <!--                    </template>-->
                  <!--                  </el-table-column>-->
                  <!--                  <el-table-column show-overflow-tooltip prop="businessSystem" label="关联业务系统">-->
                  <!--                    <template #default="scope">-->
                  <!--                      <span class="c-comPower-table-cell-blue-theme">{{-->
                  <!--                        scope.row.businessSystem-->
                  <!--                      }}</span>-->
                  <!--                    </template>-->
                  <!--                  </el-table-column>-->
                </template>

                <!-- DCN类型的列 -->
                <template v-if="infrastructureTabValue === '2'">
                  <el-table-column show-overflow-tooltip prop="instanceId" label="配置项编号" />
                  <el-table-column show-overflow-tooltip prop="prefix" label="子网前缀" />
                  <el-table-column show-overflow-tooltip prop="mask" label="掩码" />
                  <el-table-column
                    show-overflow-tooltip
                    prop="platformTypeName"
                    label="所属云平台"
                    width="300"
                  >
                    <template #default="scope">
                      <span class="c-comPower-table-cell-blue-theme">{{
                        scope.row.platformTypeName
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column show-overflow-tooltip prop="relatedPool" label="所属物理资源池">
                    <template #default="scope">
                      <span class="c-comPower-table-cell-blue-theme">{{
                        scope.row.relatedPool
                      }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </template>
          </el-auto-resizer>
        </div>
        <div style="margin-top: 10px">
          <Pagination
            :pageable="pageable"
            :handle-size-change="handleSizeChange"
            :handle-current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CaretBottom, Search } from '@element-plus/icons-vue'
import { reactive, ref, watch, onMounted, type Ref } from 'vue'
import {
  getNetworkEipListApi,
  getNetworkDcnListApi,
  getRegionsListApi,
  getVirtualResourcePoolListApi,
  getCloudGroupStatsApi,
  getComputerPowerMapListBusinessApi,
  getComputerPowerMapListAppApi,
} from '@/api/modules/computingPowerMap'
import { ElTable } from 'element-plus'
import Pagination from '../pagination.vue'
import { useExport } from '../../hooks/useExport'

// 定义接口请求参数类型
interface NetworkRequestParams {
  cloudName?: string
  platformTypeName?: string
  cityCode?: string
  areaCode?: string
  pageNum?: number
  pageSize?: number
  regionId?: string
  relatedPool?: string
  prefix?: string
  ip?: string
  mask?: string
  appName?: string
  name?: string
  bsName?: string
  cmpTenantId?: string
}

// 定义公网IP响应数据类型
interface EipRecord {
  id: string
  name: string
  domainName: string
  appName: string
  regionName: string
  ip: string
  bandwidth: number
  resType: string
  resName: string
  mask: string
  type: string
}

interface EipResponse {
  records: EipRecord[]
  size: number
  current: number
  total: number
}

// 定义DCN响应数据类型（根据8.9.6接口文档）
interface DcnRecord {
  instanceId: string
  name: string
  platformTypeName: string
  relatedPool: string
  prefix: string
  mask: string
}

interface DcnResponse {
  records: DcnRecord[]
  size: number
  current: number
  total: number
}

// 定义类型/接口
interface columnsItem {
  prop: string
  label: string
}

const searchForm = reactive({
  prefix: '',
  mask: '',
  ip: '',
  bsName: '',
  cmpTenantId: '',
  name: '',
})
const globalNetworkCloudColumns: Ref<columnsItem[]> = ref([
  {
    prop: 'name',
    label: '弹性公网名称',
  },
  {
    prop: 'address',
    label: '弹性公网地址',
  },
  {
    prop: 'mask',
    label: '掩码',
  },
  {
    prop: 'type',
    label: '地址类型',
  },
  {
    prop: 'businessSystem',
    label: '业务系统',
  },
  {
    prop: 'cloudPlatform',
    label: '所属云平台',
  },
  {
    prop: 'resourcePool',
    label: '硬件资源池',
  },
])

const globalMobileCloudColumns: Ref<columnsItem[]> = ref([
  {
    prop: 'name',
    label: '弹性公网名称',
  },
  {
    prop: 'type',
    label: '弹性公网类型',
  },
  {
    prop: 'address',
    label: '弹性公网地址',
  },
  {
    prop: 'bandwidth',
    label: '带宽',
  },
  {
    prop: 'deviceName',
    label: '绑定设备名称',
  },
  {
    prop: 'deviceType',
    label: '绑定设备类型',
  },
  {
    prop: 'businessSystem',
    label: '所属业务',
  },
])
const globalDefaultCloudColumns: Ref<columnsItem[]> = ref([
  {
    prop: 'name',
    label: '弹性公网名称',
  },
  {
    prop: 'address',
    label: '弹性公网地址',
  },
  {
    prop: 'cloudPlatform',
    label: '所属云平台',
  },
  {
    prop: 'resourcePool',
    label: '硬件资源池',
  },
])
// const columns: Ref<columnsItem[]> = ref([
//   {
//     prop: 'name',
//     label: '弹性公网名称',
//   },
//   {
//     prop: 'address',
//     label: '弹性公网地址',
//   },
//   {
//     prop: 'cloudPlatform',
//     label: '所属云平台',
//   },
//   {
//     prop: 'resourcePool',
//     label: '硬件资源池',
//   },
// ])
// table 实例
const tableRef = ref<InstanceType<typeof ElTable>>()

// 定义props接收父组件传递的参数
const props = defineProps({
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  internetActiveTab: {
    type: String,
    required: true,
    default: '1',
  },
})

// 定义需要发出的事件类型
// const emit = defineEmits(['close'])

const pageable = reactive({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: 20,
  // 总条数
  total: 0,
})

const handleSizeChange = (val: number) => {
  pageable.pageNum = 1
  pageable.pageSize = val
  fetchNetworkData()
}
/**
 * @description 当前页改变
 * @param {Number} val 当前页
 * @return void
 * */
const handleCurrentChange = (val: number) => {
  pageable.pageNum = val
  fetchNetworkData()
}

// 定义数据类型接口
interface PublicNetworkItem {
  date: number
  name: string
  address: string
  bandwidth: string
  deviceType: string
  deviceName: string
  cloudPlatform: string
  resourcePool: string
  businessSystem: string
}

interface DCNItem {
  date: number
  instanceId: string
  name: string
  platformTypeName: string
  relatedPool: string
}

// 筛选条件
const selectedCloudName = ref<string>('')
const selectedPlatformTypeName = ref<string>('')
// 资源池选择
const selectedRegionId = ref<string>('')
const selectedRelatedPool = ref<string>('')

//网络云业务系统列表
const bsNameList: any = ref([])

//移动云业务系统列表
const tenantIdList: any = ref([])
// 选项列表
const cloudOptions = ref<Array<{ label: string; value: string }>>([])
const platformTypeOptions = ref<Array<{ label: string; value: string }>>([])

// 原始云平台数据
const originalCloudData = ref<Array<any>>([])

const { exportLoading, handleExport } = useExport()

// 资源池选项列表
const regionOptions = ref<Array<{ id: string; name: string }>>([])
const relatedPoolOptions = ref<Array<{ name: string }>>([])
// 业务系统备选列表
// const bslList = ref<Array<{ name: string }>>([])

// 获取云平台数据
const fetchCloudPlatformData = async () => {
  try {
    console.log('获取云平台数据')

    const response: { entity: Array<any>; code?: number; message?: string } =
      await getCloudGroupStatsApi()

    if (response.code === 200 && response.entity && Array.isArray(response.entity)) {
      // 保存原始数据
      originalCloudData.value = response.entity

      // 提取云类型选项
      const cloudNames = [...new Set(response.entity.map((item) => item.cloudName))].filter(Boolean)
      cloudOptions.value = cloudNames.map((name) => ({ label: name, value: name }))

      // 根据当前选中的云类型更新平台类型选项
      updatePlatformTypeOptions()

      console.log('云平台数据获取成功:', {
        cloudTypes: cloudOptions.value.length,
        platformTypes: platformTypeOptions.value.length,
      })
    } else {
      console.warn('云平台数据接口返回异常:', response)
      // 返回为null或异常时清空数据
      cloudOptions.value = []
      platformTypeOptions.value = []
    }
  } catch (error) {
    console.error('获取云平台数据失败:', error)
    // 异常时清空数据
    cloudOptions.value = []
    platformTypeOptions.value = []
  }
}

// 根据选中的云类型更新平台类型选项
const updatePlatformTypeOptions = () => {
  if (!selectedCloudName.value || !originalCloudData.value.length) {
    platformTypeOptions.value = []
    return
  }

  // 查找选中云类型对应的数据
  const selectedCloudData = originalCloudData.value.find(
    (item) => item.cloudName === selectedCloudName.value,
  )

  if (
    selectedCloudData &&
    selectedCloudData.platformTypes &&
    Array.isArray(selectedCloudData.platformTypes)
  ) {
    platformTypeOptions.value = selectedCloudData.platformTypes.map((item: any) => ({
      label: item.platformTypeName,
      value: item.platformTypeName,
    }))
  } else {
    platformTypeOptions.value = []
  }

  console.log('更新平台类型选项:', {
    cloudName: selectedCloudName.value,
    platformTypes: platformTypeOptions.value.length,
  })
}

const tableData = ref<(PublicNetworkItem | DCNItem)[]>([])

// 公网数据
const publicNetworkData = ref<PublicNetworkItem[]>([])

// DCN数据
const dcnData = ref<DCNItem[]>([])

const infrastructureTabValue = ref(props.internetActiveTab)
const changeInfrastructureTabValue = (val: string) => {
  infrastructureTabValue.value = val
  // 重置分页
  pageable.pageNum = 1
  // 重置资源池选择
  selectedRegionId.value = ''
  selectedRelatedPool.value = ''
  resetSearchFormFunc()
  // 根据选择的类型获取数据
  fetchNetworkData()
  // 加载对应的资源池选项
  loadResourcePoolOptions()
}

// 加载资源池选项
const loadResourcePoolOptions = async () => {
  try {
    const requestParams = {
      cloudName: selectedCloudName.value,
      platformTypeName: selectedPlatformTypeName.value,
      cityCode: props.requestParams?.cityCode,
      areaCode: props.requestParams?.areaCode,
    }

    if (infrastructureTabValue.value === '1') {
      // 公网IP需要加载资源池列表 (8.2.3接口)
      console.log('加载公网IP资源池选项')
      const response: {
        entity: Array<{ id: string; name: string }> | null
        code?: number
        message?: string
      } = await getRegionsListApi(requestParams)

      if (response.code === 200 && response.entity && Array.isArray(response.entity)) {
        regionOptions.value = response.entity
        console.log('公网IP资源池列表获取成功:', regionOptions.value.length, '个')
      } else {
        console.warn('公网IP资源池列表接口返回异常:', response)
        regionOptions.value = []
      }
    } else if (infrastructureTabValue.value === '2') {
      // DCN需要加载物理资源池列表 (8.2.1接口)
      console.log('加载DCN物理资源池选项')
      const requestParamsWithPaging = {
        ...requestParams,
        pageNum: 1,
        pageSize: 9999,
      }

      const response: {
        entity: { records: Array<{ name: string }> } | null
        code?: number
        message?: string
      } = await getVirtualResourcePoolListApi(requestParamsWithPaging)

      if (
        response.code === 200 &&
        response.entity &&
        response.entity.records &&
        Array.isArray(response.entity.records)
      ) {
        relatedPoolOptions.value = response.entity.records.map((item) => ({ name: item.name }))
        console.log('DCN物理资源池列表获取成功:', relatedPoolOptions.value.length, '个')
      } else {
        console.warn('DCN物理资源池列表接口返回异常:', response)
        relatedPoolOptions.value = []
      }
    }
  } catch (error) {
    console.error('加载资源池选项失败:', error)
    if (infrastructureTabValue.value === '1') {
      regionOptions.value = []
    } else {
      relatedPoolOptions.value = []
    }
  }
}

// 资源池选择变化处理
const handleResourcePoolChange = () => {
  // 重置分页
  // pageable.pageNum = 1
  // 重新获取数据
  // fetchNetworkData()
}

// 更新表格数据的函数
const updateTableData = () => {
  if (infrastructureTabValue.value === '1') {
    // 公网数据
    tableData.value = publicNetworkData.value
  } else if (infrastructureTabValue.value === '2') {
    // DCN数据
    tableData.value = dcnData.value
  }
}

// 获取网络数据的统一函数
const fetchNetworkData = async () => {
  try {
    const requestParams: NetworkRequestParams = {
      cloudName: queryParams.cloudName,
      platformTypeName: queryParams.platformTypeName,

      cityCode: props.requestParams?.cityCode,
      areaCode: props.requestParams?.areaCode,
      pageNum: pageable.pageNum,
      pageSize: pageable.pageSize,
    }
    //根据选中是公网还是dcn添加参数
    if (infrastructureTabValue.value == '1') {
      // 根据选中的云类型添加参数
      if (queryParams.cloudName == '网络云') {
        requestParams.ip = queryParams.ip
        requestParams.mask = queryParams.mask
        requestParams.bsName = queryParams.bsName
        requestParams.name = queryParams.name
      } else if (queryParams.cloudName == '移动云') {
        requestParams.cmpTenantId = queryParams.cmpTenantId
        requestParams.name = queryParams.name
        requestParams.ip = queryParams.ip
      } else {
        requestParams.name = queryParams.name
      }
    } else if (infrastructureTabValue.value === '2') {
      requestParams.prefix = queryParams.prefix
      requestParams.mask = queryParams.mask
    }
    // 根据当前选择的类型添加相应的资源池参数
    if (infrastructureTabValue.value === '1' && queryParams.regionId) {
      requestParams.regionId = queryParams.regionId
    } else if (infrastructureTabValue.value === '2' && queryParams.relatedPool) {
      requestParams.relatedPool = queryParams.relatedPool
    }

    console.log('请求网络数据:', {
      type: infrastructureTabValue.value === '1' ? '公网IP' : 'DCN',
      params: requestParams,
    })

    if (infrastructureTabValue.value === '1') {
      // 获取公网IP列表
      const response: { entity: EipResponse | null; code?: number; message?: string } =
        await getNetworkEipListApi(requestParams)

      if (
        response.code === 200 &&
        response.entity &&
        response.entity.records &&
        Array.isArray(response.entity.records)
      ) {
        // 转换数据格式
        publicNetworkData.value = response.entity.records.map((item: EipRecord, index: number) => ({
          date: (pageable.pageNum - 1) * pageable.pageSize + index + 1,
          name: item.name || '-',
          address: item.ip || '-',
          bandwidth: item.bandwidth?.toString() || '-',
          deviceType: item.resType || '-',
          deviceName: item.resName || '-',
          cloudPlatform: item.domainName || '-',
          resourcePool: item.regionName || '-',
          businessSystem: item.appName || '-',
          mask: item.mask || '-',
          type: item.type || '-',
        }))

        // 更新分页信息
        pageable.total = response.entity.total || 0
        console.log('公网IP数据获取成功:', publicNetworkData.value.length, '条')
      } else {
        console.warn('公网IP接口返回异常:', response)
        publicNetworkData.value = []
        pageable.total = 0
      }
    } else if (infrastructureTabValue.value === '2') {
      // 获取DCN列表 - 根据8.9.6接口文档
      const response: { entity: DcnResponse | null; code?: number; message?: string } =
        await getNetworkDcnListApi(requestParams)

      if (
        response.code === 200 &&
        response.entity &&
        response.entity.records &&
        Array.isArray(response.entity.records)
      ) {
        // 转换数据格式 - 根据文档中的返回字段
        dcnData.value = response.entity.records.map((item: DcnRecord, index: number) => ({
          date: (pageable.pageNum - 1) * pageable.pageSize + index + 1,
          instanceId: item.instanceId || '-',
          name: item.name || '-',
          platformTypeName: item.platformTypeName || '-',
          relatedPool: item.relatedPool || '-',
          prefix: item.prefix || '-',
          mask: item.mask || '-',
        }))

        // 更新分页信息
        pageable.total = response.entity.total || 0
        console.log('DCN数据获取成功:', dcnData.value.length, '条')
      } else {
        console.warn('DCN接口返回异常:', response)
        dcnData.value = []
        pageable.total = 0
      }
    }

    // 更新表格数据
    updateTableData()
  } catch (error) {
    console.error('获取网络数据失败:', error)
    // 发生错误时清空数据
    if (infrastructureTabValue.value === '1') {
      publicNetworkData.value = []
    } else {
      dcnData.value = []
    }
    pageable.total = 0
    updateTableData()
  }
}
const resetSearchFormFunc = () => {
  selectedCloudName.value = ''
  selectedPlatformTypeName.value = ''
  platformTypeOptions.value = []
  selectedRegionId.value = ''
  selectedRelatedPool.value = ''
  relatedPoolOptions.value = []
  searchForm.prefix = ''
  searchForm.mask = ''
  searchForm.ip = ''
  searchForm.bsName = ''
  searchForm.cmpTenantId = ''
  searchForm.name = ''
}
// 筛选条件变化处理
const handleCloudNameChange = () => {
  // console.log(selectedCloudName.value)

  // 重置分页
  // pageable.pageNum = 1
  // 更新平台类型选项

  // 重置下级选择
  selectedPlatformTypeName.value = ''
  platformTypeOptions.value = []
  selectedRegionId.value = ''
  selectedRelatedPool.value = ''
  relatedPoolOptions.value = []
  searchForm.prefix = ''
  searchForm.mask = ''
  searchForm.ip = ''
  searchForm.bsName = ''
  searchForm.cmpTenantId = ''
  searchForm.name = ''

  updatePlatformTypeOptions()
  loadResourcePoolOptions()

  // 重新获取数据
  // fetchNetworkData()
}

const handlePlatformTypeChange = () => {
  // 重置资源池选择
  selectedRegionId.value = ''
  selectedRelatedPool.value = ''
  regionOptions.value = []
  loadResourcePoolOptions()
  // 重置分页
  // pageable.pageNum = 1
  // 重新获取数据
  // fetchNetworkData()
}

// 组件挂载时初始化数据
onMounted(async () => {
  // 先获取云平台数据
  await fetchCloudPlatformData()

  // 然后设置初始值为父组件传入的参数
  selectedCloudName.value = props.requestParams?.cloudName || ''
  selectedPlatformTypeName.value = props.requestParams?.platformTypeName || ''
  // 根据初始云类型更新平台类型选项
  updatePlatformTypeOptions()

  //获取网络云情况下的业务系统
  getComputerPowerMapListBusinessApi({
    type: 'eip',
  }).then((res: any) => {
    bsNameList.value = res.entity
  })

  //获取移动云情况下的业务系统
  getComputerPowerMapListAppApi({}).then((res: any) => {
    tenantIdList.value = res.entity
    console.log(res)
  })
  loadResourcePoolOptions()
  searchFunc()
  // fetchNetworkData()
})

// 监听父组件参数变化
watch(
  () => props.requestParams,
  async (newParams: any) => {
    console.log('父组件参数变化:', newParams)

    // 先获取云平台数据（确保选项列表是最新的）
    await fetchCloudPlatformData()

    // 更新筛选条件
    selectedCloudName.value = newParams?.cloudName || ''
    selectedPlatformTypeName.value = newParams?.platformTypeName || ''

    // 根据新的云类型更新平台类型选项
    updatePlatformTypeOptions()

    // 重置资源池选择
    selectedRegionId.value = ''
    selectedRelatedPool.value = ''
    // 重新获取数据
    loadResourcePoolOptions()
    fetchNetworkData()
  },
  { deep: true },
)

const queryParams = reactive({
  cloudName: '',
  platformTypeName: '',
  regionId: '',
  relatedPool: '',

  prefix: '',
  mask: '',
  ip: '',
  bsName: '',
  cmpTenantId: '',
  name: '',
})
const searchFunc = () => {
  pageable.pageNum = 1
  queryParams.cloudName = selectedCloudName.value
  queryParams.platformTypeName = selectedPlatformTypeName.value

  queryParams.regionId = selectedRegionId.value
  queryParams.relatedPool = selectedRelatedPool.value
  //当前是公网点击查询
  if (infrastructureTabValue.value == '1') {
    if (selectedCloudName.value == '网络云') {
      queryParams.ip = searchForm.ip
      queryParams.mask = searchForm.mask
      queryParams.bsName = searchForm.bsName
      queryParams.name = searchForm.name
    } else if (selectedCloudName.value == '移动云') {
      queryParams.cmpTenantId = searchForm.cmpTenantId
      queryParams.name = searchForm.name
      queryParams.ip = searchForm.ip
      queryParams.prefix = ''
      queryParams.mask = ''
    } else {
      queryParams.name = searchForm.name
      queryParams.prefix = ''
      queryParams.mask = ''
      queryParams.ip = ''
      queryParams.bsName = ''
      queryParams.cmpTenantId = ''
    }
  } else if (infrastructureTabValue.value === '2') {
    queryParams.prefix = searchForm.prefix
    queryParams.mask = searchForm.mask
    queryParams.ip = ''
    queryParams.bsName = ''
    queryParams.cmpTenantId = ''
    queryParams.name = ''
  }
  fetchNetworkData()
}
// 新的导出函数
const onExport = async () => {
  const requestParams: any = {
    cloudName: queryParams.cloudName,
    platformTypeName: queryParams.platformTypeName,
    cityCode: props.requestParams?.cityCode,
    areaCode: props.requestParams?.areaCode,
  }
  if (infrastructureTabValue.value == '1') {
    if (selectedCloudName.value == '网络云') {
      requestParams.prefix = queryParams.prefix
      requestParams.mask = queryParams.mask
      requestParams.bsName = queryParams.bsName
      requestParams.name = queryParams.name
    } else if (selectedCloudName.value == '移动云') {
      requestParams.cmpTenantId = queryParams.cmpTenantId
      requestParams.name = queryParams.name
      requestParams.ip = queryParams.ip
    } else {
      requestParams.name = queryParams.name
    }
  } else if (infrastructureTabValue.value === '2') {
    requestParams.prefix = queryParams.prefix
    requestParams.mask = queryParams.mask
  }
  if (infrastructureTabValue.value === '1' && queryParams.regionId) {
    requestParams.regionId = queryParams.regionId
  } else if (infrastructureTabValue.value === '2' && queryParams.relatedPool) {
    requestParams.relatedPool = queryParams.relatedPool
  }
  await handleExport({
    type: infrastructureTabValue.value === '1' ? 'eip' : 'dcn',
    params: requestParams,
  })
}

// 关闭当前弹窗
// const closeBottomDialog = () => {
//   emit('close')
// }
</script>
<style scoped lang="scss">
.bottom-dialog-container {
  height: 100vh;
  // position: fixed;
  // right: 10px;
  // bottom: 10px;
  // width: calc(100% - 494px);
  // height: 436px;
  // z-index: 20;
  box-sizing: border-box;
  //height: 367px;
  & > img {
    position: absolute;
    top: 0;
    right: 10px;
    cursor: pointer;
  }
  .bottom-dialog-content {
    // position: absolute;
    // left: 0;
    // bottom: 0;
    // width: 100%;
    // height: 400px;
    height: 100%;
    padding: 12px 9px 11px 10px;
    box-shadow: 0px 0px 5px 0px #09155c;
    border-radius: 1px;
    box-sizing: border-box;
    background-color: rgba(170, 198, 245, 0.3);
    //background: url('/images/computingPower/comPowerBottomDialogBg.png') no-repeat 0 0;
    //background-size: 100% 100%;
    .bottom-dialog-main {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      padding: 8px 15px;
      border-radius: 1px;
      box-sizing: border-box;
      background: linear-gradient(0deg, rgba(124, 166, 238, 0.92), rgba(10, 52, 103, 0.92));
    }
    //opacity: 0.92;
    .bottom-dialog-title {
      //display: flex;
      div {
        display: inline-block;
        font-size: 18px;
        color: #ffffff;
        width: calc(100% - 100px);
        vertical-align: top;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei';
        img {
          width: 100%;
        }
      }
      & > div:nth-child(1) {
        width: 70px;
        margin-right: 12px;
      }
    }
    .bottom-dialog-tooltip {
      //height: 41px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border-bottom: 2px solid #3161b4;
      padding-top: 4px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      align-content: stretch;
      flex-direction: column;
      flex-wrap: nowrap;
      .bottom-dialog-infrastructure {
        //display: inline-block;
        //margin-right: 250px;
        vertical-align: bottom;
        .bottom-dialog-infrastructure-item {
          width: 120px;
          height: 25px;
          line-height: 25px;
          font-size: 16px;
          color: #7f91a7;
          display: inline-block;
          margin-right: 21px;
          background: url('/images/computingPower/comPowerInfrastructureTabBg.png') no-repeat 0 0;
          background-size: 100% 100%;
          text-align: center;
          cursor: pointer;
          &.active {
            color: #ffffff;
            background: url('/images/computingPower/comPowerInfrastructureTabActiveBg.png')
              no-repeat 0 0;
            background-size: 100% 100%;
          }
        }
      }
      .bottom-dialog-search {
        width: 100%;
        text-align: right;
        vertical-align: top;
        margin: 20px 40px 20px 0;
        .el-select {
          background: transparent;
          font-size: 15px;
          color: #ffffff;
          margin-right: 13px;
        }
        .el-select {
          background: transparent;
          font-size: 15px;
          color: #ffffff;
          margin-right: 13px;
        }
        .el-input {
          background: transparent;
          font-size: 14px;
          color: #ffffff;
          margin-right: 13px;
          height: 32px;
        }
        .exportBtn {
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          img {
            margin-right: 8px;
          }
        }
      }
    }
    .bottom-dialog-infrastructure-sub-tab {
      text-align: center;
      .bottom-dialog-infrastructure-sub-item {
        display: inline-block;
        margin: 10px 23px;
        background: #3c5784;
        border-radius: 2px;
        font-size: 16px;
        color: #ffffff;
        padding: 3px 16px;
        cursor: pointer;
        &.active {
          background: linear-gradient(-90deg, #2d78d3, #0f87a3, #2064b7);
        }
      }
    }
    .bottom-dialog-table {
      flex: 1;
      overflow: hidden;
      .el-table {
        background: transparent;
        th.el-table__cell {
          background: transparent;
        }
        // 移除表格行的hover效果
        .el-table__row:hover {
          background-color: transparent !important;
        }
        .el-table__row:hover > td {
          background-color: transparent !important;
        }
        // 更具体的选择器来覆盖Element Plus的默认样式
        tbody tr.el-table__row:hover > td.el-table__cell {
          background-color: transparent !important;
        }
        tbody tr.el-table__row:hover {
          background-color: transparent !important;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.comPowerPage.el-pagination {
  .el-select__wrapper.el-tooltip__trigger {
    background: #3f6eb8;
    color: #fff;
    box-shadow: none;
    border-radius: 1px;
    border: 1px solid #42659e;
    .el-select__input.is-default,
    .el-select__selected-item.el-select__placeholder {
      color: #fff;
    }
  }
  .el-pagination__total.is-first {
    color: #ffffff;
    font-size: 15px;
  }
  .el-pagination__goto {
    color: #ffffff;
  }
  .el-input__wrapper {
    background: #3f6eb8;
    border: none;
    box-shadow: none;
    .el-input__inner {
      color: #ffffff;
    }
  }
  .el-pagination__classifier {
    color: #ffffff;
  }
}
.comPowerPage.el-pagination.is-background .btn-next:disabled,
.comPowerPage.el-pagination.is-background .btn-prev:disabled {
  background: transparent;
  color: #3f5d78;
}
.comPowerPage.el-pagination.is-background .btn-next,
.comPowerPage.el-pagination.is-background .btn-prev {
  background: transparent;
  color: #0787de;
}
.comPowerPage.el-pagination.is-background .el-pager li {
  background: #3f6eb8;
  padding: 2px 16px;
}
.comPowerPage.el-pagination.is-background .el-pager li.is-active {
  background: #317ced;
}
.comPowerPage.el-pagination .btn-next .el-icon,
.comPowerPage.el-pagination .btn-prev .el-icon {
  font-size: 24px;
}

// 全局覆盖Element Plus表格的hover效果
.el-table tbody tr.el-table__row:hover > td.el-table__cell {
  background-color: transparent !important;
}

.el-table tbody tr.el-table__row:hover {
  background-color: transparent !important;
}
</style>
