<template>
  <el-dialog title="网络状态" :lock-scroll="false" class="escDialog" @close="close" width="1000">
    <!-- 添加查询输入框和查询按钮 -->
    <div style="margin-bottom: 16px">
      <sl-form
        ref="slFormRef"
        :options="formOptions"
        :model-value="formModel"
        style="overflow: hidden"
      >
      </sl-form>
    </div>
    <SlProTable
      highlight-current-row
      :columns="columns"
      style="min-height: 300px"
      :request-api="ipStatusList"
      :init-param="queryParams"
      hidden-table-header
      row-key="instanceId"
    >
    </SlProTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="tsx" setup name="ecsDailog">
import { reactive, ref } from 'vue'
import type { ColumnProps } from '@/components/SlProTable/interface'
import { ipStatusList } from '@/api/modules/resourecenter'
import SlForm from '@/components/form/SlForm.vue'

const props = defineProps({
  regionCode: {
    type: String,
    required: true,
  },
})

const columns = reactive<ColumnProps<any>[]>([
  { prop: 'prefix', label: 'IP地址', width: 200 },
  { prop: 'familyType', label: 'IP版本', width: 200 },
  { prop: 'relatedPool', label: '资源池', width: 200 },
  { prop: 'state', label: '状态', width: 120 },
  { prop: 'vpn', label: 'VPN' },
])

const formModel = reactive<{ [key: string]: any }>({
  prefix: '',
  mask: '',
  vpn: '',
  state: '',
})

const formOptions = reactive([
  {
    style: 'margin-top: 0;margin-bottom: 0;padding:0',
    gutter: 10,
    groupItems: [
      {
        label: 'IP地址',
        type: 'input',
        key: 'prefix',
        span: 6,
      },
      {
        label: '掩码长度',
        type: 'input',
        key: 'mask',
        span: 5,
      },
      {
        label: '状态',
        type: 'select',
        key: 'state',
        options: [
          {
            label: '已划分',
            value: '已划分',
          },
          {
            label: '未划分',
            value: '未划分',
          },
        ],
        span: 5,
      },
      {
        label: 'VPN',
        type: 'input',
        key: 'vpn',
        span: 6,
      },
      {
        span: 2,
        render() {
          return (
            <el-button style="margin-top: 0px;" type="primary" onClick={handleSearch}>
              查 询
            </el-button>
          )
        },
      },
    ],
  },
])

const queryParams = ref<any>({
  prefix: formModel.prefix,
  mask: formModel.mask,
  state: formModel.state,
  vpn: formModel.vpn,
  regionCode: props.regionCode,
})
// 定义查询方法
const handleSearch = () => {
  // 这里可以添加查询逻辑，例如触发 dataList 的查询方法
  queryParams.value = {
    ...queryParams.value,
    ...formModel,
  }
}

const emit = defineEmits(['update:modelValue', 'confirm', 'dialogClose'])

const close = () => {
  emit('update:modelValue', false)
  emit('dialogClose')
}
</script>

<style>
.escDialog .el-dialog__body {
  padding: 8px 0 0 0;
}
</style>
