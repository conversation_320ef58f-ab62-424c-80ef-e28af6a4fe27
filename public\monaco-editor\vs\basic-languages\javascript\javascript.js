/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/
define('vs/basic-languages/javascript/javascript', ['require', 'require'], (require) => {
  'use strict'
  var moduleExports = (() => {
    var x = Object.create
    var a = Object.defineProperty
    var u = Object.getOwnPropertyDescriptor
    var f = Object.getOwnPropertyNames
    var b = Object.getPrototypeOf,
      k = Object.prototype.hasOwnProperty
    var y = ((e) =>
      typeof require < 'u'
        ? require
        : typeof Proxy < 'u'
          ? new Proxy(e, { get: (t, n) => (typeof require < 'u' ? require : t)[n] })
          : e)(function (e) {
      if (typeof require < 'u') return require.apply(this, arguments)
      throw Error('Dynamic require of "' + e + '" is not supported')
    })
    var w = (e, t) => () => (t || e((t = { exports: {} }).exports, t), t.exports),
      h = (e, t) => {
        for (var n in t) a(e, n, { get: t[n], enumerable: !0 })
      },
      s = (e, t, n, c) => {
        if ((t && typeof t == 'object') || typeof t == 'function')
          for (let r of f(t))
            !k.call(e, r) &&
              r !== n &&
              a(e, r, { get: () => t[r], enumerable: !(c = u(t, r)) || c.enumerable })
        return e
      },
      g = (e, t, n) => (s(e, t, 'default'), n && s(n, t, 'default')),
      p = (e, t, n) => (
        (n = e != null ? x(b(e)) : {}),
        s(t || !e || !e.__esModule ? a(n, 'default', { value: e, enumerable: !0 }) : n, e)
      ),
      v = (e) => s(a({}, '__esModule', { value: !0 }), e)
    var d = w((C, l) => {
      var A = p(y('vs/editor/editor.api'))
      l.exports = A
    })
    var _ = {}
    h(_, { conf: () => $, language: () => T })
    var i = {}
    g(i, p(d()))
    var m = {
        wordPattern:
          /(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,
        comments: { lineComment: '//', blockComment: ['/*', '*/'] },
        brackets: [
          ['{', '}'],
          ['[', ']'],
          ['(', ')'],
        ],
        onEnterRules: [
          {
            beforeText: /^\s*\/\*\*(?!\/)([^\*]|\*(?!\/))*$/,
            afterText: /^\s*\*\/$/,
            action: { indentAction: i.languages.IndentAction.IndentOutdent, appendText: ' * ' },
          },
          {
            beforeText: /^\s*\/\*\*(?!\/)([^\*]|\*(?!\/))*$/,
            action: { indentAction: i.languages.IndentAction.None, appendText: ' * ' },
          },
          {
            beforeText: /^(\t|(\ \ ))*\ \*(\ ([^\*]|\*(?!\/))*)?$/,
            action: { indentAction: i.languages.IndentAction.None, appendText: '* ' },
          },
          {
            beforeText: /^(\t|(\ \ ))*\ \*\/\s*$/,
            action: { indentAction: i.languages.IndentAction.None, removeText: 1 },
          },
        ],
        autoClosingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"', notIn: ['string'] },
          { open: "'", close: "'", notIn: ['string', 'comment'] },
          { open: '`', close: '`', notIn: ['string', 'comment'] },
          { open: '/**', close: ' */', notIn: ['string'] },
        ],
        folding: {
          markers: {
            start: new RegExp('^\\s*//\\s*#?region\\b'),
            end: new RegExp('^\\s*//\\s*#?endregion\\b'),
          },
        },
      },
      o = {
        defaultToken: 'invalid',
        tokenPostfix: '.ts',
        keywords: [
          'abstract',
          'any',
          'as',
          'asserts',
          'bigint',
          'boolean',
          'break',
          'case',
          'catch',
          'class',
          'continue',
          'const',
          'constructor',
          'debugger',
          'declare',
          'default',
          'delete',
          'do',
          'else',
          'enum',
          'export',
          'extends',
          'false',
          'finally',
          'for',
          'from',
          'function',
          'get',
          'if',
          'implements',
          'import',
          'in',
          'infer',
          'instanceof',
          'interface',
          'is',
          'keyof',
          'let',
          'module',
          'namespace',
          'never',
          'new',
          'null',
          'number',
          'object',
          'out',
          'package',
          'private',
          'protected',
          'public',
          'override',
          'readonly',
          'require',
          'global',
          'return',
          'satisfies',
          'set',
          'static',
          'string',
          'super',
          'switch',
          'symbol',
          'this',
          'throw',
          'true',
          'try',
          'type',
          'typeof',
          'undefined',
          'unique',
          'unknown',
          'var',
          'void',
          'while',
          'with',
          'yield',
          'async',
          'await',
          'of',
        ],
        operators: [
          '<=',
          '>=',
          '==',
          '!=',
          '===',
          '!==',
          '=>',
          '+',
          '-',
          '**',
          '*',
          '/',
          '%',
          '++',
          '--',
          '<<',
          '</',
          '>>',
          '>>>',
          '&',
          '|',
          '^',
          '!',
          '~',
          '&&',
          '||',
          '??',
          '?',
          ':',
          '=',
          '+=',
          '-=',
          '*=',
          '**=',
          '/=',
          '%=',
          '<<=',
          '>>=',
          '>>>=',
          '&=',
          '|=',
          '^=',
          '@',
        ],
        symbols: /[=><!~?:&|+\-*\/\^%]+/,
        escapes: /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,
        digits: /\d+(_+\d+)*/,
        octaldigits: /[0-7]+(_+[0-7]+)*/,
        binarydigits: /[0-1]+(_+[0-1]+)*/,
        hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,
        regexpctl: /[(){}\[\]\$\^|\-*+?\.]/,
        regexpesc: /\\(?:[bBdDfnrstvwWn0\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,
        tokenizer: {
          root: [[/[{}]/, 'delimiter.bracket'], { include: 'common' }],
          common: [
            [/#?[a-z_$][\w$]*/, { cases: { '@keywords': 'keyword', '@default': 'identifier' } }],
            [/[A-Z][\w\$]*/, 'type.identifier'],
            { include: '@whitespace' },
            [
              /\/(?=([^\\\/]|\\.)+\/([dgimsuy]*)(\s*)(\.|;|,|\)|\]|\}|$))/,
              { token: 'regexp', bracket: '@open', next: '@regexp' },
            ],
            [/[()\[\]]/, '@brackets'],
            [/[<>](?!@symbols)/, '@brackets'],
            [/!(?=([^=]|$))/, 'delimiter'],
            [/@symbols/, { cases: { '@operators': 'delimiter', '@default': '' } }],
            [/(@digits)[eE]([\-+]?(@digits))?/, 'number.float'],
            [/(@digits)\.(@digits)([eE][\-+]?(@digits))?/, 'number.float'],
            [/0[xX](@hexdigits)n?/, 'number.hex'],
            [/0[oO]?(@octaldigits)n?/, 'number.octal'],
            [/0[bB](@binarydigits)n?/, 'number.binary'],
            [/(@digits)n?/, 'number'],
            [/[;,.]/, 'delimiter'],
            [/"([^"\\]|\\.)*$/, 'string.invalid'],
            [/'([^'\\]|\\.)*$/, 'string.invalid'],
            [/"/, 'string', '@string_double'],
            [/'/, 'string', '@string_single'],
            [/`/, 'string', '@string_backtick'],
          ],
          whitespace: [
            [/[ \t\r\n]+/, ''],
            [/\/\*\*(?!\/)/, 'comment.doc', '@jsdoc'],
            [/\/\*/, 'comment', '@comment'],
            [/\/\/.*$/, 'comment'],
          ],
          comment: [
            [/[^\/*]+/, 'comment'],
            [/\*\//, 'comment', '@pop'],
            [/[\/*]/, 'comment'],
          ],
          jsdoc: [
            [/[^\/*]+/, 'comment.doc'],
            [/\*\//, 'comment.doc', '@pop'],
            [/[\/*]/, 'comment.doc'],
          ],
          regexp: [
            [
              /(\{)(\d+(?:,\d*)?)(\})/,
              ['regexp.escape.control', 'regexp.escape.control', 'regexp.escape.control'],
            ],
            [
              /(\[)(\^?)(?=(?:[^\]\\\/]|\\.)+)/,
              ['regexp.escape.control', { token: 'regexp.escape.control', next: '@regexrange' }],
            ],
            [/(\()(\?:|\?=|\?!)/, ['regexp.escape.control', 'regexp.escape.control']],
            [/[()]/, 'regexp.escape.control'],
            [/@regexpctl/, 'regexp.escape.control'],
            [/[^\\\/]/, 'regexp'],
            [/@regexpesc/, 'regexp.escape'],
            [/\\\./, 'regexp.invalid'],
            [
              /(\/)([dgimsuy]*)/,
              [{ token: 'regexp', bracket: '@close', next: '@pop' }, 'keyword.other'],
            ],
          ],
          regexrange: [
            [/-/, 'regexp.escape.control'],
            [/\^/, 'regexp.invalid'],
            [/@regexpesc/, 'regexp.escape'],
            [/[^\]]/, 'regexp'],
            [/\]/, { token: 'regexp.escape.control', next: '@pop', bracket: '@close' }],
          ],
          string_double: [
            [/[^\\"]+/, 'string'],
            [/@escapes/, 'string.escape'],
            [/\\./, 'string.escape.invalid'],
            [/"/, 'string', '@pop'],
          ],
          string_single: [
            [/[^\\']+/, 'string'],
            [/@escapes/, 'string.escape'],
            [/\\./, 'string.escape.invalid'],
            [/'/, 'string', '@pop'],
          ],
          string_backtick: [
            [/\$\{/, { token: 'delimiter.bracket', next: '@bracketCounting' }],
            [/[^\\`$]+/, 'string'],
            [/@escapes/, 'string.escape'],
            [/\\./, 'string.escape.invalid'],
            [/`/, 'string', '@pop'],
          ],
          bracketCounting: [
            [/\{/, 'delimiter.bracket', '@bracketCounting'],
            [/\}/, 'delimiter.bracket', '@pop'],
            { include: 'common' },
          ],
        },
      }
    var $ = m,
      T = {
        defaultToken: 'invalid',
        tokenPostfix: '.js',
        keywords: [
          'break',
          'case',
          'catch',
          'class',
          'continue',
          'const',
          'constructor',
          'debugger',
          'default',
          'delete',
          'do',
          'else',
          'export',
          'extends',
          'false',
          'finally',
          'for',
          'from',
          'function',
          'get',
          'if',
          'import',
          'in',
          'instanceof',
          'let',
          'new',
          'null',
          'return',
          'set',
          'static',
          'super',
          'switch',
          'symbol',
          'this',
          'throw',
          'true',
          'try',
          'typeof',
          'undefined',
          'var',
          'void',
          'while',
          'with',
          'yield',
          'async',
          'await',
          'of',
        ],
        typeKeywords: [],
        operators: o.operators,
        symbols: o.symbols,
        escapes: o.escapes,
        digits: o.digits,
        octaldigits: o.octaldigits,
        binarydigits: o.binarydigits,
        hexdigits: o.hexdigits,
        regexpctl: o.regexpctl,
        regexpesc: o.regexpesc,
        tokenizer: o.tokenizer,
      }
    return v(_)
  })()
  return moduleExports
})
