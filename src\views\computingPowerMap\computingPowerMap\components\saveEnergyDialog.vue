<template>
  <div class="bottom-dialog-container">
    <!-- <img @click="closeBottomDialog" src="/images/computingPower/comPowerIconClose.png" alt="" /> -->
    <div class="bottom-dialog-content">
      <div class="bottom-dialog-main">
        <div class="bottom-dialog-title">
          <div>绿色节能</div>
          <div>
            <img src="/images/computingPower/comPowerDialogTitleBigBg.png" alt="" />
          </div>
        </div>
        <div class="bottom-dialog-tooltip">
          <div class="bottom-dialog-search">
            <el-select
              v-model="cloudValue"
              @change="changeCloudTypeFunc"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 100px"
            >
              <el-option
                v-for="item in cloudOption"
                :key="item.cloudName"
                :label="item.cloudName"
                :value="item.cloudName"
              />
            </el-select>
            <el-select
              v-model="platformTypeName"
              @change="changeCloudPlatformTypeNameFunc"
              placeholder="请选择云平台"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 140px"
            >
              <el-option
                v-for="item in platformTypeNamePoolList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
            <el-select
              v-model="resourcePoolValue"
              @change="changeResourcePooolFunc"
              placeholder="请选择资源池"
              clearable
              :suffix-icon="CaretBottom"
              style="width: 160px"
            >
              <el-option
                v-for="item in resourcePoolList"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>

            <el-input
              v-model="searchForm.name"
              placeholder="请输入服务器名称"
              clearable
              style="width: 160px"
            ></el-input>
            <el-input
              v-model="searchForm.osVersion"
              placeholder="请输入操作系统"
              clearable
              style="width: 160px"
            ></el-input>
            <el-input
              v-model="searchForm.model"
              placeholder="请输入设备型号"
              clearable
              style="width: 160px"
            ></el-input>
            <el-input
              v-model="searchForm.idcRoom"
              placeholder="请输入所属机房"
              clearable
              style="width: 160px"
            ></el-input>
            <el-input
              v-model="searchForm.idcRack"
              placeholder="请输入所属机架"
              clearable
              style="width: 160px"
            ></el-input>
            <el-input
              v-model="searchForm.ip"
              placeholder="请输入IPV4或IPV6"
              clearable
              style="width: 160px"
            ></el-input>

            <el-button class="searchBtn" :icon="Search" @click="searchFunc"> 搜索 </el-button>
            <el-button class="exportBtn" @click="exportFile">
              <img src="/images/computingPower/comPowerIconExport.png" alt="" />
              导出
            </el-button>
          </div>
        </div>
        <div class="bottom-dialog-table">
          <el-auto-resizer>
            <template #default="{ height }">
              <el-table
                class="comPowerTable"
                :data="tableData"
                :height="height"
                style="width: 100%"
              >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column
                  :key="item.prop"
                  v-for="item in columns"
                  show-overflow-tooltip
                  :prop="item.prop"
                  :label="item.label"
                >
                  <template #default="scope">
                    <span
                      v-if="item.prop == 'relatedPool' || item.prop == 'vmCount'"
                      class="c-comPower-table-cell-blue-theme"
                      >{{ scope.row[item.prop] || '--' }}</span>
                    <span v-else>{{ scope.row[item.prop] || '--' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-auto-resizer>
        </div>
        <div style="margin-top: 10px">
          <Pagination
            :pageable="pageable"
            :handle-size-change="handleSizeChange"
            :handle-current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CaretBottom, Search } from '@element-plus/icons-vue'
import { onMounted, reactive, type Ref, ref, watch } from 'vue'
import Pagination from '@/views/computingPowerMap/computingPowerMap/components/pagination.vue'
import { useDownload } from '@/hooks/useDownload'
import {
  getComPowerMapPageBaseDeviceHandResourcePool,
  getComPowerMapPageGreenEnergySaved,
  downloadComPowerMapPageGreenEnergySaved,
} from '@/api/modules/comPowerCenter'
import { ElTable } from 'element-plus'

// 定义类型/接口
interface OptionItem {
  name: string
  instanceId: string
}
// 定义类型/接口
interface columnsItem {
  prop: string
  label: string
}

const searchForm = reactive({
  name: '',
  osVersion: '',
  model: '',
  idcRoom: '',
  idcRack: '',
  ip: '',
})
const columns: Ref<columnsItem[]> = ref([
  {
    prop: 'name',
    label: '计算服务器名称',
  },
  {
    prop: 'osVersion',
    label: '操作系统版本',
  },
  {
    prop: 'model',
    label: '设备型号',
  },
  {
    prop: 'idcRoom',
    label: '所属机房',
  },
  {
    prop: 'idcRack',
    label: '所属机架',
  },
  {
    prop: 'platformTypeName',
    label: '所属云平台',
  },
  {
    prop: 'relatedPool',
    label: '所属物理资源池',
  },
  {
    prop: 'mgmtIpv4',
    label: '管理网IPV4地址',
  },
  {
    prop: 'mgmtIpv6',
    label: '管理网IPV6地址',
  },
  {
    prop: 'memTotalCapacity',
    label: '内存大小(GB)',
  },
  {
    prop: 'localStorageCapacity',
    label: '存储大小(GB)',
  },
  {
    prop: 'cpuCoresCount',
    label: 'CPU核数',
  },
  {
    prop: 'vmCount',
    label: '关联虚拟机(个)',
  },
])

const cloudValue = ref('')
const cloudOption: any = ref([])
const tableData = ref([])
//云平台
const platformTypeName = ref('')
const platformTypeNamePoolList = ref([])

const resourcePoolValue = ref('')
const resourcePoolList: Ref<OptionItem[]> = ref([])

onMounted(() => {
  cloudValue.value = props.requestParams.cloudName
  platformTypeName.value = props.requestParams.platformTypeName
  cloudOption.value = props.cloudListArr
  cloudOption.value.forEach((item: any) => {
    if (item.cloudName == cloudValue.value) {
      platformTypeNamePoolList.value =
        (item.platformTypes && item.platformTypes.map((item: any) => item.platformTypeName)) || []
    }
  })
  getBaseDeviceHandResourcePool()
  getDataInfo()
})

const changeCloudTypeFunc = () => {
  // pageable.pageNum = 1
  platformTypeName.value = ''
  platformTypeNamePoolList.value = []
  cloudOption.value.forEach((item: any) => {
    if (item.cloudName == cloudValue.value) {
      platformTypeNamePoolList.value =
        (item.platformTypes && item.platformTypes.map((item: any) => item.platformTypeName)) || []
    }
  })
  //清空掉资源池信息
  resourcePoolValue.value = ''
  resourcePoolList.value = []
  getBaseDeviceHandResourcePool()
  // getDataInfo()
}
//切换云平台
const changeCloudPlatformTypeNameFunc = () => {
  // pageable.pageNum = 1
  resourcePoolValue.value = ''
  resourcePoolList.value = []
  getBaseDeviceHandResourcePool()
  // getDataInfo()
}
//查询物理资源池
const getBaseDeviceHandResourcePool = () => {
  let params = {
    cloudName: cloudValue.value,
    platformTypeName: platformTypeName.value,
    cityCode: props.requestParams.cityCode,
    pageNum: 1,
    pageSize: 9999,
  }
  getComPowerMapPageBaseDeviceHandResourcePool(params).then((res: any) => {
    if (res.code == 200) {
      resourcePoolList.value = res.entity.records || []
    }
  })
}
//切换资源池
const changeResourcePooolFunc = () => {
  // pageable.pageNum = 1
  // getDataInfo()
}

// 导出文件
const exportFile = () => {
  let params: any = {
    cloudName: queryParams.cloudName,
    platformTypeName: queryParams.platformTypeName,
    relatedPool: queryParams.relatedPool,
    cityCode: props.requestParams.cityCode,
    name: queryParams.name,
    osVersion: queryParams.osVersion,
    model: queryParams.model,
    idcRoom: queryParams.idcRoom,
    idcRack: queryParams.idcRack,
    ip: queryParams.ip,
  }
  useDownload(downloadComPowerMapPageGreenEnergySaved, '绿色节能.xlsx', params)
}

const queryParams = reactive({
  cloudName: '',
  platformTypeName: '',
  relatedPool: '',
  name: '',
  osVersion: '',
  model: '',
  idcRoom: '',
  idcRack: '',
  ip: '',
})
const searchFunc = () => {
  pageable.pageNum = 1
  queryParams.cloudName = cloudValue.value
  queryParams.platformTypeName = platformTypeName.value
  queryParams.relatedPool = resourcePoolValue.value
  queryParams.name = searchForm.name
  queryParams.osVersion = searchForm.osVersion
  queryParams.model = searchForm.model
  queryParams.idcRoom = searchForm.idcRoom
  queryParams.idcRack = searchForm.idcRack
  queryParams.ip = searchForm.ip
  getDataInfo()
}
const getDataInfo = () => {
  let parms: any = {
    cloudName: queryParams.cloudName,
    platformTypeName: queryParams.platformTypeName,
    relatedPool: queryParams.relatedPool,
    cityCode: props.requestParams.cityCode,
    name: queryParams.name,
    osVersion: queryParams.osVersion,
    model: queryParams.model,
    idcRoom: queryParams.idcRoom,
    idcRack: queryParams.idcRack,
    ip: queryParams.ip,
    pageNum: pageable.pageNum,
    pageSize: pageable.pageSize,
  }
  getComPowerMapPageGreenEnergySaved(parms).then((res: any) => {
    if (res.code == 200) {
      tableData.value = res.entity.records || []
      pageable.total = res.entity.total || 0
    }
  })
}
// 定义需要发出的事件类型
// const emit = defineEmits(['close'])

// 定义props接收父组件传递的参数
const props = defineProps({
  cloudListArr: {
    type: Array,
    required: false,
    default: () => [],
  },
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

const pageable = reactive({
  // 当前页数
  pageNum: 1,
  // 每页显示条数
  pageSize: 20,
  // 总条数
  total: 0,
})

const handleSizeChange = (val: number) => {
  pageable.pageNum = 1
  pageable.pageSize = val
  getDataInfo()
}
/**
 * @description 当前页改变
 * @param {Number} val 当前页
 * @return void
 * */
const handleCurrentChange = (val: number) => {
  pageable.pageNum = val
  getDataInfo()
}

// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)
// 监听参数变化自动重新请求（可选）
// watch(
//   () => props.baseDeviceHandResourcePoolList,
//   (newParams: any) => {
//     resourcePoolList.value = newParams
//   },
//   { deep: true },
// )
// 关闭当前弹窗
// const closeBottomDialog = () => {
//   emit('close')
// }
</script>
<style scoped lang="scss">
.bottom-dialog-container {
  height: 100vh;
  // position: fixed;
  // left: 0;
  // bottom: 10px;
  // width: 100%;
  // height: 436px;
  // z-index: 20;
  box-sizing: border-box;
  & > img {
    position: absolute;
    top: 0;
    right: 10px;
    cursor: pointer;
  }
  .bottom-dialog-content {
    // position: absolute;
    // left: 0;
    // bottom: 0;
    // width: 100%;
    // height: 400px;
    height: 100%;
    padding: 12px 9px 11px 10px;
    border-radius: 1px;
    box-sizing: border-box;
    background-color: rgba(170, 198, 245, 0.3);
    .bottom-dialog-main {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      padding: 8px 15px;
      border-radius: 1px;
      box-sizing: border-box;
      background: linear-gradient(0deg, rgba(124, 166, 238, 0.92), rgba(10, 52, 103, 0.92));
    }
    .bottom-dialog-title {
      div {
        display: inline-block;
        font-size: 18px;
        color: #ffffff;
        width: calc(100% - 120px);
        vertical-align: top;
        font-family: 'ziHuiJingDianYaHei', 'Microsoft YaHei';
        img {
          width: 100%;
        }
      }
      div:nth-child(1) {
        width: 80px;
        margin-right: 15px;
      }
    }
    .bottom-dialog-tooltip {
      margin-bottom: 10px;
      text-align: right;
      .bottom-dialog-alarm {
        display: inline-block;
        margin-right: 139px;
        .bottom-dialog-alarm-item {
          display: inline-block;
          margin-right: 50px;
          img {
            margin-right: 8px;
          }
          span {
            font-size: 16px;
          }
        }
        .bottom-dialog-alarm-item:nth-child(1) {
          color: #ff0000;
        }
        .bottom-dialog-alarm-item:nth-child(2) {
          color: #ffa800;
        }
        .bottom-dialog-alarm-item:nth-child(3) {
          color: #ffea00;
        }
        .bottom-dialog-alarm-item:nth-child(4) {
          color: #fcff00;
        }
        .bottom-dialog-alarm-item:nth-child(5) {
          color: #27d9ff;
        }
        .bottom-dialog-alarm-item:nth-child(6) {
          color: #2093ff;
        }
      }
      .bottom-dialog-search {
        display: inline-block;
        //float: right;
        margin-right: 60px;
        .el-select {
          background: transparent;
          font-size: 15px;
          color: #ffffff;
          margin-right: 13px;
          margin-bottom: 10px;
        }
        .el-input {
          background: transparent;
          font-size: 14px;
          color: #ffffff;
          margin-right: 13px;
          height: 32px;
          margin-bottom: 10px;
        }
        .searchBtn {
          min-width: 80px;
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          margin-bottom: 10px;
          img {
            margin-right: 8px;
          }
        }
        .exportBtn {
          background: linear-gradient(270deg, #327ef1, #225fbb);
          border-radius: 3px;
          font-size: 15px;
          color: #ffffff;
          margin-left: 20px;
          border: none;
          margin-bottom: 10px;
          img {
            margin-right: 8px;
          }
        }
      }
    }
    .bottom-dialog-table {
      flex: 1;
      overflow: hidden;
      .el-table {
        background: transparent;
        th.el-table__cell {
          background: transparent;
        }
      }
    }
  }
}
</style>
