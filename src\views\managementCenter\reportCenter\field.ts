const tenantFields = [
  {
    label: '平台',
    value: 'platform',
  },
  {
    label: '虚拟资源池',
    value: 'cmdbRegionName',
  },
  {
    label: '资源池',
    value: 'regionName',
  },
  {
    label: '部门',
    value: 'deptName',
  },
  {
    label: '业务系统名称',
    value: 'businessSystemName',
  },
  {
    label: '负责人',
    value: 'principalName',
  },
  {
    label: '租户名称',
    value: 'tenantName',
  },
  {
    label: '系统等级',
    value: 'systemLevel',
  },
  {
    label: '容灾',
    value: 'hasDisasterRecovery',
  },
  {
    label: '项目名称',
    value: 'projectName',
  },
  {
    label: 'vCPU分配数(核)',
    value: 'vcpuNum',
  },
  {
    label: '内存分配数(GB)',
    value: 'memory',
  },
  {
    label: '存储分配数(GB)',
    value: 'storage',
  },
  {
    label: 'vCPU均值利用率(%)',
    value: 'vcpuUtil',
  },
  {
    label: '内存均值利用率(%)',
    value: 'memoryUtil',
  },
  {
    label: '存储均值利用率(%)',
    value: 'storageUtil',
  },
  {
    label: 'vCPU峰值利用率(%)',
    value: 'vcpuPeakUtil',
  },
  {
    label: '内存峰值利用率(%)',
    value: 'memoryPeakUtil',
  },
  {
    label: '存储峰值利用率(%)',
    value: 'storagePeakUtil',
  },
  {
    label: 'GPU数量',
    value: 'gpuNum',
  },
  {
    label: '公网带宽(Mb)',
    value: 'eipBandwidth',
  },
  {
    label: '对象存储(G)',
    value: 'obsStorage',
  },
  {
    label: '业务系统创建时间',
    value: 'businessSystemCreateTime',
  },
  {
    label: '业务系统到期时间',
    value: 'businessSystemExpireTime',
  },
  {
    label: '计费号状态',
    value: 'billIdStatus',
  },
  {
    label: '计费号',
    value: 'billId',
  },
]
const regionFields = [
  {
    label: '平台',
    value: 'platform',
  },
  {
    label: '虚拟资源池名称',
    value: 'virtualRegionName',
  },
  {
    label: '计算服务器节点数',
    value: 'computeNodeNum',
  },
  {
    label: '虚拟机数量',
    value: 'vmNum',
  },
  {
    label: 'vCPU总量',
    value: 'vcpuTotal',
  },
  {
    label: 'vCPU可用总量',
    value: 'vcpuAvailable',
  },
  {
    label: '内存总量',
    value: 'memoryTotal',
  },
  {
    label: '内存可用总量',
    value: 'memoryAvailable',
  },
  {
    label: '存储总量',
    value: 'storageTotal',
  },
  {
    label: '资源池',
    value: 'regionName',
  },
  {
    label: 'vCPU已分配',
    value: 'vcpuAllocated',
  },
  {
    label: '内存已分配',
    value: 'memoryAllocated',
  },
  {
    label: '存储已分配',
    value: 'storageAllocated',
  },
  {
    label: 'vCPU剩余',
    value: 'vcpuRemaining',
  },
  {
    label: '内存剩余',
    value: 'memoryRemaining',
  },
  {
    label: '存储剩余',
    value: 'storageRemaining',
  },
  {
    label: 'vCPU分配率',
    value: 'vcpuAllocationRate',
  },
  {
    label: '内存分配率',
    value: 'memoryAllocationRate',
  },
  {
    label: '存储分配率',
    value: 'storageAllocationRate',
  },
  {
    label: 'vCPU使用率均值',
    value: 'vcpuUsageAvg',
  },
  {
    label: '内存使用率均值',
    value: 'memoryUsageAvg',
  },
  {
    label: '存储使用率均值',
    value: 'storageUsageAvg',
  },
  {
    label: 'vCPU使用率峰值',
    value: 'vcpuUsagePeak',
  },
  {
    label: '内存使用率峰值',
    value: 'memoryUsagePeak',
  },
  {
    label: '存储使用率峰值',
    value: 'storageUsagePeak',
  },
]

const gpuFields = [
  {
    label: '数据时间',
    value: 'dataTime',
  },
  {
    label: '所属云',
    value: 'domainName',
  },
  {
    label: '资源池',
    value: 'regionName',
  },
  {
    label: '业务系统',
    value: 'businessSystemName',
  },
  {
    label: '算力利用率(%)',
    value: 'gpuUtilPercent',
  },
  {
    label: '显存利用率(%)',
    value: 'memUtilPercent',
  },
  {
    label: '所属裸金属/虚拟机',
    value: 'deviceName',
  },
  {
    label: '申请人',
    value: 'applyUserName',
  },
  {
    label: 'IP地址',
    value: 'deviceIp',
  },
  {
    label: '型号',
    value: 'modelName',
  },
  {
    label: '温度(℃)',
    value: 'temperature',
  },
]

const k8sFields = [
  {
    label: '数据时间',
    value: 'dataTime',
  },
  {
    label: '组织名称',
    value: 'orgName',
  },
  {
    label: '项目名称',
    value: 'projectName',
  },
  {
    label: '集群名称',
    value: 'clusterName',
  },
  {
    label: '命名空间',
    value: 'namespaceName',
  },
  {
    label: '项目ID',
    value: 'projectId',
  },
  {
    label: '节点池名称',
    value: 'nodePoolName',
  },
  {
    label: '节点池别名',
    value: 'nodePoolAliasName',
  },
  {
    label: '描述',
    value: 'description',
  },
  {
    label: 'CPU总量',
    value: 'cpuTotal',
  },
  {
    label: 'CPU已用',
    value: 'cpuUsed',
  },
  {
    label: 'CPU使用率(%)',
    value: 'cpuUsageRate',
  },
  {
    label: '内存总量(GB)',
    value: 'memoryTotal',
  },
  {
    label: '内存已用(GB)',
    value: 'memoryUsed',
  },
  {
    label: '内存使用率(%)',
    value: 'memoryUsageRate',
  },
  {
    label: '是否有GPU',
    value: 'hasGpu',
  },
  {
    label: 'GPU核心数',
    value: 'gpuCore',
  },
  {
    label: 'GPU核心已用',
    value: 'gpuCoreUsed',
  },
  {
    label: 'GPU内存(GB)',
    value: 'gpuMemory',
  },
  {
    label: 'GPU内存已用(GB)',
    value: 'gpuMemoryUsed',
  },
]
export { tenantFields, regionFields, gpuFields, k8sFields }
