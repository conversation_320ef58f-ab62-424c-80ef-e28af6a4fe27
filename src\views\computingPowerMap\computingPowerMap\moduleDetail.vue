<template>
  <component :is="compMap[moduleName as string]" v-bind="data[moduleName as string]" />
</template>

<script setup lang="ts">
// 国产化
import domesticDialog from './components/domesticDialog.vue'
//资产分析
import assetAnalysisDialog from './components/assetAnalysisDialog.vue'
// 绿色节能
import saveEnergyDialog from './components/saveEnergyDialog.vue'
//重保业务
import importantBusinessDialog from './components/importantBusinessDialog.vue'
// 基础设施
import infrastructureDialog from './components/infrastructureDialog.vue'
import StorageDialog from './components/detail/StorageDialog.vue'
import InternetDialog from './components/detail/InternetDialog.vue'
import VirtualMachineDialog from './components/detail/VirtualMachineDialog.vue'
import VCpuOrMemoryLine from './components/detail/VCpuOrMemoryLine.vue'
import { useRoute } from 'vue-router'
import { getStorage } from '@/utils/storage'

const {
  params: { moduleName },
  query: { page },
} = useRoute()

const compMap: any = {
  importantBusiness: importantBusinessDialog,
  infrastructure: infrastructureDialog,
  saveEnergy: saveEnergyDialog,
  assetAnalysis: assetAnalysisDialog,
  domestic: domesticDialog,
  storage: StorageDialog,
  internet: InternetDialog,
  virtualmachine: VirtualMachineDialog,
  vcpu: VCpuOrMemoryLine,
  memory: VCpuOrMemoryLine,
}

const data = getStorage(Number(page) === 1 ? 'computingPowerMap' : 'computingPowerMapDetail')
console.log('moduleDetail:  ', data)
</script>

<style lang="scss">
.bottom-dialog-search .el-select .el-select__wrapper .el-select__placeholder {
  color: #ffffff;
}
.bottom-dialog-search .el-select .el-select__wrapper {
  background: transparent;
  box-shadow: none;
  border-radius: 2px;
  border: 1px solid #3069b0;
}
.bottom-dialog-table {
  .el-table {
    background: transparent;
    tr {
      background: transparent;
    }
    th.el-table__cell {
      background: transparent;
      border-bottom: 1px solid rgba(102, 102, 102, 0.5);
      font-size: 15px !important;
      color: #b7d7ff !important;
    }
  }
  .el-table__inner-wrapper:before {
    background-color: rgba(102, 102, 102, 0.5) !important;
  }
}

.el-table.comPowerTable .el-table__empty-block .el-table__empty-text {
  color: #ffffff;
}
</style>
