<template>
  <SlProTable
    ref="proTable"
    highlight-current-row
    :columns="columns"
    style="min-height: 300px"
    :request-api="getResourceList"
    :init-param="queryParams"
    :current-change="currentChange"
    @selection-change="handleSelectionChange"
    hidden-table-header
    row-key="goodsOrderId"
  >
  </SlProTable>
  <SlDialog
    v-model="updatePasswordDialogVisible"
    title="修改密码"
    width="600px"
    destroy-on-close
    confirm-text="提交"
    @close="handleUpdatePasswordClose"
    @confirm="handleUpdatePasswordConfirm"
  >
    <sl-form
      ref="updatePasswordFormRef"
      :options="updatePasswordFormOptions"
      v-model="updatePasswordFormModel"
    >
    </sl-form>
    <div class="warning">
      提醒：密码格式必须包含字母和数字，且长度在8到26个字符之间，只可包含特殊字符()~!@#$%^&*-+=|{}[]∶;,.?/
    </div>
  </SlDialog>
  <SlDialog
    v-model="subnetDialogVisible"
    title="子网信息"
    width="1000px"
    :show-cancel="false"
    @confirm="subnetDialogVisible = false"
    destroy-on-close
  >
    <SubnetList :subnet-list="currentSubnetList" />
  </SlDialog>
  <ResourceChangeDialog
    v-model:visible="changeDialogVisible"
    resource-type="mysql"
    :selected-resources="selectedResources"
    :allowed-change-types="allowedChangeTypes"
    @confirm="handleConfirm"
  />
  <ResourceChangeDialog
    v-model:visible="delayDialogVisible"
    resource-type="mysql"
    :selected-resources="selectedResources"
    :is-delay="true"
    @confirm="handleConfirm"
  />
  <!-- 安全组弹窗 -->
  <SlDialog
    v-model="securityGroupDialogVisible"
    title="选择安全组"
    width="1000px"
    @close="securityGroupDialogVisible = false"
    :show-confirm="false"
    cancel-text="关闭"
    destroy-on-close
  >
    <DataList
      ref="securityGroupListRef"
      :query-params="securityGroupQueryParams"
      :is-select-mode="true"
      @selected="handleSecurityGroupSelected"
    />
  </SlDialog>
  <!-- 虚拟网卡弹窗 -->
  <SlDialog
    v-model="virtualNicDialogVisible"
    title="选择虚拟网卡"
    width="1200px"
    @close="virtualNicDialogVisible = false"
    :show-confirm="false"
    cancel-text="关闭"
    destroy-on-close
  >
    <VirtualNicDataList
      ref="virtualNicListRef"
      :query-params="virtualNicQueryParams"
      :is-select-mode="true"
      @selected="handleVirtualNicSelected"
    />
  </SlDialog>
</template>
<script setup lang="tsx" name="dataList">
import { ref, reactive, type VNode, computed } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import {
  getResourceList,
  cycleBinCreate,
  getSubnetList,
  securityGroupOperate,
  virtualNicOperate,
} from '@/api/modules/resourecenter'
import {
  useResourceOperationFormModel,
  useUpdatePasswordFormOptions,
  executeResourceOperationConfirm,
  useResourceOperationFormClear,
  type ResourceOperationModel,
} from '../hooks/useResourceOperationModels'
import eventBus from '@/utils/eventBus'
import SlMessage from '@/components/base/SlMessage'
import SubnetList from './components/subnetList.vue'
import ResourceChangeDialog from '../components/ResourceChangeDialog.vue'
import type { ResourceChangeType } from '../types'
import { ElMessage, ElMessageBox, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { useResourceChange } from '../hooks/useResourceChange'
import { useRecycleValidation } from '../hooks/useRecycleValidation'
import { useVerifyThePromptBox } from '@/hooks/useVerifyThePromptBox'
import DataList from '../securityGroup/components/DataList.vue'
import VirtualNicDataList from '../virtualNic/components/DataList.vue'
import { ArrowDown } from '@element-plus/icons-vue'

const { queryParams, isEvsSelect, isBindDialog, isHiddenSelection, hideOperations } = defineProps<{
  queryParams: any
  isEvsSelect?: boolean
  isBindDialog?: boolean
  isHiddenSelection?: boolean
  hideOperations?: boolean
}>()
const emit = defineEmits(['currentChange', 'selectDevice'])
const radioValue = ref()
const currentChange = (currentRow: any, oldCurrentRow: any) => {
  if (!currentRow) return
  radioValue.value = currentRow.goodsOrderId
  emit('currentChange', currentRow, oldCurrentRow)
}

const selectColumn: ColumnProps<any> = isEvsSelect
  ? {
      render: ({ row }) => {
        return (
          <el-radio modelValue={radioValue.value} value={row.goodsOrderId} size="large"></el-radio>
        )
      },
      label: '选择',
      width: 55,
    }
  : queryParams?.hideSelection
    ? { type: 'index', label: '序号', width: 55 }
    : {
        type: 'selection',
        width: 55,
      }
const opColumn: [ColumnProps<any>] | [] =
  isEvsSelect || hideOperations
    ? []
    : isHiddenSelection
      ? []
      : isBindDialog
        ? [
            {
              prop: 'operation',
              label: '操作',
              width: 100,
              fixed: 'right',
              render: bindDialogOperationRender,
            },
          ]
        : [
            {
              prop: 'operation',
              label: '操作',
              width: 200,
              fixed: 'right',
              className: 'operation-column',
              render: operationRender,
            },
          ]
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  selectColumn,
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  {
    prop: 'deviceName',
    label: '云数据库名称',
    width: 150,
    fixed: 'left',
    render: ({ row }: any): VNode => {
      return (
        <router-link
          to={{
            path: '/mysqlDetail',
            query: {
              id: row.id,
              deviceId: row.deviceId,
            },
          }}
          style="color: #0052D9; text-decoration: none;"
        >
          {row.deviceName}
        </router-link>
      )
    },
  },
  { prop: 'handoverStatus', label: '交维状态', width: 100 },
  { prop: 'deviceId', label: '资源ID', width: 200 },
  { prop: 'osVersion', label: '版本', width: 120 },
  {
    prop: 'mountOrNot',
    label: '系列',
    width: 100,
    render: ({ row }) => {
      const seriesMap: Record<string, string> = {
        ALONE: '单机版',
        COLONY: '主备版本',
      }
      return seriesMap[row.mountOrNot] || row.mountOrNot
    },
  },
  { prop: 'spec', label: '实例规格', width: 120 },
  { prop: 'sysDisk', label: '系统盘', width: 150 },
  { prop: 'dataDisk', label: '数据盘', width: 150 },
  { prop: 'ip', label: 'IP', width: 150 },
  { prop: 'eip', label: '弹性公网IP', width: 150 }, // 显示绑定的公网IP或占位符
  { prop: 'bandWidth', label: '带宽', width: 100 }, // 显示带宽或占位符
  { prop: 'subnet', label: '子网信息', width: 100, render: subnetRender },
  { prop: 'applyTime', label: '申请时长', width: 120 },
  { prop: 'tenantName', label: '租户', width: 100 },
  { prop: 'businessSysName', label: '业务系统', width: 150 }, // 支持筛选过滤
  { prop: 'cloudPlatform', label: '所属云', width: 120 }, // 支持筛选过滤
  { prop: 'resourcePoolName', label: '资源池', width: 120 }, // 支持筛选过滤
  { prop: 'orderCode', label: '工单编号', width: 150 },
  { prop: 'projectName', label: '项目名称', width: 150 },
  { prop: 'securityGroupName', label: '安全组', width: 150 },
  { prop: 'vnicName', label: '虚拟网卡', width: 150 },
  { prop: 'createTime', label: '开通时间', width: 150 },
  { prop: 'expireTime', label: '到期时间', width: 150 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'instanceUuid', label: 'UUID', width: 150 },
  { prop: 'deviceStatusCn', label: '状态', width: 100 }, // 运行中，已关机，异常
  { prop: 'recoveryStatusCn', label: '回收状态', width: 100 },
  { prop: 'changeStatusCn', label: '变更状态', width: 100 },
  { prop: 'applyUserName', label: '申请人', width: 100 },
  ...opColumn,
])
const handleCommand = (command: string | number, row: any) => {
  switch (command) {
    case 'resetPwd':
      handleUpdatePassword(row)
      break
    case 'bindOrUnbindSecurityGroup':
      handleBindSecurityGroups(row)
      break
    case 'bindVirtualNic':
      handleBindVirtualNic(row, 'BIND')
      break
    case 'unbindVirtualNic':
      handleBindVirtualNic(row, 'UNBIND')
      break
  }
}

function operationRender({ row }: any): VNode {
  return (
    <>
      {['RUNING', 'STOPED'].includes(row.deviceStatus) && (
        <el-button onClick={() => handleSwitchMachine(row)} type="primary" link>
          {row.deviceStatus == 'RUNING' ? '关机' : '开机'}
        </el-button>
      )}
      <el-button
        onClick={() => handleRestart(row)}
        type="primary"
        disabled={row.deviceStatus != 'RUNING'}
        link
      >
        重启
      </el-button>
      <ElDropdown
        onCommand={(command) => handleCommand(command, row)}
        v-slots={{
          dropdown: () => (
            <ElDropdownMenu>
              <ElDropdownItem command="resetPwd" disabled={row.resetPwd !== 1}>
                修改密码
              </ElDropdownItem>
              <ElDropdownItem command="bindOrUnbindSecurityGroup">
                {row.securityGroupName ? '解绑安全组' : '绑定安全组'}
              </ElDropdownItem>
              <ElDropdownItem command="bindVirtualNic">绑定虚拟网卡</ElDropdownItem>
              {row.vnicId ? (
                <ElDropdownItem command="unbindVirtualNic">解绑虚拟网卡</ElDropdownItem>
              ) : null}
            </ElDropdownMenu>
          ),
        }}
        style={{ marginLeft: '8px' }}
      >
        <el-button type="primary" link>
          更多操作
          <el-icon class="el-icon--right">
            <ArrowDown />
          </el-icon>
        </el-button>
      </ElDropdown>
    </>
  )
}

// 子网信息列渲染函数
function subnetRender({ row }: any): VNode {
  return (
    <el-button onClick={() => handleViewSubnet(row)} type="primary" link>
      查看子网
    </el-button>
  )
}

// 绑定弹窗中的操作列渲染函数
function bindDialogOperationRender({ row }: any): VNode {
  return (
    <>
      <el-button
        onClick={() => handleSelectDevice(row)}
        type="primary"
        disabled={row.recoveryStatus !== 0 || row.changeStatusCn !== '未变更'}
        link
      >
        绑定
      </el-button>
    </>
  )
}

// 选择设备
const handleSelectDevice = (row: any) => {
  emit('selectDevice', row)
}

const proTable = ref<ProTableInstance>()
// const currentRecycleIdsList = ref<string[]>([])

// 使用回收校验钩子函数
const { validateRecycle, validateChange } = useRecycleValidation()

const handleCreateRecycle = async (goodsItems: any[]) => {
  const res = await cycleBinCreate({
    goodsType: 'mysql',
    goodsItems,
  })
  if (res.code != 200) {
    return ElMessage.error(res.message || '接口请求失败')
  }
  SlMessage.success('已加入回收站')
  proTable.value?.getTableList()
  proTable.value?.clearSelection()
  eventBus.emit('cycleBins:updateCount')
}

// 批量回收功能
const handleBatchRecycle = async () => {
  const selectedList = proTable.value?.selectedList || []

  // 使用新的验证逻辑
  if (validateRecycle(selectedList, 'mysql')) {
    const arr = await useVerifyThePromptBox(selectedList)
    if (!arr || !arr.length) return
    handleCreateRecycle(arr)
  }
}

// 开关机功能
const handleSwitchMachine = async (row: any) => {
  const msgStr = {
    RUNING: '选中信息确认关机吗？',
    STOPED: '选中信息确认开机吗？',
  }
  const deviceStatus = row.deviceStatus?.toUpperCase() || 'UNKNOWN'
  const msg = msgStr[deviceStatus as keyof typeof msgStr] || '未知状态，请确认操作'
  await ElMessageBox.confirm(msg, '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await executeResourceOperationConfirm({
        orderId: row.orderId,
        goodsOrderId: row.goodsOrderId,
        operationType: deviceStatus === 'RUNING' ? 'STOP' : 'START',
      })
      proTable.value?.getTableList()
    })
    .catch(() => {
      console.log(`操作取消`)
    })
}
// 重启功能
const handleRestart = async (row: any) => {
  if (row.deviceStatus != 'RUNING') {
    return ElMessage.warning('当前云数据库状态为非运行中，无法重启')
  }
  await ElMessageBox.confirm('选中信息确认重启吗？', '信息提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await executeResourceOperationConfirm({
        orderId: row.orderId,
        goodsOrderId: row.goodsOrderId,
        operationType: 'RESTART',
      })
      proTable.value?.getTableList()
    })
    .catch(() => {
      console.log(`操作取消`)
    })
}

// 安全组相关功能
const securityGroupDialogVisible = ref(false)
const securityGroupListRef = ref<InstanceType<typeof DataList>>()
const currentRow = ref<any>({})
const securityGroupQueryParams = ref<Record<string, any>>({ type: 'securityGroup' })

async function handleBindSecurityGroups(row: any) {
  if (row.securityGroupName) {
    // 如果已经绑定了安全组，则提示是否解绑
    ElMessageBox.confirm(`确定要解绑安全组 "${row.securityGroupName}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        try {
          const res = await securityGroupOperate({
            operateType: 'UNBIND_SG',
            regionCode: row.resourcePoolCode,
            businessSystemId: row.businessSysId,
            vmId: row.deviceId,
            securityGroupIds: row.securityGroupIds,
          })
          if (res.code == 200) {
            ElMessage.success('已提交解绑安全组请求')
            proTable.value?.getTableList()
          }
        } catch (error) {
          console.error(error)
        }
      })
      .catch(() => {
        // 用户取消操作
      })
  } else {
    // 打开安全组选择弹窗，并根据vpcName进行过滤
    currentRow.value = row
    // 更新安全组查询参数，添加vpcName过滤条件
    securityGroupQueryParams.value = {
      type: 'securityGroup',
      vpcName: row.vpcName,
    }
    securityGroupDialogVisible.value = true
  }
}

async function handleSecurityGroupSelected(row: any) {
  if (!row.id) {
    ElMessage.warning('请选择要绑定的安全组')
    return
  }

  try {
    const res = await securityGroupOperate({
      operateType: 'BIND_SG',
      regionCode: currentRow.value.resourcePoolCode,
      businessSystemId: currentRow.value.businessSysId,
      vmId: currentRow.value.deviceId,
      securityGroupIds: row.id,
    })
    if (res.code == 200) {
      ElMessage.success('已提交绑定安全组请求')
      securityGroupDialogVisible.value = false
      proTable.value?.getTableList()
    }
  } catch (error) {
    console.error(error)
  }
}

// 修改密码功能
const updatePasswordDialogVisible = ref(false)
const updatePasswordFormModel = reactive<ResourceOperationModel>(useResourceOperationFormModel())
const updatePasswordFormOptions = reactive(useUpdatePasswordFormOptions(updatePasswordFormModel))

async function handleUpdatePassword(row: any) {
  updatePasswordDialogVisible.value = true
  updatePasswordFormModel.orderId = row.orderId
  updatePasswordFormModel.goodsOrderId = row.goodsOrderId
  updatePasswordFormModel.deviceStatus = row.deviceStatus
}

const handleUpdatePasswordClose = () => {
  updatePasswordDialogVisible.value = false
  useResourceOperationFormClear(updatePasswordFormModel)
}

const updatePasswordFormRef = ref()
const handleUpdatePasswordConfirm = async () => {
  if (!(await updatePasswordFormRef.value?.validate(() => true))) return
  await executeResourceOperationConfirm({ ...updatePasswordFormModel, operationType: 'RESETPWD' })
  handleUpdatePasswordClose()
  proTable.value?.getTableList()
}

// 子网弹窗相关
const subnetDialogVisible = ref(false)
const currentSubnetList = ref<any[]>([])

// 查看子网信息
const handleViewSubnet = async (row: any) => {
  // TODO: 这里需要调用获取子网信息的接口
  const res = await getSubnetList({ id: row.id })
  currentSubnetList.value = res.entity
  subnetDialogVisible.value = true
}

// 多选数据
const multipleSelection = ref<any[]>([])
const changeDialogVisible = ref(false)
const delayDialogVisible = ref(false)
const selectedResources = ref<any[]>([])

// 使用计算属性缓存处理过的资源列表以提高性能
const processedResources = computed(() => {
  if (multipleSelection.value.length === 0) return []
  return multipleSelection.value.map((resource) => ({
    ...resource,
    _resourceId: resource.id, // 添加一个唯一标识，用于跟踪变化
  }))
})

// 使用计算属性判断是否禁用变更和延期按钮
const isChangeDisabled = computed(() => multipleSelection.value.length === 0)

// ECS允许的变更类型
const allowedChangeTypes: ResourceChangeType[] = [
  'instance_spec_change',
  'storage_expand',
  'bandwidth_expand',
]

// 重构资源变更处理方法，提高性能
const handleResourceChange = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要变更的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      changeDialogVisible.value = true
    }
  }
}

// 重构资源延期处理方法，提高性能
const handleResourceDelay = async () => {
  if (isChangeDisabled.value) {
    ElMessage.warning('请选择要延期的资源')
    return
  }

  // 使用缓存的处理过的资源列表
  selectedResources.value = processedResources.value

  // 使用新的校验逻辑
  if (validateChange(selectedResources.value)) {
    // 使用钩子函数中的validateResources进行业务系统一致性校验
    const isValid = await validateResources(selectedResources.value)
    if (isValid) {
      // 校验通过，才显示弹窗
      delayDialogVisible.value = true
    }
  }
}

// 处理确认
const handleConfirm = () => {
  proTable.value?.getTableList()
}

// 选择变化事件
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 虚拟网卡相关功能
const virtualNicDialogVisible = ref(false)
const virtualNicListRef = ref<InstanceType<typeof DataList>>()
const virtualNicQueryParams = ref<Record<string, any>>({ type: 'virtualNic' })
const virtualNicSafeLock = ref(false)
async function handleBindVirtualNic(row: any, operateType: string) {
  currentRow.value = row
  virtualNicDialogVisible.value = true
  virtualNicQueryParams.value = {
    type: 'virtualNic',
    vnicId: row.vnicId,
    vmId: row.deviceId,
    operateType,
  }
}

async function handleVirtualNicSelected(row: any, operateType: string) {
  if (!row.id) {
    ElMessage.warning('请选择要操作的虚拟网卡')
    return
  }
  if (virtualNicSafeLock.value) return
  try {
    virtualNicSafeLock.value = true
    const res = await virtualNicOperate({
      operateType,
      deviceId: currentRow.value.deviceId,
      vnicOrderId: row.id,
    })
    if (res.code == 200) {
      ElMessage.success(`已提交${operateType == 'BIND' ? '绑定' : '解绑'}虚拟网卡请求`)
      virtualNicDialogVisible.value = false
      proTable.value?.getTableList()
    }
  } catch (error) {
    console.error(error)
  } finally {
    virtualNicSafeLock.value = false
  }
}

const { validateResources } = useResourceChange()

defineExpose({
  handleBatchRecycle,
  handleResourceChange,
  handleResourceDelay,
  selectedList: multipleSelection,
})
</script>
<style lang="scss" scoped>
.warning {
  width: 90%;
  margin: 0 auto;
  margin-top: -10px;
  word-break: break-all;
  color: red;
}
</style>
<style lang="scss">
.operation-column .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
