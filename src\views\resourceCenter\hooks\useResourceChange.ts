import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { ResourceChangeType, ResourceType } from '../types'
import { propertyChangeList, propertyChangeUpdate } from '@/api/modules/resourecenter'

export interface ResourceChangeProps {
  resourceDetailId: number
  resourceType: ResourceType
  changeType: ResourceChangeType
  before: any
  after: any
  eipId?: number
}

export interface ResourceProperty {
  resourceDetailId: number
  deviceName: string
  resourceType: string
  cloudPlatform: string
  resourcePoolName: string
  regionId: string
  domainCode: string
  azId: string
  props: ResourceChangeProps[]
  [key: string]: any
}

export interface ChangeData {
  basePropertyList: {
    busiSystemId: number
    [key: string]: any
  }[]
  ecsPropertyList: ResourceProperty[]
  gcsPropertyList: ResourceProperty[]
  evsPropertyList: ResourceProperty[]
  obsPropertyList: ResourceProperty[]
  slbPropertyList: ResourceProperty[]
  natPropertyList: ResourceProperty[]
  eipPropertyList: ResourceProperty[]
}

/**
 * 创建初始化的空数据结构
 */
function createEmptyChangeData(): ChangeData {
  return {
    basePropertyList: [],
    ecsPropertyList: [],
    gcsPropertyList: [],
    evsPropertyList: [],
    obsPropertyList: [],
    slbPropertyList: [],
    natPropertyList: [],
    eipPropertyList: [],
  }
}

export function useResourceChange() {
  // 为每个调用此钩子的组件创建独立的changeData引用
  const changeData = ref<ChangeData>(createEmptyChangeData())

  // 检查业务系统是否一致
  const checkBusinessSystem = (busiSystemId: number) => {
    // 如果变更区为空，直接通过
    if (changeData.value.basePropertyList.length === 0) {
      changeData.value.basePropertyList.push({ busiSystemId })
      return true
    }

    // 检查业务系统是否一致
    const existingBusiSystemId = changeData.value.basePropertyList[0].busiSystemId
    if (existingBusiSystemId !== busiSystemId) {
      ElMessage.warning('当前选择的资源的业务系统与变更区中已有的业务不一致，请重新选择')
      return false
    }
    return true
  }

  // 获取变更列表
  const fetchChangeList = async () => {
    const res = await propertyChangeList()
    if (res.entity) {
      // 使用深拷贝确保数据独立
      changeData.value = JSON.parse(JSON.stringify(res.entity))
    } else {
      // 如果返回为空，重置为空的数据结构
      changeData.value = createEmptyChangeData()
    }

    // 确保所有属性列表都存在
    const allPropertyLists = [
      'basePropertyList',
      'ecsPropertyList',
      'gcsPropertyList',
      'evsPropertyList',
      'obsPropertyList',
      'slbPropertyList',
      'natPropertyList',
      'eipPropertyList',
    ]

    // 确保所有的propertyList都正确初始化
    allPropertyLists.forEach((key) => {
      if (!Array.isArray(changeData.value[key as keyof ChangeData])) {
        changeData.value[key as keyof ChangeData] = []
      }
    })
  }

  // 校验资源
  const validateResources = async (resources: any[], dg = false) => {
    // 如果没有资源，直接返回false
    if (!resources || resources.length === 0) {
      ElMessage.warning('请选择要操作的资源')
      return false
    }

    // 判断当前是否存在变更中的资源，存在直接返回 false
    const beChangingResources = resources.filter(
      (resource) => resource.changeStatus == 'be_changing',
    )
    if (beChangingResources.length > 0) {
      ElMessage.warning('当前选择的资源中存在变更中的资源，请重新选择')
      return false
    }

    // 每次校验前都重新获取变更列表数据
    if (!dg) {
      await fetchChangeList()
    }

    // 校验步骤1：检查选中资源的业务系统一致性
    if (resources.length > 1) {
      // 获取第一个资源的业务系统ID
      const firstBusinessSysId = resources[0].businessSysId

      // 检查所有选中资源的业务系统是否一致
      const hasInconsistentSys = resources.some(
        (resource) => resource.businessSysId !== firstBusinessSysId,
      )

      if (hasInconsistentSys) {
        ElMessage.warning('当前选择的资源的业务系统不一致，请重新选择')
        return false
      }
    }

    // 校验步骤2：检查与接口返回的业务系统是否一致
    if (resources.length > 0) {
      const businessSysId = resources[0].businessSysId

      // 检查业务系统是否与变更区中的业务系统一致
      // 只有当变更区存在业务系统且不一致时才拒绝
      if (changeData.value.basePropertyList.length > 0) {
        const isBusinessSystemConsistent = checkBusinessSystem(businessSysId)

        if (!isBusinessSystemConsistent) {
          return false
        }
      } else {
        // 变更区不存在业务系统，自动添加
        changeData.value.basePropertyList.push({ busiSystemId: businessSysId })
      }
    }

    // 所有校验通过
    return true
  }
  // 更新资源变更
  const updateResourceChange = async (
    resourceType: ResourceType,
    resources: ResourceProperty[],
    busiSystemId: string,
  ) => {
    // 确保有最新数据
    await fetchChangeList()

    // 获取对应资源类型的属性列表
    const propertyListKey = `${resourceType}PropertyList` as keyof ChangeData
    // 确保属性列表存在，如果不存在则初始化为空数组
    if (!Array.isArray(changeData.value[propertyListKey])) {
      changeData.value[propertyListKey] = []
    }

    // 保存当前属性列表的引用，避免直接修改原始数据导致问题
    const propertyList = changeData.value[propertyListKey] as ResourceProperty[]

    // 创建更新后的属性列表，确保不影响原有列表
    let updatedPropertyList = [...propertyList]
    // 遍历需要更新的资源
    resources.forEach((resource) => {
      // 查找现有列表中是否已存在该资源
      const existingResourceIndex = updatedPropertyList.findIndex(
        (item) => item.resourceDetailId === resource.resourceDetailId,
      )

      if (existingResourceIndex === -1) {
        // 如果不存在，直接添加整个资源
        updatedPropertyList.push({ ...resource })
      } else {
        // 如果已存在，需要合并props
        const existingResource = { ...updatedPropertyList[existingResourceIndex] }

        resource.props.forEach((newProp) => {
          // 查找现有资源中是否有相同changeType的prop
          const existingPropIndex = existingResource.props.findIndex(
            (prop) => prop.changeType === newProp.changeType,
          )

          if (existingPropIndex === -1) {
            // 如果不存在该changeType，直接添加
            existingResource.props.push({ ...newProp })
          } else if (newProp.changeType === 'delay') {
            // 如果是延期类型，以新数据为准
            existingResource.props[existingPropIndex] = {
              ...existingResource.props[existingPropIndex],
              after: newProp.after,
            }
          }
          // 对于其他changeType，保留原有数据，不做修改
        })

        // 更新资源
        updatedPropertyList[existingResourceIndex] = existingResource
      }
    })

    // 创建一个新的提交数据对象，不直接修改原始changeData
    const submitData = JSON.parse(JSON.stringify(changeData.value))
    // 将更新后的列表赋值到提交数据中
    submitData[propertyListKey] = updatedPropertyList
    if (!submitData.basePropertyList || submitData.basePropertyList.length === 0) {
      submitData.basePropertyList.push({ busiSystemId })
    }
    // 提交更新后的数据
    try {
      const result = await propertyChangeUpdate(submitData)

      return result
    } catch (error) {
      console.error('更新变更失败:', error)
      throw error
    }
  }

  return {
    changeData,
    checkBusinessSystem,
    fetchChangeList,
    updateResourceChange,
    validateResources,
  }
}
