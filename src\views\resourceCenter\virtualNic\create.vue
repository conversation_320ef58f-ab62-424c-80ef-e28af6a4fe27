<template>
  <div id="virtual-nic-create" class="table-box">
    <sl-page-header
      :title="pageTitle"
      :icon="{
        class: 'page_xuniwangka',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: goBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="virtual-nic-scroll-view" class="scroll-view">
      <!-- 基础信息 -->
      <div class="sl-card">
        <sl-block-title>基本信息</sl-block-title>
        <sl-form
          ref="formRef"
          v-model="formModel"
          :options="formOptions"
          label-width="120px"
        ></sl-form>
      </div>
    </el-scrollbar>
    <!-- 底部按钮 -->
    <div class="footer">
      <div class="button-group">
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlBlockTitle from '@/components/base/SlBlockTitle.vue'
import SlForm from '@/components/form/SlForm.vue'
import {
  getCloudTypeDic,
  getCloudPlatformDic,
  getResourcePoolsDic,
  getAzListDic,
} from '@/api/modules/dic'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  createVirtualNic,
  getVirtualNicDetail,
  vpcList,
  networkList,
  getVpcSubnetsList,
  getNetworkSubnetsList,
  updateVirtualNic,
} from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()
const formRef = ref()
const loading = ref(false)

// 判断是创建模式还是编辑模式
const isEditMode = computed(() => route.query.id !== undefined)
const virtualNicId = computed(() => (route.query.id as string) || '')

// 判断是否为对公虚拟网卡创建
const isPublicCreate = computed(() => route.query.sourceType === 'DG')

// 标题文本
const pageTitle = computed(() => {
  if (isEditMode.value) {
    return '虚拟网卡详情'
  }
  return isPublicCreate.value ? '创建对公虚拟网卡' : '创建虚拟网卡'
})

// 表单数据
const formModel = reactive({
  id: '', // 虚拟网卡ID
  vnicName: '', // 虚拟网卡名称
  catalogueDomainCode: '', // 云类型编码
  catalogueDomainName: '', // 云类型名称
  domainCode: '', // 云平台编码
  domainName: '', // 云平台名称
  regionId: '', // 资源池ID
  regionName: '', // 资源池名称
  regionCode: '', // 资源池编码
  azId: '', // 可用区ID
  azName: '', // 可用区名称
  azCode: '', // 可用区编码
  vpcOrNetworkId: '', // VPC/网络ID
  vpcOrNetworkName: '', // VPC/网络名称
  subnetId: '', // 子网ID
  subnetName: '', // 子网名称
  description: '', // 描述
})

// 字典数据
const cloudTypeOptions = ref<any[]>([])
const cloudPlatformOptions = ref<any[]>([])
const resourcePoolOptions = ref<any[]>([])
const azOptions = ref<any[]>([])
const vpcOrNetworkOptions = ref<any[]>([])
const subnetOptions = ref<any[]>([])

// 表单配置
const formOptions = reactive([
  {
    groupItems: [
      {
        label: '虚拟网卡名称',
        type: 'input',
        key: 'vnicName',
        span: 8,
        rules: [{ required: true, message: '请输入虚拟网卡名称', trigger: 'blur' }],
      },
      {
        label: '云类型',
        type: 'select',
        key: 'catalogueDomainCode',
        span: 8,
        options: cloudTypeOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择云类型', trigger: 'change' }],
        onChange: function () {
          formModel.catalogueDomainName = ''
          if (formModel.catalogueDomainCode) {
            const obj = cloudTypeOptions.value.find(
              (item: any) => item.code === formModel.catalogueDomainCode,
            )
            formModel.catalogueDomainName = obj?.name
          }
          formModel.domainCode = ''
          formModel.domainName = ''
          formModel.regionId = ''
          formModel.regionName = ''
          formModel.regionCode = ''
          formModel.azId = ''
          formModel.azName = ''
          formModel.azCode = ''
          formModel.vpcOrNetworkId = ''
          formModel.vpcOrNetworkName = ''
          formModel.subnetId = ''
          formModel.subnetName = ''
          cloudPlatformOptions.value = []
          resourcePoolOptions.value = []
          azOptions.value = []
          vpcOrNetworkOptions.value = []
          subnetOptions.value = []
          if (formModel.catalogueDomainCode) {
            getCloudPlatformList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
            disabled: isEditMode.value || isPublicCreate.value, // 对公虚拟网卡创建时禁用编辑
          },
        },
      },
      {
        label: '云平台',
        type: 'select',
        key: 'domainCode',
        span: 8,
        options: cloudPlatformOptions,
        labelField: 'name',
        valueField: 'code',
        rules: [{ required: true, message: '请选择云平台', trigger: 'change' }],
        onChange: function () {
          formModel.domainName = ''
          if (formModel.domainCode) {
            const obj = cloudPlatformOptions.value.find(
              (item: any) => item.code === formModel.domainCode,
            )
            formModel.domainName = obj?.name
          }
          formModel.regionId = ''
          formModel.regionName = ''
          formModel.regionCode = ''
          formModel.azId = ''
          formModel.azName = ''
          formModel.azCode = ''
          formModel.vpcOrNetworkId = ''
          formModel.vpcOrNetworkName = ''
          formModel.subnetId = ''
          formModel.subnetName = ''
          resourcePoolOptions.value = []
          azOptions.value = []
          vpcOrNetworkOptions.value = []
          subnetOptions.value = []
          if (formModel.domainCode) {
            getResourcePools()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '资源池',
        type: 'select',
        key: 'regionId',
        span: 8,
        options: resourcePoolOptions,
        labelField: 'name',
        valueField: 'id',
        rules: [{ required: true, message: '请选择资源池', trigger: 'change' }],
        onChange: function () {
          formModel.regionName = ''
          formModel.regionCode = ''
          if (formModel.regionId) {
            const obj = resourcePoolOptions.value.find(
              (item: any) => item.id === formModel.regionId,
            )
            formModel.regionName = obj?.name
            formModel.regionCode = obj?.code
          }
          formModel.azId = ''
          formModel.azName = ''
          formModel.azCode = ''
          formModel.vpcOrNetworkId = ''
          formModel.vpcOrNetworkName = ''
          formModel.subnetId = ''
          formModel.subnetName = ''
          azOptions.value = []
          vpcOrNetworkOptions.value = []
          subnetOptions.value = []
          if (formModel.regionId) {
            getAzList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '可用区',
        type: 'select',
        key: 'azId',
        span: 8,
        options: azOptions,
        labelField: 'name',
        valueField: 'id',
        rules: [{ required: true, message: '请选择可用区', trigger: 'change' }],
        onChange: function () {
          formModel.azName = ''
          formModel.azCode = ''
          if (formModel.azId) {
            const obj = azOptions.value.find((item: any) => item.id === formModel.azId)
            formModel.azName = obj?.name
            formModel.azCode = obj?.code
          }
          formModel.vpcOrNetworkId = ''
          formModel.vpcOrNetworkName = ''
          formModel.subnetId = ''
          formModel.subnetName = ''
          vpcOrNetworkOptions.value = []
          subnetOptions.value = []
          if (formModel.azId && formModel.regionId) {
            getVpcOrNetworkList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: 'VPC/网络',
        type: 'select',
        key: 'vpcOrNetworkId',
        span: 8,
        options: vpcOrNetworkOptions,
        labelField: 'name',
        valueField: 'id',
        rules: [{ required: true, message: '请选择VPC/网络', trigger: 'change' }],
        onChange: function () {
          formModel.vpcOrNetworkName = ''
          if (formModel.vpcOrNetworkId) {
            const obj = vpcOrNetworkOptions.value.find(
              (item: any) => item.id === formModel.vpcOrNetworkId,
            )
            formModel.vpcOrNetworkName = obj?.name
          }
          formModel.subnetId = ''
          formModel.subnetName = ''
          subnetOptions.value = []
          if (formModel.vpcOrNetworkId) {
            getSubnetList()
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '子网',
        type: 'select',
        key: 'subnetId',
        span: 8,
        options: subnetOptions,
        labelField: 'subnetName',
        valueField: 'id',
        rules: [{ required: true, message: '请选择子网', trigger: 'change' }],
        onChange: function () {
          formModel.subnetName = ''
          if (formModel.subnetId) {
            const obj = subnetOptions.value.find((item: any) => item.id === formModel.subnetId)
            formModel.subnetName = obj?.subnetName
          }
        },
        props: {
          select: {
            filterable: true,
            clearable: true,
          },
        },
      },
      {
        label: '描述',
        type: 'input',
        key: 'description',
        span: 16,
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 200,
          showWordLimit: true,
        },
      },
    ],
  },
])

// 初始化数据
onMounted(async () => {
  // 获取云类型数据
  await getCloudTypeList()

  // 如果是对公虚拟网卡创建，设置默认云类型
  if (isPublicCreate.value && !isEditMode.value) {
    formModel.catalogueDomainCode = 'cloudst_group_moc'
    // 查找对应的云类型名称
    const cloudType = cloudTypeOptions.value.find((item) => item.code === 'cloudst_group_moc')
    if (cloudType) {
      formModel.catalogueDomainName = cloudType.name
      // 自动加载云平台列表
      await getCloudPlatformList()
    }
  }

  // 如果是编辑模式，加载虚拟网卡详情
  if (isEditMode.value) {
    await loadVirtualNicDetail()
  }
})

// 获取云类型列表
const getCloudTypeList = async () => {
  try {
    const { entity } = await getCloudTypeDic(null)
    // 过滤云类型，只展示code为cloudst_group_moc和cloudst_group_nwc的云类型
    cloudTypeOptions.value = (entity || []).filter((item: any) =>
      ['cloudst_group_moc', 'cloudst_group_nwc'].includes(item.code),
    )
  } catch (error) {
    console.error('获取云类型失败', error)
  }
}

// 获取云平台列表
const getCloudPlatformList = async () => {
  try {
    const params: any = {
      parentCode: formModel.catalogueDomainCode,
      businessCode: 'virtualNic',
    }

    const { entity } = await getCloudPlatformDic(params)
    cloudPlatformOptions.value = entity || []
  } catch (error) {
    console.error('获取云平台失败', error)
  }
}

// 获取资源池列表
const getResourcePools = async () => {
  try {
    const { entity } = await getResourcePoolsDic({
      domainCode: formModel.domainCode,
      realmType: isPublicCreate.value ? 'iaas' : '',
    })
    resourcePoolOptions.value = entity || []
  } catch (error) {
    console.error('获取资源池失败', error)
  }
}

// 获取可用区列表
const getAzList = async () => {
  try {
    const { entity } = await getAzListDic({
      regionId: formModel.regionId,
    })
    azOptions.value = entity || []
  } catch (error) {
    console.error('获取可用区失败', error)
  }
}

// 获取VPC/网络列表
const getVpcOrNetworkList = async () => {
  console.log(formModel.catalogueDomainCode)
  if (formModel.catalogueDomainCode === 'cloudst_group_moc') {
    const params: any = {
      pageNum: 1,
      pageSize: 9999,
      resourcePoolId: formModel.regionId,
      azCode: formModel.azCode,
    }

    // 如果是对公虚拟网卡创建，添加sourceType参数
    if (isPublicCreate.value) {
      params.sourceType = 'DG'
    }

    const { entity } = await vpcList(params)
    entity.records.forEach((item: any) => {
      item.name = item.vpcName
    })
    vpcOrNetworkOptions.value = entity.records || []
  } else if (['cloudst_group_nwc', 'cloudst_group_plf'].includes(formModel.catalogueDomainCode)) {
    const { entity } = await networkList({
      pageNum: 1,
      pageSize: 9999,
      resourcePoolId: formModel.regionId,
      azCode: formModel.azCode,
    })
    vpcOrNetworkOptions.value = entity.records || []
  } else {
    vpcOrNetworkOptions.value = []
  }
}

// 获取子网列表
const getSubnetList = async () => {
  if (formModel.catalogueDomainCode === 'cloudst_group_moc') {
    const { entity } = await getVpcSubnetsList({
      vpcId: formModel.vpcOrNetworkId,
    })
    subnetOptions.value = entity || []
  } else if (['cloudst_group_nwc', 'cloudst_group_plf'].includes(formModel.catalogueDomainCode)) {
    const { entity } = await getNetworkSubnetsList({
      networkId: formModel.vpcOrNetworkId,
    })
    subnetOptions.value = entity || []
  } else {
    subnetOptions.value = []
  }
}

// 加载虚拟网卡详情
const loadVirtualNicDetail = async () => {
  try {
    loading.value = true
    const res = await getVirtualNicDetail({ id: virtualNicId.value })

    if (res.code === 200 && res.entity) {
      const detail = res.entity

      // 设置基本信息
      formModel.id = detail.id || ''
      formModel.vnicName = detail.vnicName || ''
      formModel.catalogueDomainCode = detail.catalogueDomainCode || ''
      formModel.catalogueDomainName = detail.catalogueDomainName || ''
      formModel.domainCode = detail.domainCode || ''
      formModel.domainName = detail.domainName || ''
      formModel.regionId = detail.regionId || ''
      formModel.regionName = detail.regionName || ''
      formModel.regionCode = detail.regionCode || ''
      formModel.azId = detail.azId || ''
      formModel.azName = detail.azName || ''
      formModel.azCode = detail.azCode || ''
      formModel.vpcOrNetworkId = detail.vpcId || ''
      formModel.vpcOrNetworkName = detail.vpcName || ''
      formModel.subnetId = detail.subnetId || ''
      formModel.subnetName = detail.subnetName || ''
      formModel.description = detail.description || ''

      // 加载资源池、可用区等关联数据
      if (detail.domainCode) {
        await getCloudPlatformList()
        if (detail.regionId) {
          await getResourcePools()
          await getAzList()
          if (detail.azId && formModel.vpcOrNetworkId) {
            await getVpcOrNetworkList()
            if (detail.subnetId) {
              await getSubnetList()
            }
          }
        }
      }
    } else {
      ElMessage.error(res.message || '获取虚拟网卡详情失败')
    }
  } catch (error) {
    console.error('获取虚拟网卡详情失败', error)
    ElMessage.error('获取虚拟网卡详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 返回列表页
const goBack = () => {
  if (!isEditMode.value && formModel.vnicName) {
    ElMessageBox.confirm('确定要离开吗？未保存的数据将会丢失', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        router.push('virtualNicList')
      })
      .catch(() => {
        // 用户取消操作，不执行任何动作
      })
  } else {
    router.push('virtualNicList')
  }
}

// 表单提交前处理数据
const prepareFormDataAndValidate = () => {
  // 验证表单
  if (!formModel.vnicName) {
    ElMessage.warning('虚拟网卡名称不能为空')
    return false
  }
  if (!formModel.vpcOrNetworkId) {
    ElMessage.warning('VPC/网络不能为空')
    return false
  }
  if (!formModel.subnetId) {
    ElMessage.warning('子网不能为空')
    return false
  }
  // 构造提交数据
  const submitData: any = {
    // id: formModel.id,
    vnicName: formModel.vnicName,
    catalogueDomainCode: formModel.catalogueDomainCode,
    catalogueDomainName: formModel.catalogueDomainName,
    domainCode: formModel.domainCode,
    domainName: formModel.domainName,
    regionId: formModel.regionId,
    regionName: formModel.regionName,
    regionCode: formModel.regionCode,
    azId: formModel.azId,
    azName: formModel.azName,
    azCode: formModel.azCode,
    vpcId: formModel.vpcOrNetworkId,
    vpcName: formModel.vpcOrNetworkName,
    subnetId: formModel.subnetId,
    subnetName: formModel.subnetName,
    description: formModel.description,
  }

  // 如果是对公虚拟网卡创建，添加sourceType字段
  if (isPublicCreate.value) {
    submitData.sourceType = 'DG'
  }

  return submitData
}

// 表单提交
const submit = async () => {
  try {
    loading.value = true
    const formData = await prepareFormDataAndValidate()
    if (!formData) {
      loading.value = false
      return
    }
    if (isEditMode.value) {
      formData.id = formModel.id
      const res = await updateVirtualNic(formData)
      if (res.code == 200) {
        ElMessage.success('编辑虚拟网卡成功')
        router.push('virtualNicList')
        return
      }
    } else {
      const res = await createVirtualNic(formData)
      if (res.code == 200) {
        ElMessage.success('创建虚拟网卡成功')
        router.push('virtualNicList')
        return
      }
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.scroll-view {
  margin-top: 2px;
}
.footer {
  height: 48px;
  padding: 8px 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  .button-group {
    display: flex;
    align-items: center;
    justify-content: right;
    flex-grow: 1;
  }
}
.add-busisystem-btn {
  display: flex;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
}
</style>
