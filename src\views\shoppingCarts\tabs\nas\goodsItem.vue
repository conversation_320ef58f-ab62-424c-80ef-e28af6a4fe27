<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="goods.orderJson"
  >
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { INasModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<INasModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: route.query.orderId ? true : false,
  })
}
//  不能包含linux系统文件夹 如mnt opt bin等
const validateStoragePath = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入存储路径'))
    return
  }

  // 检查是否包含斜杠
  if (value.includes('/')) {
    callback(new Error('存储路径不能包含斜杠 /'))
    return
  }

  // 定义linux系统文件夹列表
  const systemFolders = [
    'bin',
    'sbin',
    'etc',
    'dev',
    'proc',
    'sys',
    'tmp',
    'var',
    'usr',
    'lib',
    'lib64',
    'boot',
    'root',
    'mnt',
    'media',
    'opt',
    'srv',
    'home',
    'run',
  ]

  // 将路径转换为小写
  const normalizedPath = value.toLowerCase()

  // 检查是否是系统文件夹
  if (systemFolders.includes(normalizedPath)) {
    callback(new Error(`不能使用linux系统文件夹: ${normalizedPath}`))
    return
  }

  callback()
}

const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<INasModel>) {
  goods.ref = slFormRef
}

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: 'NAS名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入NAS名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '存储路径',
        type: 'input',
        key: 'storagePath',
        span: 8,
        rules: [
          { required: true, message: '请输入存储路径', trigger: ['blur', 'change'] },
          { validator: validateStoragePath, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '存储大小  ',
        type: 'inputNumber',
        key: 'storageSize',
        span: 8,
        props: {
          min: 0,
        },
        suffix: 'GB',
        required: true,
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
      {
        label: '开通数量',
        type: 'inputNumber',
        key: 'numbers',
        props: {
          disabled: true,
          min: 1,
          step: 1,
          max: 100,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请选择开通数量', trigger: ['blur', 'change'] },
        ],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
