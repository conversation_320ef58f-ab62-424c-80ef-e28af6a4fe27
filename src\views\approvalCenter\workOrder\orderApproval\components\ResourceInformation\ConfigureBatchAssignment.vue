<template>
  <div class="batch-assignment">
    <!--
    1.可以选择赋值可用取区 也可以选择赋值网络 单选

    -->
    <el-form :form="form" :inline="true">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="选择要操作的类型:">
            <el-radio-group v-model="form.type" @change="handleTypeChange">
              <el-radio v-for="item in batchOperationType" :key="item.value" :value="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item class="w100" label="可用区:">
            <el-select
              class="w100"
              v-model="form.az"
              placeholder="请选择可用区"
              @change="handleAzChange"
              value-key="id"
            >
              <el-option
                v-for="item in azList[selePool?.[0]?.regionId]"
                :key="item.value"
                :label="item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <template v-if="form.type === 'network'">
          <el-col :span="6">
            <el-form-item class="w100" label="配置网络:">
              <el-button type="primary" @click="openNetworkDialog" link>配置网络</el-button>
            </el-form-item>
          </el-col>
        </template>

        <el-form-item>
          <el-button type="primary" @click="submit">确 定</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <ConfigureNetwork @updateNetworks="updateNetworks" ref="configureBatchNetworkRef" />
  </div>
</template>

<script lang="tsx" setup>
import { showTips } from '@/utils'
import { inject, ref, toRefs, watch } from 'vue'
import ConfigureNetwork from './ConfigureNetwork.vue'

type propsType = {
  batchOperationType: any[]
  azList: FormDataType
  selePool: FormDataType[]
  orderId: string
  multiple: boolean
}

const props = withDefaults(defineProps<propsType>(), {
  batchOperationType: () => [],
  azList: () => [],
  selePool: () => [],
})

const form = ref<{
  type: string
  az: any
}>({
  type: 'az',
  az: null,
})

const { selePool } = toRefs(props)
const planeNetworkModel = ref<FormDataType[]>([])

const configureBatchNetworkRef = ref<InstanceType<typeof ConfigureNetwork>>()

// const isInnovationPool = inject('isInnovationPool', ref(false))
const domainCodeStatus = inject('domainCodeStatus', ref(false))

watch(
  () => selePool.value,
  (newVal) => {
    if (newVal.length === 0) {
      form.value.az = null
      planeNetworkModel.value = []
    }
  },
)

const emit = defineEmits(['handleTypeChange', 'submit'])

const handleTypeChange = () => {
  form.value.az = null
  planeNetworkModel.value = []
  emit('handleTypeChange', form.value.type)
}

const handleAzChange = () => {
  planeNetworkModel.value = []
}

const openNetworkDialog = () => {
  if (!form.value.az) return showTips('请先选择可用区')
  let params = {
    orderId: props.orderId,
    regionCode: selePool.value?.[0]?.regionCode,
    azCode: form.value.az?.code,
  }
  let planes: any = selePool.value?.[0]?.plane?.split(',') ?? []

  //  获取交集
  selePool.value.forEach((item) => {
    const plane = item.plane?.split(',') ?? []
    planes = planes.filter((i: any) => plane.includes(i))
  })
  if (planes.length === 0) return showTips('选择数据网路平面无交集')

  configureBatchNetworkRef.value?.openDialog({
    row: {
      planeNetworkModel: planeNetworkModel.value,
      plane: planes.join(','),
    },
    index: 1,
    disabled: false,
    multiple: props.multiple,
    vpc: domainCodeStatus.value,
    params,
  })
}

const updateNetworks = (data: any) => {
  planeNetworkModel.value = data
}

const submit = () => {
  // 1.校验不为空
  if (!form.value.az) return showTips('请选择可用区')
  if (form.value.type === 'network' && !planeNetworkModel.value.length)
    return showTips('请选择网络')
  // 2.处理数据
  let params: any = {
    azId: form.value.az.id,
    azName: form.value.az.name,
    azCode: form.value.az.code,
  }

  if (form.value.type === 'network') {
    params = {
      ...params,
      planeNetworkModel: JSON.parse(JSON.stringify(planeNetworkModel.value)),
    }
  } else {
    // 清空子网网络
    params = {
      ...params,
      planeNetworkModel: [],
    }
  }

  emit('submit', params)
}

const resetForm = () => {
  form.value = {
    type: 'az',
    az: null,
  }
  planeNetworkModel.value = []
}
defineExpose({
  resetForm,
})
</script>

<style lang="scss" scoped>
.batch-assignment {
  width: 100%;
  margin-bottom: 20px;
}
.w100 {
  width: 100%;
}
</style>
