import { defineStore } from 'pinia'
import {
  getCloudPlatformDic,
  getResourcePoolsDic,
  getCloudLeaderDic,
  getDic,
} from '@/api/modules/dic'
import { computed } from 'vue'

type IdicItem = {
  label: string
  value: string
  id?: string
}
type Dic = {
  [key: string]: IdicItem[]
}
type State = {
  dic: Record<string, any> // 假设 dic 是一个键值对的对象
}

const dicKeys = [
  'imageOs',
  'time',
  'functionalModule',
  'securityDomain',
  'plane',
  'importanceLevel',
  'businessSystemType',
  'lifeCycleStates',
  'trueOrFalse',
  'netRange',
  'vmStatus',
  'sysDisk',
  'ecs',
  'gcs',
  'evs',
  'eip',
  'slb',
  'obs',
  'nat',
  'userStatus',
  'userCategory',
  'evsStatus',
  'natStatus',
  'obsStatus',
  'slbStatus',
  'recovery', // 回收工单状态
  'subscribe', // 创建工单状态
  'nonStandard', // 非标工单状态
  'change', // 变更工单状态
].join(',')

export const useGlobalDicStore = defineStore('globalDic', {
  state: () => ({
    dic: {} as Dic,
  }),
  actions: {
    async initDic() {
      const res = await getDic({
        dicKeys: dicKeys,
      })
      this.dic = res.entity as Dic
    },
  },
  getters: {
    getDic: (state: State) => {
      return (key: string) => computed(() => state.dic[key] || [])
    },
    getDicNumber: (state: State) => {
      return (key: string) =>
        computed(() => {
          const dicArray = state.dic[key] || []
          return dicArray.map((item: any) => ({
            ...item,
            value: Number(item.value), // 将 value 字段转换为数字
          }))
        })
    },
  },
})

// 全局单利store
export const useDicStore = defineStore('dic', {
  state: () => ({
    cloudTypeDic: [] as IdicItem[], // 云类型
    securityDomainDic: [] as IdicItem[], // 安全域,从配置表获取
    cloudLeaderDic: [] as IdicItem[], // 云资源部领导
    businessLeaderDic: [] as IdicItem[], // 业务部门领导
  }),
  actions: {
    async updateCloudLeaderDic() {
      const res = await getCloudLeaderDic({
        roleCode: 'cloud_leader',
      })
      resetReactiveArray(this.cloudLeaderDic, res.entity, (item) => ({
        value: item.id,
        label: item.userOrgName,
      }))
    },
  },
})

/**
 * 多实例store
 * 使用实例:
 * import { createPinia } from 'pinia'
 * const piniaInstance1 = createPinia()
 * const piniaInstance2 = createPinia()
 * const dicStore1 = useIndependentDicStore(piniaInstance1)
 * const dicStore2 = useIndependentDicStore(piniaInstance2)
 */
export const useIndependentDicStore = defineStore('dic', {
  state: () => ({
    cloudPlatformDic: [] as IdicItem[], // 云平台
    resourcePoolsDic: [] as IdicItem[], // 资源池
  }),
  actions: {
    async updateCloudPlatformDic(config: any) {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await getCloudPlatformDic(config) // 调用异步接口
          resetReactiveArray(this.cloudPlatformDic, res.entity, (item) => ({
            value: item.code,
            label: item.name,
          }))
          resolve(res)
        } catch (error) {
          reject(error)
        }
      })
    },
    async updateResourcePoolsDic(config: any) {
      const res = await getResourcePoolsDic(config)
      resetReactiveArray(this.resourcePoolsDic, res.entity, (item) => ({
        value: item.id,
        label: item.name,
      }))
    },
  },
})

function resetReactiveArray(target: Array<any>, value: Array<any>, cb: (item: any) => any) {
  target.length = 0
  value.forEach((item: any, index) => {
    target[index] = cb(item)
  })
}
