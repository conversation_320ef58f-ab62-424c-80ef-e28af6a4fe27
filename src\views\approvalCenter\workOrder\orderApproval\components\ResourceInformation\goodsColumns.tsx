import { useGlobalDicStore } from '@/stores/modules/dic'
import type { goodsColumnsType } from '@/views/approvalCenter/workOrder/interface/type'
import { h, ref, defineComponent } from 'vue'
import SlDialog from '@/components/SlDialog/index.vue'
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const whethers = [
  { label: '是', value: true },
  { label: '否', value: false },
]
// 云服务器
const ecsColumns: goodsColumnsType['ecs'] = [
  {
    prop: 'originName',
    label: '主机名称',
    minWidth: '180px',
  },
  {
    label: '功能模块',
    prop: 'functionalModule',
    minWidth: '100px',
  },
  {
    prop: 'flavorName',
    label: '实例规格',
    minWidth: 180,
    render(scope) {
      return (
        <div>
          {scope.row.flavorType ?? '--'} / {scope.row.flavorName ?? '--'}
        </div>
      )
    },
  },
  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  {
    prop: 'imageName',
    label: '镜像',
    minWidth: '180px',
    render(scope) {
      return (
        <div>
          {scope.row.imageVersion ?? '--'} / {scope.row.imageOs ?? '--'}
        </div>
      )
    },
  },
  {
    label: '带宽',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  {
    prop: 'plane',
    label: '网络平面',
    minWidth: '150px',
  },
  {
    prop: 'disasterRecovery',
    label: '是否容灾',
    minWidth: '150px',
    headerRender,

    enum: whethers,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },

  // 云硬盘配置
]
// 云硬盘
const evsColumns: goodsColumnsType['evs'] = [
  {
    label: '数据盘',
    prop: 'evsInfo',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.sysDiskType ?? '--'} / {scope.row.sysDiskSize ?? '--'} GB
        </div>
      )
    },
  },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '是否挂载云主机',
    prop: 'mountDataDisk',
    minWidth: 130,
    render(scope) {
      return <div>{scope.row.vmName ? '是' : '否'}</div>
    },
  },
  {
    label: '云主机名称(IP)',
    prop: 'vmName',
    minWidth: 150,
    render(scope) {
      return <div>{scope.row.vmName?.length ? scope.row.vmName : '--'}</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]
// 对象存储
const obsColumns: goodsColumnsType['obs'] = [
  { label: '对象存储名称', prop: 'originName', minWidth: 150 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '存储类型',
    prop: 'storageDiskType',
    minWidth: 120,
  },
  {
    label: '大小',
    prop: 'storageDiskSize',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.storageDiskSize ?? '--'} GB</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// 负载均衡
const slbColumns: goodsColumnsType['slb'] = [
  { label: '负载均衡名称', prop: 'originName', minWidth: 150 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '实例规格',
    prop: 'flavorName',
    minWidth: 180,
  },
  { label: '是否绑定公网', prop: 'bindPublicIp', enum: whethers, minWidth: 120 },
  {
    label: '带宽',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]
// NAT网关
const natColumns: goodsColumnsType['nat'] = [
  { label: '网关名称', prop: 'originName', minWidth: 150 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '实例规格',
    prop: 'flavorName',
    minWidth: 180,
  },
  { label: '是否绑定公网', prop: 'bindPublicIp', enum: whethers, minWidth: 120 },
  {
    label: '带宽',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// NAS
const nasColumns: goodsColumnsType['nas'] = [
  { label: 'NAS名称', prop: 'originName', minWidth: 150 },
  { label: '存储路径', prop: 'path', minWidth: 120 },
  {
    label: '储存大小',
    prop: 'storageSize',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.storageSize ? scope.row.storageSize + 'GB' : '--'}</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// VPN
const vpnColumns: goodsColumnsType['vpn'] = [
  { label: 'VPN名称', prop: 'originName', minWidth: 150 },
  { label: '最大客户端数', prop: 'maxConnection', minWidth: 120 },
  {
    label: '带宽',
    prop: 'bandwidth',
    minWidth: 120,
    render(scope) {
      return <div>{scope.row.bandwidth ? scope.row.bandwidth + 'M' : '--'} </div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// 弹性IP
const eipColumns: goodsColumnsType['eip'] = [
  { label: '弹性公网名称', prop: 'originName', minWidth: 150 },
  { label: '功能模块', prop: 'functionalModule', minWidth: 120 },
  {
    label: '云主机名称(IP)',
    prop: 'vmName',
    minWidth: 150,
    render(scope) {
      return <div>{scope.row.vmName?.length ? scope.row.vmName : '--'}</div>
    },
  },
  {
    label: '带宽',
    prop: 'bandwidth',
    minWidth: 100,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// 容器配额
const cqColumns: goodsColumnsType['cq'] = [
  { label: '容器配额名称', prop: 'originName', minWidth: 200 },
  { label: 'vCPU(核)', prop: 'vCpus', minWidth: 100 },
  {
    label: '内存(GB)',
    prop: 'ram',
    minWidth: 100,
  },
  {
    label: 'GPU算力',
    prop: 'gpuRatio',
    minWidth: 100,
  },
  {
    label: 'GPU显存(GB)',
    prop: 'gpuVirtualMemory',
    minWidth: 130,
  },
  {
    label: 'GPU卡数量(个)',
    prop: 'gpuCore',
    minWidth: 130,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// MySQL数据库
const mysqlColumns: goodsColumnsType['mysql'] = [
  {
    prop: 'originName',
    label: '云数据库名称',
    minWidth: '180px',
  },
  {
    label: '功能模块',
    prop: 'functionalModule',
    minWidth: '100px',
  },
  {
    label: '系列',
    prop: 'deployType',
    minWidth: 100,
    enum: [
      { label: '单机版本', value: 'ALONE' },
      { label: '主备版本', value: 'COLONY' },
    ],
  },
  {
    prop: 'flavorName',
    label: '实例规格',
    minWidth: 180,
    render(scope) {
      return (
        <div>
          {scope.row.flavorType ?? '--'} / {scope.row.flavorName ?? '--'}
        </div>
      )
    },
  },
  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  {
    prop: 'imageName',
    label: '镜像',
    minWidth: '180px',
    render(scope) {
      return (
        <div>
          {scope.row.imageVersion ?? '--'} / {scope.row.imageOs ?? '--'}
        </div>
      )
    },
  },
  {
    label: '带宽',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  {
    prop: 'disasterRecovery',
    label: '是否容灾',
    minWidth: '150px',
    headerRender,

    enum: whethers,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },

  // 云硬盘配置
]

// Redis缓存
const redisColumns: goodsColumnsType['redis'] = [
  {
    prop: 'originName',
    label: '通用Redis名称',
    minWidth: '180px',
  },
  {
    label: '功能模块',
    prop: 'functionalModule',
    minWidth: '100px',
  },
  {
    prop: 'flavorName',
    label: '实例规格',
    minWidth: 180,
    render(scope) {
      return (
        <div>
          {scope.row.flavorType ?? '--'} / {scope.row.flavorName ?? '--'}
        </div>
      )
    },
  },
  {
    prop: 'sysDiskInfo',
    label: '系统盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row?.sysDiskType ?? '--'} / {scope.row?.sysDiskSize ?? '--'}
          GB
        </div>
      )
    },
  },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row.mountDataDisk
            ? scope.row.mountDataDiskList
                ?.map((item: any) => {
                  return `${item.sysDiskType ?? '--'} / ${item.sysDiskSize ?? '--'} GB`
                })
                .join(',')
            : '--'}
        </div>
      )
    },
  },
  {
    prop: 'imageName',
    label: '镜像',
    minWidth: '180px',
    render(scope) {
      return (
        <div>
          {scope.row.imageVersion ?? '--'} / {scope.row.imageOs ?? '--'}
        </div>
      )
    },
  },
  {
    label: '带宽',
    prop: 'bandWiDthNumbers',
    minWidth: 100,
    render(scope) {
      return (
        <div>
          {scope.row.bindPublicIp
            ? scope.row.eipModelList?.map((item: any) => item.bandwidth).join(',')
            : '--'}
        </div>
      )
    },
  },
  {
    prop: 'disasterRecovery',
    label: '是否容灾',
    minWidth: '150px',
    headerRender,

    enum: whethers,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },

  // 云硬盘配置
]
// 云灾备
const backupColumns: goodsColumnsType['backup'] = [
  { label: '云灾备名称', prop: 'originName', minWidth: 200 },
  // ECS：云主机 EVS：云硬盘
  {
    label: '备份类型',
    prop: 'backupType',
    minWidth: 100,
    enum: [
      { label: '云主机', value: 'ECS' },
      {
        label: '云硬盘',
        value: 'EVS',
      },
    ],
  },
  {
    label: '备份频率',
    prop: 'frequency',
    minWidth: 100,
    enum: [
      { label: '每天', value: 'days' },
      { label: '每周', value: 'weeks' },
    ],
  },
  {
    label: '星期',
    prop: 'daysOfWeek',
    minWidth: 100,
  },
]

// Kafka
const kafkaColumns: goodsColumnsType['kafka'] = [
  { label: 'Kafka名称', prop: 'name', minWidth: 150 },
  {
    label: '数据流量',
    prop: 'dataFlow',
    minWidth: 120,
    render(scope) {
      return <div>{scope.row.dataFlow ? scope.row.dataFlow + 'MB/天' : '--'}</div>
    },
  },
  { label: '分区', prop: 'partition', minWidth: 100 },
  {
    label: '副本',
    prop: 'replication',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.replication ? scope.row.replication + '个' : '--'}</div>
    },
  },
  {
    label: '保留时间',
    prop: 'retainTime',
    minWidth: 120,
    render(scope) {
      return <div>{scope.row.retainTime ? scope.row.retainTime + '天' : '--'}</div>
    },
  },
  {
    label: '数据存储总量',
    prop: 'dataStorageTotal',
    minWidth: 120,
    render(scope) {
      return <div>{scope.row.dataStorageTotal ? scope.row.dataStorageTotal + 'GB' : '--'}</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// 裸金属
const pmColumns: goodsColumnsType['pm'] = [
  { label: '裸金属名称', prop: 'pmName', minWidth: 150 },
  {
    label: 'CPU',
    prop: 'vCpus',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.vCpus ? scope.row.vCpus + '核' : '--'}</div>
    },
  },
  {
    label: '内存',
    prop: 'ram',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.ram ? scope.row.ram + 'GB' : '--'}</div>
    },
  },
  {
    label: '硬盘',
    prop: 'diskSize',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.diskSize ? scope.row.diskSize + 'GB' : '--'}</div>
    },
  },
  { label: '显卡型号', prop: 'gpuCardType', minWidth: 120 },
  { label: '显卡类型', prop: 'gpuType', minWidth: 120 },
  {
    label: '显卡数量',
    prop: 'gpuNum',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.gpuNum ? scope.row.gpuNum + '张' : '--'}</div>
    },
  },

  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// Flink
const flinkColumns: goodsColumnsType['flink'] = [
  { label: 'Flink名称', prop: 'flinkName', minWidth: 150 },
  {
    label: 'vCPU',
    prop: 'vCpus',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.vCpus ? scope.row.vCpus + '核' : '--'}</div>
    },
  },
  {
    label: '内存',
    prop: 'ram',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.ram ? scope.row.ram + 'GB' : '--'}</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// ES
const esColumns: goodsColumnsType['es'] = [
  { label: '索引模板名称', prop: 'name', minWidth: 150 },
  {
    label: '索引模板',
    prop: 'indexTemplate',
    minWidth: 150,
    render(scope) {
      return h(
        defineComponent({
          setup() {
            const showDialog = ref(false)
            const handleClick = (e: MouseEvent) => {
              e.stopPropagation()
              showDialog.value = true
            }
            return () => [
              h(
                'span',
                {
                  style: 'color: #409EFF; cursor: pointer;',
                  onClick: handleClick,
                },
                '查看',
              ),
              showDialog.value &&
                h(
                  SlDialog,
                  {
                    modelValue: showDialog.value,
                    'onUpdate:modelValue': (val: boolean) => (showDialog.value = val),
                    title: '索引模板',
                    width: '600px',
                    showCancel: false,
                    showConfirm: false,
                    closeOnClickModal: true,
                    appendToBody: true,
                  },
                  {
                    default: () =>
                      h(
                        'pre',
                        {
                          style:
                            'max-height: 400px; overflow: auto; background: #f5f5f5; padding: 16px; border-radius: 4px;',
                        },
                        JSON.stringify(scope.row.indexTemplate, null, 2),
                      ),
                  },
                ),
            ]
          },
        }),
      )
    },
  },
  {
    label: '日均增量数据',
    prop: 'averageDailyIncrementData',
    minWidth: 120,
    render(scope) {
      return (
        <div>
          {scope.row.averageDailyIncrementData
            ? scope.row.averageDailyIncrementData + 'GB/天'
            : '--'}
        </div>
      )
    },
  },
  {
    label: '数据保留时间',
    prop: 'retainTime',
    minWidth: 120,
    render(scope) {
      return <div>{scope.row.retainTime ? scope.row.retainTime + '天' : '--'}</div>
    },
  },
  {
    label: '索引副本',
    prop: 'numberOfReplicas',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.numberOfReplicas ? scope.row.numberOfReplicas + '个' : '--'}</div>
    },
  },
  {
    label: '磁盘大小',
    prop: 'diskSize',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.diskSize ? scope.row.diskSize + 'GB' : '--'}</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

// 国产Redis
const bldRedisColumns: goodsColumnsType['bldRedis'] = [
  { label: '实例名称', prop: 'name', minWidth: 150 },
  { label: '实例ip', prop: 'ip', minWidth: 150 },
  { label: 'CPU架构', prop: 'cpuArchitecture', minWidth: 120 },
  // { label: '业务系统名称', prop: 'businessSysName', minWidth: 150 },
  // { label: '用户名', prop: 'username', minWidth: 120 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
]

const goodsAllColumns: goodsColumnsType = {
  ecs: ecsColumns,
  gcs: ecsColumns,
  evs: evsColumns,
  obs: obsColumns,
  slb: slbColumns,
  nat: natColumns,
  nas: nasColumns,
  vpn: vpnColumns,
  eip: eipColumns,
  cq: cqColumns,
  mysql: mysqlColumns,
  redis: redisColumns,
  backup: backupColumns,
  kafka: kafkaColumns,
  pm: pmColumns,
  flink: flinkColumns,
  es: esColumns,
  bldRedis: bldRedisColumns,
  other: [],
  unknown: [],
}

function headerRender({ column }: any) {
  return (
    <>
      <div>{column.label}</div>
      <div>
        <span style="color: red; font-size: 12px">容灾选"否"可能导致业务系统高可用缺失</span>
      </div>
    </>
  )
}
export default goodsAllColumns

export const goodsNameKey = {
  ecs: 'vmName',
  gcs: 'vmName',
  evs: 'evsName',
  obs: 'obsName',
  slb: 'slbName',
  nat: 'natName',
  nas: 'name',
  vpn: 'name',
  eip: 'eipName',
  cq: 'cqName',
  backup: 'jobName',
  kafka: 'name',
  pm: 'pmName',
  flink: 'flinkName',
  es: 'name',
  bldRedis: 'name',
  mysql: 'vmName',
  redis: 'vmName',
}
