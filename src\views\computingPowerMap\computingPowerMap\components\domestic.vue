<template>
  <section class="panel power-panel" :class="{ active: props.isActive }">
    <div class="card-content">
      <Title title="国产化" />
      <div class="comPowerDataBaseBox">
        <div class="comPowerDataBaseBoxLeft">
          <img src="/images/computingPower/comPowerDataBaseIcon.png" alt="" />
          <p>{{ dataInfo.total }}</p>
          <span>数据库节点数</span>
        </div>
        <div>
          <img src="/images/computingPower/comPowerSplitIcon.png" alt="" />
        </div>
        <div style="width: 60%; margin-top: -30px">
          <div style="display: flex; align-items: center; justify-content: center">
            <div ref="memLineRef" style="width: 200px; height: 110px; margin-top: 0px"></div>
          </div>
          <div class="infrastructureRight">
            <div class="infrastructureRightRow">
              <span class="item1"></span>
              <span class="item2">数据库</span>
              <span class="item3">占比</span>
            </div>
            <div class="infrastructureRightRow">
              <span class="item1">
                <i class="bg3D6EEFGrid"></i>
                国产化
              </span>
              <span class="item2">{{ dataInfo.domesticDBNumber }}</span>
              <span class="item3">{{ dataInfo.domesticDBRate }}</span>
            </div>
            <div class="infrastructureRightRow">
              <span class="item1">
                <i class="bg34B3E0Grid"></i>
                非国产化
              </span>
              <span class="item2">{{ dataInfo.foreignDBNumber }}</span>
              <span class="item3"> {{ dataInfo.foreignDBRate }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount, watch, nextTick } from 'vue'
import Title from './Title.vue'

import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import 'echarts-gl' // 引入 3D 扩展
import { getComPowerMapStatsDataBaseNode } from '@/api/modules/comPowerCenter'

// import { MapChart, ScatterChart } from 'echarts/charts'
// import { GeoComponent, TooltipComponent, VisualMapComponent } from 'echarts/components'
// import { SVGRenderer } from 'echarts/renderers'

const memLineRef = ref<HTMLDivElement | null>(null)

const props = defineProps({
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  isActive: {
    type: Boolean,
    default: false,
  },
})

// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)
const dataInfo = ref({
  total: '',
  foreignDBNumber: '',
  foreignDBRate: '',
  domesticDBNumber: '',
  domesticDBRate: '',
})
const getDataInfo = () => {
  getComPowerMapStatsDataBaseNode({
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
    cityCode: props.requestParams.cityCode,
  }).then((res: any) => {
    if (res.code === 200) {
      let data = res.entity
      let testData: any = []
      data.forEach((item: any) => {
        if (item.dbCategory == 'total') {
          dataInfo.value.total = item.nodeNum
        } else if (item.dbCategory == 'foreignDB') {
          dataInfo.value.foreignDBNumber = item.nodeNum
          testData.push(parseFloat((item.rate * 100).toFixed(2)))
          dataInfo.value.foreignDBRate = parseFloat((item.rate * 100).toFixed(2)) + '%'
        } else if (item.dbCategory == 'domesticDB') {
          dataInfo.value.domesticDBNumber = item.nodeNum
          testData.push(parseFloat((item.rate * 100).toFixed(2)))
          dataInfo.value.domesticDBRate = parseFloat((item.rate * 100).toFixed(2)) + '%'
        }
      })
      nextTick(() => {
        sum = testData.reduce(
          (accumulator: any, currentValue: any) => accumulator + currentValue,
          0,
        )
        optionsData = []
        for (let i = 0; i < xData.length; i++) {
          optionsData.push({
            name: xData[i],
            value: testData[i],
            itemStyle: {
              color: colors[i],
            },
          })
        }
        // let ydata:any = [dataInfo.value.domesticDBRate, dataInfo.value.foreignDBNumber]
        console.log('*******************')
        console.log(testData)
        setOption()
      })
    }
  })
}

// 原始数值堆叠柱状图数据
onMounted(() => {
  // 折线图：显存利用率
  memLine = echarts.init(memLineRef.value!, null, {
    width: 200,
    height: 110,
    // renderer: 'svg', // 明确指定使用SVG渲染器
  })
  getDataInfo()
})

onBeforeMount(() => {
  memLine?.dispose()
})
let memLine: ECharts | null = null
// watch(
//   () => props.data,
//   () => {
//     setOption()
//   },
// )
function getParametricEquation(
  startRatio: any,
  endRatio: any,
  isSelected: any,
  isHovered: any,
  k: any,
  height: any,
) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2

  let startRadian = startRatio * Math.PI * 2
  let endRadian = endRatio * Math.PI * 2
  let midRadian = midRatio * Math.PI * 2

  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = 1

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0
  offsetX -= 1.56 // 向左移动

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 0.7

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    y: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    z: function (u: any, v: any) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u)
      }
      return Math.sin(v) > 0 ? 1 * height : -1
    },
  }
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData: any, internalDiameterRatio: any) {
  let series = []
  let sumValue = 0
  let startValue = 0
  let endValue = 0
  let legendData = []
  let k =
    typeof internalDiameterRatio !== 'undefined'
      ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
      : 1 / 3

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value

    type ChartConfig = {
      name: string
      type: string
      parametric: boolean
      wireframe: { show: boolean }
      pieData?: any // 改为可选
      pieStatus?: { selected: boolean; hovered: boolean; k: number }
      itemStyle: null | { opacity: number; color: string } // 合并 itemStyle
      parametricEquation?: {
        u: { min: number; max: number; step: number }
        v: { min: number; max: number; step: number }
        x: any
        y: any
        z: any
      }
    }
    let seriesItem: ChartConfig = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
      itemStyle: null,
    }
    // 正确做法：声明为正确的类型
    interface Style {
      color: any | null
      opacity: any | null
    }
    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle: Style = {
        color: null,
        opacity: null,
      }

      typeof pieData[i].itemStyle.color != 'undefined'
        ? (itemStyle.color = pieData[i].itemStyle.color)
        : null
      typeof pieData[i].itemStyle.opacity != 'undefined'
        ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
        : null

      seriesItem.itemStyle = itemStyle
    }
    series.push(seriesItem)
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value
    series[i].pieData.startRatio = startValue / sumValue
    series[i].pieData.endRatio = endValue / sumValue
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value,
    )

    startValue = endValue

    legendData.push(series[i].name)
  }

  // // // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.1,
      color: '#E1E8EC',
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 1,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u: any, v: any) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.5 - 1.5
      },
      y: function (u: any, v: any) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.5
      },
      z: function (u: any, v: any) {
        return Math.cos(v) > 0 ? -0.5 : -5
      },
    },
  })
  // // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.1,
      color: '#fff',
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u: any, v: any) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.7 - 1.5
      },
      y: function (u: any, v: any) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.7
      },
      z: function (u: any, v: any) {
        return Math.cos(v) > 0 ? -5 : -7
      },
    },
  })
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.1,
      color: '#E1E8EC',
    },

    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u: any, v: any) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 3.2 - 1.5
      },
      y: function (u: any, v: any) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 3.2
      },
      z: function (u: any, v: any) {
        return Math.cos(v) > 0 ? -10 : -10
      },
    },
  })
  return series
}

let colors = ['#3D6EEF', '#33A5D6', '#3586fa', '#f5b64b']
let xData = ['非国产化', '国产化']
// let yData = [dataInfo.value.domesticDBNumber, dataInfo.value.foreignDBNumber]
// 使用 reduce 方法计算数组的总和
let sum = 0

// 传入数据生成 option
let optionsData: any = []

function setOption() {
  if (!memLine) return

  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  let option = {
    animation: true,
    tooltip: {
      formatter: (params: any) => {
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          let value =
            option.series[params.seriesIndex].pieData &&
            option.series[params.seriesIndex].pieData.value
          console.log(value)
          console.log(sum)
          let num: any = ((value / sum) * 100).toFixed(2)
          console.log(num)
          return `
                占比：
            <span style="color: #2BFFCA ;font-size: 18px;">${num}% </span>`
        }
      },

      borderColor: '#1CE7FF',
      backgroundColor: 'rgba(3,18,62,0.66)',
      padding: [5, 10],
      textStyle: {
        color: '#fff',
        fontSize: 16,
        align: 'left',
      },
    },
    title: {
      x: 'center',
      top: '20',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    // backgroundColor: '#07239e',
    labelLine: {
      show: false,
      lineStyle: {
        color: 'transparent',
      },
    },
    label: {
      show: false,
      color: 'transparent',
      position: 'outside',
      formatter: '{b} \n{c} {d}%',
    },
    xAxis3D: {},
    yAxis3D: {},
    zAxis3D: {},
    grid3D: {
      show: false,
      boxHeight: 10,
      //top: '30%',
      bottom: '20%',

      viewControl: {
        distance: 70,
        alpha: 25,
        beta: 0,
        rotateSensitivity: 0, //设置是否可以旋转
        zoomSensitivity: 0, //设置是否可以缩放
        panSensitivity: 0, //设置是否可以平移
      },
    },

    series: getPie3D(optionsData, 0.8),
  }
  // 给最小值和最大值留一些边距
  memLine.setOption(option)
}
</script>

<style lang="scss" scoped>
.tip-con {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.right-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: right;
}

.power-bar-chart {
  width: 100%;
  background: transparent;
  margin-top: 0;
}

.panel {
  position: relative;
}
.power-panel {
  cursor: pointer;
  height: 268px;
  box-sizing: border-box;
  border-radius: 16px;
  padding: 2px;
  position: relative;
  background-color: #fff;
  background: url('/images/computingPower/comPowerLeft2Bg.png') no-repeat center center;
  background-size: 100% 100%;
}
.power-panel.active {
  background: url('/images/computingPower/comPowerLeft2BgActive.png') no-repeat center center;
  background-size: 100% 100%;
}
.comPowerDataBaseBox {
  display: flex;
  gap: 20px;
  padding-left: 20px;
  padding-top: 30px;
  .comPowerDataBaseBoxLeft {
    text-align: center;
    p {
      color: #004fb1;
      font-size: 24px;
      margin: 20px 0 9px;
      line-height: 1;
    }
    span {
      font-size: 15px;
      color: #080c13;
    }
  }
}

.bg34B3E0Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #34b3e0;
  margin-right: 5px;
}

.bg3D6EEFGrid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #3d6eef;
  margin-right: 5px;
}
.infrastructureRight {
  width: 100%;
  .infrastructureRightRow {
    border-bottom: 1px solid #dee8fb;
    padding: 8px 0;
    span {
      display: inline-block;
      font-size: 16px;
      padding: 0 6px;
      box-sizing: border-box;
    }
    span.item1 {
      font-size: 15px;
      width: 42%;
    }
    span.item2 {
      color: #004fb1;
      text-align: right;
      width: 33%;
      padding-right: 12px;
      box-sizing: border-box;
    }
    span.item3 {
      width: 25%;
      color: #004fb1;
    }
  }
}
</style>
