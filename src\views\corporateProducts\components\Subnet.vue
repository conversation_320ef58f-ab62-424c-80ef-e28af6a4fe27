<template>
  <div class="subnet-container">
    <!-- 子网项 -->
    <div v-for="(subnet, index) in subnets" :key="index" class="subnet-item">
      <div class="subnet-form">
        <!-- 名称字段 -->
        <div class="form-row">
          <label class="form-label">名称</label>
          <div class="input-wrapper">
            <el-input
              v-model="subnet.name"
              placeholder="请输入 子网 名称"
              maxlength="128"
              show-word-limit
              class="name-input"
            />
          </div>
        </div>

        <!-- 网段字段 -->
        <div class="form-row">
          <label class="form-label">网段</label>
          <div class="ip-input-group">
            <el-input
              v-model="subnet.ip1"
              class="ip-segment"
              placeholder="xxx"
              @input="validateIpSegment($event, 'ip1', index)"
            />
            <span class="separator">.</span>
            <el-input
              v-model="subnet.ip2"
              class="ip-segment"
              @input="validateIpSegment($event, 'ip2', index)"
              placeholder="xx"
            />
            <span class="separator">.</span>
            <el-input
              v-model="subnet.ip3"
              class="ip-segment"
              @input="validateIpSegment($event, 'ip3', index)"
              placeholder="x"
            />
            <span class="separator">.</span>
            <el-input
              v-model="subnet.ip4"
              class="ip-segment"
              @input="validateIpSegment($event, 'ip4', index)"
              placeholder="x"
            />
            <span class="separator">/</span>
            <el-select v-model="subnet.cidr" placeholder="xx" class="cidr-select">
              <el-option v-for="cidr in cidrOptions" :key="cidr" :label="cidr" :value="cidr" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 删除按钮 -->
      <div v-if="subnets.length > 1" class="delete-btn-wrapper">
        <el-button type="danger" link @click="removeSubnet(index)" class="delete-btn">
          删除
        </el-button>
      </div>
    </div>

    <!-- 添加按钮 -->
    <div class="add-section">
      <el-button
        type="primary"
        link
        @click="addSubnet"
        :disabled="subnets.length >= maxSubnets"
        class="add-btn"
      >
        <el-icon><Plus /></el-icon>
        添加 ({{ subnets.length }} / {{ maxSubnets }})
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'

interface Subnet {
  name: string
  ip1: string
  ip2: string
  ip3: string
  ip4: string
  cidr: string
}

// Props
const props = defineProps<{
  item: any
  form: any
}>()

// 最大子网数量
const maxSubnets = props.item.maxRows || 10

// CIDR选项
const cidrOptions = ref([
  '8',
  '9',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
  '24',
  '25',
  '26',
  '27',
  '28',
  '29',
  '30',
])

// 子网列表
const subnets = ref<Subnet[]>(props.form.subnet)

// 验证IP段输入
const validateIpSegment = (value: string, field: keyof Subnet, index: number) => {
  const numValue = parseInt(value)
  if (isNaN(numValue) || numValue < 0 || numValue > 255) {
    // 如果输入无效，恢复到前一个有效值
    return
  }
  subnets.value[index][field] = value
}

// 添加子网
const addSubnet = () => {
  if (subnets.value.length < maxSubnets) {
    subnets.value.push({
      name: '',
      ip1: '',
      ip2: '',
      ip3: '',
      ip4: '',
      cidr: '',
    })
  }
}

// 删除子网
const removeSubnet = (index: number) => {
  if (subnets.value.length > 1) {
    subnets.value.splice(index, 1)
  }
}
</script>

<style scoped>
.subnet-container {
  width: 100%;
}

.subnet-item {
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  position: relative;
}

.subnet-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-label {
  font-weight: 500;
  color: #333;
  min-width: 60px;
  text-align: right;
}

.input-wrapper {
  flex: 1;
  max-width: 400px;
}

.name-input {
  width: 100%;
}

.ip-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.ip-segment {
  width: 60px;
}

.ip-segment :deep(.el-input__inner) {
  text-align: center;
}

.separator {
  font-weight: 500;
  color: #606266;
  font-size: 16px;
}

.cidr-select {
  width: 80px;
}

.delete-btn-wrapper {
  position: absolute;
  top: 12px;
  right: 12px;
}

.delete-btn {
  font-size: 12px;
  padding: 4px 8px;
}

.add-section {
  display: flex;
  justify-content: flex-start;
}

.add-btn {
  font-size: 14px;
  font-weight: 500;
}

.add-btn :deep(.el-icon) {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .form-label {
    min-width: auto;
    text-align: left;
  }

  .ip-input-group {
    flex-wrap: wrap;
  }
}
</style>
