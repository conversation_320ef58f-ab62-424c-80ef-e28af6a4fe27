<template>
  <el-drawer
    v-model="dialogVisible"
    direction="btt"
    destroy-on-close
    :before-close="handleCancel"
    :with-header="false"
    size="100%"
  >
    <div class="gpuOrMemoryLine table-box">
      <div class="drawer-header">
        <Title :title="comTitle + '利用率'" id="drawer-title" />
        <!-- //城市下拉框 -->
        <div class="city-select">
          <el-select
            v-model="cityName"
            placeholder="请选择城市"
            clearable
            filterable
            @change="(value) => initData(comType, value)"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <!-- 选择类型 -->
      <div class="card-type-select">
        <span
          v-for="item in CardTypeOptions"
          :key="item.value"
          class="card-type-item"
          :class="{ active: cardType === item.value }"
          @click="cardType = item.value"
        >
          {{ item.label }}
        </span>
      </div>
      <div class="chart-row">
        <div class="chart-container">
          <!-- // 利用率 折线图 -->
          <div ref="chartContainer" />
        </div>
        <div class="chart-container">
          <!-- 显存利用率业务TOP5排行 柱状图 -->
          <div ref="chartContainer2" />
        </div>
      </div>
      <!-- 算力列表 -->
      <div class="computing-table drawer-body-dashboard table-main">
        <div
          class="title-export"
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
          "
        >
          <Title :title="comTitle + '列表'" style="flex: 1" />
          <div class="select-input">
            <el-select
              class="select-item"
              v-model="deptName"
              placeholder="请选择归属部门"
              size="small"
              @change="loadTableData"
              clearable
              filterable
            >
              <el-option
                v-for="item in deptOptions"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
            <el-select
              class="select-item"
              v-model="businessSystemName"
              placeholder="请选择所属业务"
              size="small"
              @change="loadTableData"
              clearable
              filterable
            >
              <el-option
                v-for="item in businessOptions"
                :key="item.id"
                :label="item.name"
                :value="item.name"
                @change="loadTableData"
              />
            </el-select>
            <el-select
              class="select-item"
              v-model="subModelName"
              placeholder="请选择型号"
              size="small"
              clearable
              filterable
            >
              <el-option v-for="item in subModelOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div style="margin-right: 20px">
            <el-button @click="handleExport" type="primary" size="small"> 导出 </el-button>
          </div>
        </div>
        <!-- 表格 -->
        <el-table
          :data="tableData"
          class="power-table mt10"
          size="small"
          v-loading="loading"
          element-loading-text="加载中..."
          highlight-current-row
          ref="tableRef"
        >
          <el-table-column show-overflow-tooltip prop="datacenter" label="所属平台" width="260" />
          <el-table-column prop="deptName" label="归属部门" width="180" />
          <el-table-column show-overflow-tooltip prop="business" label="所属业务" width="180" />
          <el-table-column show-overflow-tooltip prop="subModelName" label="型号" width="180" />
          <el-table-column prop="powerUtilization" label="算力利用率（%）" width="180" />
          <el-table-column prop="memoryUtilization" label="显存利用率（%）" width="180" />
          <el-table-column prop="memorySize" label="显存大小（G）" width="180" />
          <el-table-column prop="temperature" label="温度（℃）" width="180" />
          <el-table-column prop="taskCount" label="任务数" />
          <el-table-column prop="runStatus" label="运行状态" width="180" />
          <el-table-column prop="collectStatus" label="底层采集状态" width="180" />
        </el-table>
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            layout="total,prev, pager, next, sizes, jumper"
            :page-sizes="[5, 10, 20, 30]"
            size="small"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  deviceGpuDicApi,
  exportDeviceGpuInfoApi,
  pageDeviceGpuInfoListApi,
} from '@/api/modules/zsMap'
import Title from '../Title.vue'
import { computed, ref } from 'vue'
import { useDownload } from '@/hooks/useDownload'

const comType = ref('gpu')
const cityName = ref('')
const cardType = ref('910B')
const comTitle = computed(() => {
  return comType.value === 'gpu' ? '算力' : '显存'
})

const cityOptions = [
  {
    value: '杭州市',
    label: '杭州市',
  },
  {
    value: '宁波市',
    label: '宁波市',
  },
  {
    value: '温州市',
    label: '温州市',
  },
  {
    value: '嘉兴市',
    label: '嘉兴市',
  },
  {
    value: '湖州市',
    label: '湖州市',
  },
  {
    value: '绍兴市',
    label: '绍兴市',
  },
  {
    value: '金华市',
    label: '金华市',
  },
  {
    value: '衢州市',
    label: '衢州市',
  },
  {
    value: '舟山市',
    label: '舟山市',
  },
  {
    value: '台州市',
    label: '台州市',
  },
  {
    value: '丽水市',
    label: '丽水市',
  },
]

const CardTypeOptions = [
  {
    value: '910B',
    label: '910B',
    options: ['910B2', '910B4'],
  },
  {
    value: '300I',
    label: '300I',
    options: ['300I'],
  },
  {
    value: 'T4',
    label: 'T4',
    options: ['T4'],
  },
  {
    value: 'A10',
    label: 'A10',
    options: ['A10'],
  },
  {
    value: 'A40',
    label: 'A40',
    options: ['A40'],
  },
  {
    value: 'V100',
    label: 'V100',
    options: ['V100'],
  },
]

const chartContainer = ref<HTMLDivElement | null>(null)
const chartContainer2 = ref<HTMLDivElement | null>(null)

const dialogVisible = ref(false)
const handleCancel = () => {
  dialogVisible.value = false
}

const deptOptions = ref<any[]>([])
const businessOptions = ref<any[]>([])
const subModelOptions = computed(() => {
  return CardTypeOptions.find((item) => item.value === cardType.value)?.options || []
})

const businessSystemName = ref<string>('')
const deptName = ref<string>('')
const subModelName = ref<string>('')
//下拉字典
const gitDic = async () => {
  const { entity: dept } = await deviceGpuDicApi({ type: 'dept' })
  const { entity: business } = await deviceGpuDicApi({ type: 'business' })
  deptOptions.value = dept.map((item: string) => ({ name: item, id: item }))
  businessOptions.value = business.map((item: string) => ({ item: item, id: item }))
}

const handleExport = () => {
  const params: any = {
    modelName: cardType.value,
    deptName: deptName.value,
    businessSystemName: businessSystemName.value,
  }
  useDownload(exportDeviceGpuInfoApi, `${cardType.value}_设备信息`, params, true, '.xlsx')
}

const tableData = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const loadTableData = async () => {
  loading.value = true
  try {
    const { entity } = await pageDeviceGpuInfoListApi({
      modelName: cardType.value,
      deptName: deptName.value,
      businessSystemName: businessSystemName.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    })
    tableData.value = entity
    total.value = entity.total
  } finally {
    loading.value = false
  }
}

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTableData() // 重新加载数据
}

// 处理页面变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTableData()
}

const initData = (type: string, city: string) => {
  comType.value = type
  console.log(city)

  // cityName.value = city
  dialogVisible.value = true
}

gitDic()

defineExpose({
  initData,
})
</script>

<style lang="scss" scoped>
.gpuOrMemoryLine,
.chart-row {
  width: 100%;
}

.gpuOrMemoryLine {
  background-color: #e2edfb;
}

.drawer-header {
  height: 40px;
  position: relative;

  .city-select {
    position: absolute;
    left: 300px;
    top: 50%;
    width: 200px;
    transform: translateY(-50%);
  }

  #drawer-title {
    height: 100%;
    font-size: 22px;
  }
}

.chart-row {
  display: flex;
  gap: 16px;
  width: 100%;
  height: 200px;
  justify-content: space-between;

  .chart-container {
    flex: 1;
    height: 100%;
    background: pink;
  }
}

.card-type-select {
  display: flex;
  gap: 16px;
  margin: 10px 0;
  flex-wrap: wrap;
  padding-left: 50px;
  .card-type-item {
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    background: #ffffff;
    color: #333;
  }
  .card-type-item.active {
    background: #3b7ced;
    color: #fff;
  }
}

.title-export {
  position: relative;
  .select-input {
    position: absolute;
    width: 60%;
    top: 3px;
    left: 120px;
    display: flex;
    gap: 8px;
    padding: 0 16px;
    box-sizing: border-box;
  }
  .select-item {
    width: 33%;
  }
}

.table-section {
  background: #f3f8fd;
  margin: 0 16px;
  border-radius: 8px;
  padding-bottom: 8px;
  box-shadow: none;
  border: none;
  margin-top: 8px;

  .power-table {
    height: 226px;
    border: none;
    border-radius: 6px;
    overflow: hidden;
  }
}
.drawer-body-dashboard {
  background: #e2edfb;

  .pagination-container {
    display: flex;
    justify-content: flex-start;

    background: #f3f8fd !important;
    .btn-next {
      background: #f3f8fd !important;
      color: #333;
    }
    .btn-prev {
      background: #f3f8fd !important;
      color: #333;
    }
    .el-pager {
      background: #f3f8fd !important;
      .number {
        background: #f3f8fd !important;
        border: 1px solid #e6ebf5;
        color: #333;
      }
      .more {
        background: #f3f8fd !important;
        color: #333;
      }
    }
  }

  .el-table__header {
    background-color: #cbe2ff;
  }
  .el-table__row.current-row td {
    background: #cbe2ff !important;
  }
  .el-table th {
    color: #333;
    font-weight: 500;
    border-bottom: 1px solid #e6ebf5;
    background: #cbe2ff !important;
  }
  .el-table__header-wrapper {
    background: #cbe2ff !important;
  }
  .el-table__body-wrapper {
    background: #f3f8fd !important;
  }

  .el-table td {
    padding: 12px 0;
    border-bottom: 1px solid #f0f2f7;
    background-color: #f3f8fd;
  }

  .el-table tr:hover > td {
    background-color: #e8f2ff !important;
  }

  .el-table__body tr:last-child td {
    border-bottom: none;
  }
}
</style>
