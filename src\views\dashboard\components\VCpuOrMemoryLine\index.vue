<template>
  <div class="gpuOrMemoryLine table-box-dashboard" v-show="dialogVisible">
    <div class="drawer-header">
      <Title :title="comTitle + '利用率'" id="drawer-title" />

      <!-- 关闭按钮 -->
      <el-button
        @click="handleCancel"
        type="primary"
        style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%)"
        plain
      >
        关闭
      </el-button>
    </div>
    <!-- 选择类型 -->
    <div class="info-section-dashboard">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">所属地区：</span>
          <div class="city-controls">
            <el-select
              v-model="cityName"
              placeholder="请选择城市"
              @change="(value: string) => initData(comType, value)"
              clearable
              filterable
            >
              <el-option
                v-for="item in cityOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
        <div class="info-item">
          <span class="info-label">所属平台：</span>
          <span class="info-value">{{ regionName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">卡类型：</span>
          <div class="card-type-controls">
            <el-select v-model="cardType" placeholder="请选择卡类型" filterable>
              <el-option
                v-for="item in CardTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
      </div>
    </div>

    <div class="chart-row">
      <div class="chart-container" v-loading="loadingUtilizationData">
        <!-- 时间范围检索 -->
        <div class="bottom-dialog-controls">
          <el-date-picker
            v-model="selectedDateRange"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            @change="fetchUtilizationData"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :clearable="false"
          />
        </div>
        <!-- // 利用率 折线图 -->
        <div class="container" ref="chartContainer"></div>
      </div>
      <div class="chart-container" v-loading="loadingRankingData">
        <!-- 显存利用率业务TOP5排行 柱状图 -->
        <div class="container" ref="chartContainer2" />
      </div>
    </div>
    <!-- 算力列表 -->
    <div class="table-main-dashboard mt10 gpuOrMemoryLine-table">
      <div
        class="title-export"
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12px;
          height: 32px;
        "
      >
        <Title :title="comTitle + '列表'" style="flex: 1" />
        <div class="select-input">
          <el-select
            class="select-item"
            v-model="deptName"
            placeholder="请选择归属部门"
            @change="loadTableData"
            clearable
            size="small"
            filterable
          >
            <el-option
              v-for="item in deptOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
          <el-select
            class="select-item"
            v-model="businessSystemName"
            placeholder="请选择所属业务"
            @change="loadTableData"
            clearable
            size="small"
            filterable
          >
            <el-option
              v-for="item in businessOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
              @change="loadTableData"
            />
          </el-select>
          <el-select
            class="select-item"
            v-model="subModelName"
            placeholder="请选择型号"
            clearable
            size="small"
            filterable
            @change="loadTableData"
          >
            <el-option v-for="item in subModelOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div style="margin-right: 20px">
          <el-button @click="handleExport" type="primary"> 导出 </el-button>
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-box-dashboard">
        <el-table
          :data="tableData"
          class="power-table mt10"
          v-loading="loading"
          element-loading-text="加载中..."
          highlight-current-row
          hidden-table-header
          height="calc(100vh - 436px)"
          size="small"
        >
          <el-table-column
            show-overflow-tooltip
            prop="regionName"
            label="所属平台"
            min-width="140"
          />
          <el-table-column prop="deptName" label="归属部门" min-width="120" />
          <el-table-column
            show-overflow-tooltip
            prop="businessSystemName"
            label="所属业务"
            min-width="180"
          />
          <el-table-column show-overflow-tooltip prop="subModelName" label="型号" min-width="120" />
          <el-table-column prop="gpuUtilPercent" label="算力利用率（%）" min-width="120" />
          <el-table-column prop="memUtilpercent" label="显存利用率（%）" min-width="120" />
          <el-table-column prop="memory" label="显存大小（G）" min-width="120" />
          <el-table-column prop="temperature" label="温度（℃）" min-width="120" />
          <el-table-column prop="taskNum" label="任务数" />
          <el-table-column prop="runStatus" label="运行状态" min-width="120" />
          <el-table-column prop="collectStatus" label="底层采集状态" min-width="120" />
          <!-- 暂无数据 -->
          <template #empty>
            <div class="empty-data">
              <el-empty description="暂无数据" />
            </div>
          </template>
        </el-table>
      </div>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          layout="total,prev, pager, next, sizes, jumper"
          :page-sizes="[5, 10, 20, 30]"
          size="small"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import {
  deviceGpuDicApi,
  exportDeviceGpuInfoApi,
  pageDeviceGpuInfoListApi,
  cardUtilizationApi,
  businessSystemRankingApi,
} from '@/api/modules/zsMap'
import Title from '../Title.vue'
import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useDownload } from '@/hooks/useDownload'
import * as echarts from 'echarts'
import type {
  CardUtilizationRequest,
  CardUtilizationItem,
  BusinessSystemRankingRequest,
  BusinessSystemRankingResponse,
} from '@/types/zs2map'

// ==================== 响应式数据定义 ====================
/** 当前组件类型：gpu(算力) 或 memory(显存) */
const comType = ref<'gpu' | 'memory'>('gpu')
/** 当前选择的城市名称 */
const cityName = ref<string>('')
/** 当前选择的卡类型 */
const cardType = ref<string>('910B')
/* 所属平台 */
const regionName = ref<string>('')

/** 计算属性：根据组件类型返回对应的中文标题 */
const comTitle = computed(() => {
  return comType.value === 'gpu' ? '算力' : '显存'
})

/* ==================== 时间选择相关 ==================== */
const formatDate = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
// 默认最近一月
const startDate = new Date()
startDate.setMonth(startDate.getMonth() - 1)
const endDate = new Date()
const selectedDateRange = ref<string[]>([formatDate(startDate), formatDate(endDate)])

// 最多选一个月
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// ==================== 静态配置数据 ====================
/** 城市选项配置 */
const cityOptions = [
  { value: '杭州', label: '杭州市' },
  { value: '宁波', label: '宁波市' },
  { value: '温州', label: '温州市' },
  { value: '嘉兴', label: '嘉兴市' },
  { value: '湖州', label: '湖州市' },
  { value: '绍兴', label: '绍兴市' },
  { value: '金华', label: '金华市' },
  { value: '衢州', label: '衢州市' },
  { value: '舟山', label: '舟山市' },
  { value: '台州', label: '台州市' },
  { value: '丽水', label: '丽水市' },
]

/** 卡类型选项配置 */
const CardTypeOptions = [
  { value: '910B', label: '910B', options: ['910B2', '910B4'] },
  { value: '300I', label: '300I', options: ['300I'] },
  { value: 'T4', label: 'T4', options: ['T4'] },
  { value: 'A10', label: 'A10', options: ['A10'] },
  { value: 'A40', label: 'A40', options: ['A40'] },
  { value: 'V100', label: 'V100', options: ['V100'] },
]

// ==================== 图表相关数据 ====================
/** 折线图容器DOM引用 */
const chartContainer = ref<HTMLDivElement | null>(null)
/** 柱状图容器DOM引用 */
const chartContainer2 = ref<HTMLDivElement | null>(null)
/** 折线图实例 */
let lineChart: echarts.ECharts | null = null
/** 柱状图实例 */
let barChart: echarts.ECharts | null = null

/** 利用率趋势数据 */
const utilizationData = ref<CardUtilizationItem[]>([])
/** 业务系统排行数据 */
const rankingData = ref<BusinessSystemRankingResponse>({
  memoryUtilizationRanking: [],
  computeUtilizationRanking: [],
})

// ==================== 弹窗控制 ====================
/** 弹窗显示状态 */
const dialogVisible = ref<boolean>(false)

/**
 * 关闭弹窗并清理资源
 */
const handleCancel = () => {
  dialogVisible.value = false
  // 筛选条件清空
  cityName.value = ''
  cardType.value = '910B'
  selectedDateRange.value = [formatDate(startDate), formatDate(endDate)]
  deptName.value = ''
  businessSystemName.value = ''
  subModelName.value = ''
  // 表格清空
  tableData.value = []
  // 图表清空
  cleanupCharts()
}

/**
 * 清理图表实例，防止内存泄漏
 */
const cleanupCharts = () => {
  if (lineChart) {
    lineChart.dispose()
    lineChart = null
  }
  if (barChart) {
    barChart.dispose()
    barChart = null
  }
}

// ==================== 表格筛选相关 ====================
/** 部门选项列表 */
const deptOptions = ref<Array<{ name: string; id: string }>>([])
/** 业务系统选项列表 */
const businessOptions = ref<Array<{ name: string; id: string }>>([])
/** 子型号选项列表（根据当前卡类型动态计算） */
const subModelOptions = computed(() => {
  return CardTypeOptions.find((item) => item.value === cardType.value)?.options || []
})

/** 当前选择的业务系统名称 */
const businessSystemName = ref<string>('')
/** 当前选择的部门名称 */
const deptName = ref<string>('')
/** 当前选择的子型号 */
const subModelName = ref<string>('')

/**
 * 获取下拉框字典数据
 * 包括部门列表和业务系统列表
 */
const fetchDictionaryData = async () => {
  try {
    const [deptResult, businessResult] = await Promise.all([
      deviceGpuDicApi({ type: 'dept' }),
      deviceGpuDicApi({ type: 'business' }),
    ])

    deptOptions.value = deptResult.entity?.map((item: string) => ({ name: item, id: item }))
    businessOptions.value = businessResult.entity?.map((item: string) => ({ name: item, id: item }))
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// ==================== 导出功能 ====================
/**
 * 导出设备信息到Excel文件
 */
const handleExport = () => {
  const params = {
    modelName: cardType.value,
    deptName: deptName.value,
    businessSystemName: businessSystemName.value,
    subModelName: subModelName.value,
    areaCode: cityName.value,
  }
  useDownload(exportDeviceGpuInfoApi, `${cardType.value}_设备信息`, params, true, '.xlsx')
}

// ==================== 表格数据管理 ====================
/** 表格数据 */
const tableData = ref<any[]>([])
/** 当前页码 */
const currentPage = ref<number>(1)
/** 每页显示数量 */
const pageSize = ref<number>(20)
/** 数据总数 */
const total = ref<number>(0)
/** 表格加载状态 */
const loading = ref<boolean>(false)

/**
 * 加载表格数据
 * 根据当前筛选条件和分页参数获取设备列表
 */
const loadTableData = async () => {
  loading.value = true
  try {
    const { entity } = await pageDeviceGpuInfoListApi({
      modelName: cardType.value,
      deptName: deptName.value,
      businessSystemName: businessSystemName.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      subModelName: subModelName.value,
      areaCode: cityName.value,
    })
    entity.records.forEach((item: any) => {
      item.subModelName = item.subModelName || item.modelName
    })
    tableData.value = entity.records
    total.value = +entity.total
    regionName.value = entity.records[0]?.regionName
  } catch (error) {
    console.error('加载表格数据失败:', error)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/**
 * 处理每页显示数量变化
 * @param size 新的每页显示数量
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  loadTableData()
}

/**
 * 处理页面变化
 * @param page 新的页码
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTableData()
}

// ==================== API数据获取 ====================
const loadingUtilizationData = ref<boolean>(false)
const loadingRankingData = ref<boolean>(false)
/**
 * 获取卡类型利用率趋势数据
 * 默认查询最近7天的数据，按天聚合
 */
const fetchUtilizationData = async () => {
  loadingUtilizationData.value = true
  try {
    const params: CardUtilizationRequest = {
      // 时分秒
      startTime: selectedDateRange.value[0] + ' 00:00:00',
      endTime: selectedDateRange.value[1] + ' 23:59:59',
      aggregationType: selectedDateRange.value[0] === selectedDateRange.value[1] ? 'day' : 'month',
      modelName: cardType.value,
      areaCode: cityName.value || undefined,
    }

    const { entity } = await cardUtilizationApi(params)
    utilizationData.value = entity ?? []
    loadingUtilizationData.value = false
    nextTick(() => {
      updateLineChart()
    })
  } catch (error) {
    loadingUtilizationData.value = false
    console.error('获取利用率数据失败:', error)
    utilizationData.value = []
  }
}

/**
 * 获取业务系统利用率排行数据
 * 获取TOP5排行榜数据
 */
const fetchRankingData = async () => {
  loadingRankingData.value = true
  try {
    const params: BusinessSystemRankingRequest = {
      areaCode: cityName.value || undefined,
      modelName: cardType.value,
    }

    const { entity } = await businessSystemRankingApi(params)
    rankingData.value.memoryUtilizationRanking = entity.memoryUtilizationRanking ?? []
    rankingData.value.computeUtilizationRanking = entity.computeUtilizationRanking ?? []
    loadingRankingData.value = false
    nextTick(() => {
      updateBarChart()
    })
  } catch (error) {
    loadingRankingData.value = false
    console.error('获取排行数据失败:', error)
    rankingData.value = { memoryUtilizationRanking: [], computeUtilizationRanking: [] }
  }
}

// ==================== 组件初始化 ====================
/**
 * 初始化组件数据和图表
 * @param type 组件类型：'gpu'(算力) 或 'memory'(显存)
 * @param city 城市名称
 */
const initData = (type: 'gpu' | 'memory', city: string) => {
  comType.value = type
  cityName.value = city?.split('市')[0] ?? ''
  dialogVisible.value = true

  // 延迟执行，确保DOM已渲染
  nextTick(() => {
    initCharts()
    refreshAllData()
  })
}

/**
 * 刷新所有数据
 * 包括图表数据和表格数据
 */
const refreshAllData = () => {
  fetchUtilizationData()
  fetchRankingData()
  loadTableData()
}

// ==================== 图表管理 ====================
/**
 * 初始化图表实例
 * 创建折线图和柱状图的ECharts实例
 */
const initCharts = () => {
  if (chartContainer.value && !lineChart) {
    lineChart = echarts.init(chartContainer.value, null, { renderer: 'svg' })
  }
  if (chartContainer2.value && !barChart) {
    barChart = echarts.init(chartContainer2.value, null, { renderer: 'svg' })
  }
}

/**
 * 生成折线图配置选项
 * @returns ECharts配置对象
 */
const generateLineChartOption = () => {
  if (!utilizationData.value.length) {
    return emptyOption
  }

  //开始结束时间相同 只保留时 否则只保留 月日
  const timePoints = utilizationData.value.map((item) => {
    if (selectedDateRange.value && selectedDateRange.value[0] === selectedDateRange.value[1]) {
      // 1-23
      return item.timePoint?.split(' ')[1]?.split(':')[0] + ' : 00'
    } else {
      return item.timePoint?.split(' ')[0]?.slice(5)
    }
  })
  const memoryData = utilizationData.value.map((item) => item.memoryUtilization ?? 0)
  const computeData = utilizationData.value.map((item) => item.computeUtilization ?? 0)
  const seriesData = comType.value === 'gpu' ? computeData : memoryData
  const maxValue = Math.ceil(Math.max(...seriesData) / 10) * 10
  return {
    title: {
      text: `${comTitle.value}利用率`,
      left: 'center',
      textStyle: { fontSize: 14, color: '#333' },
    },

    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 10,
      },
      formatter: function (params: any) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;z-index: 1000;">${params[0].axisValue}</div>`
        // params按照value降序排序
        params.sort((a: any, b: any) => b.value - a.value)
        params.forEach((param: any) => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
            <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${param.color}; margin-right: 8px;"></span>
            <span style="margin-left: auto; font-weight: bold;">${param.value}%</span>
          </div>`
        })
        return result
      },
    },
    legend: {
      data: comType.value === 'gpu' ? ['显存利用率'] : ['算力利用率'],
      bottom: 10,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: timePoints,
      axisLine: { lineStyle: { color: 'gray' } },
      axisLabel: { color: 'black', fontSize: 10 },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: maxValue,
      axisLine: { show: false },
      splitLine: { lineStyle: { color: '#eaf6ff' } },
      axisLabel: {
        color: 'black',
        fontSize: 10,
        formatter: '{value}%',
      },
    },
    series: [
      {
        // name: '显存利用率',
        data: seriesData,
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          color: '#ff6b6b',
          width: 2,
        },
        itemStyle: {
          color: '#ff6b6b',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: `${'#ff6b6b'}60`,
              },
              {
                offset: 1,
                color: `${'#ff6b6b'}10`,
              },
            ],
          },
        },
      },
    ],
  }
}

/**
 * 更新折线图
 * 根据最新的利用率数据更新图表显示
 */
const updateLineChart = () => {
  if (!lineChart) return

  const option = generateLineChartOption()
  lineChart.setOption(option, true) // true表示不合并，完全替换
}
const emptyOption = {
  graphic: {
    type: 'text',
    left: 'center',
    top: 'center',
    style: {
      text: '暂无数据',
      fontSize: 14,
      fill: '#999',
      textAlign: 'center',
      textVerticalAlign: 'middle',
    },
  },
  xAxis: {
    show: false,
  },
  yAxis: {
    show: false,
  },
  grid: {
    show: false,
  },
}
/**
 * 生成柱状图配置选项
 * @returns ECharts配置对象
 */
const generateBarChartOption = () => {
  if (
    (!rankingData.value.computeUtilizationRanking.length && comType.value === 'gpu') ||
    (!rankingData.value.memoryUtilizationRanking.length && comType.value === 'memory')
  ) {
    return emptyOption
  }

  const currentRanking =
    comType.value === 'gpu'
      ? rankingData.value.computeUtilizationRanking
      : rankingData.value.memoryUtilizationRanking

  const businessNames = currentRanking.map((item: any) => item.businessSystemName)
  const utilizationValues = currentRanking.map((item: any) =>
    comType.value === 'gpu' ? item.computeUtilization : item.memoryUtilization,
  )
  const maxValue = Math.ceil(Math.max(...utilizationValues) / 10) * 10
  return {
    title: {
      text: `${comTitle.value}利用率业务TOP5排行`,
      left: 'center',
      textStyle: { fontSize: 14, color: '#333' },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;z-index: 1000;">${params[0].axisValue}</div>`
        // params按照value降序排序
        params.sort((a: any, b: any) => b.value - a.value)
        params.forEach((param: any) => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
            <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${param.color}; margin-right: 8px;"></span>
            <span style="margin-left: auto; font-weight: bold;">${param.value}</span>
          </div>`
        })
        return result
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: businessNames,
      axisLabel: { interval: 0 },
    },
    yAxis: {
      type: 'value',
      // name: '利用率(%)',
      min: 0,
      max: maxValue,
      axisLabel: {
        color: 'black',
        fontSize: 10,
        formatter: '{value}%',
      },
    },
    series: [
      {
        type: 'bar',
        data: utilizationValues,
        itemStyle: { color: '#5289FF' },
        barWidth: '40%',
      },
    ],
  }
}

/**
 * 更新柱状图
 * 根据当前选择的类型（算力/显存）和排行数据更新图表
 */
const updateBarChart = () => {
  if (!barChart) return

  const option = generateBarChartOption()
  barChart.setOption(option, true) // true表示不合并，完全替换
}

// ==================== 组件生命周期和监听器 ====================
// 初始化字典数据
fetchDictionaryData()

/**
 * 监听卡类型变化
 * 当卡类型改变时，重新获取所有相关数据
 */
watch(cardType, () => {
  refreshAllData()
  // 清空实列
  subModelName.value = ''
})

/**
 * 监听城市变化
 * 当城市改变时，重新获取图表数据（不需要重新加载表格）
 */
watch(cityName, () => {
  fetchUtilizationData()
  fetchRankingData()
})

/**
 * 监听组件类型变化（算力/显存）
 * 当类型改变时，只需要更新柱状图显示
 */
watch(comType, () => {
  updateBarChart()
})

// ==================== 窗口大小监听 ====================
/**
 * 处理窗口大小变化
 * 重新调整图表尺寸以适应新的容器大小
 */
const handleResize = () => {
  if (lineChart) {
    lineChart.resize()
  }
  if (barChart) {
    barChart.resize()
  }
}

// ==================== 组件生命周期钩子 ====================
/**
 * 组件挂载时的初始化操作
 */
onMounted(() => {
  nextTick(() => {
    // 监听窗口大小变化事件
    window.addEventListener('resize', handleResize)
  })
})

/**
 * 组件卸载时的清理操作
 */
onUnmounted(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)
  // 清理图表实例，防止内存泄漏
  cleanupCharts()
})

// ==================== 组件对外暴露的方法 ====================
/**
 * 暴露给父组件的方法
 * 允许父组件调用initData方法来初始化组件
 */
defineExpose({
  initData,
})
</script>

<style lang="scss" scoped>
.gpuOrMemoryLine {
  background-color: #e2edfb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  padding: 20px;
  overflow: auto;
  box-sizing: border-box;
}

.drawer-header {
  position: relative;

  #drawer-title {
    height: 100%;
    font-size: 22px;
  }
}

.chart-row {
  display: flex;
  gap: 16px;
  width: 100%;
  height: 200px;
  justify-content: space-between;

  .chart-container {
    position: relative;
    flex: 1;
    height: 100%;
    padding-top: 10px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    .container {
      width: 100%;
      height: 100%;
    }
  }
  .bottom-dialog-controls {
    width: 300px;
    height: 30px;
    position: absolute;
    top: 10px;
    right: 20px;
    z-index: 9999;
    display: flex;
  }
}

.title-export {
  position: relative;

  .power-panel-title {
    height: 100%;
    font-size: 22px;
  }

  .select-input {
    position: absolute;
    width: 60%;
    top: 3px;
    left: 120px;
    display: flex;
    gap: 8px;
    padding: 0 16px;
    box-sizing: border-box;
  }
  .select-item {
    width: 33%;
  }
}

/* ==================== 表格样式 ==================== */

.table-box-dashboard .table-main-dashboard {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}

.gpuOrMemoryLine-table {
  //表头蓝色
  .el-table {
    --el-table-header-bg-color: #cbe2ff;
    --el-table-tr-bg-color: #fff;
    --el-table-tr-hover-bg-color: #fff;
    --el-table-tr-active-bg-color: #fff;
    --el-table-border-color: #fff;
    --el-table-header-border-color: #fff;
    --el-table-row-hover-bg-color: #fff;
    --el-table-row-active-bg-color: #fff;
    --el-table-row-hover-color: #333;
    --el-table-row-active-color: #333;
    --el-table-header-color: #333;
    --el-table-header-font-weight: 500;
    --el-table-header-height: 45px;
    --el-table-header-padding: 0 0;
    --el-table-header-font-size: 14px;
    --el-table-header-text-color: #333;
    --el-table-header-text-align: left;
  }
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    background: #f3f8fd !important;
  }
}

// ==================== 信息行样式 ====================
.info-section-dashboard {
  margin: 0 16px 0px;
  border-radius: 8px;
  padding: 16px 0px;
  box-shadow: none;
  border: none;

  .info-row {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;

    .info-item {
      display: flex;
      align-items: center;

      .info-label {
        color: #666;
        font-size: 14px;
        margin-right: 8px;
      }

      .info-value {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }

      .time-controls {
        width: 400px;
        display: flex;
        align-items: center;
        gap: 8px;

        .time-range {
          color: #333;
          font-size: 14px;
          padding: 4px 8px;
          background: #f5f5f5;
          border-radius: 4px;
          border: 1px solid #e0e0e0;
        }
      }
    }
  }
}
</style>
