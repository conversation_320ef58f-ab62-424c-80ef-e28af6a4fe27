<template>
  <div class="gpuOrMemoryLine table-box-dashboard" v-show="dialogVisible">
    <div class="drawer-header">
      <Title :title="comTitle + '利用率'" id="drawer-title" />
      <!-- //城市下拉框 -->
      <div class="city-select">
        <el-select
          v-model="cityName"
          placeholder="请选择城市"
          clearable
          filterable
          @change="(value: string) => initData(comType, value)"
        >
          <el-option
            v-for="item in cityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>

      <!-- 关闭按钮 -->
      <el-button
        @click="handleCancel"
        type="primary"
        style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%)"
        plain
      >
        关闭
      </el-button>
    </div>
    <!-- 选择类型 -->
    <div class="card-type-select">
      <span
        v-for="item in CardTypeOptions"
        :key="item.value"
        class="card-type-item"
        :class="{ active: cardType === item.value }"
        @click="cardType = item.value"
      >
        {{ item.label }}
      </span>
    </div>

    <div class="chart-row">
      <div class="chart-container" v-loading="loadingUtilizationData">
        <!-- 时间范围检索 -->
        <div class="bottom-dialog-controls">
          <el-date-picker
            v-model="selectedDateRange"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            @change="fetchUtilizationData"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :clearable="false"
          />
        </div>
        <!-- // 利用率 折线图 -->
        <div class="container" ref="chartContainer"></div>
        <!-- 暂无数据 -->
        <div v-if="!utilizationData.length" class="empty-data">
          <el-empty description="暂无数据" />
        </div>
      </div>
      <div class="chart-container" v-loading="loadingRankingData">
        <!-- 显存利用率业务TOP5排行 柱状图 -->
        <div class="container" ref="chartContainer2" />
        <!-- 暂无数据 -->
        <div
          v-if="
            (!rankingData.computeUtilizationRanking?.length && comType === 'gpu') ||
            (!rankingData.memoryUtilizationRanking?.length && comType === 'memory')
          "
          class="empty-data"
        >
          <el-empty description="暂无数据" />
        </div>
      </div>
    </div>
    <!-- 算力列表 -->
    <div class="table-main-dashboard mt10 gpuOrMemoryLine-table">
      <div
        class="title-export"
        style="display: flex; justify-content: space-between; align-items: center; margin-top: 12px"
      >
        <Title :title="comTitle + '列表'" style="flex: 1" />
        <div class="select-input">
          <el-select
            class="select-item"
            v-model="deptName"
            placeholder="请选择归属部门"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in deptOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
          <el-select
            class="select-item"
            v-model="businessSystemName"
            placeholder="请选择所属业务"
            size="small"
            @change="loadTableData"
            clearable
            filterable
          >
            <el-option
              v-for="item in businessOptions"
              :key="item.id"
              :label="item.name"
              :value="item.name"
              @change="loadTableData"
            />
          </el-select>
          <el-select
            class="select-item"
            v-model="subModelName"
            placeholder="请选择型号"
            size="small"
            clearable
            filterable
            @change="loadTableData"
          >
            <el-option v-for="item in subModelOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div style="margin-right: 20px">
          <el-button @click="handleExport" type="primary" size="small"> 导出 </el-button>
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-box-dashboard">
        <el-table
          :data="tableData"
          class="power-table mt10"
          size="small"
          v-loading="loading"
          element-loading-text="加载中..."
          highlight-current-row
          hidden-table-header
          height="calc(100vh - 600px)"
        >
          <el-table-column show-overflow-tooltip prop="datacenter" label="所属平台" width="260" />
          <el-table-column prop="deptName" label="归属部门" width="180" />
          <el-table-column show-overflow-tooltip prop="business" label="所属业务" width="180" />
          <el-table-column show-overflow-tooltip prop="subModelName" label="型号" width="180" />
          <el-table-column prop="powerUtilization" label="算力利用率（%）" width="180" />
          <el-table-column prop="memoryUtilization" label="显存利用率（%）" width="180" />
          <el-table-column prop="memorySize" label="显存大小（G）" width="180" />
          <el-table-column prop="temperature" label="温度（℃）" width="180" />
          <el-table-column prop="taskCount" label="任务数" />
          <el-table-column prop="runStatus" label="运行状态" width="180" />
          <el-table-column prop="collectStatus" label="底层采集状态" width="180" />
          <!-- 暂无数据 -->
          <template #empty>
            <div class="empty-data">
              <el-empty description="暂无数据" />
            </div>
          </template>
        </el-table>
      </div>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          layout="total,prev, pager, next, sizes, jumper"
          :page-sizes="[5, 10, 20, 30]"
          size="small"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import {
  deviceGpuDicApi,
  exportDeviceGpuInfoApi,
  pageDeviceGpuInfoListApi,
  cardUtilizationApi,
  businessSystemRankingApi,
} from '@/api/modules/zsMap'
import Title from '../Title.vue'
import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useDownload } from '@/hooks/useDownload'
import * as echarts from 'echarts'
import type {
  CardUtilizationRequest,
  CardUtilizationItem,
  BusinessSystemRankingRequest,
  BusinessSystemRankingResponse,
} from '@/types/zs2map'

// ==================== 响应式数据定义 ====================
/** 当前组件类型：gpu(算力) 或 memory(显存) */
const comType = ref<'gpu' | 'memory'>('gpu')
/** 当前选择的城市名称 */
const cityName = ref<string>('')
/** 当前选择的卡类型 */
const cardType = ref<string>('910B')

/** 计算属性：根据组件类型返回对应的中文标题 */
const comTitle = computed(() => {
  return comType.value === 'gpu' ? '算力' : '显存'
})

/* ==================== 时间选择相关 ==================== */
const formatDate = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
// 默认最近一月
const startDate = new Date()
startDate.setMonth(startDate.getMonth() - 1)
const endDate = new Date()
const selectedDateRange = ref<string[]>([formatDate(startDate), formatDate(endDate)])

// 最多选一个月
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// ==================== 静态配置数据 ====================
/** 城市选项配置 */
const cityOptions = [
  { value: '杭州市', label: '杭州市' },
  { value: '宁波市', label: '宁波市' },
  { value: '温州市', label: '温州市' },
  { value: '嘉兴市', label: '嘉兴市' },
  { value: '湖州市', label: '湖州市' },
  { value: '绍兴市', label: '绍兴市' },
  { value: '金华市', label: '金华市' },
  { value: '衢州市', label: '衢州市' },
  { value: '舟山市', label: '舟山市' },
  { value: '台州市', label: '台州市' },
  { value: '丽水市', label: '丽水市' },
]

/** 卡类型选项配置 */
const CardTypeOptions = [
  { value: '910B', label: '910B', options: ['910B2', '910B4'] },
  { value: '300I', label: '300I', options: ['300I'] },
  { value: 'T4', label: 'T4', options: ['T4'] },
  { value: 'A10', label: 'A10', options: ['A10'] },
  { value: 'A40', label: 'A40', options: ['A40'] },
  { value: 'V100', label: 'V100', options: ['V100'] },
]

// ==================== 图表相关数据 ====================
/** 折线图容器DOM引用 */
const chartContainer = ref<HTMLDivElement | null>(null)
/** 柱状图容器DOM引用 */
const chartContainer2 = ref<HTMLDivElement | null>(null)
/** 折线图实例 */
const lineChart = ref<echarts.ECharts | null>(null)
/** 柱状图实例 */
const barChart = ref<echarts.ECharts | null>(null)

/** 利用率趋势数据 */
const utilizationData = ref<CardUtilizationItem[]>([])
/** 业务系统排行数据 */
const rankingData = ref<BusinessSystemRankingResponse>({
  memoryUtilizationRanking: [],
  computeUtilizationRanking: [],
})

// ==================== 弹窗控制 ====================
/** 弹窗显示状态 */
const dialogVisible = ref<boolean>(false)

/**
 * 关闭弹窗并清理资源
 */
const handleCancel = () => {
  dialogVisible.value = false
  cleanupCharts()
}

/**
 * 清理图表实例，防止内存泄漏
 */
const cleanupCharts = () => {
  if (lineChart.value) {
    lineChart.value.dispose()
    lineChart.value = null
  }
  if (barChart.value) {
    barChart.value.dispose()
    barChart.value = null
  }
}

// ==================== 表格筛选相关 ====================
/** 部门选项列表 */
const deptOptions = ref<Array<{ name: string; id: string }>>([])
/** 业务系统选项列表 */
const businessOptions = ref<Array<{ name: string; id: string }>>([])
/** 子型号选项列表（根据当前卡类型动态计算） */
const subModelOptions = computed(() => {
  return CardTypeOptions.find((item) => item.value === cardType.value)?.options || []
})

/** 当前选择的业务系统名称 */
const businessSystemName = ref<string>('')
/** 当前选择的部门名称 */
const deptName = ref<string>('')
/** 当前选择的子型号 */
const subModelName = ref<string>('')

/**
 * 获取下拉框字典数据
 * 包括部门列表和业务系统列表
 */
const fetchDictionaryData = async () => {
  try {
    const [deptResult, businessResult] = await Promise.all([
      deviceGpuDicApi({ type: 'dept' }),
      deviceGpuDicApi({ type: 'business' }),
    ])

    deptOptions.value = deptResult.entity.map((item: string) => ({ name: item, id: item }))
    businessOptions.value = businessResult.entity.map((item: string) => ({ name: item, id: item }))
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// ==================== 导出功能 ====================
/**
 * 导出设备信息到Excel文件
 */
const handleExport = () => {
  const params = {
    modelName: cardType.value,
    deptName: deptName.value,
    businessSystemName: businessSystemName.value,
    subModelName: subModelName.value,
    areaCode: cityName.value,
  }
  useDownload(exportDeviceGpuInfoApi, `${cardType.value}_设备信息`, params, true, '.xlsx')
}

// ==================== 表格数据管理 ====================
/** 表格数据 */
const tableData = ref<any[]>([])
/** 当前页码 */
const currentPage = ref<number>(1)
/** 每页显示数量 */
const pageSize = ref<number>(10)
/** 数据总数 */
const total = ref<number>(0)
/** 表格加载状态 */
const loading = ref<boolean>(false)

/**
 * 加载表格数据
 * 根据当前筛选条件和分页参数获取设备列表
 */
const loadTableData = async () => {
  loading.value = true
  try {
    const { entity } = await pageDeviceGpuInfoListApi({
      modelName: cardType.value,
      deptName: deptName.value,
      businessSystemName: businessSystemName.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      subModelName: subModelName.value,
      areaCode: cityName.value,
    })
    tableData.value = entity.records
    total.value = +entity.total
  } catch (error) {
    console.error('加载表格数据失败:', error)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/**
 * 处理每页显示数量变化
 * @param size 新的每页显示数量
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  loadTableData()
}

/**
 * 处理页面变化
 * @param page 新的页码
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTableData()
}

// ==================== API数据获取 ====================
const loadingUtilizationData = ref<boolean>(false)
const loadingRankingData = ref<boolean>(false)
/**
 * 获取卡类型利用率趋势数据
 * 默认查询最近7天的数据，按天聚合
 */
const fetchUtilizationData = async () => {
  loadingUtilizationData.value = true
  try {
    const params: CardUtilizationRequest = {
      // 时分秒
      startTime: selectedDateRange.value[0] + ' 00:00:00',
      endTime: selectedDateRange.value[1] + ' 23:59:59',
      aggregationType: selectedDateRange.value[0] === selectedDateRange.value[1] ? 'day' : 'month',
      modelName: cardType.value,
      areaCode: cityName.value || undefined,
    }

    const { entity } = await cardUtilizationApi(params)
    utilizationData.value = entity ?? []
    loadingUtilizationData.value = false
    nextTick(() => {
      updateLineChart()
    })
  } catch (error) {
    loadingUtilizationData.value = false
    console.error('获取利用率数据失败:', error)
    utilizationData.value = []
  }
}

/**
 * 获取业务系统利用率排行数据
 * 获取TOP5排行榜数据
 */
const fetchRankingData = async () => {
  loadingRankingData.value = true
  try {
    const params: BusinessSystemRankingRequest = {
      areaCode: cityName.value || undefined,
      modelName: cardType.value,
    }

    const { entity } = await businessSystemRankingApi(params)
    rankingData.value.memoryUtilizationRanking = entity.memoryUtilizationRanking ?? []
    rankingData.value.computeUtilizationRanking = entity.computeUtilizationRanking ?? []
    loadingRankingData.value = false
    nextTick(() => {
      updateBarChart()
    })
  } catch (error) {
    loadingRankingData.value = false
    console.error('获取排行数据失败:', error)
    rankingData.value = { memoryUtilizationRanking: [], computeUtilizationRanking: [] }
  }
}

// ==================== 组件初始化 ====================
/**
 * 初始化组件数据和图表
 * @param type 组件类型：'gpu'(算力) 或 'memory'(显存)
 * @param city 城市名称
 */
const initData = (type: 'gpu' | 'memory', city: string) => {
  comType.value = type
  cityName.value = city
  dialogVisible.value = true

  // 延迟执行，确保DOM已渲染和弹窗动画完成
  nextTick(() => {
    setTimeout(() => {
      initCharts()
      refreshAllData()
      // 强制刷新图表尺寸，确保正确显示
      forceResizeCharts()
    }, 300) // 等待弹窗动画完成
  })
}

/**
 * 刷新所有数据
 * 包括图表数据和表格数据
 */
const refreshAllData = () => {
  fetchUtilizationData()
  fetchRankingData()
  loadTableData()
}

// ==================== 图表管理 ====================
/**
 * 初始化图表实例
 * 创建折线图和柱状图的ECharts实例
 */
const initCharts = () => {
  if (chartContainer.value && !lineChart.value) {
    lineChart.value = echarts.init(chartContainer.value, null, {
      renderer: 'svg',
      width: 'auto',
      height: 'auto',
    })

    // 监听图表容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      if (lineChart.value) {
        lineChart.value.resize()
      }
    })
    resizeObserver.observe(chartContainer.value)
  }

  if (chartContainer2.value && !barChart.value) {
    barChart.value = echarts.init(chartContainer2.value, null, {
      renderer: 'svg',
      width: 'auto',
      height: 'auto',
    })

    // 监听图表容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      if (barChart.value) {
        barChart.value.resize()
      }
    })
    resizeObserver.observe(chartContainer2.value)
  }
}

/**
 * 生成空数据状态的图表配置
 * @param title 图表标题
 * @returns 空数据状态的ECharts配置对象
 */
const generateEmptyChartOption = (title: string) => {
  return {
    title: {
      text: title,
      left: 'center',
      textStyle: { fontSize: 14, color: '#333' },
    },
    graphic: {
      type: 'group',
      left: 'center',
      top: 'center',
      children: [
        {
          type: 'image',
          style: {
            image:
              'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMyIDU2QzQ1LjI1NDggNTYgNTYgNDUuMjU0OCA1NiAzMkM1NiAxOC43NDUyIDQ1LjI1NDggOCAzMiA4QzE4Ljc0NTIgOCA4IDE4Ljc0NTIgOCAzMkM4IDQ1LjI1NDggMTguNzQ1MiA1NiAzMiA1NloiIGZpbGw9IiNGNUY1RjUiLz4KPHBhdGggZD0iTTMyIDQ4QzQwLjgzNjYgNDggNDggNDAuODM2NiA0OCAzMkM0OCAyMy4xNjM0IDQwLjgzNjYgMTYgMzIgMTZDMjMuMTYzNCAxNiAxNiAyMy4xNjM0IDE2IDMyQzE2IDQwLjgzNjYgMjMuMTYzNCA0OCAzMiA0OFoiIGZpbGw9IiNFNUU1RTUiLz4KPHBhdGggZD0iTTI4IDI4SDM2VjM2SDI4VjI4WiIgZmlsbD0iI0Q1RDVENSIvPgo8L3N2Zz4K',
            width: 64,
            height: 64,
          },
        },
        {
          type: 'text',
          style: {
            text: '暂无数据',
            textAlign: 'center',
            fill: '#999',
            fontSize: 14,
            fontWeight: 'normal',
          },
          top: 80,
        },
      ],
    },
    xAxis: {
      show: false,
    },
    yAxis: {
      show: false,
    },
    grid: {
      show: false,
    },
  }
}

/**
 * 生成折线图配置选项
 * @returns ECharts配置对象
 */
const generateLineChartOption = () => {
  // 检查是否有数据
  if (!utilizationData.value || utilizationData.value.length === 0) {
    return generateEmptyChartOption(`${comTitle.value} 利用率`)
  }

  //开始结束时间相同 只保留时 否则只保留 月日
  const timePoints = utilizationData.value.map((item) => {
    if (selectedDateRange.value && selectedDateRange.value[0] === selectedDateRange.value[1]) {
      // 1-23
      return item.timePoint.split(' ')[1].split(':')[0] + ' : 00'
    } else {
      return item.timePoint.split(' ')[0].slice(5)
    }
  })
  const memoryData = utilizationData.value.map((item) => item.memoryUtilization ?? 0)
  const computeData = utilizationData.value.map((item) => item.computeUtilization ?? 0)
  const seriesData = comType.value === 'gpu' ? computeData : memoryData
  const maxValue = Math.max(Math.ceil(Math.max(...seriesData) / 10) * 10, 10) // 最小值为10

  return {
    title: {
      text: `${comTitle.value} 利用率`,
      left: 'center',
      textStyle: { fontSize: 14, color: '#333' },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 10,
      },
      formatter: (params: any) => {
        console.log(params, 'params')

        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}%<br/>`
        })
        return result
      },
    },
    legend: {
      data: comType.value === 'gpu' ? ['算力利用率'] : ['显存利用率'],
      bottom: 10,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: timePoints,
      axisLabel: { rotate: 45 },
      axisLine: { lineStyle: { color: 'gray' } },
    },
    yAxis: {
      type: 'value',
      name: '利用率(%)',
      min: 0,
      max: maxValue,
      axisLine: { show: false },
      splitLine: { lineStyle: { color: '#eaf6ff' } },
      axisLabel: {
        color: 'black',
        fontSize: 10,
        formatter: '{value}%',
      },
    },
    series: [
      {
        // name: comType.value === 'gpu' ? '算力利用率' : '显存利用率',
        data: seriesData,
        itemStyle: { color: '#5470c6' },
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 4,
      },
    ],
  }
}

/**
 * 更新折线图
 * 根据最新的利用率数据更新图表显示，支持空数据状态
 */
const updateLineChart = () => {
  if (!lineChart.value) return

  const option = generateLineChartOption()
  lineChart.value.setOption(option, true) // true表示不合并，完全替换

  // 确保图表能正确响应容器大小变化
  nextTick(() => {
    if (lineChart.value) {
      lineChart.value.resize()
    }
  })
}

/**
 * 生成柱状图配置选项
 * @returns ECharts配置对象
 */
const generateBarChartOption = () => {
  const currentRanking =
    comType.value === 'gpu'
      ? rankingData.value.computeUtilizationRanking
      : rankingData.value.memoryUtilizationRanking

  // 检查是否有数据
  if (!currentRanking || currentRanking.length === 0) {
    return generateEmptyChartOption(`${comTitle.value}利用率业务TOP5排行`)
  }

  const businessNames = currentRanking.map((item: any) => item.businessSystemName)
  const utilizationValues = currentRanking.map((item: any) =>
    comType.value === 'gpu' ? item.computeUtilization : item.memoryUtilization,
  )
  const maxValue = Math.max(Math.ceil(Math.max(...utilizationValues) / 10) * 10, 10) // 最小值为10

  return {
    title: {
      text: `${comTitle.value}利用率业务TOP5排行`,
      left: 'center',
      textStyle: { fontSize: 14, color: '#333' },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 10,
      },
      formatter: (params: any) => {
        console.log();
        
        const data = currentRanking[params[0].dataIndex]
        return `
          业务系统: ${data.businessSystemName}<br/>
          ${comTitle.value}利用率: ${comType.value === 'gpu' ? data.computeUtilization : data.memoryUtilization}%<br/>
          任务数: ${data.taskNum}<br/>
          设备数量: ${data.deviceCount}<br/>
          部门: ${data.deptName}
        `
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: businessNames,
      axisLabel: { interval: 0 },
    },
    yAxis: {
      type: 'value',
      name: '利用率(%)',
      min: 0,
      max: maxValue,
      axisLine: { show: false },
      splitLine: { lineStyle: { color: '#eaf6ff' } },
      axisLabel: {
        color: 'black',
        fontSize: 10,
        formatter: '{value}%',
      },
    },
    series: [
      {
        type: 'bar',
        data: utilizationValues,
        itemStyle: { color: '#5470c6' },
        barWidth: '60%',
      },
    ],
  }
}

/**
 * 更新柱状图
 * 根据当前选择的类型（算力/显存）和排行数据更新图表，支持空数据状态
 */
const updateBarChart = () => {
  if (!barChart.value) return

  const option = generateBarChartOption()
  barChart.value.setOption(option, true) // true表示不合并，完全替换

  // 确保图表能正确响应容器大小变化
  nextTick(() => {
    if (barChart.value) {
      barChart.value.resize()
    }
  })
}

// ==================== 组件生命周期和监听器 ====================
// 初始化字典数据
fetchDictionaryData()

/**
 * 监听卡类型变化
 * 当卡类型改变时，重新获取所有相关数据
 */
watch(cardType, () => {
  refreshAllData()
})

/**
 * 监听城市变化
 * 当城市改变时，重新获取图表数据（不需要重新加载表格）
 */
watch(cityName, () => {
  fetchUtilizationData()
  fetchRankingData()
})

/**
 * 监听组件类型变化（算力/显存）
 * 当类型改变时，只需要更新柱状图显示
 */
watch(comType, () => {
  updateBarChart()
})

// ==================== 窗口大小监听 ====================
/**
 * 处理窗口大小变化
 * 重新调整图表尺寸以适应新的容器大小
 */
const handleResize = () => {
  // 使用防抖处理，避免频繁调用
  clearTimeout(resizeTimer.value)
  resizeTimer.value = setTimeout(() => {
    if (lineChart.value) {
      lineChart.value.resize()
    }
    if (barChart.value) {
      barChart.value.resize()
    }
  }, 100)
}

/** 防抖定时器 */
const resizeTimer = ref<number | undefined>(undefined)

/**
 * 强制刷新图表尺寸
 * 用于解决某些情况下图表尺寸不正确的问题
 */
const forceResizeCharts = () => {
  nextTick(() => {
    setTimeout(() => {
      if (lineChart.value) {
        lineChart.value.resize()
      }
      if (barChart.value) {
        barChart.value.resize()
      }
    }, 200)
  })
}

// ==================== 组件生命周期钩子 ====================
/**
 * 组件挂载时的初始化操作
 */
onMounted(() => {
  nextTick(() => {
    // 监听窗口大小变化事件
    window.addEventListener('resize', handleResize)
    // 初始化时强制刷新一次图表尺寸
    forceResizeCharts()
  })
})

/**
 * 组件卸载时的清理操作
 */
onUnmounted(() => {
  // 清理防抖定时器
  if (resizeTimer.value) {
    clearTimeout(resizeTimer.value)
  }
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)
  // 清理图表实例，防止内存泄漏
  cleanupCharts()
})

// ==================== 组件对外暴露的方法 ====================
/**
 * 暴露给父组件的方法
 * 允许父组件调用initData方法来初始化组件
 */
defineExpose({
  initData,
})
</script>

<style lang="scss" scoped>
.gpuOrMemoryLine {
  background-color: #e2edfb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  padding: 20px;
  overflow: auto;
  box-sizing: border-box;
}

.drawer-header {
  height: 40px;
  position: relative;

  .city-select {
    position: absolute;
    left: 300px;
    top: 50%;
    width: 200px;
    transform: translateY(-50%);
  }

  #drawer-title {
    height: 100%;
    font-size: 22px;
  }
}

.chart-row {
  display: flex;
  gap: 16px;
  width: 100%;
  height: 350px;
  justify-content: space-between;

  .chart-container {
    position: relative;
    flex: 1;
    height: 100%;
    padding-top: 10px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    .container {
      width: 100%;
      height: 100%;
    }
  }
  .bottom-dialog-controls {
    width: 300px;
    height: 30px;
    position: absolute;
    top: 10px;
    right: 20px;
    z-index: 9999;
    display: flex;
  }
}

/* ==================== 卡类型选择器样式 ==================== */
.card-type-select {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  padding: 5px 24px;
  backdrop-filter: blur(10px);
  margin-top: 10px;
  .card-type-item {
    cursor: pointer;
    padding: 10px 20px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #4a5568;
    font-weight: 500;
    border: 2px solid transparent;
    border-color: #eee;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 124, 237, 0.15);
      border-color: rgba(59, 124, 237, 0.3);

      &::before {
        left: 100%;
      }
    }

    &.active {
      background: linear-gradient(135deg, #3b7ced 0%, #4f8ef7 100%);
      color: #fff;
      border-color: #3b7ced;
      box-shadow: 0 6px 20px rgba(59, 124, 237, 0.3);
      transform: translateY(-1px);
    }
  }
}

.title-export {
  position: relative;
  .select-input {
    position: absolute;
    width: 60%;
    top: 3px;
    left: 120px;
    display: flex;
    gap: 8px;
    padding: 0 16px;
    box-sizing: border-box;
  }
  .select-item {
    width: 33%;
  }
}

/* ==================== 表格样式 ==================== */

.table-box-dashboard .table-main-dashboard {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}

.gpuOrMemoryLine-table {
  //表头蓝色
  .el-table {
    --el-table-header-bg-color: #cbe2ff;
    --el-table-tr-bg-color: #fff;
    --el-table-tr-hover-bg-color: #fff;
    --el-table-tr-active-bg-color: #fff;
    --el-table-border-color: #fff;
    --el-table-header-border-color: #fff;
    --el-table-row-hover-bg-color: #fff;
    --el-table-row-active-bg-color: #fff;
    --el-table-row-hover-color: #333;
    --el-table-row-active-color: #333;
    --el-table-header-color: #333;
    --el-table-header-font-weight: 500;
    --el-table-header-height: 45px;
    --el-table-header-padding: 0 0;
    --el-table-header-font-size: 14px;
    --el-table-header-text-color: #333;
    --el-table-header-text-align: left;
  }
  .pagination-container {
    display: flex;
    justify-content: flex-start;
    padding: 10px;
    background: #f3f8fd !important;
  }
}

/* ==================== 空数据状态样式 ==================== */
:deep(.echarts-empty-state) {
  .empty-icon {
    opacity: 0.6;
    transition: opacity 0.3s ease;
  }

  .empty-text {
    color: #999;
    font-size: 14px;
    margin-top: 12px;
  }
}

/* ==================== 图表容器响应式优化 ==================== */
.chart-container {
  position: relative;
}

/* ==================== 图表加载状态 ==================== */
.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 14px;

  &::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #e1e8f0;
    border-top-color: #3b7ced;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
