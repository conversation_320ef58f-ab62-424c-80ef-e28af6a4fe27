<template>
  <el-drawer
    v-model="dialogVisible"
    direction="btt"
    destroy-on-close
    :before-close="handleCancel"
    :with-header="false"
    size="100%"
  >
    <div class="gpuOrMemoryLine table-box">
      <div class="drawer-header">
        <Title :title="comTitle + '利用率'" id="drawer-title" />
        <!-- //城市下拉框 -->
        <div class="city-select">
          <el-select
            v-model="cityName"
            placeholder="请选择城市"
            clearable
            filterable
            @change="(value: string) => initData(comType, value)"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <!-- 选择类型 -->
      <div class="card-type-select">
        <span
          v-for="item in CardTypeOptions"
          :key="item.value"
          class="card-type-item"
          :class="{ active: cardType === item.value }"
          @click="cardType = item.value"
        >
          {{ item.label }}
        </span>
      </div>
      <div class="chart-row">
        <div class="chart-container">
          <!-- // 利用率 折线图 -->
          <div ref="chartContainer" />
        </div>
        <div class="chart-container">
          <!-- 显存利用率业务TOP5排行 柱状图 -->
          <div ref="chartContainer2" />
        </div>
      </div>
      <!-- 算力列表 -->
      <div class="computing-table drawer-body-dashboard table-main">
        <div
          class="title-export"
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
          "
        >
          <Title :title="comTitle + '列表'" style="flex: 1" />
          <div class="select-input">
            <el-select
              class="select-item"
              v-model="deptName"
              placeholder="请选择归属部门"
              size="small"
              @change="loadTableData"
              clearable
              filterable
            >
              <el-option
                v-for="item in deptOptions"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
            <el-select
              class="select-item"
              v-model="businessSystemName"
              placeholder="请选择所属业务"
              size="small"
              @change="loadTableData"
              clearable
              filterable
            >
              <el-option
                v-for="item in businessOptions"
                :key="item.id"
                :label="item.name"
                :value="item.name"
                @change="loadTableData"
              />
            </el-select>
            <el-select
              class="select-item"
              v-model="subModelName"
              placeholder="请选择型号"
              size="small"
              clearable
              filterable
            >
              <el-option v-for="item in subModelOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div style="margin-right: 20px">
            <el-button @click="handleExport" type="primary" size="small"> 导出 </el-button>
          </div>
        </div>
        <!-- 表格 -->
        <div class="table-box">
          <el-table
            :data="tableData"
            class="power-table mt10"
            size="small"
            v-loading="loading"
            element-loading-text="加载中..."
          >
            <el-table-column show-overflow-tooltip prop="datacenter" label="所属平台" width="260" />
            <el-table-column prop="deptName" label="归属部门" width="180" />
            <el-table-column show-overflow-tooltip prop="business" label="所属业务" width="180" />
            <el-table-column show-overflow-tooltip prop="subModelName" label="型号" width="180" />
            <el-table-column prop="powerUtilization" label="算力利用率（%）" width="180" />
            <el-table-column prop="memoryUtilization" label="显存利用率（%）" width="180" />
            <el-table-column prop="memorySize" label="显存大小（G）" width="180" />
            <el-table-column prop="temperature" label="温度（℃）" width="180" />
            <el-table-column prop="taskCount" label="任务数" />
            <el-table-column prop="runStatus" label="运行状态" width="180" />
            <el-table-column prop="collectStatus" label="底层采集状态" width="180" />
          </el-table>
        </div>
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            layout="total,prev, pager, next, sizes, jumper"
            :page-sizes="[5, 10, 20, 30]"
            size="small"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import {
  deviceGpuDicApi,
  exportDeviceGpuInfoApi,
  pageDeviceGpuInfoListApi,
  cardUtilizationApi,
  businessSystemRankingApi,
} from '@/api/modules/zsMap'
import Title from '../Title.vue'
import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useDownload } from '@/hooks/useDownload'
import * as echarts from 'echarts'
import type {
  CardUtilizationRequest,
  CardUtilizationItem,
  BusinessSystemRankingRequest,
  BusinessSystemRankingResponse,
} from '@/types/zs2map'

// ==================== 响应式数据定义 ====================
/** 当前组件类型：gpu(算力) 或 memory(显存) */
const comType = ref<'gpu' | 'memory'>('gpu')
/** 当前选择的城市名称 */
const cityName = ref<string>('')
/** 当前选择的卡类型 */
const cardType = ref<string>('910B')

/** 计算属性：根据组件类型返回对应的中文标题 */
const comTitle = computed(() => {
  return comType.value === 'gpu' ? '算力' : '显存'
})

// ==================== 静态配置数据 ====================
/** 城市选项配置 */
const cityOptions = [
  { value: '杭州市', label: '杭州市' },
  { value: '宁波市', label: '宁波市' },
  { value: '温州市', label: '温州市' },
  { value: '嘉兴市', label: '嘉兴市' },
  { value: '湖州市', label: '湖州市' },
  { value: '绍兴市', label: '绍兴市' },
  { value: '金华市', label: '金华市' },
  { value: '衢州市', label: '衢州市' },
  { value: '舟山市', label: '舟山市' },
  { value: '台州市', label: '台州市' },
  { value: '丽水市', label: '丽水市' },
]

/** 卡类型选项配置 */
const CardTypeOptions = [
  { value: '910B', label: '910B', options: ['910B2', '910B4'] },
  { value: '300I', label: '300I', options: ['300I'] },
  { value: 'T4', label: 'T4', options: ['T4'] },
  { value: 'A10', label: 'A10', options: ['A10'] },
  { value: 'A40', label: 'A40', options: ['A40'] },
  { value: 'V100', label: 'V100', options: ['V100'] },
]

// ==================== 图表相关数据 ====================
/** 折线图容器DOM引用 */
const chartContainer = ref<HTMLDivElement | null>(null)
/** 柱状图容器DOM引用 */
const chartContainer2 = ref<HTMLDivElement | null>(null)
/** 折线图实例 */
const lineChart = ref<echarts.ECharts | null>(null)
/** 柱状图实例 */
const barChart = ref<echarts.ECharts | null>(null)

/** 利用率趋势数据 */
const utilizationData = ref<CardUtilizationItem[]>([])
/** 业务系统排行数据 */
const rankingData = ref<BusinessSystemRankingResponse>({
  memoryUtilizationRanking: [],
  computeUtilizationRanking: [],
})

// ==================== 弹窗控制 ====================
/** 弹窗显示状态 */
const dialogVisible = ref<boolean>(false)

/**
 * 关闭弹窗并清理资源
 */
const handleCancel = () => {
  dialogVisible.value = false
  cleanupCharts()
}

/**
 * 清理图表实例，防止内存泄漏
 */
const cleanupCharts = () => {
  if (lineChart.value) {
    lineChart.value.dispose()
    lineChart.value = null
  }
  if (barChart.value) {
    barChart.value.dispose()
    barChart.value = null
  }
}

// ==================== 表格筛选相关 ====================
/** 部门选项列表 */
const deptOptions = ref<Array<{ name: string; id: string }>>([])
/** 业务系统选项列表 */
const businessOptions = ref<Array<{ name: string; id: string }>>([])
/** 子型号选项列表（根据当前卡类型动态计算） */
const subModelOptions = computed(() => {
  return CardTypeOptions.find((item) => item.value === cardType.value)?.options || []
})

/** 当前选择的业务系统名称 */
const businessSystemName = ref<string>('')
/** 当前选择的部门名称 */
const deptName = ref<string>('')
/** 当前选择的子型号 */
const subModelName = ref<string>('')

/**
 * 获取下拉框字典数据
 * 包括部门列表和业务系统列表
 */
const fetchDictionaryData = async () => {
  try {
    const [deptResult, businessResult] = await Promise.all([
      deviceGpuDicApi({ type: 'dept' }),
      deviceGpuDicApi({ type: 'business' }),
    ])

    deptOptions.value = deptResult.entity.map((item: string) => ({ name: item, id: item }))
    businessOptions.value = businessResult.entity.map((item: string) => ({ name: item, id: item }))
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// ==================== 导出功能 ====================
/**
 * 导出设备信息到Excel文件
 */
const handleExport = () => {
  const params = {
    modelName: cardType.value,
    deptName: deptName.value,
    businessSystemName: businessSystemName.value,
  }
  useDownload(exportDeviceGpuInfoApi, `${cardType.value}_设备信息`, params, true, '.xlsx')
}

// ==================== 表格数据管理 ====================
/** 表格数据 */
const tableData = ref<any[]>([])
/** 当前页码 */
const currentPage = ref<number>(1)
/** 每页显示数量 */
const pageSize = ref<number>(10)
/** 数据总数 */
const total = ref<number>(0)
/** 表格加载状态 */
const loading = ref<boolean>(false)

/**
 * 加载表格数据
 * 根据当前筛选条件和分页参数获取设备列表
 */
const loadTableData = async () => {
  loading.value = true
  try {
    const { entity } = await pageDeviceGpuInfoListApi({
      modelName: cardType.value,
      deptName: deptName.value,
      businessSystemName: businessSystemName.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    })
    tableData.value = entity.records
    total.value = +entity.total
  } catch (error) {
    console.error('加载表格数据失败:', error)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/**
 * 处理每页显示数量变化
 * @param size 新的每页显示数量
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  loadTableData()
}

/**
 * 处理页面变化
 * @param page 新的页码
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTableData()
}

// ==================== API数据获取 ====================
/**
 * 获取卡类型利用率趋势数据
 * 默认查询最近7天的数据，按天聚合
 */
const fetchUtilizationData = async () => {
  try {
    const params: CardUtilizationRequest = {
      // 时分秒
      startTime: '2025-07-01 00:00:00',
      endTime: '2025-07-31 00:00:00',
      aggregationType: 'month',
      modelName: cardType.value,
      areaCode: cityName.value || undefined,
    }

    const { entity } = await cardUtilizationApi(params)
    utilizationData.value = entity || []
    updateLineChart()
  } catch (error) {
    console.error('获取利用率数据失败:', error)
    utilizationData.value = []
  }
}

/**
 * 获取业务系统利用率排行数据
 * 获取TOP5排行榜数据
 */
const fetchRankingData = async () => {
  try {
    const params: BusinessSystemRankingRequest = {
      areaCode: cityName.value || undefined,
      modelName: cardType.value,
    }

    const { entity } = await businessSystemRankingApi(params)
    rankingData.value = entity || { memoryUtilizationRanking: [], computeUtilizationRanking: [] }
    updateBarChart()
  } catch (error) {
    console.error('获取排行数据失败:', error)
    rankingData.value = { memoryUtilizationRanking: [], computeUtilizationRanking: [] }
  }
}

// ==================== 组件初始化 ====================
/**
 * 初始化组件数据和图表
 * @param type 组件类型：'gpu'(算力) 或 'memory'(显存)
 * @param city 城市名称
 */
const initData = (type: 'gpu' | 'memory', city: string) => {
  comType.value = type
  cityName.value = city
  dialogVisible.value = true

  // 延迟执行，确保DOM已渲染
  nextTick(() => {
    initCharts()
    refreshAllData()
  })
}

/**
 * 刷新所有数据
 * 包括图表数据和表格数据
 */
const refreshAllData = () => {
  fetchUtilizationData()
  fetchRankingData()
  loadTableData()
}

// ==================== 图表管理 ====================
/**
 * 初始化图表实例
 * 创建折线图和柱状图的ECharts实例
 */
const initCharts = () => {
  if (chartContainer.value && !lineChart.value) {
    lineChart.value = echarts.init(chartContainer.value)
  }
  if (chartContainer2.value && !barChart.value) {
    barChart.value = echarts.init(chartContainer2.value)
  }
}

/**
 * 生成折线图配置选项
 * @returns ECharts配置对象
 */
const generateLineChartOption = () => {
  const timePoints = utilizationData.value.map((item) => item.timePoint)
  const memoryData = utilizationData.value.map((item) => item.memoryUtilization)
  const computeData = utilizationData.value.map((item) => item.computeUtilization)

  return {
    title: {
      text: `${cardType.value} 利用率`,
      left: 'center',
      textStyle: { fontSize: 14, color: '#333' },
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}%<br/>`
        })
        return result
      },
    },
    legend: {
      data: ['显存利用率', '算力利用率'],
      bottom: 10,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: timePoints,
      axisLabel: { rotate: 45 },
    },
    yAxis: {
      type: 'value',
      name: '利用率(%)',
      min: 0,
      max: 100,
    },
    series: [
      {
        name: '显存利用率',
        type: 'line',
        data: memoryData,
        smooth: true,
        itemStyle: { color: '#5470c6' },
      },
      {
        name: '算力利用率',
        type: 'line',
        data: computeData,
        smooth: true,
        itemStyle: { color: '#91cc75' },
      },
    ],
  }
}

/**
 * 更新折线图
 * 根据最新的利用率数据更新图表显示
 */
const updateLineChart = () => {
  if (!lineChart.value || !utilizationData.value.length) return

  const option = generateLineChartOption()
  lineChart.value.setOption(option, true) // true表示不合并，完全替换
}

/**
 * 生成柱状图配置选项
 * @returns ECharts配置对象
 */
const generateBarChartOption = () => {
  const currentRanking =
    comType.value === 'gpu'
      ? rankingData.value.computeUtilizationRanking
      : rankingData.value.memoryUtilizationRanking

  const businessNames = currentRanking.map((item: any) => item.businessSystemName)
  const utilizationValues = currentRanking.map((item: any) =>
    comType.value === 'gpu' ? item.computeUtilization : item.memoryUtilization,
  )

  return {
    title: {
      text: `${comTitle.value}利用率业务TOP5排行`,
      left: 'center',
      textStyle: { fontSize: 14, color: '#333' },
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = currentRanking[params[0].dataIndex]
        return `
          业务系统: ${data.businessSystemName}<br/>
          ${comTitle.value}利用率: ${comType.value === 'gpu' ? data.computeUtilization : data.memoryUtilization}%<br/>
          任务数: ${data.taskNum}<br/>
          设备数量: ${data.deviceCount}<br/>
          部门: ${data.deptName}
        `
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: businessNames,
      axisLabel: { rotate: 45, interval: 0 },
    },
    yAxis: {
      type: 'value',
      name: '利用率(%)',
      min: 0,
      max: 100,
    },
    series: [
      {
        type: 'bar',
        data: utilizationValues,
        itemStyle: { color: '#5470c6' },
        barWidth: '60%',
      },
    ],
  }
}

/**
 * 更新柱状图
 * 根据当前选择的类型（算力/显存）和排行数据更新图表
 */
const updateBarChart = () => {
  if (!barChart.value) return

  const currentRanking =
    comType.value === 'gpu'
      ? rankingData.value.computeUtilizationRanking
      : rankingData.value.memoryUtilizationRanking

  if (!currentRanking.length) return

  const option = generateBarChartOption()
  barChart.value.setOption(option, true) // true表示不合并，完全替换
}

// ==================== 组件生命周期和监听器 ====================
// 初始化字典数据
fetchDictionaryData()

/**
 * 监听卡类型变化
 * 当卡类型改变时，重新获取所有相关数据
 */
watch(cardType, () => {
  refreshAllData()
})

/**
 * 监听城市变化
 * 当城市改变时，重新获取图表数据（不需要重新加载表格）
 */
watch(cityName, () => {
  fetchUtilizationData()
  fetchRankingData()
})

/**
 * 监听组件类型变化（算力/显存）
 * 当类型改变时，只需要更新柱状图显示
 */
watch(comType, () => {
  updateBarChart()
})

// ==================== 窗口大小监听 ====================
/**
 * 处理窗口大小变化
 * 重新调整图表尺寸以适应新的容器大小
 */
const handleResize = () => {
  if (lineChart.value) {
    lineChart.value.resize()
  }
  if (barChart.value) {
    barChart.value.resize()
  }
}

// ==================== 组件生命周期钩子 ====================
/**
 * 组件挂载时的初始化操作
 */
onMounted(() => {
  // 监听窗口大小变化事件
  window.addEventListener('resize', handleResize)
})

/**
 * 组件卸载时的清理操作
 */
onUnmounted(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)
  // 清理图表实例，防止内存泄漏
  cleanupCharts()
})

// ==================== 组件对外暴露的方法 ====================
/**
 * 暴露给父组件的方法
 * 允许父组件调用initData方法来初始化组件
 */
defineExpose({
  initData,
})
</script>

<style lang="scss" scoped>
.gpuOrMemoryLine,
.chart-row {
  width: 100%;
}

.gpuOrMemoryLine {
  background-color: #e2edfb;
}

.drawer-header {
  height: 40px;
  position: relative;

  .city-select {
    position: absolute;
    left: 300px;
    top: 50%;
    width: 200px;
    transform: translateY(-50%);
  }

  #drawer-title {
    height: 100%;
    font-size: 22px;
  }
}

.chart-row {
  display: flex;
  gap: 16px;
  width: 100%;
  height: 350px;
  justify-content: space-between;

  .chart-container {
    flex: 1;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    div {
      width: 100%;
      height: 100%;
    }
  }
}

.card-type-select {
  display: flex;
  gap: 16px;
  margin: 10px 0;
  flex-wrap: wrap;
  padding-left: 50px;
  .card-type-item {
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    background: #ffffff;
    color: #333;
  }
  .card-type-item.active {
    background: #3b7ced;
    color: #fff;
  }
}

.title-export {
  position: relative;
  .select-input {
    position: absolute;
    width: 60%;
    top: 3px;
    left: 120px;
    display: flex;
    gap: 8px;
    padding: 0 16px;
    box-sizing: border-box;
  }
  .select-item {
    width: 33%;
  }
}

.table-section {
  background: #f3f8fd;
  margin: 0 16px;
  border-radius: 8px;
  padding-bottom: 8px;
  box-shadow: none;
  border: none;
  margin-top: 8px;

  // .power-table {
  //   height: 226px;
  //   border: none;
  //   border-radius: 6px;
  //   overflow: hidden;
  // }
}
.drawer-body-dashboard {
  background: #e2edfb;

  .pagination-container {
    margin: 8px;
    height: 45px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #f3f8fd !important;
    .btn-next {
      background: #f3f8fd !important;
      color: #333;
    }
    .btn-prev {
      background: #f3f8fd !important;
      color: #333;
    }
    .el-pager {
      background: #f3f8fd !important;
      .number {
        background: #f3f8fd !important;
        border: 1px solid #e6ebf5;
        color: #333;
      }
      .more {
        background: #f3f8fd !important;
        color: #333;
      }
    }
  }

  .el-table__header {
    background-color: #cbe2ff;
  }
  .el-table__row.current-row td {
    background: #cbe2ff !important;
  }
  .el-table th {
    color: #333;
    font-weight: 500;
    border-bottom: 1px solid #e6ebf5;
    background: #cbe2ff !important;
  }
  .el-table__header-wrapper {
    background: #cbe2ff !important;
  }
  .el-table__body-wrapper {
    background: #f3f8fd !important;
  }

  .el-table td {
    padding: 12px 0;
    border-bottom: 1px solid #f0f2f7;
    background-color: #f3f8fd;
  }

  .el-table tr:hover > td {
    background-color: #e8f2ff !important;
  }

  .el-table__body tr:last-child td {
    border-bottom: none;
  }
}
</style>
