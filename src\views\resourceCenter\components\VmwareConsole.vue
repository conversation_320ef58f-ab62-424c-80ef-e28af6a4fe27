<template>
  <div class="vmware-console">
    <!-- 控制台工具栏 -->
    <div id="bar" class="console-toolbar">
      <div id="buttonBar">
        <div class="buttonC">
          <el-button id="fullscreen" type="primary" @click="enterFullScreen"> 查看全屏 </el-button>
          <el-button id="cad" type="primary" @click="sendCAD"> 发送 Ctrl+Alt+Delete </el-button>
          <el-button type="default" @click="closeConsole"> 关闭控制台 </el-button>
        </div>
      </div>
    </div>

    <!-- VMware 控制台容器 -->
    <div id="wmksContainer" class="console-container"></div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
      </div>
      <p>正在连接VMware控制台...</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error && !showTrustDialog" class="error-overlay">
      <el-icon class="error-icon" color="#F56C6C" size="48">
        <CircleClose />
      </el-icon>
      <p class="error-message">{{ error }}</p>
      <el-button type="primary" @click="retry">重试</el-button>
    </div>

    <!-- 信任域名弹窗 -->
    <el-dialog
      v-model="showTrustDialog"
      title="需要信任SSL证书"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="trust-dialog-content">
        <el-icon class="warning-icon" color="#E6A23C" size="48">
          <Warning />
        </el-icon>
        <p class="trust-message">检测到SSL证书未被信任，需要您手动信任该域名才能正常连接控制台。</p>
        <p class="trust-url">
          域名地址：<strong>https://{{ hostIp }}</strong>
        </p>
        <p class="trust-instruction">
          请点击"跳转链接"打开新页面，在浏览器中信任该SSL证书，然后返回此页面点击"已信任该域名"进行重连。
        </p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openTrustLink" type="primary">跳转链接</el-button>
          <el-button @click="handleTrusted" type="success">已信任该域名</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleClose, Warning } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { getVmwareVncUrl } from '@/api/modules/resourecenter'
// WMKS类型声明
declare global {
  interface Window {
    $: any
    WMKS: {
      createWMKS: (containerId: string, options: any) => any
      CONST: {
        Events: {
          CONNECTION_STATE_CHANGE: string
        }
        ConnectionState: {
          CONNECTED: string
          DISCONNECTED: string
        }
      }
    }
  }
}

const emit = defineEmits<{
  close: []
}>()

const loading = ref(true)
const error = ref('')
const wmks = ref<any>(null)
const route = useRoute()
const showTrustDialog = ref(false)

const hostIp = ref('')

// 获取设备ID
const getDeviceId = () => {
  // 尝试从多个来源获取设备ID
  const deviceId = sessionStorage.getItem('deviceId') || route.query.deviceId || route.query.id
  return typeof deviceId === 'string' ? deviceId : Array.isArray(deviceId) ? deviceId[0] : ''
}

// 更新连接参数
const updateConnectionParams = async () => {
  try {
    const deviceId = getDeviceId()
    if (!deviceId) {
      throw new Error('设备ID不存在')
    }

    const { entity } = await getVmwareVncUrl({ id: deviceId })
    if (!entity) {
      throw new Error('获取VMware连接信息失败')
    }

    // 更新sessionStorage中的连接参数
    const { sid, ticket, hostIp: newHostIp, ticketPort } = entity
    sessionStorage.setItem('sid', sid)
    sessionStorage.setItem('ticket', ticket)
    sessionStorage.setItem('hostIp', newHostIp)
    sessionStorage.setItem('port', ticketPort)

    hostIp.value = newHostIp

    console.log('更新连接参数成功:', { sid, ticket, hostIp: newHostIp, port: ticketPort })
    return true
  } catch (error) {
    console.error('更新连接参数失败:', error)
    ElMessage.error('更新连接参数失败')
    return false
  }
}

// 检查是否为连接超时错误
const isConnectionTimeoutError = (errorMessage: string) => {
  return errorMessage.includes('Error in connection establishment: net::ERR_CONNECTION_TIMED_OUT')
}

// 打开信任链接
const openTrustLink = () => {
  if (hostIp.value) {
    window.open(`https://${hostIp.value}`, '_blank')
  }
}

// 处理已信任域名
const handleTrusted = async () => {
  showTrustDialog.value = false

  // 重新获取连接参数
  const success = await updateConnectionParams()
  if (success) {
    // 重新连接
    retry()
  }
}

// 初始化VMware控制台
const initVmwareConsole = async () => {
  try {
    loading.value = true
    error.value = ''
    showTrustDialog.value = false

    console.log('开始初始化VMware控制台...')

    // 检查WMKS是否已加载
    if (typeof window.WMKS === 'undefined') {
      console.log('WMKS未加载，开始加载WMKS脚本...')
      await loadWMKSScripts()
    } else {
      console.log('WMKS已存在')
    }

    // 验证WMKS.createWMKS是否可用
    if (typeof window.WMKS.createWMKS !== 'function') {
      throw new Error('WMKS.createWMKS 函数不可用')
    }

    // 从sessionStorage获取sid和ticket  hostIp和port
    const sid = sessionStorage.getItem('sid')
    const ticket = sessionStorage.getItem('ticket')
    const currentHostIp = sessionStorage.getItem('hostIp')
    const port = sessionStorage.getItem('port')
    const wsUrl = sessionStorage.getItem('wsUrl')

    hostIp.value = currentHostIp || ''

    console.log('获取连接信息:', {
      sid: sid ? '存在' : '不存在',
      ticket: ticket ? '存在' : '不存在',
      hostIp: currentHostIp ? '存在' : '不存在',
      port: port ? '存在' : '不存在',
      wsUrl: wsUrl ? '存在' : '不存在',
    })

    let wssUrl = ''
    const type = route.query.type
    if (type == '1') {
      wssUrl = `wss://${currentHostIp}:${port}/ticket/${ticket}`
    } else if (type == '2') {
      const currentLocation = window.location
      const protocol = currentLocation.protocol.replace(':', '')
      const domain = currentLocation.hostname
      const currentPort = currentLocation.port || (protocol === 'https' ? '443' : '80')
      wssUrl = `ws://${domain}:${currentPort}/vmconsole/${sid}/ticket/${ticket}`
    }

    console.log('准备创建WMKS实例...')

    // 创建WMKS实例
    wmks.value = window.WMKS.createWMKS('wmksContainer', {
      onError: function (err: any) {
        console.error('WMKS选项onError:', err)
        loading.value = false

        const errorMessage = err.message || err.toString() || ''
        console.log('onError错误信息:', errorMessage)
      },
    }).register(window.WMKS.CONST.Events.CONNECTION_STATE_CHANGE, function (event: any, data: any) {
      console.log('WMKS连接状态变化:', data, data.state)
      if (data.state === window.WMKS.CONST.ConnectionState.CONNECTED) {
        console.log('VMware控制台连接成功')
        loading.value = false
        showTrustDialog.value = false
      } else if (data.state === window.WMKS.CONST.ConnectionState.DISCONNECTED) {
        // 检查是否为type=
        if (route.query.type == '1') {
          console.log('onError检测到连接超时错误，显示信任域名弹窗')
          loading.value = false
          showTrustDialog.value = true
          error.value = '控制台连接已断开'
        } else {
          console.log('VMware控制台连接断开')
          loading.value = false
          error.value = '控制台连接已断开'
        }
      }
    })

    // 获取底层WebSocket对象进行错误监听
    setTimeout(() => {
      if (wmks.value && wmks.value._vncDecoder && wmks.value._vncDecoder._websocket) {
        const ws = wmks.value._vncDecoder._websocket
        const originalOnError = ws.onerror

        ws.onerror = function (event: any) {
          console.error('WebSocket error event:', event)

          // 只在type=1时特殊处理连接超时错误
          if (route.query.type == '1') {
            // 检查是否为连接超时错误（通过WebSocket的readyState和错误信息判断）
            const isTimeout =
              ws.readyState === WebSocket.CLOSED && event.target?.readyState === WebSocket.CLOSED

            if (isTimeout) {
              console.log('WebSocket连接超时错误，type=1，显示信任域名弹窗')
              loading.value = false
              showTrustDialog.value = true
              return
            }
          }

          // 对于type=2或非超时错误，调用原有的错误处理逻辑
          if (originalOnError) {
            originalOnError.call(ws, event)
          } else {
            // 如果没有原有错误处理，设置通用错误信息
            loading.value = false
            error.value = '连接失败'
          }
        }
      }
    }, 100)

    console.log('WMKS实例创建成功，开始连接:', wssUrl)

    // 连接到VMware控制台
    wmks.value.connect(wssUrl)
  } catch (err: any) {
    console.error('初始化VMware控制台失败:', err)
    loading.value = false

    const errorMessage = err.message || '初始化控制台失败'

    // 检查是否为type=1且为连接超时错误
    if (route.query.type == '1' && isConnectionTimeoutError(errorMessage)) {
      console.log('检测到连接超时错误，显示信任域名弹窗')
      showTrustDialog.value = true
    } else {
      error.value = errorMessage
    }
  }
}

// 加载WMKS相关脚本
const loadWMKSScripts = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 如果WMKS已经存在，直接返回
    if (typeof window.WMKS !== 'undefined' && typeof window.WMKS.createWMKS === 'function') {
      resolve()
      return
    }

    // 顺序加载脚本
    const loadScript = (src: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = src
        script.onload = () => resolve()
        script.onerror = () => reject(new Error(`加载脚本失败: ${src}`))
        document.head.appendChild(script)
      })
    }

    // 按顺序加载
    loadScript('/wmware/jquery-1.8.3.min.js')
      .then(() => loadScript('/wmware/jquery-ui.min.js'))
      .then(() => loadScript('/wmware/wmks.js'))
      .then(() => {
        // 验证jQuery是否正确加载
        if (typeof window.$ === 'undefined') {
          reject(new Error('jQuery未正确加载'))
          return
        }

        // 验证WMKS是否正确加载
        if (typeof window.WMKS === 'undefined') {
          reject(new Error('WMKS对象未正确加载'))
          return
        }

        if (typeof window.WMKS.createWMKS !== 'function') {
          reject(new Error('WMKS.createWMKS函数不可用'))
          return
        }

        // 检查WMKS的必要常量
        if (!window.WMKS.CONST || !window.WMKS.CONST.Events || !window.WMKS.CONST.ConnectionState) {
          reject(new Error('WMKS常量对象不完整'))
          return
        }

        // 加载CSS样式
        loadWMKSStyles()

        console.log('WMKS脚本加载成功，所有组件验证通过')
        resolve()
      })
      .catch(reject)
  })
}

// 加载WMKS相关样式
const loadWMKSStyles = () => {
  const styles = ['/wmware/wmks-all.css', '/wmware/vscss.css']

  styles.forEach((href) => {
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = href
    document.head.appendChild(link)
  })
}

// 进入全屏
const enterFullScreen = () => {
  if (wmks.value && wmks.value.canFullScreen()) {
    wmks.value.enterFullScreen()
  } else {
    ElMessage.warning('当前浏览器不支持全屏功能')
  }
}

// 发送Ctrl+Alt+Delete命令
const sendCAD = () => {
  if (wmks.value) {
    wmks.value.sendCAD()
    ElMessage.success('已发送 Ctrl+Alt+Delete 命令')
  } else {
    ElMessage.warning('控制台未连接')
  }
}

// 关闭控制台
const closeConsole = () => {
  if (wmks.value) {
    wmks.value.disconnect()
  }
  emit('close')
}

// 重试连接
const retry = () => {
  error.value = ''
  showTrustDialog.value = false
  initVmwareConsole()
}

// 监听窗口大小变化
const handleResize = () => {
  if (wmks.value && !wmks.value.isFullScreen()) {
    wmks.value.updateScreen()
  }
}

onMounted(() => {
  // 保存设备ID到sessionStorage，以便后续重新获取连接参数
  const deviceId = route.query.deviceId || route.query.id
  if (deviceId && typeof deviceId === 'string') {
    sessionStorage.setItem('deviceId', deviceId)
  }

  initVmwareConsole()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (wmks.value) {
    wmks.value.disconnect()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.vmware-console {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #000;
  z-index: 9998;
}

.console-toolbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  z-index: 9999;
  display: flex;
  align-items: center;
  padding: 0 16px;

  .buttonC {
    display: flex;
    gap: 8px;
  }
}

.console-container {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: calc(100vh - 40px);
  background-color: #000;
}

.loading-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
  z-index: 10000;

  .loading-spinner {
    font-size: 24px;
    margin-bottom: 16px;

    i {
      animation: rotating 2s linear infinite;
    }
  }

  p {
    margin-top: 16px;
    font-size: 16px;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
  z-index: 10000;

  .error-icon {
    margin-bottom: 16px;
  }

  .error-message {
    margin-bottom: 16px;
    font-size: 16px;
  }
}

// 信任域名对话框样式
.trust-dialog-content {
  text-align: center;
  padding: 20px;

  .warning-icon {
    margin-bottom: 20px;
  }

  .trust-message {
    font-size: 16px;
    color: #606266;
    margin-bottom: 16px;
    line-height: 1.6;
  }

  .trust-url {
    font-size: 14px;
    color: #909399;
    margin-bottom: 16px;

    strong {
      color: #409eff;
      font-weight: 600;
    }
  }

  .trust-instruction {
    font-size: 14px;
    color: #909399;
    line-height: 1.6;
    margin-bottom: 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
