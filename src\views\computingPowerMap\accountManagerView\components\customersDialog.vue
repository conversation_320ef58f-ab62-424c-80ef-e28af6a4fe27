<template>
  <el-dialog append-to-body title="客户详情" width="1000px" destroy-on-close>
    <div class="filter-form-con">
      <sl-form
        class="filter-form"
        :class="{ collapsed: collapsed }"
        ref="formRef"
        :options="formOptions"
        v-model="formModel"
      >
      </sl-form>
    </div>
    <SlProTable
      ref="proTable"
      highlight-current-row
      :columns="columns"
      :max-height="400"
      style="min-height: 400px"
      :request-api="getCustomListApi"
      :init-param="queryParams"
      hidden-table-header
      class="scrollable-table"
    >
    </SlProTable>
  </el-dialog>
</template>

<script setup lang="tsx" name="DataList">
import { reactive, ref, type VNode } from 'vue'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import { getCustomListApi } from '@/api/modules/computingPowerMap'
import { useRouter } from 'vue-router'
import ConditionFilter from '@/views/resourceCenter/conditionFilter.vue'
import { Delete, Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue'

const router = useRouter()
const formRef = ref<any>(null)
const queryParams = ref<any>({})
function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...formModel }
}
function search() {
  queryParams.value = { ...formModel }
}
const collapsed = ref(true)
const formModel = reactive({
  customNo: '',
  customName: '',
  billId: '',
  contactName: '',
  email: '',
  contactMobile: '',
  createTime: '',
})
const formOptions = reactive([
  {
    style: 'padding: 0',
    gutter: 16,
    groupItems: [
      {
        label: '企业名称',
        type: 'input',
        key: 'customName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '企业联系人',
        type: 'input',
        key: 'contactName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },

      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '客户账号',
        type: 'input',
        key: 'customNo',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '计费号',
        type: 'input',
        key: 'billId',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '邮箱',
        type: 'input',
        key: 'email',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '手机号',
        type: 'input',
        key: 'contactMobile',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '创建时间',
        type: 'date',
        key: 'createTime',
        span: 8,
        disabled: false,
        hidden: true,
        props: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '至',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
        },
      },
    ],
  },
])

// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { type: 'index', label: '序号', width: 55, fixed: 'left' },
  { prop: 'customName', label: '企业名称', width: 150 },
  { prop: 'customNo', label: '客户账号', width: 150 },
  { prop: 'contactName', label: '企业联系人', width: 150 },
  { prop: 'billId', label: '计费号', width: 120 },
  { prop: 'email', label: '邮箱', width: 150 },
  { prop: 'contactMobile', label: '手机号', width: 150 },
  { prop: 'createdTime', label: '创建时间', width: 180 },
  { prop: 'operation', label: '操作', width: 100, fixed: 'right', render: operationRender },
])

function operationRender({ row }: any): VNode {
  return (
    <el-button onClick={() => handleShowDetail(row)} link type="primary">
      详情 {row.id}
    </el-button>
  )
}
const handleShowDetail = (row: any) => {
  router.push({
    path: '/accountDetail',
    query: {
      customId: row.customId,
    },
  })
}
const proTable = ref<ProTableInstance>()
</script>

<style scoped>
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
