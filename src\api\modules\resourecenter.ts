import http from '@/api'
import { WOC, V1CLOUD, USER, PERFORMANCE } from '../config/servicePort'
import { changeDateFormat } from '@/utils'

/**
 * @name 云主机开通资源==获取业务模块
 */
export const belongingBusiness = (config: { businessSystemId: string }) => {
  return http.get(WOC + '/business/moduleList', config)
}

/**
 * @name 云主机开通资源==获取厂家
 */
export const manuFacturerapi = () => http.get(WOC + '/config/manufacturerList')
/**
 * @name 云主机开通资源==获取厂家负责人电话
 */
export const manuFacturerapiiphone = (config: { manufacturerName: string }) =>
  http.get(WOC + '/config/manufacturerNameList', config)
/**
 * @name 云主机开通资源==获取领导
 */
export const queryUsersByRoleCode = (config: { roleCode: string; domainCode: string }) =>
  http.post(WOC + '/user/queryUsersByRoleCode', config)

/**
 * @name 云主机开通资源==提交资源申请
 */
export const submitResourceRequest = (config: any) =>
  http.post(WOC + '/standardWorkorder/create', config)

/**
 * @name 业务系统详情
 */
export const busisystemDetail = (config: { id: number | undefined | string }) =>
  http.get<any>(WOC + '/business/detail', config)

/**
 * @name 获取可用区
 */
export const getAz = (config: { resourcePoolId: string }) =>
  http.get<any>(WOC + '/az/query', config)

/**
 * @name 云主机开通资源提交资源申请
 */
export const vpcCreate = (config: any) => http.post(WOC + '/vpc/vpcCreate', config)

export const vpcCreateBatch = (config: any) => http.post(WOC + '/vpc/vpcCreateBatch', config)

export const addVpcSubnetBatch = (config: any) =>
  http.post(WOC + '/vpc/createVpcSubnetBatch', config)
/**
 * @name vpc列表
 */
export const vpcList = (config: any) => {
  return http.post<any>(WOC + '/form/queryVpcListPage', changeDateFormat(config, ['createdTime']))
}

/**
 * @name 网络列表
 */
export const networkList = (config: any) => {
  return http.post<any>(
    WOC + '/form/queryNetworkListPage',
    changeDateFormat(config, ['createdTime']),
  )
}

/**
 * @name nat网关规则列表
 */
export const natRuleList = (config: any) =>
  http.post(WOC + '/nat/queryNatRulesPage', changeDateFormat(config, ['createdTime']))

/**
 * 获取 nat 详情
 */
export const getNatDetails = (config: any) =>
  http.post<any>(WOC + '/nat/getNatRulesDetails', config)

export const natRuleCreate = (config: any) =>
  http.post<any>(WOC + '/nat/rulesCreate', config, { loading: true })

/**
 * @name 获取资源列表
 */
export const getResourceList = (config: any) => {
  return http.post<any>(
    WOC + '/resource/page',
    changeDateFormat(config, ['effectiveTime', 'expireTime']),
  )
}

/**
 * @name 获取对公资源列表
 */
export const getCorporateResourceList = (config: any) =>
  http.post<any>(
    WOC + '/resource/pageCorporate',
    changeDateFormat(config, ['effectiveTime', 'expireTime', 'createTime']),
  )

/**
 * @name 订单回显
 */
export const displayBackOrder = (config: any) =>
  http.post<any>(WOC + '/standardWorkorder/detail', config)

/**
 * @name 非标订单回显
 */
export const displayBackNonStandardOrder = (config: any) =>
  http.post<any>(WOC + '/nonStanderOrder/detail', config)

/**
 * @name 购物车 创建
 */
export const shoppingCartCreate = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/create', config)

/**
 * @name 购物车 更新
 */
export const shoppingCartUpdate = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/update', config)

/**
 * @name 购物车 列表
 */
export const shoppingCartList = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/list', config)

/**
 * @name 购物车 删除
 */
export const shoppingCartDelete = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/delete', config)

/**
 * @name 购物车 总数
 */
export const shoppingCartCount = (config: any = {}) =>
  http.post<any>(WOC + '/order/shoppingCart/count', config)

/**
 * @name 网段列表
 */
export const ipRangeList = (config: any) => http.get<any>(WOC + '/network/ip/list', config)

/**
 * @name 网段校验
 */
export const ipRangeCheck = (config: any = {}) => http.post<any>(WOC + '/network/ip/check', config)

/**
 * @name 子网切割
 */
export const ipRangeSplit = (config: any = {}) => http.post<any>(WOC + '/network/ip/subnet', config)

/**
 * @name 网络创建
 */
export const networkCreate = (config: any = {}) =>
  http.post<any>(WOC + '/network/networkCreate', config)

export const networkCreateBatch = (config: any) =>
  http.post(WOC + '/network/networkCreateBatch', config)

export const addNetworkSubnetBatch = (config: any) =>
  http.post(WOC + '/network/createSubnetWork', config)

/**
 * @name 更新网络状态
 */
export const updateNetworkStatus = (config: any = {}) =>
  http.post<any>(WOC + '/network/ip/updateStatus', config)

/**
 * @name vlan列表
 */
export const vlanList = (config: any) => http.get<any>(WOC + '/network/vlan/list', config)

/**
 * @name 更新vlan状态
 */
export const updateVlanStatus = (config: any = {}) =>
  http.post<any>(WOC + '/network/vlan/updateStatus', config)

/*
 * @name 资源导出
 */
export const resourceExport = (config: any = {}) =>
  http.post<any>(WOC + '/goods/resource/export', config, { responseType: 'blob' })

/**
 * @name network详情
 */
export const getNetworkDetail = (config: any) =>
  http.get<any>(WOC + '/network/getNetworkDetail', config)

/**
 * @name vpc详情
 */
export const getVpcDetail = (config: any) => http.get<any>(WOC + '/vpc/getVpcDetail', config)

/**
 * @name 草稿工单
 */
export const addDraftsOrder = (config: any = {}) =>
  http.post<any>(WOC + '/standardWorkorder/draft', config)

/**
 * @name 获取规格树
 */
export const flavorModelTree = (config: any = {}) =>
  http.post<any>(WOC + '/flavorModel/listTree', config)

/**
 * @name 获取镜像树
 */
export const imageTree = (config: any = {}) => http.post<any>(WOC + '/images/listTree', config)

/**
 *
 * @name 资源操作
 */
export const executeResourceOperation = (config: any = {}) =>
  http.post<any>(WOC + '/resource/vm/operate', config)

/**
 * @name 查询云主机挂载资源
 */
export const queryVmMountResource = (config: any = {}) =>
  http.post<any>(WOC + '/resource/getMountResource', config)

/**
 * @name 查询子网列表
 * @param config
 * @returns
 */
export const getSubnetList = (config: any = {}) =>
  http.post<any>(WOC + '/resource/getNetWorkDetail', config)

export const cycleBinCreate = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/create', config)

export const cycleBinUpdate = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/update', config)

export const cycleBinList = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/list', config)

export const cycleBinDelete = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/delete', config)

export const cycleBinCount = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/count', config)

export const cycleBinListProduct = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/shoppingCart/listProduct', config)

export const recoveryDetail = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/workOrder/detail', config)

export const submitRecoveryOrder = (config: any) =>
  http.post(WOC + '/recovery/workOrder/create', config)

export const addDraftsRecoveryOrder = (config: any = {}) =>
  http.post<any>(WOC + '/recovery/workOrder/draft', config)

/**
 * @name 变更区 更新
 */
export const propertyChangeUpdate = (config: any = {}) =>
  http.post<any>(WOC + '/change/shoppingCart/update', config)

/**
 * @name 变更区 列表
 */
export const propertyChangeList = (config: any = {}) =>
  http.post<any>(WOC + '/change/shoppingCart/list', config)

/**
 * @name 变更区 总数
 */
export const propertyChangeCount = (config: any = {}) =>
  http.post<any>(WOC + '/change/shoppingCart/count', config)

/**
 * @name 通过账单id获取租户信息
 */
export const tenantGetByBillId = (config: any = {}) =>
  http.get<any>(USER + '/external/tenant/getByBillId', config)

/**
 * @name 自定义规格创建
 */
export const customFlavorCreate = (config: any = {}) =>
  http.post<any>(WOC + '/nonStanderOrder/createFlavor', config)

/**
 * @name 非标准订单创建
 */
export const nonStandardOrderCreate = (config: any = {}) =>
  http.post<any>(WOC + '/nonStanderOrder/create', config)

/**
 * @name 非标准订单校验
 */
export const nonStandardOrderCapacityCheck = (config: any = {}, showErrorMessage = false) =>
  http.post<any>(WOC + '/nonStanderOrder/capacityCheck', config, { showErrorMessage })

/**
 * @name 非标流程节点
 */
export const nonStandardOrderNode = (config: any = {}) =>
  http.post<any>(WOC + '/nonStanderOrder/treeNode', config)

/**
 * @name 变更区 提交
 */
export const changeWorkOrderCreate = (config: any) =>
  http.post(WOC + '/changeWorkOrder/create', config)

/**
 * @name 变更区 草稿
 */
export const changeWorkOrderDraft = (config: any) =>
  http.post(WOC + '/changeWorkOrder/draft', config)

/**
 * @name 变更区 详情
 */
export const changeWorkOrderDetail = (config: any) =>
  http.post<any>(WOC + '/changeWorkOrder/detail', config)

/**
 * @name EIP绑定设备
 */
export const eipBind = (config: { id: string; deviceId: string; deviceType: string }) =>
  http.post<any>(WOC + '/eip/bind', config)

/**
 * @name EIP解绑设备
 */
export const eipUnbind = (config: { id: string }) => http.post<any>(WOC + '/eip/unbind', config)

/**
 * @name 云硬盘绑定设备
 */
export const evsBind = (config: { volumeResourceDetailId: string; vmResourceDetailId: string }) =>
  http.post<any>(WOC + '/evs/bind', config)

/**
 * @name 云硬盘解绑设备
 */
export const evsUnbind = (config: { volumeResourceDetailId: string }) =>
  http.post<any>(WOC + '/evs/unbind', config)

/**
 * @name 备份策略操作（绑定/解绑）
 */
export const backupOperate = (config: {
  type: string
  detailId: string[]
  backupDetailId: string
}) => http.post<any>(WOC + '/backup/operate', config)

/**
 * @name 获取镜像列表
 */
export const getImagesList = (config: { regionId: string; domainCode: string; azId: string }) =>
  http.post<any>(WOC + '/images/list', config)

/**
 * @name 获取镜像列表（镜像管理页面使用）
 */
export const getImageList = (config: any = {}) =>
  http.post<any>(WOC + '/imageFile/page', changeDateFormat(config, ['uploadTime']))

/**
 * @name 获取镜像增加
 */
export const imageAdd = (config: any = {}) => http.post<any>(WOC + '/imageFile/insert', config)

/**
 * @name 镜像删除
 */
export const imageDelete = (config: any = {}) => http.get<any>(WOC + '/imageFile/delete', config)

/**
 * @name 镜像更新
 */
export const imageUpdate = (config: any = {}) => http.post<any>(WOC + '/imageFile/update', config)

/**
 * @name 安全组列表查询
 */
export const getSecurityGroupList = (config: any) =>
  http.post<any>(WOC + '/securityGroup/page', changeDateFormat(config, ['createTime']))

/**
 * @name 安全组创建
 */
export const createSecurityGroup = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/create', config)

/**
 * @name 安全组删除
 */
export const deleteSecurityGroup = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/delete', config)

/**
 * @name 安全组操作（绑定/解绑）
 */
export const securityGroupOperate = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/operate', config)

/**
 * @name 安全组详情
 */
export const getSecurityGroupDetail = (config: { id: string }) =>
  http.get<any>(WOC + '/securityGroup/detail', config)

/**
 * @name 安全组规则添加
 */
export const addSecurityGroupRule = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/createRule', config)

/**
 * @name 安全组规则编辑
 */
export const updateSecurityGroupRule = (config: any = {}) =>
  http.post<any>(WOC + '/securityGroup/updateRule', config)

/**
 * @name 安全组规则删除
 */
export const deleteSecurityGroupRule = (config: { id: string; ruleIds: string[] }) =>
  http.post<any>(WOC + '/securityGroup/deleteRule', config)

/**
 * @name 虚拟网卡列表查询
 */
export const getVirtualNicList = (config: any) =>
  http.post<any>(WOC + '/vnic/page', changeDateFormat(config, ['createTime']))

/**
 * @name 虚拟网卡创建
 */
export const createVirtualNic = (config: any = {}) => http.post<any>(WOC + '/vnic/save', config)

/**
 * @name 虚拟网卡删除
 */
export const deleteVirtualNic = (config: any = {}) => http.get<any>(WOC + '/vnic/delete', config)

/**
 * @name 虚拟网卡操作（绑定/解绑）
 */
export const virtualNicOperate = (config: any = {}) => http.post<any>(WOC + '/vnic/operate', config)

/**
 * @name 虚拟网卡详情
 */
export const getVirtualNicDetail = (config: { id: string }) =>
  http.get<any>(WOC + '/vnic/detail', config)

/**
 * @name 虚拟网卡编辑
 */
export const updateVirtualNic = (config: any = {}) => http.post<any>(WOC + '/vnic/update', config)

/**
 * @name 获取VPC子网列表
 */
export const getVpcSubnetsList = (config: any = {}) =>
  http.get<any>(WOC + '/vpc/getSubnetsByVpcId', config)

/**
 * @name 获取网络子网列表
 */
export const getNetworkSubnetsList = (config: any = {}) =>
  http.get<any>(WOC + '/network/getSubnetsByNetworkId', config)

/**
 * @name 虚拟IP列表查询
 */
export const getVirtualIpList = (config: any) =>
  http.post<any>(WOC + '/virtualIp/page', changeDateFormat(config, ['createdTime']))

/**
 * @name 虚拟IP创建
 */
export const createVirtualIp = (config: any = {}) => http.post<any>(WOC + '/virtualIp/add', config)

/**
 * @name 虚拟IP删除
 */
export const deleteVirtualIp = (config: any = {}) =>
  http.post<any>(WOC + '/virtualIp/delete', config)

/**
 * @name 虚拟IP详情
 */
export const getVirtualIpDetail = (config: { id: string }) =>
  http.post<any>(WOC + '/virtualIp/detail', config)

/**
 * @name 虚拟IP编辑
 */
export const updateVirtualIp = (config: any = {}) =>
  http.post<any>(WOC + '/virtualIp/update', config)

/**
 * @name 获取未使用的虚拟IP列表
 */
export const getUnusedVirtualIp = (config: any = {}) =>
  http.post<any>(WOC + '/virtualIp/getUnusedIpList', config)

/**
 * @name 获取SLB详情
 */
export const getResourceDetail = (config: any = {}) => {
  return http.post<any>(WOC + `/resource/detail`, config)
}

/**
 * @name 获取负载均衡监听列表
 */
export const getSlbListenerList = (config: any = {}) =>
  http.post<any>(WOC + '/slbListener/page', config)

/**
 * @name 创建负载均衡监听
 */
export const createSlbListener = (data: any) => http.post(WOC + '/slbListener/create', data)

/**
 * @name 删除负载均衡监听
 */
export const deleteSlbListener = (data: any) => http.post(WOC + `/slbListener/delete`, data)

/**
 * @name 获取负载均衡监听详情
 */
export const getSlbListenerDetail = (data: any) =>
  http.post<any>(WOC + '/slbListener/getById', data)

/**
 * @name 获取负载均衡服务器组列表
 */
export const getSlbServerGroupList = (config: any = {}) =>
  http.post<any>(WOC + '/slbListenerServerGroup/page', config)

/**
 * @name 获取负载均衡服务器组详情
 */
export const getSlbServerGroupDetail = (data: any) =>
  http.post<any>(WOC + '/slbListenerServerGroup/detail', data)

/**
 * @name 获取容器配额商品列表
 */
export const containerPageApi = (config: any) =>
  http.post<any>(WOC + '/container/quota/page', changeDateFormat(config, ['createTime']))

/**
 * @name 容器配额商品批量导出
 */
export const containerExportApi = (config: any) =>
  http.post(WOC + '/container/quota/export', config, { responseType: 'blob', loading: true })

/**
 * @name 获取容器配额详情
 */
export const getContainerQuotaDetail = (config: { id: string }) =>
  http.post<any>(WOC + '/container/quota/detail', config)

/**
 * @name 证书管理列表查询
 */
export const getCertificateList = (config: any) =>
  http.post<any>(WOC + '/slbCertificate/page', changeDateFormat(config, ['createTime']))

/**
 * @name 创建证书
 */
export const createCertificate = (config: any = {}) =>
  http.post<any>(WOC + '/slbCertificate/create', config)

/**
 * @name 删除证书
 */
export const deleteCertificate = (config: any = {}) =>
  http.post<any>(WOC + '/slbCertificate/delete', config)

/**
 * @name 获取证书详情
 */
export const getCertificateDetail = (config: { id: string }) =>
  http.post<any>(WOC + '/slbCertificate/detail', config)

/**
 * @name 获取云端口列表
 */
export const getCloudPortList = (config: any = {}) =>
  http.post<any>(WOC + '/cloudPort/page', config)

/**
 * @name 创建云端口
 */
export const createCloudPort = (config: any = {}) =>
  http.post<any>(WOC + '/cloudPort/create', config)

/**
 * @name 删除云端口
 */
export const deleteCloudPort = (config: any = {}) =>
  http.post<any>(WOC + '/cloudPort/delete', config)

/**
 * @name 获取云端口详情
 */
export const getCloudPortDetail = (config: { id: string }) =>
  http.post<any>(WOC + '/cloudPort/detail', config)

/**
 * @name 编排创建
 */
export const createDagTemplate = (config: any) =>
  http.post<any>(WOC + '/dagTemplate/create', config)

/**
 * @name 编排更新
 */
export const updateDagTemplate = (config: any) =>
  http.post<any>(WOC + '/dagTemplate/update', config)

/**
 * @name 获取云主机VNC控制台URL
 */
export const getEcsVncUrl = (config: { ecsId: string }) =>
  http.get<any>(V1CLOUD + '/ecs/vnc', config)

/**
 * @name 获取VMware云主机VNC控制台URL
 */
export const getVmwareVncUrl = (config: { id: string }) =>
  http.get<any>(V1CLOUD + '/ecs/vmware/vnc', config)

/**
 * @name 编排详情
 */
export const getDagTemplateDetail = (config: { id: string }) =>
  http.post<any>(WOC + '/dagTemplate/detail', config)

/**
 * @name 获取外部网络列表
 */
export const getExternalNetworkList = (config: any) =>
  http.post<any>(WOC + '/mcExternalNetwork/listAll', config)

/**
 * @name 获取对公ecs实例规格
 */
export const getEcsSpecList = (config: any) =>
  http.post<any>(WOC + '/flavorModel/pageFlavor', config)

/**
 * @name 获取租户列表
 */
export const getTenantList = (config: any) => http.get<any>(USER + '/tenant/get/userId', config)

// ---------------------- 资源开通订购----------------------

/**
 * @name 资源开通订购 -- 直接开通
 */
export const corporateOrderApi = (config: any) =>
  http.post<any>(WOC + '/corporate/order/create', config, { loading: true })

/**
 * @name 资源开通订购 -- 加入清单
 */
export const corporateOrderTemSaveApi = (config: any) =>
  http.post<any>(WOC + '/corporateOrderTemSave/create', config, { loading: true })

/**
 * @name 资源开通订购 -- 获取镜像
 */
export const colrporateListTreeApi = (config: any) =>
  http.post<any>(WOC + '/images/colrporateListTree', config, { loading: true })

/**
 * @name 资源开通订购 -- 获取清单列表
 */
export const getCorporateOrderTemSave = (config: any) =>
  http.post<any>(WOC + '/corporateOrderTemSave/getTempSave', config)

/**
 * @name 资源开通订购 -- 取消订购
 */
export const corporateOrderTemSaveDeleteAll = (config: any) =>
  http.post<any>(WOC + '/corporateOrderTemSave/deleteAll', config)

/**
 * @name 资源开通订购 -- 删除清单
 */
export const corporateOrderTemSaveDelete = (config: any) =>
  http.post<any>(WOC + '/corporateOrderTemSave/delete', config)

/**
 * @name 资源开通订购 -- vpc创建
 */
export const vpcCreateBatchDGApi = (config: any) =>
  http.post(WOC + '/vpc/vpcCreateBatchDG', config, { loading: true })

/**
 * @name 对公购物清单 总数
 */
export const getCorporateTemSaveCount = (config: any = {}) =>
  http.post<any>(WOC + '/corporateOrderTemSave/getcount', config)

/**
 * @name 对公购物清单 统计视图
 */
export const resourceOpenCorporateApi = (config: any = {}) =>
  http.post<any>(WOC + '/home/<USER>/resourceOpenCorporate', config)

/**
 * @name 对公vpn-校验资源池是否支持
 */
export const productSpecSupportApi = (config: any = {}) =>
  http.post<any>(WOC + '/productSpecSupport/list', config)

/**
 * 获取云主机详情使用情况
 */
export const getEcsDetailUsage = (id: any, config: any) => {
  return http.get<any>(PERFORMANCE + `/view/vm/${id}/performance`, config)
}

/**
 * @name RDS用户管理 - 获取用户列表
 */
export const getRdsUserPage = (config: any) => http.post<any>(WOC + '/rdsUser/page', config)

/**
 * @name RDS用户管理 - 新增用户
 */
export const createRdsUser = (config: any) => http.post<any>(WOC + '/rdsUser/create', config)

/**
 * @name RDS用户管理 - 修改用户密码
 */
export const updateRdsUser = (config: any) => http.post<any>(WOC + '/rdsUser/update', config)

/**
 * @name RDS用户管理 - 删除用户
 */
export const deleteRdsUser = (config: any) => http.post<any>(WOC + '/rdsUser/delete', config)

/**
 * @name RDS白名单管理 - 获取白名单列表
 */
export const getRdsWhitePage = (config: any) => http.post<any>(WOC + '/rdsWhite/page', config)

/**
 * @name RDS白名单管理 - 新增白名单
 */
export const createRdsWhite = (config: any) => http.post<any>(WOC + '/rdsWhite/create', config)

/**
 * @name RDS白名单管理 - 修改白名单
 */
export const updateRdsWhite = (config: any) => http.post<any>(WOC + '/rdsWhite/update', config)

/**
 * @name RDS白名单管理 - 删除白名单
 */
export const deleteRdsWhite = (config: any) => http.post<any>(WOC + '/rdsWhite/delete', config)

/**
 * @name 裸金属-下载模板
 */
export const downloadPmTemplateApi = (config: any = {}) =>
  http.get<any>(WOC + '/goods/pm/template/download', config, {
    responseType: 'blob',
    loading: true,
  })

/**
 * @name 裸金属-导入
 */
export const importPmApi = (data: any) =>
  http.post<any>(WOC + '/goods/pm/import', data, {
    loading: true,
  })

/**
 * @name 对公退订工单创建
 */
export const dgRecoveryWorkOrderCreate = (config: any) =>
  http.post<any>(WOC + '/dg/recovery/workOrder/create', config)
