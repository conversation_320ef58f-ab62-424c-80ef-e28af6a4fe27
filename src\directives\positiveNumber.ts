import type { Directive } from 'vue'

export const positiveNumber: Directive = {
  mounted(el, binding) {
    const input = el.querySelector('input[type="number"]') || el
    const allowDecimal = binding.value?.allowDecimal !== false

    // 监听输入事件，过滤非正数
    input.addEventListener('input', (e: { target: { value: any } }) => {
      let filtered = e.target.value

      if (allowDecimal) {
        // 允许小数：只允许数字和一个小数点
        filtered = filtered.replace(/[^0-9.]/g, '')

        // 确保只有一个小数点
        const decimalCount = (filtered.match(/\./g) || []).length
        if (decimalCount > 1) {
          filtered = filtered.substring(0, filtered.lastIndexOf('.'))
        }
      } else {
        // 不允许小数：只允许数字
        filtered = filtered.replace(/[^0-9]/g, '')
      }

      // 确保最小值为0（移除前导0，但保留0本身）
      if (filtered.startsWith('0') && filtered.length > 1 && filtered[1] !== '.') {
        filtered = filtered.replace(/^0+/, '')
      }

      e.target.value = filtered
    })

    // 失去焦点时确保值 >= 0
    input.addEventListener('blur', (e: { target: { value: string } }) => {
      if (e.target.value === '' || isNaN(parseFloat(e.target.value))) {
        e.target.value = '0'
      } else if (parseFloat(e.target.value) < 0) {
        e.target.value = '0'
      }
    })
  },
}

export default positiveNumber

/*

<!-- 只允许正数（允许小数） -->
<el-input
  type="number"
  v-model="value"
  v-disable-number-wheel
  v-positive-number
/>

<!-- 只允许整数 -->
<el-input
  type="number"
  v-model="integerValue"
  v-disable-number-wheel
  v-positive-number="{ allowDecimal: false }"
/>

*/
