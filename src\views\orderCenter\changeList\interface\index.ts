import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'

export type ChangeType =
  | 'ecs'
  | 'gcs'
  | 'evs'
  | 'obs'
  | 'slb'
  | 'nat'
  | 'eip'
  | 'mysql'
  | 'redis'
  | 'rdsMysql'

export type ChangeColumnsType<T = any> = {
  [key in ChangeType]: ColumnProps<T>[]
}

export type ChangeTabsType = {
  label: string
  name: ChangeType
  count: number
  list: FormDataType[]
} & FormDataType

export type ChangeProTableType = {
  [key in ChangeType]: ProTableInstance | null
}
