<template>
  <div id="BusinessSystemCreated">
    <!-- <div class="header">
      <div class="header-title">
        <el-icon><InfoFilled /></el-icon>
        <div>业务系统说明</div>
      </div>
      <div class="header-item">1. 一个租户可创建多个业务系统，相关实例需开通在业务系统内。</div>
      <div class="header-item">2. 业务系统下可开通多个不同类型的产品资源。</div>
      <div class="header-item">
        3. 业务系统归开通租户所有，除部门领导外其他部门成员无法查看与操作。
      </div>
    </div> -->
    <div class="body">
      <div class="body-header">
        <div class="body-title">{{ isEdit ? '编辑业务系统' : '创建业务系统' }}</div>
        <div class="body-desc">默认均为必填项</div>
      </div>
      <div class="body-content">
        <sl-form ref="slFormRef" :options="formOptions" :model-value="formData">
          <template #oacBusinessSystemModuleListSlot="{ form, item }">
            <el-form-item
              class="module-item"
              v-for="(moduleItem, index) in form[item.key]"
              :key="index"
              :prop="item.key + '.' + index + '.moduleName'"
              :rules="oacBusinessSystemModuleListRules"
            >
              <div class="module-item-content">
                <el-input v-model="moduleItem.moduleName" />
                <el-icon @click="handleModuleRemove(index)" v-if="form[item.key].length > 1">
                  <RemoveFilled />
                </el-icon>
                <el-icon @click="handleModuleAdd" v-if="form[item.key].length == index + 1">
                  <CirclePlusFilled />
                </el-icon>
              </div>
            </el-form-item>
          </template>
          <template #manufacturerContactsSuffixSlot v-if="formData.manufacturer">
            <el-tooltip
              effect="dark"
              content="点击后可新增厂家负责人，再次点击改回选择"
              placement="top-start"
            >
              <el-icon @click="handleContactsSuffixSlotClick">
                <RemoveFilled v-if="formData.manufacturerUserUpdate" />
                <CirclePlusFilled v-else />
              </el-icon>
            </el-tooltip>
          </template>
          <template
            #manufacturerEmailSuffixSlot="{ item }"
            v-if="formData.manufacturerContacts && !formData.manufacturerUserUpdate"
          >
            <el-tooltip
              effect="dark"
              content="点击后可编辑厂家负责人邮箱，再次点击恢复"
              placement="top-start"
            >
              <el-icon @click="handleSuffixSlotClick(item.key)">
                <Unlock v-if="formData.manufacturerEmailUpdate" />
                <Lock v-else />
              </el-icon>
            </el-tooltip>
          </template>
          <template
            #manufacturerMobileSuffixSlot="{ item }"
            v-if="formData.manufacturerContacts && !formData.manufacturerUserUpdate"
          >
            <el-tooltip
              effect="dark"
              content="点击后可编辑厂家负责人联系方式，再次点击恢复"
              placement="top-start"
            >
              <el-icon @click="handleSuffixSlotClick(item.key)">
                <Unlock v-if="formData.manufacturerMobileUpdate" />
                <Lock v-else />
              </el-icon>
            </el-tooltip>
          </template>
        </sl-form>
      </div>
    </div>
    <div class="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="danger" @click="handleDelete" v-if="isEdit">删除</el-button>
      <el-button type="primary" @click="submitValidator" :loading="btnLoading">{{
        isEdit ? '保存' : '创建'
      }}</el-button>
    </div>
  </div>
</template>
<script setup lang="ts" name="BusinessSystemCreated">
import { ref, reactive, onMounted, computed } from 'vue'
import { CirclePlusFilled, RemoveFilled, Lock, Unlock } from '@element-plus/icons-vue'
import type { BusinessSystemFormDataType } from '../interface/type'
import slForm from '@/components/form/SlForm.vue'
import { useUserStore } from '@/stores/modules/user'
import { useGlobalDicStore } from '@/stores/modules/dic'
import {
  manuFacturerapi,
  manuFacturerapiiphone,
  busisystemDetail,
} from '@/api/modules/resourecenter'
import {
  businessSystemSubmit,
  businessSystemUpdate,
  businessSystemDelete,
} from '@/api/modules/computingPowerMap'
import SlMessage from '@/components/base/SlMessage'
import { validateNoSpecialChars, validateEmail, validatePhone } from '@/utils/validate'
import { useRefreshToken } from '@/hooks/refreshToken'
import { ElMessageBox } from 'element-plus'

const props = defineProps({
  businessSystemId: {
    type: [Number, String],
    default: undefined,
  },
})

const isEdit = computed(() => !!props.businessSystemId)

const { refresh } = useRefreshToken()
const userStore = useUserStore()
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const slFormRef = ref()
const formData = reactive<BusinessSystemFormDataType>({
  busiSystemName: '', // 业务系统名称
  oacBusinessSystemModuleList: [{ moduleName: '' }], // 业务模块名称集合
  importanceLevelId: '', // 业务系统等级，1-核心 2-重要 3-一般
  firstFieldId: '', // 业务归属类别，1-基础通信类系统 2-增值业务类系统 3-网管支撑类系统 4-业务平台类系统
  lifeCycle: '1', // 生命周期状态，1-工程 2-在网 3-下线，默认为在网
  applicant: userStore.userInfo.userName, // 业务负责人
  department: userStore.userInfo.sysDeptName, // 所属部门
  phone: userStore.userInfo.phone, // 联系电话
  userEmail: userStore.userInfo.userEmail, // 邮箱
  manufacturer: '', // 厂家名称
  instanceId: '', // 厂家实例 ID
  manufacturerUserId: '', // 厂家用户名称 ID，有值表示原有用户，无值表示新增用户
  manufacturerContacts: '', // 厂家负责人名称
  manufacturerShortName: '', // 厂家简称
  manufacturerUserUpdate: false, // true-编辑用户信息 false-不编辑
  manufacturerEmail: '', // 厂家负责人邮箱
  manufacturerEmailUpdate: false, // 是否编辑厂家负责人邮箱
  manufacturerMobile: '', // 厂家负责人联系方式
  manufacturerMobileUpdate: false, // 是否编辑厂家负责人联系方式
})

// 获取厂家相关信息
const optionmanufacturer = ref<any>([])
const getOptionmanufacturer = async () => {
  const { entity } = await manuFacturerapi()
  if (entity && Array.isArray(entity)) {
    optionmanufacturer.value = entity.map((e: any) => ({
      value: e.name,
      label: e.name,
      instanceId: e.instanceId,
    }))
  }
}
getOptionmanufacturer()
// 获取厂家负责人相关信息
const manufacturerContactsOptions = ref<any>([])
const funcManufacturer = async (name: any) => {
  const { entity } = await manuFacturerapiiphone({
    manufacturerName: name,
  })
  if (entity && Array.isArray(entity)) {
    manufacturerContactsOptions.value = entity.map((e: any) => ({
      value: e.name,
      label: e.name,
      iphone: e.CONCAT_NUMBER,
      email: e.CONCAT_MAIL,
      instanceId: e.instanceId,
    }))
  }
}
const oacBusinessSystemModuleListRules = ref([
  { required: true, message: '请输入业务模块名称', trigger: ['blur'] },
  { validator: validateNoSpecialChars, trigger: ['blur'] },
])
const formOptions = reactive([
  {
    groupName: '',
    groupItems: [
      {
        label: '业务名称',
        type: 'input',
        key: 'busiSystemName',
        span: 24,
        props: {
          maxlength: 30,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入业务名称', trigger: ['blur'] },
          { validator: validateNoSpecialChars, trigger: ['blur'] },
        ],
      },
      {
        label: '业务模块名称',
        type: 'slot',
        slotName: 'oacBusinessSystemModuleListSlot',
        key: 'oacBusinessSystemModuleList',
        span: 24,
        required: true,
        props: {
          maxlength: 30,
          showWordLimit: true,
        },
      },
      {
        label: '业务系统等级',
        type: 'select',
        key: 'importanceLevelId',
        options: getDic('importanceLevel'),
        span: 24,
        rules: {
          required: true,
          message: '请选择业务系统等级',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '业务归属类别',
        type: 'select',
        key: 'firstFieldId',
        options: getDic('businessSystemType'),
        span: 24,
        rules: {
          required: true,
          message: '请选择业务归属类别',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '生命周期状态',
        type: isEdit.value ? 'text' : 'select',
        key: 'lifeCycle',
        options: getDic('lifeCycleStates'),
        span: 24,
        props: {
          select: {
            disabled: true,
          },
        },
        rules: isEdit.value
          ? undefined
          : {
              required: true,
              message: '请选择生命周期状态',
              trigger: ['blur', 'change'],
            },
      },
      {
        label: '业务负责人',
        type: isEdit.value ? 'text' : 'input',
        key: 'applicant',
        span: 24,
        props: isEdit.value
          ? undefined
          : {
              disabled: true,
            },
      },
      {
        label: '所属部门',
        type: isEdit.value ? 'text' : 'input',
        key: 'department',
        span: 24,
        props: isEdit.value
          ? undefined
          : {
              disabled: true,
            },
      },
      {
        label: '联系电话',
        type: isEdit.value ? 'text' : 'input',
        key: 'phone',
        span: 24,
        props: isEdit.value
          ? undefined
          : {
              disabled: true,
            },
      },
      {
        label: '邮箱',
        type: isEdit.value ? 'text' : 'input',
        key: 'userEmail',
        span: 24,
        props: isEdit.value
          ? undefined
          : {
              disabled: true,
            },
      },
      {
        label: '业务厂家',
        type: 'select',
        key: 'manufacturer',
        options: optionmanufacturer,
        span: 24,
        onChange: (value: any) => {
          formData.instanceId = ''
          formData.manufacturerContacts = ''
          formData.manufacturerEmail = ''
          formData.manufacturerMobile = ''
          formData.manufacturerUserId = ''
          if (value) {
            funcManufacturer(value.value)
            formData.instanceId = value.instanceId
          } else {
            manufacturerContactsOptions.value = []
          }
        },
        rules: {
          required: true,
          message: '请选择业务厂家',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '业务厂家简称',
        type: 'input',
        key: 'manufacturerShortName',
        span: 24,
        rules: [
          { required: true, message: '请输入业务厂家简称', trigger: ['blur'] },
          {
            pattern: /^[a-zA-Z]+$/,
            message: '业务厂家简称只能输入英文字母',
            trigger: ['blur', 'change'],
          },
        ],
        props: {},
      },
      {
        label: '厂家负责人',
        type: 'select',
        key: 'manufacturerContacts',
        options: manufacturerContactsOptions,
        suffixSlot: true,
        span: 24,
        onChange: (value: any) => {
          formData.manufacturerEmail = value ? value.email : ''
          formData.manufacturerMobile = value ? value.iphone : ''
          formData.manufacturerUserId = value ? value.instanceId : ''
        },
        rules: {
          required: true,
          message: '请选择厂家负责人',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '厂家负责人邮箱',
        type: 'input',
        key: 'manufacturerEmail',
        span: 24,
        suffixSlot: true,
        props: {
          maxlength: 60,
          showWordLimit: true,
          disabled: true,
        },
        rules: [
          { required: true, message: '请输入厂家负责人邮箱', trigger: ['blur', 'change'] },
          { validator: validateEmail, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '联系方式',
        type: 'input',
        key: 'manufacturerMobile',
        span: 24,
        suffixSlot: true,
        props: {
          maxlength: 60,
          showWordLimit: true,
          disabled: true,
        },
        rules: [
          { required: true, message: '请输入联系方式', trigger: ['blur', 'change'] },
          { validator: validatePhone, trigger: ['blur', 'change'] },
        ],
      },
    ],
  },
])
/**
 * 业务模块移除
 * @param index
 */
const handleModuleRemove = (index: number) => {
  formData.oacBusinessSystemModuleList.splice(index, 1)
}

/**
 * 业务模块新增
 */
const handleModuleAdd = () => {
  formData.oacBusinessSystemModuleList.push({
    moduleName: '',
  })
}
/**
 * 触发厂家负责人邮箱、联系方式后缀图标点击事件
 * @param key
 */
const handleSuffixSlotClick = (key: 'manufacturerEmail' | 'manufacturerMobile') => {
  const option = formOptions[0].groupItems.find((i) => i.key === key)
  const user =
    manufacturerContactsOptions.value.find((i: any) => i.value === formData.manufacturerContacts) ||
    {}
  type UpdateKeys = 'manufacturerEmailUpdate' | 'manufacturerMobileUpdate'
  const updateKeys = `${key}Update` as UpdateKeys
  formData[updateKeys] = !formData[updateKeys]
  if (option && option.props) {
    option.props.disabled = !formData[updateKeys]
  }
  if (formData[updateKeys] === false) {
    if (key === 'manufacturerEmail') {
      formData.manufacturerEmail = user.email
    } else if (key === 'manufacturerMobile') {
      formData.manufacturerMobile = user.iphone
    }
  }
}

/**
 * 触发厂家负责人后缀图标点击事件
 */
const handleContactsSuffixSlotClick = () => {
  const option = formOptions[0].groupItems.find((i) => i.key === 'manufacturerContacts')
  formData.manufacturerUserUpdate = !formData.manufacturerUserUpdate
  if (option) {
    option.type = formData.manufacturerUserUpdate ? 'input' : 'select'
  }
  if (formData.manufacturerEmailUpdate !== formData.manufacturerUserUpdate) {
    handleSuffixSlotClick('manufacturerEmail')
  }
  if (formData.manufacturerMobileUpdate !== formData.manufacturerUserUpdate) {
    handleSuffixSlotClick('manufacturerMobile')
  }
}
const btnLoading = ref(false)

// 获取业务系统详情
const getBusinessSystemDetails = async () => {
  if (!props.businessSystemId) return
  const { entity } = await busisystemDetail({ id: props.businessSystemId })
  formData.busiSystemName = entity.systemName
  formData.oacBusinessSystemModuleList = entity.oacBusinessSystemModuleList
  formData.importanceLevelId = entity.importanceLevelId
  formData.firstFieldId = entity.firstFieldId
  formData.lifeCycle = entity.lifeCycleName
  formData.applicant = entity.applyUserName
  formData.department = entity.departmentName
  formData.phone = entity.applyUserMobile
  formData.userEmail = entity.userEmail
  formData.manufacturer = entity.manufacturer
  formData.manufacturerShortName = entity.manufacturerShortName
  formData.instanceId = entity.instanceId
  formData.manufacturerUserId = entity.manufacturerUserId
  formData.manufacturerContacts = entity.manufacturerContacts
  formData.manufacturerEmail = entity.manufacturerEmail
  formData.manufacturerMobile = entity.manufacturerMobile
  if (entity.manufacturer) {
    await funcManufacturer(entity.manufacturer)
  }
}

onMounted(() => {
  if (isEdit.value) {
    getBusinessSystemDetails()
  }
})

/**
 * 最终数据提交
 */
const handleSubmit = async () => {
  if (btnLoading.value) return
  btnLoading.value = true
  const {
    busiSystemName,
    oacBusinessSystemModuleList,
    importanceLevelId,
    firstFieldId,
    lifeCycle,
    manufacturer,
    instanceId,
    manufacturerUserId,
    manufacturerShortName,
    manufacturerContacts,
    manufacturerUserUpdate,
    manufacturerEmail,
    manufacturerEmailUpdate,
    manufacturerMobile,
    manufacturerMobileUpdate,
  } = formData
  const submitData: any = {
    busiSystemName,
    oacBusinessSystemModuleList,
    importanceLevelId,
    firstFieldId,
    lifeCycle,
    manufacturer,
    instanceId,
    manufacturerUserId: manufacturerUserUpdate ? '' : manufacturerUserId,
    manufacturerShortName,
    manufacturerContacts,
    manufacturerUserUpdate: manufacturerEmailUpdate || manufacturerMobileUpdate,
    manufacturerEmail,
    manufacturerMobile,
  }
  if (isEdit.value) {
    submitData.id = props.businessSystemId
  }
  try {
    if (isEdit.value) {
      await businessSystemUpdate(submitData)
    } else {
      await businessSystemSubmit(submitData)
    }

    await refresh()
    SlMessage({
      message: isEdit.value ? '修改成功' : '创建成功',
      type: 'success',
    })
    emit('submit')
  } finally {
    btnLoading.value = false
  }
}

/**
 * 删除业务系统
 */
const handleDelete = () => {
  ElMessageBox.confirm('确认删除该业务系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await businessSystemDelete(props.businessSystemId!)
        await refresh()
        SlMessage({
          message: '删除成功',
          type: 'success',
        })
        emit('submit')
      } catch (error) {
        console.error(error)
      }
    })
    .catch(() => {
      // 用户取消删除操作
    })
}

const emit = defineEmits(['submit', 'close'])

/**
 * 表单数据校验
 */
const submitValidator = async () => {
  if (!slFormRef.value) return
  await slFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      handleSubmit()
    } else {
      console.log('error submit!', fields)
    }
  })
}

const handleClose = () => {
  emit('close')
}
</script>
<style lang="scss" scoped>
#BusinessSystemCreated {
  font-size: 14px;

  .header {
    padding: 20px;
    background-color: #d3e3fc;
    .header-title {
      display: flex;
      align-items: center;
      font-weight: 600;
      margin-bottom: 10px;
      .el-icon {
        color: #0053d9;
        margin-right: 10px;
        font-size: 18px;
      }
    }
    .header-item {
      margin-left: 28px;
      color: #666;
      height: 24px;
      line-height: 24px;
    }
  }
  .body {
    padding: 20px;
    .body-header {
      display: flex;
      align-items: center;
      .body-title {
        font-size: 18px;
        font-weight: 600;
      }
      .body-desc {
        margin-left: 10px;
        color: #999;
      }
    }
    .body-content {
      margin-left: -30px;
      .module-item {
        width: 100%;
        margin-bottom: 20px;
        flex-basis: auto;
        .module-item-content {
          width: 100%;
          display: flex;
          align-items: center;
        }
        &:last-child {
          margin-bottom: 0;
        }
        .el-icon {
          margin-left: 10px;
        }
      }
      .el-icon {
        font-size: 18px;
        cursor: pointer;
      }
    }
  }
  .footer {
    height: 48px;
    margin: 0 8px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: right;
    padding: 0 14px;
  }
}
</style>
