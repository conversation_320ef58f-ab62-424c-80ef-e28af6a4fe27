<template>
  <sl-form
    class="goods-info-form"
    size="small"
    ref="slFormRef"
    :options="goodsInfoOptions"
    :model-value="goods.orderJson"
  >
    <!-- 删除按钮 -->
    <template #globalFormSlot>
      <div @click="handleGoodsDelete" class="goods-del-btn">
        <el-icon><CircleCloseFilled /></el-icon>
      </div>
    </template>
  </sl-form>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { IKafkaModel } from '@/views/resourceCenter/hooks/useGoodsModels'
import type { IGoodsItem } from '@/views/resourceCenter/hooks/useShoppingCarts'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { validateGoodsName } from '@/views/resourceCenter/utils'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { validateEmpty } from '@/utils/validate'
import eventBus from '@/utils/eventBus'
import slForm from '@/components/form/SlForm.vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const props = defineProps<{
  goods: IGoodsItem<IKafkaModel>
}>()
function handleGoodsDelete() {
  eventBus.emit('shoppingCarts:deleteGoods', {
    goods: props.goods,
    isEdit: route.query.orderId ? true : false,
  })
}

const slFormRef = ref()
setRef(props.goods)
function setRef(goods: IGoodsItem<IKafkaModel>) {
  goods.ref = slFormRef
}

const goodsInfoOptions = reactive([
  {
    style: 'margin:0;padding:0;background: rgb(237 245 255);padding-top: 18px;',
    gutter: 40,
    groupItems: [
      {
        label: 'Kafka名称',
        type: 'input',
        key: 'instanceName',
        span: 8,
        props: {
          maxlength: 64,
          showWordLimit: true,
        },
        rules: [
          { required: true, message: '请输入Kafka名称', trigger: ['blur', 'change'] },
          { validator: validateGoodsName, trigger: ['blur', 'change'] },
        ],
      },
      {
        label: '数据流量',
        type: 'inputNumber',
        key: 'dataFlow',
        span: 8,
        rules: {
          required: true,
          message: '请输入数据流量',
          trigger: ['blur', 'change'],
        },
        props: {
          min: 1,
          step: 1,
        },
        suffix: 'M/天',
      },
      {
        label: '分区',
        type: 'inputNumber',
        key: 'partition',
        span: 8,
        props: {
          min: 1,
          step: 1,
        },
        rules: {
          required: true,
          message: '请输入分区数',
          trigger: ['blur', 'change'],
        },
      },
      {
        label: '副本数',
        type: 'inputNumber',
        key: 'replication',
        span: 8,
        required: true,
        props: {
          min: 1,
          step: 1,
        },
        rules: {
          required: true,
          message: '请输入副本数',
          trigger: ['blur', 'change'],
        },
        suffix: '个',
      },
      {
        label: '保留时间',
        type: 'inputNumber',
        key: 'retentionTime',
        props: {
          min: 1,
          step: 1,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请输入保留时间', trigger: ['blur', 'change'] },
        ],
        suffix: '天',
      },
      {
        label: '数据存储总量',
        type: 'inputNumber',
        key: 'dataStorage',
        props: {
          min: 1,
        },
        required: true,
        span: 8,
        rules: [
          { validator: validateEmpty, message: '请输入数据存储总量', trigger: ['blur', 'change'] },
        ],
        suffix: 'GB',
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'time',
        span: 8,
        options: getDic('time'),
        rules: [{ required: true, message: '请选择申请时长', trigger: ['blur', 'change'] }],
      },
    ],
  },
])
</script>
<style scoped>
.goods-del-btn {
  position: absolute;
  top: -7px;
  right: -6px;
  z-index: 10;
  color: var(--el-color-danger);
  font-size: 1.2rem;
  cursor: pointer;
  display: none;
}
.goods-info-form:hover .goods-del-btn {
  display: block;
}

.evs-item {
  width: 100%;
  margin-bottom: 15px;
  .evs-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .evs-icons {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .el-icon {
      cursor: pointer;
    }
  }
}
</style>
