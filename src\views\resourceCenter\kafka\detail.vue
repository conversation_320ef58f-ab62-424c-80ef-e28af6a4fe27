<template>
  <div id="KafkaDetail" class="table-box">
    <sl-page-header
      title="Kafka详情"
      :icon="{
        class: 'page_kafka',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="kafka-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="140"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        >
        </sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getResourceDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  deviceName: '',
  spec: '',
  deviceStatus: '',
  frequency: '',
  dataStorageTotal: '',
  applyTime: '',
  tenantName: '',
  businessSysName: '',
  cloudPlatform: '',
  resourcePoolName: '',
  orderCode: '',
  effectiveTime: '',
  expireTime: '',
  billId: '',
  deviceStatusCn: '',
  applyUserName: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: 'Kafka Topic命名',
        type: 'text',
        key: 'deviceName',
        span: 8,
      },
      {
        label: '数据流量',
        type: 'text',
        key: 'spec',
        span: 8,
      },
      {
        label: '副本',
        type: 'text',
        key: 'deviceStatus',
        span: 8,
      },
      {
        label: '保留时间',
        type: 'text',
        key: 'frequency',
        span: 8,
      },
      {
        label: '数据存储总量',
        type: 'text',
        key: 'dataStorageTotal',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '租户',
        type: 'text',
        key: 'tenantName',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSysName',
        span: 8,
      },
      {
        label: '所属云',
        type: 'text',
        key: 'cloudPlatform',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'resourcePoolName',
        span: 8,
      },
      {
        label: '工单编号',
        type: 'text',
        key: 'orderCode',
        span: 8,
      },
      {
        label: '开通时间',
        type: 'text',
        key: 'effectiveTime',
        span: 8,
      },
      {
        label: '到期时间',
        type: 'text',
        key: 'expireTime',
        span: 8,
      },
      {
        label: '计费号',
        type: 'text',
        key: 'billId',
        span: 8,
      },
      {
        label: '状态',
        type: 'text',
        key: 'deviceStatusCn',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getResourceDetail({
    id: resourceId.value,
    type: 'kafka',
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/kafkaList',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: 0;
}

.kafka-detail-scroll-view {
  height: 100%;
}

.sl-card {
  margin: 8px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}
</style>
