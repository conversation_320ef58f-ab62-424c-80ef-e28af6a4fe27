// import SlLayout from '@/views/SlLayout.vue'
import type { RouteRecordRaw } from 'vue-router'

const approvalCenterRouter: RouteRecordRaw[] = [
  {
    path: '/resArrangementList',
    name: 'resArrangementList',
    component: () => import('@/views/resArrangement/list.vue'),
    meta: {
      title: '产品组合编排',
    },
  },
  {
    path: '/resArrangement',
    name: 'resArrangement',
    component: () => import('@/views/resArrangement/index.vue'),
    meta: {
      title: '验证',
    },
  },
]

export default approvalCenterRouter
