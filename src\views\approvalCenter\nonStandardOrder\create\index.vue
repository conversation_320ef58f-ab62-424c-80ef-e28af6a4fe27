<template>
  <div class="table-box">
    <sl-page-header
      :title="title"
      :icon="{
        class: 'page_SPOderApproval',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="shopping-carts-scroll-view" class="scroll-view">
      <!--步骤条 -->
      <div class="steps-con" v-if="nodeTree">
        <BranchStep :node-tree="nodeTree" :active-id="activeTask" />
      </div>
      <!-- Tabs 组件 -->
      <sl-base-tabs :show-count="false" :tabs="tabs" v-model="activeTab"> </sl-base-tabs>
      <div v-show="['home', 'profile'].includes(activeTab)" class="sl-page-content">
        <div v-show="activeTab == 'home'">
          <BasicInfo :basic-model="basicModel" />
        </div>
        <!-- 资源信息  -->
        <div v-show="activeTab == 'profile'">
          <GoodsInfo ref="goodsInfoRef" :goods-list="goodsListModel" :basic-model="basicModel" />
        </div>
      </div>
    </el-scrollbar>
    <!-- 按钮组件 -->
    <div v-show="['home', 'profile'].includes(activeTab)" class="sl-page-content page-footer">
      <div class="sl-card">
        <sl-button type="primary" style="width: 80px" :api-function="handleSubmit">提 交</sl-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BasicInfo from './basicInfo/basicInfo.vue'
import { useBasicModel } from './model'
import GoodsInfo from './goodsInfo/goodsInfo.vue'
import {
  nonStandardOrderCreate,
  nonStandardOrderCapacityCheck,
  nonStandardOrderNode,
  displayBackNonStandardOrder,
} from '@/api/modules/resourecenter'
import BranchStep from '@/views/approvalCenter/components/BranchStep.vue'
const { basicModel, goodsListModel } = useBasicModel()
const nodeTree = ref(null)
const activeTask = ref('province_gov_leader')
const getNodeTree = async () => {
  const { entity } = await nonStandardOrderNode()
  nodeTree.value = entity
  activeTask.value = entity.task
}
getNodeTree()

/**
 * 根据idHash合并数据，合并后openNum相加
 * @param {Object} entity 原始数据
 * @returns {Object} 合并后的数据
 */
const dataMerge = (entity: Record<string, any>) => {
  // 处理各种资源列表
  const modelLists = [
    'ecsModelList',
    'gcsModelList',
    'bmsModelList',
    'gmsModelList',
    'mysqlModelList',
    'redisModelList',
    'postgreSqlModelList',
  ]

  modelLists.forEach((listName) => {
    if (entity[listName] && entity[listName].length > 0) {
      const mergedMap = new Map()

      // 第一步：根据idHash分组并累加openNum
      entity[listName].forEach((item: Record<string, any>) => {
        if (item.idHash) {
          if (mergedMap.has(item.idHash)) {
            const existing = mergedMap.get(item.idHash)
            existing.openNum = (Number(existing.openNum) || 0) + (Number(item.openNum) || 0)
          } else {
            mergedMap.set(item.idHash, { ...item })
          }
        } else {
          // 如果没有idHash，则保留原样
          mergedMap.set(Math.random().toString(), { ...item })
        }
      })

      // 更新合并后的列表
      entity[listName] = Array.from(mergedMap.values())
    }
  })

  return entity
}

const route = useRoute()
const router = useRouter()
const goodsInfoRef = ref<InstanceType<typeof GoodsInfo>>()
const orderId = route.query.orderId as string
const title = orderId ? '非标工单编辑' : '非标工单创建'
const handleBack = () => {
  router.go(-1)
}

const tabs = [
  { name: 'home', label: '基础信息' },
  { name: 'profile', label: '资源信息' },
]

const activeTab = ref('home') // 默认激活的 tab

const displayBack = async () => {
  if (orderId) {
    let { entity } = await displayBackNonStandardOrder({ workOrderId: orderId })
    console.log(entity)
    // 数据合并
    entity = dataMerge(entity)
    // 基本信息回显
    basicModel.billNo = entity.billId
    basicModel.customerName = entity.customerName
    basicModel.contactPerson = entity.contactPerson
    basicModel.contactPhone = entity.contactPhone
    basicModel.customerManager = entity.customerManager
    basicModel.managerPhone = entity.managerPhone
    basicModel.customerId = entity.customerId
    basicModel.customerCode = entity.customerCode
    basicModel.workOrderTitle = entity.orderTitle
    basicModel.orderDesc = entity.orderDesc
    basicModel.contractCost = entity.contractCost
    basicModel.branchLeader = entity.businessDepartLeaderId
    basicModel.branchLeaderName = entity.businessDepartLeaderName

    // 文件回显
    if (entity.resourceApplyFile && entity.resourceApplyFile.length > 0) {
      basicModel.files = entity.resourceApplyFile.map((file: any) => ({
        id: file.fileId,
        targetName: file.targetName,
        fileName: file.fileName,
        orderFileType: file.fileType,
      }))
    }

    // 资源信息回显
    const goodsList: any[] = []

    // ECS回显
    if (entity.ecsModelList && entity.ecsModelList.length > 0) {
      entity.ecsModelList.forEach((ecs: any) => {
        goodsList.push({
          productType: 'ecs',
          openType: entity.offlineOpen ? 'offline' : 'online',
          azName: ecs.azName,
          azCode: ecs.azCode,
          azId: ecs.azId,
          resourcePoolId: entity.offlineOpen ? ecs.regionName : ecs.regionId,
          regionCode: ecs.regionCode,
          resourcePoolName: ecs.regionName,
          instanceName: ecs.originName,
          catalogueDomainCode: ecs.catalogueDomainCode,
          catalogueDomainName: ecs.catalogueDomainName,
          domainCode: ecs.domainCode,
          domainName: ecs.domainName,
          imageOs: [ecs.imageOs, ecs.imageVersion],
          ecs: entity.offlineOpen
            ? ecs.flavorName.split('C').map((ele: string) => Number(ele.replace(/GB/g, '')))
            : ecs.flavorName,
          sysDisk: [ecs.sysDiskType, ecs.sysDiskSize],
          isMountEvs: ecs.mountDataDisk ? '1' : '0',
          isBindPublicNetworkIp: ecs.bindPublicIp ? '1' : '0',
          time: ecs.applyTime,
          numbers: ecs.openNum,
          eipValue:
            ecs.eipModelList && ecs.eipModelList.length > 0 ? ecs.eipModelList[0].bandwidth : null,
          evs: ecs.mountDataDiskList
            ? ecs.mountDataDiskList.map((disk: any) => [disk.sysDiskType, disk.sysDiskSize])
            : [],
        })
      })
    }

    // GCS回显
    if (entity.gcsModelList && entity.gcsModelList.length > 0) {
      entity.gcsModelList.forEach((gcs: any) => {
        const flavorParts =
          entity.offlineOpen && gcs.flavorName
            ? gcs.flavorName.split('C').map((part: string) => part.split('/'))
            : []
        goodsList.push({
          productType: 'gcs',
          openType: entity.offlineOpen ? 'offline' : 'online',
          azName: gcs.azName,
          azCode: gcs.azCode,
          azId: gcs.azId,
          resourcePoolId: entity.offlineOpen ? gcs.regionName : gcs.regionId,
          regionCode: gcs.regionCode,
          resourcePoolName: gcs.regionName,
          instanceName: gcs.originName,
          catalogueDomainCode: gcs.catalogueDomainCode,
          catalogueDomainName: gcs.catalogueDomainName,
          domainCode: gcs.domainCode,
          domainName: gcs.domainName,
          imageOs: [gcs.imageOs, gcs.imageVersion],
          gcs: entity.offlineOpen
            ? [
                Number(flavorParts[0][0]),
                Number(flavorParts[1][0].replace('GB', '')),
                flavorParts[1][1],
              ]
            : gcs.flavorName,
          sysDisk: [gcs.sysDiskType, gcs.sysDiskSize],
          isMountEvs: gcs.mountDataDisk ? '1' : '0',
          isBindPublicNetworkIp: gcs.bindPublicIp ? '1' : '0',
          time: gcs.applyTime,
          numbers: gcs.openNum,
          eipValue:
            gcs.eipModelList && gcs.eipModelList.length > 0 ? gcs.eipModelList[0].bandwidth : null,
          evs: gcs.mountDataDiskList
            ? gcs.mountDataDiskList.map((disk: any) => [disk.sysDiskType, disk.sysDiskSize])
            : [],
        })
      })
    }

    // BMS回显
    if (entity.bmsModelList && entity.bmsModelList.length > 0) {
      entity.bmsModelList.forEach((bms: any) => {
        const flavorParts = bms.flavorName
          ? bms.flavorName.split('C').map((ele: string) => Number(ele.replace('GB', '')))
          : [0, 0]
        goodsList.push({
          productType: 'bms',
          openType: entity.offlineOpen ? 'offline' : 'online',
          resourcePoolName: bms.regionName,
          instanceName: bms.originName,
          imageOs: [bms.imageOs, bms.imageVersion],
          bms: flavorParts,
          sysDisk: [bms.sysDiskType, bms.sysDiskSize],
          isMountEvs: bms.mountDataDisk ? '1' : '0',
          isBindPublicNetworkIp: bms.bindPublicIp ? '1' : '0',
          time: bms.applyTime,
          numbers: bms.openNum,
          eipValue:
            bms.eipModelList && bms.eipModelList.length > 0 ? bms.eipModelList[0].bandwidth : null,
          evs: bms.mountDataDiskList
            ? bms.mountDataDiskList.map((disk: any) => [disk.sysDiskType, disk.sysDiskSize])
            : [],
        })
      })
    }
    // GMS回显
    if (entity.gmsModelList && entity.gmsModelList.length > 0) {
      entity.gmsModelList.forEach((gms: any) => {
        const flavorParts =
          entity.offlineOpen && gms.flavorName
            ? gms.flavorName.split('C').map((part: string) => part.split('/'))
            : []
        goodsList.push({
          productType: 'gms',
          openType: entity.offlineOpen ? 'offline' : 'online',
          resourcePoolName: gms.regionName,
          instanceName: gms.originName,
          imageOs: [gms.imageOs, gms.imageVersion],
          gms: [
            Number(flavorParts[0][0]),
            Number(flavorParts[1][0].replace('GB', '')),
            flavorParts[1][1],
          ],
          sysDisk: [gms.sysDiskType, gms.sysDiskSize],
          isMountEvs: gms.mountDataDisk ? '1' : '0',
          isBindPublicNetworkIp: gms.bindPublicIp ? '1' : '0',
          time: gms.applyTime,
          numbers: gms.openNum,
          eipValue:
            gms.eipModelList && gms.eipModelList.length > 0 ? gms.eipModelList[0].bandwidth : null,
          evs: gms.mountDataDiskList
            ? gms.mountDataDiskList.map((disk: any) => [disk.sysDiskType, disk.sysDiskSize])
            : [],
        })
      })
    }

    // MySQL回显
    if (entity.mysqlModelList && entity.mysqlModelList.length > 0) {
      entity.mysqlModelList.forEach((mysql: any) => {
        const flavorParts = mysql.flavorName
          ? mysql.flavorName.split('C').map((ele: string) => Number(ele.replace('GB', '')))
          : [0, 0]
        goodsList.push({
          productType: 'mysql',
          openType: entity.offlineOpen ? 'offline' : 'online',
          resourcePoolName: mysql.regionName,
          instanceName: mysql.originName,
          imageOs: [mysql.imageOs, mysql.imageVersion],
          mysql: flavorParts,
          sysDisk: [mysql.sysDiskType, mysql.sysDiskSize],
          isMountEvs: mysql.mountDataDisk ? '1' : '0',
          isBindPublicNetworkIp: mysql.bindPublicIp ? '1' : '0',
          eipValue:
            mysql.eipModelList && mysql.eipModelList.length > 0
              ? mysql.eipModelList[0].bandwidth
              : null,
          time: mysql.applyTime,
          numbers: mysql.openNum,
          version: mysql.mysqlVersion,
          mysqlType: mysql.mysqlType,
          evs: mysql.mountDataDiskList
            ? mysql.mountDataDiskList.map((disk: any) => [disk.sysDiskType, disk.sysDiskSize])
            : [],
        })
      })
    }

    // Redis回显
    if (entity.redisModelList && entity.redisModelList.length > 0) {
      entity.redisModelList.forEach((redis: any) => {
        const flavorParts = redis.flavorName
          ? redis.flavorName.split('C').map((ele: string) => Number(ele.replace('GB', '')))
          : [0, 0]
        goodsList.push({
          productType: 'redis',
          openType: entity.offlineOpen ? 'offline' : 'online',
          resourcePoolName: redis.regionName,
          instanceName: redis.originName,
          imageOs: [redis.imageOs, redis.imageVersion],
          redis: flavorParts,
          sysDisk: [redis.sysDiskType, redis.sysDiskSize],
          isMountEvs: redis.mountDataDisk ? '1' : '0',
          isBindPublicNetworkIp: redis.bindPublicIp ? '1' : '0',
          eipValue:
            redis.eipModelList && redis.eipModelList.length > 0
              ? redis.eipModelList[0].bandwidth
              : null,
          time: redis.applyTime,
          numbers: redis.openNum,
          version: redis.redisVersion,
          redisType: redis.redisType,
          evs: redis.mountDataDiskList
            ? redis.mountDataDiskList.map((disk: any) => [disk.sysDiskType, disk.sysDiskSize])
            : [],
        })
      })
    }

    // PostgreSQL回显
    if (entity.postgreSqlModelList && entity.postgreSqlModelList.length > 0) {
      entity.postgreSqlModelList.forEach((pg: any) => {
        const flavorParts = pg.flavorName
          ? pg.flavorName.split('C').map((ele: string) => Number(ele.replace('GB', '')))
          : [0, 0]
        goodsList.push({
          productType: 'postgreSql',
          openType: entity.offlineOpen ? 'offline' : 'online',
          resourcePoolName: pg.regionName,
          instanceName: pg.originName,
          imageOs: [pg.imageOs, pg.imageVersion],
          postgreSql: flavorParts,
          sysDisk: [pg.sysDiskType, pg.sysDiskSize],
          isMountEvs: pg.mountDataDisk ? '1' : '0',
          isBindPublicNetworkIp: pg.bindPublicIp ? '1' : '0',
          eipValue:
            pg.eipModelList && pg.eipModelList.length > 0 ? pg.eipModelList[0].bandwidth : null,
          time: pg.applyTime,
          numbers: pg.openNum,
          version: pg.postgreSqlVersion,
          postgreSqlType: pg.postgreSqlType,
          evs: pg.mountDataDiskList
            ? pg.mountDataDiskList.map((disk: any) => [disk.sysDiskType, disk.sysDiskSize])
            : [],
        })
      })
    }

    // 将所有回显的资源信息添加到goodsListModel
    if (goodsList.length > 0) {
      goodsListModel.length = 0 // 清空原有数据
      goodsList.forEach((item) => goodsListModel.push(item))
    }
  }
}
displayBack()

const prepareParams = () => {
  const params: any = {
    id: orderId,
    billId: basicModel.billNo,
    customerName: basicModel.customerName,
    contactPerson: basicModel.contactPerson,
    contactPhone: basicModel.contactPhone,
    customerManager: basicModel.customerManager,
    managerPhone: basicModel.managerPhone,
    customerCode: basicModel.customerCode,
    customerId: basicModel.customerId,
    orderTitle: basicModel.workOrderTitle,
    orderDesc: basicModel.orderDesc,
    contractCost: basicModel.contractCost,
    offlineOpen: goodsListModel[0]?.openType === 'offline',
    businessDepartLeaderName: basicModel.branchLeaderName,
    businessDepartLeaderId: basicModel.branchLeader,
    ecsModelList: [],
    gcsModelList: [],
    bmsModelList: [],
    gmsModelList: [],
    mysqlModelList: [],
    redisModelList: [],
    postgreSqlModelList: [],
    resourceApplyFiles: basicModel.files.map((e: any) => {
      return {
        orderFileType: e.orderFileType,
        fileId: e.id,
        ...e,
      }
    }),
  }
  goodsListModel.forEach((item) => {
    switch (item.productType) {
      case 'ecs': {
        const offline = item.openType === 'offline'
        const temp: any = {
          openType: item.openType,
          productType: item.productType,
          azName: item.azName,
          azCode: item.azCode,
          azId: item.azId,
          regionId: offline ? '' : item.resourcePoolId,
          regionCode: offline ? '' : item.regionCode,
          regionName: offline ? item.resourcePoolId : item.resourcePoolName,
          vmName: item.instanceName,
          catalogueDomainCode: item.catalogueDomainCode,
          catalogueDomainName: item.catalogueDomainName,
          domainCode: item.domainCode,
          domainName: item.domainName,
          imageOs: item.imageOs[0],
          imageVersion: item.imageOs[1],
          flavorName: item.openType === 'online' ? item.ecs : `${item.ecs[0]}C${item.ecs[1]}GB`,
          sysDiskType: item.sysDisk[0],
          sysDiskSize: item.sysDisk[1],
          mountDataDisk: item.isMountEvs === '1',
          bindPublicIp: item.isBindPublicNetworkIp === '1',
          applyTime: item.time,
          openNum: item.numbers,
          ...(item.isBindPublicNetworkIp === '1'
            ? {
                eipModelList: [
                  {
                    bandwidth: item.eipValue,
                  },
                ],
              }
            : {}),
          mountDataDiskList: item.evs.map((item) => {
            return {
              sysDiskType: item[0],
              sysDiskSize: item[1],
            }
          }),
        }
        params.ecsModelList.push(temp)
        break
      }

      case 'gcs': {
        const offline = item.openType === 'offline'
        const temp: any = {
          openType: item.openType,
          productType: item.productType,
          azName: item.azName,
          azCode: item.azCode,
          azId: item.azId,
          regionId: offline ? '' : item.resourcePoolId,
          regionCode: offline ? '' : item.regionCode,
          regionName: offline ? item.resourcePoolId : item.resourcePoolName,
          vmName: item.instanceName,
          catalogueDomainCode: item.catalogueDomainCode,
          catalogueDomainName: item.catalogueDomainName,
          domainCode: item.domainCode,
          domainName: item.domainName,
          imageOs: item.imageOs[0],
          imageVersion: item.imageOs[1],
          flavorName:
            item.openType === 'online'
              ? item.gcs
              : `${item.gcs[0]}C${item.gcs[1]}GB/${item.gcs[2]}`,
          sysDiskType: item.sysDisk[0],
          sysDiskSize: item.sysDisk[1],
          mountDataDisk: item.isMountEvs === '1',
          bindPublicIp: item.isBindPublicNetworkIp === '1',
          applyTime: item.time,
          openNum: item.numbers,
          ...(item.isBindPublicNetworkIp === '1'
            ? {
                eipModelList: [
                  {
                    bandwidth: item.eipValue,
                  },
                ],
              }
            : {}),
          mountDataDiskList: item.evs.map((item) => {
            return {
              sysDiskType: item[0],
              sysDiskSize: item[1],
            }
          }),
        }
        params.gcsModelList.push(temp)
        break
      }
      case 'bms': {
        const temp: any = {
          openType: item.openType,
          productType: item.productType,
          regionName: item.resourcePoolName,
          bmsName: item.instanceName,
          imageOs: item.imageOs[0],
          imageVersion: item.imageOs[1],
          flavorName: `${item.bms[0]}C${item.bms[1]}GB`,
          sysDiskType: item.sysDisk[0],
          sysDiskSize: item.sysDisk[1],
          mountDataDisk: item.isMountEvs === '1',
          bindPublicIp: item.isBindPublicNetworkIp === '1',
          applyTime: item.time,
          openNum: item.numbers,
          ...(item.isBindPublicNetworkIp === '1'
            ? {
                eipModelList: [
                  {
                    bandwidth: item.eipValue,
                  },
                ],
              }
            : {}),
          mountDataDiskList: item.evs.map((item) => {
            return {
              sysDiskType: item[0],
              sysDiskSize: item[1],
            }
          }),
        }
        params.bmsModelList.push(temp)
        break
      }
      case 'gms': {
        const temp: any = {
          openType: item.openType,
          productType: item.productType,
          regionName: item.resourcePoolName,
          gmsName: item.instanceName,
          imageOs: item.imageOs[0],
          imageVersion: item.imageOs[1],
          flavorName: `${item.gms[0]}C${item.gms[1]}GB/${item.gms[2]}`,
          sysDiskType: item.sysDisk[0],
          sysDiskSize: item.sysDisk[1],
          mountDataDisk: item.isMountEvs === '1',
          bindPublicIp: item.isBindPublicNetworkIp === '1',
          applyTime: item.time,
          openNum: item.numbers,
          ...(item.isBindPublicNetworkIp === '1'
            ? {
                eipModelList: [
                  {
                    bandwidth: item.eipValue,
                  },
                ],
              }
            : {}),
          mountDataDiskList: item.evs.map((item) => {
            return {
              sysDiskType: item[0],
              sysDiskSize: item[1],
            }
          }),
        }
        params.gmsModelList.push(temp)
        break
      }
      case 'mysql': {
        const temp: any = {
          openType: item.openType,
          productType: item.productType,
          regionName: item.resourcePoolName,
          mysqlName: item.instanceName,
          imageOs: item.imageOs[0],
          imageVersion: item.imageOs[1],
          flavorName: `${item.mysql[0]}C${item.mysql[1]}GB`,
          sysDiskType: item.sysDisk[0],
          sysDiskSize: item.sysDisk[1],
          mountDataDisk: item.isMountEvs === '1',
          bindPublicIp: item.isBindPublicNetworkIp === '1',
          applyTime: item.time,
          openNum: item.numbers,
          mysqlVersion: item.version,
          mysqlType: item.mysqlType,
          ...(item.isBindPublicNetworkIp === '1'
            ? {
                eipModelList: [
                  {
                    bandwidth: item.eipValue,
                  },
                ],
              }
            : {}),
          mountDataDiskList: item.evs.map((item) => {
            return {
              sysDiskType: item[0],
              sysDiskSize: item[1],
            }
          }),
        }
        params.mysqlModelList.push(temp)
        break
      }
      case 'redis': {
        const temp: any = {
          openType: item.openType,
          productType: item.productType,
          regionName: item.resourcePoolName,
          redisName: item.instanceName,
          imageOs: item.imageOs[0],
          imageVersion: item.imageOs[1],
          flavorName: `${item.redis[0]}C${item.redis[1]}GB`,
          sysDiskType: item.sysDisk[0],
          sysDiskSize: item.sysDisk[1],
          mountDataDisk: item.isMountEvs === '1',
          bindPublicIp: item.isBindPublicNetworkIp === '1',
          applyTime: item.time,
          openNum: item.numbers,
          redisVersion: item.version,
          redisType: item.redisType,
          ...(item.isBindPublicNetworkIp === '1'
            ? {
                eipModelList: [
                  {
                    bandwidth: item.eipValue,
                  },
                ],
              }
            : {}),
          mountDataDiskList: item.evs.map((item) => {
            return {
              sysDiskType: item[0],
              sysDiskSize: item[1],
            }
          }),
        }
        params.redisModelList.push(temp)
        break
      }
      case 'postgreSql': {
        const temp: any = {
          openType: item.openType,
          productType: item.productType,
          regionName: item.resourcePoolName,
          postgreSqlName: item.instanceName,
          imageOs: item.imageOs[0],
          imageVersion: item.imageOs[1],
          flavorName: `${item.postgreSql[0]}C${item.postgreSql[1]}GB`,
          sysDiskType: item.sysDisk[0],
          sysDiskSize: item.sysDisk[1],
          mountDataDisk: item.isMountEvs === '1',
          bindPublicIp: item.isBindPublicNetworkIp === '1',
          applyTime: item.time,
          openNum: item.numbers,
          postgreSqlVersion: item.version,
          postgreSqlType: item.postgreSqlType,
          ...(item.isBindPublicNetworkIp === '1'
            ? {
                eipModelList: [
                  {
                    bandwidth: item.eipValue,
                  },
                ],
              }
            : {}),
          mountDataDiskList: item.evs.map((item) => {
            return {
              sysDiskType: item[0],
              sysDiskSize: item[1],
            }
          }),
        }
        params.postgreSqlModelList.push(temp)
        break
      }
    }
  })
  return params
}

const handleSubmit = async () => {
  // 前端表单校验
  const promiseList = [
    ...goodsListModel.map((item) => item.ref?.validate()),
    basicModel.ref?.validate(),
    goodsInfoRef.value?.validate(),
  ]
  const validate = await Promise.all(promiseList)
  if (validate.some((item) => !item)) {
    ElMessage.error('请检查必填项')
    return
  }
  if (!basicModel.customerName) {
    ElMessage.error('请点击搜索获取客户名称')
    return
  }
  // 参数准备
  const params = prepareParams()
  const goods = [
    ...params.ecsModelList,
    ...params.gcsModelList,
    ...params.bmsModelList,
    ...params.gmsModelList,
    ...params.mysqlModelList,
    ...params.redisModelList,
    ...params.postgreSqlModelList,
  ]
  if (goods.length === 0) {
    ElMessage.error('请添加资源信息')
    return
  }
  // 校验所有产品openType 一致
  if (goods.some((item) => item.openType !== goodsListModel[0].openType)) {
    ElMessage.error('不支持同时开通线上线下产品')
    return
  }
  // 线上开通-后端校验
  if (goodsListModel[0].openType === 'online') {
    try {
      await nonStandardOrderCapacityCheck(params, false)
    } catch (error: any) {
      ElMessage.error(error.message)
      return
    }
  }
  // 校验成功提交
  await nonStandardOrderCreate(params)
  ElMessage.success('提交成功')
  handleBack()
}
</script>

<style lang="scss" scoped>
.steps-con {
  background: #fff;
  padding: 20px;
  height: 230px;
  font-size: 12px !important;
}
.page-footer {
  text-align: right;
  padding-top: 2px;
  padding-bottom: 0;
}
</style>
