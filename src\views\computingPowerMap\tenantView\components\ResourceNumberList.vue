<template>
  <div id="ResourceNumberList" :class="list.length > 3 ? 'flex-space-between' : ''">
    <div class="resource" v-for="item in list" :key="item.key">
      <div class="resource-header">
        <img :class="getImageClass(item.key)" :src="getImageSrc(item.key)" :alt="item.name" />
        <span>{{ item.name }}</span>
        <i
          class="iconfont cart"
          @click.stop="handleCartAdd(item.key)"
          v-if="!['vpc', 'network', 'cloudport'].includes(item.key)"
          v-permission="'AddToCart'"
        ></i>
      </div>
      <div class="resource-body">
        <div class="resource-amount" @click="handleRouterGo(item.key)">
          <span>{{ item.data?.amount }}</span>
          <span class="unit">{{ item.unit }}</span>
        </div>
        <div class="resource-ratio" :class="item.data?.diffAmount! >= 0 ? 'increase' : 'decrease'">
          {{ item.data?.ratio }}
        </div>
      </div>
      <div class="resource-footer">
        本月{{ item.data?.diffAmount! >= 0 ? '增加' : '减少' }}
        <span :class="item.data?.diffAmount! >= 0 ? 'increase' : 'decrease'">{{
          item.data?.diffAmount! >= 0 ? item.data?.diffAmount : -item.data?.diffAmount!
        }}</span>
        相比上个月
      </div>
    </div>
    <div class="resource empty-placeholder" v-for="n in 4 - (list.length % 4)" :key="n"></div>
  </div>
</template>
<script setup lang="ts" name="ResourceNumberList">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { shoppingCartCreate } from '@/api/modules/resourecenter'
import SlMessage from '@/components/base/SlMessage'
import eventBus from '@/utils/eventBus'

// 动态导入图片
import iconEcs from '@/assets/images/overview/icon_ecs.png'
import iconGcs from '@/assets/images/overview/icon_gcs.png'
import iconEvs from '@/assets/images/overview/icon_evs.png'
import iconObs from '@/assets/images/overview/icon_obs.png'
import iconNat from '@/assets/images/overview/icon_nat.png'
import iconSlb from '@/assets/images/overview/icon_slb.png'
import iconEip from '@/assets/images/overview/icon_eip.png'
import iconVpc from '@/assets/images/overview/icon_vpc.png'
import iconNetwork from '@/assets/images/overview/icon_network.png'
import iconCq from '@/assets/images/overview/icon_cq.png'
import iconMysql from '@/assets/images/overview/icon_mysql.png'
import iconRedis from '@/assets/images/overview/icon_redis.png'
import iconCloudport from '@/assets/images/overview/icon_cloudport.png'
import iconBackup from '@/assets/images/overview/icon_backup.png'
import iconVpn from '@/assets/images/overview/icon_vpn.png'
import iconNas from '@/assets/images/overview/icon_nas.png'
import iconPm from '@/assets/images/overview/icon_pm.png'
import iconKafka from '@/assets/images/overview/icon_kafka.png'
import iconEs from '@/assets/images/overview/icon_es.png'
import iconFlink from '@/assets/images/overview/icon_flink.png'
import iconBldRedis from '@/assets/images/overview/icon_bld_redis.png'
import iconPostgresql from '@/assets/images/overview/icon_postgreSql.png'
import iconShareEvs from '@/assets/images/overview/icon_evs.png'
interface ResourceData {
  amount?: number
  diffAmount?: number
  ratio?: string
}
interface ResourceItem {
  key: string
  name: string
  show: boolean
  unit?: string
  data: ResourceData
}
const props = defineProps<{
  resourceList: ResourceItem[]
  selectedSystemIds: number[]
}>()

// 图片映射配置
const imageMap = {
  ecs: iconEcs,
  gcs: iconGcs,
  evs: iconEvs,
  obs: iconObs,
  nat: iconNat,
  slb: iconSlb,
  eip: iconEip,
  vpc: iconVpc,
  network: iconNetwork,
  cq: iconCq,
  mysql: iconMysql,
  redis: iconRedis,
  cloudport: iconCloudport,
  backup: iconBackup,
  vpn: iconVpn,
  nas: iconNas,
  pm: iconPm,
  kafka: iconKafka,
  es: iconEs,
  flink: iconFlink,
  bldRedis: iconBldRedis,
  postgreSql: iconPostgresql,
  shareEvs: iconShareEvs,
}

// 需要特殊宽度的图标
const specialWidthIcons = ['ecs', 'gcs', 'obs', 'network', 'pm', 'kafka', 'es', 'flink', 'bldRedis']

// 已选业务系统列表
const list = computed(() => {
  return props.resourceList.filter((item) => item.show)
})

// 路由跳转
const router = useRouter()
/**
 * 跳转至对应资源中心页面
 * @param key
 */
const handleRouterGo = (key: string) => {
  const ids = props.selectedSystemIds
  if (ids.length <= 50) {
    router.push(`/${key}List?businessSysIds=${ids.join(',')}`)
  } else {
    router.push(`/${key}List`)
  }
}

const handleCartAdd = async (key: string) => {
  const entity = await shoppingCartCreate({
    [`${key}List`]: [
      {
        goodsType: key,
        orderJson: {},
      },
    ],
  })
  if (entity.code == 200) {
    SlMessage({
      message: '已加入到购物车',
      type: 'success',
    })
    eventBus.emit('shoppingCarts:updateCount')
    // 触发全局更新购物车商品数量
  } else {
    SlMessage.error('提交失败')
  }
}

// 获取图片CSS类名
const getImageClass = (key: string) => {
  return specialWidthIcons.includes(key) ? 'width-40' : ''
}

// 获取图片路径
const getImageSrc = (key: string) => {
  return imageMap[key as keyof typeof imageMap] || iconEcs
}
</script>
<style lang="scss" scoped>
#ResourceNumberList {
  display: flex;
  flex-wrap: wrap;
  padding: 20px 10px;
  &.flex-space-between {
    justify-content: space-between;
  }
  .resource {
    width: 22%;
    margin-bottom: 20px;
    .resource-header {
      height: 50px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      background-color: #f3f4f9;
      img {
        width: 50px;
        height: 50px;
      }
      & > span {
        width: 100%;
        margin-left: 10px;
      }
      .cart {
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--el-color-primary);
        cursor: pointer;
      }
      .width-40 {
        width: 40px;
        height: 40px;
        margin-left: 5px;
        margin-right: 5px;
      }
      .width-30 {
        width: 30px;
        height: 30px;
        margin-left: 5px;
        margin-right: 5px;
      }
    }
    .resource-body {
      height: 50px;
      padding: 10px;
      padding-bottom: 0;
      display: flex;
      align-items: center;
      .resource-amount {
        font-size: 28px;
        cursor: pointer;
        .unit {
          font-size: 20px;
        }
      }
      .resource-ratio {
        padding: 0 5px;
        height: 24px;
        margin-left: 10px;
        border-radius: 10px;
        font-size: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        &.increase {
          color: var(--el-color-primary);
          background-color: #ecf2fd;
        }
        &.decrease {
          color: #fcb72e;
          background-color: #fff1dd;
        }
      }
    }
    .resource-footer {
      padding: 0 10px;
      font-size: 14px;
      .increase {
        color: #70cda8;
      }
      .decrease {
        color: #fcb72e;
      }
    }
  }
}
</style>
