<template>
  <div class="bottom-dialog-container">
    <!-- <img @click="closeBottomDialog" src="/images/computingPower/comPowerIconClose.png" alt="" /> -->
    <div class="bottom-dialog-content">
      <div class="bottom-dialog-main">
        <div class="bottom-dialog-title">
          <div>{{ chartTitle }}</div>
          <div>
            <img src="/images/computingPower/comPowerDialogTitleBg.png" alt="" />
          </div>
        </div>

        <!-- 日期选择和快捷按钮区域 -->
        <div class="bottom-dialog-controls">
          <!-- 禁用未来日期 -->
          <el-date-picker
            v-model="selectedDateRange"
            type="date"
            placeholder="请选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            @change="handleDateChange"
          />
          <div class="quick-select-btn">
            <div @click="handleQuickSelect('week')" :class="{ active: quickSelectType === 'week' }">
              周
            </div>
            <div
              @click="handleQuickSelect('month')"
              :class="{ active: quickSelectType === 'month' }"
            >
              月
            </div>
          </div>
        </div>

        <!-- 折线图区域 -->
        <div class="bottom-dialog-chart">
          <div class="chart-container" ref="chartContainer"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick, toRefs, computed } from 'vue'

import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { getResourceUsageDetailApi } from '@/api/modules/comPowerCenter'

// 注册 ECharts 组件
echarts.use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

const props = defineProps<{
  type: string
  requestParams: any
}>()

const { type, requestParams } = toRefs(props)

// 日期相关状态
const selectedDateRange = ref<string>()
const quickSelectType = ref<'week' | 'month' | ''>('')

// 计算属性
const chartTitle = computed(() => {
  return type.value === 'memory' ? '内存' : 'VCpu'
})

// 图表实例
const chartContainer = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

// 定义需要发出的事件类型
// const emit = defineEmits(['close'])

// 日期格式化函数
const formatDate = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 计算日期
const getDateRange = () => {
  if (!selectedDateRange.value) return { startTime: '', endTime: '' }
  //  前一周 或者前一个月
  const startTime = new Date(selectedDateRange.value)
  const endTime = new Date(selectedDateRange.value)
  if (quickSelectType.value === 'week') {
    startTime.setDate(endTime.getDate() - 6)
  } else if (quickSelectType.value === 'month') {
    startTime.setMonth(endTime.getMonth() - 1)
    // startTime.setDate(1)
  } else {
    startTime.setDate(endTime.getDate() - 6)
  }

  return {
    startTime: formatDate(startTime)!,
    endTime: formatDate(endTime)!,
  }
}

// 初始化日期范围（默认为最近一周）
const initializeDateRange = () => {
  const endTime = new Date()

  selectedDateRange.value = formatDate(endTime)
  quickSelectType.value = 'week'
}

// 立即初始化日期范围
initializeDateRange()

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// 快捷选择处理
const handleQuickSelect = (selectType: any) => {
  quickSelectType.value = selectType as 'week' | 'month'

  // 重新获取数据
  getDataApi()
}

// 日期变化处理
const handleDateChange = (value: string[]) => {
  if (value) {
    getDataApi()
  }
}

// 获取数据信息
const getDataApi = () => {
  if (!selectedDateRange.value) {
    return
  }

  const { startTime, endTime } = getDateRange()

  let parms: any = {
    startTime,
    endTime,
    ...requestParams.value,
  }

  getResourceUsageDetailApi(parms)
    .then((res: any) => {
      if (res.code == 200) {
        let { entity } = res
        // 更新图表
        updateChart(entity)
      }
    })
    .catch((error: any) => {
      console.error('获取数据失败:', error)
      // 使用模拟数据
      updateChart(null)
    })
}

// 监听参数变化自动重新请求（可选）
watch(
  () => [requestParams.value, type.value],
  (newParams: any) => {
    console.log('请求参数变化:', newParams)
    getDataApi()
  },
  { deep: true },
)
// 生成模拟数据
const generateMockData = () => {
  if (!selectedDateRange.value) {
    return { dates: [], values: [] }
  }

  const dates = []
  const values = []
  const { startTime: startTimeSt, endTime: endTimeSt } = getDateRange()
  const startTime = new Date(startTimeSt)
  const endTime = new Date(endTimeSt)

  // 计算日期差
  const timeDiff = endTime.getTime() - startTime.getTime()
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))

  for (let i = 0; i <= daysDiff; i++) {
    const currentDate = new Date(startTime)
    currentDate.setDate(startTime.getDate() + i)
    dates.push(
      `${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`,
    )

    values.push(0)
  }

  return { dates, values }
}

// 更新图表数据
const updateChart = (apiData: any) => {
  try {
    if (!chartInstance) {
      console.warn('图表实例未初始化')
      return
    }

    // 如果有API数据，使用API数据，否则使用模拟数据
    const { dates, values } = apiData ? processApiData(apiData) : generateMockData()

    if (!dates || !values || dates.length === 0 || values.length === 0) {
      console.warn('图表数据为空')
      return
    }

    const seriesName = type.value === 'memory' ? '内存利用率' : 'VCpu利用率'
    const unit = '%'

    // 计算数据的最大值和最小值，用于优化Y轴显示
    const maxValue = Math.max(...values)
    const minValue = Math.min(...values)

    // 计算Y轴的最大值和最小值（向上/向下取整到最接近的10的倍数）
    const yAxisMax = Math.min(100, Math.ceil(maxValue / 10) * 10 + 10)
    const yAxisMin = Math.max(0, Math.floor(minValue / 10) * 10 - 10)

    const option: echarts.EChartsCoreOption = {
      backgroundColor: 'transparent',
      legend: {
        text: seriesName,
        left: 'center',
        top: '5%',
        textStyle: {
          color: '#fff',
          fontSize: 14,
        },
      },
      grid: {
        left: '1%',
        right: '2%',
        bottom: '2%',
        top: '10%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00d4ff',
        borderWidth: 1,
        textStyle: {
          color: '#fff',
        },
        formatter: (params: any) => {
          const param = params[0]
          // 显示完整日期（从原始数据中获取）
          const originalDate =
            apiData &&
            ((type.value === 'memory' ? apiData.memData?.days : apiData.cpuData?.days) || [])[
              param.dataIndex
            ]
          const displayDate = originalDate || param.name
          return `${displayDate}<br/>${seriesName}: ${param.value.toFixed(2)}${unit}`
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          interval: 'auto', // 自动计算间隔，避免标签重叠
          rotate: dates.length > 10 ? 30 : 0, // 如果日期太多，旋转标签
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        min: yAxisMin,
        max: yAxisMax,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5a6ef6',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          formatter: `{value}${unit}`,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: seriesName,
          type: 'line',
          data: values,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#00d4ff',
            width: 2,
          },
          itemStyle: {
            color: '#00d4ff',
            borderColor: '#00d4ff',
            borderWidth: 2,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 212, 255, 0.3)',
                },
                {
                  offset: 1,
                  color: 'rgba(0, 212, 255, 0.05)',
                },
              ],
            },
          },
          // markPoint: {
          //   symbol: 'pin',
          //   symbolSize: 40,
          //   data: [
          //     { type: 'max', name: '最大值' },
          //     { type: 'min', name: '最小值' },
          //   ],
          //   label: {
          //     color: '#fff',
          //   },
          //   itemStyle: {
          //     color: '#00d4ff',
          //   },
          // },
        },
      ],
    }

    chartInstance.setOption(option)
  } catch (error) {
    console.error('更新图表时发生错误:', error)
  }
}

// 处理API数据
const processApiData = (apiData: any) => {
  let dates: string[] = []
  let values: number[] = []

  try {
    if (type.value === 'memory') {
      // 处理内存数据
      if (apiData.memData?.days && apiData.memData?.dataUtil?.[0]?.performanceData) {
        dates = apiData.memData.days
        values = apiData.memData.dataUtil[0].performanceData
      }
    } else {
      // 处理vCPU数据 - 修复字段名错误
      if (apiData.cpuData?.days && apiData.cpuData?.dataUtil?.[0]?.performanceData) {
        dates = apiData.cpuData.days
        values = apiData.cpuData.dataUtil[0].performanceData
      }
    }

    // 验证数据有效性
    if (
      !dates ||
      !values ||
      dates.length === 0 ||
      values.length === 0 ||
      dates.length !== values.length
    ) {
      console.warn('API数据格式不正确或为空，使用模拟数据')
      return generateMockData()
    }

    // 格式化日期显示（只显示月-日）
    const formattedDates = dates.map((date) => {
      const dateObj = new Date(date)
      return `${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`
    })

    // 确保数值为数字类型并保留两位小数
    const formattedValues = values.map((value) => {
      const numValue = Number(value)
      return isNaN(numValue) ? 0 : Number(numValue.toFixed(2))
    })

    return {
      dates: formattedDates,
      values: formattedValues,
    }
  } catch (error) {
    console.error('处理API数据时发生错误:', error)
    return generateMockData()
  }
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)

  // 初始化图表数据（日期范围已经在组件初始化时设置）
  getDataApi()
}

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 组件卸载前销毁图表实例
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 关闭当前弹窗
// const closeBottomDialog = () => {
//   emit('close')
// }
</script>
<style scoped lang="scss">
.bottom-dialog-container {
  // position: fixed;
  // left: 10px;
  // bottom: 10px;
  // width: calc(100% - 494px);
  height: 100vh;
  // height: 436px;
  // z-index: 20;
  box-sizing: border-box;
  & > img {
    position: absolute;
    top: 0;
    right: 10px;
    cursor: pointer;
  }
  .bottom-dialog-content {
    // position: absolute;
    // left: 0;
    // bottom: 0;
    width: 100%;
    // height: 400px;
    height: 100%;
    padding: 12px 9px 11px 10px;
    box-shadow: 0px 0px 5px 0px #09155c;
    border-radius: 1px;
    box-sizing: border-box;
    background: linear-gradient(0deg, rgba(112, 152, 219, 0.92) 10%, rgba(8, 34, 97, 0.92)) 90%;
    .bottom-dialog-main {
      position: relative;
      display: flex;
      flex-direction: column;
      top: 0;
      left: 0;
      width: 100%;
      // height: 100%;
      height: 450px;
      padding: 8px 15px;
      border-radius: 1px;
      box-sizing: border-box;
      // background: linear-gradient(0deg, rgba(112, 152, 219, 0.92) 10%, rgba(8, 34, 97, 0.92)) 90%;
    }
    .bottom-dialog-title {
      height: 20px;
      line-height: 20px;
      div {
        display: inline-block;
        font-size: 18px;
        color: #ffffff;
        width: calc(100% - 100px);
        vertical-align: top;
        img {
          width: 100%;
        }
      }
      & > div:nth-child(1) {
        width: 70px;
        margin-right: 12px;
      }
    }

    .bottom-dialog-controls {
      position: absolute;
      top: 30px;
      right: 30px;
      z-index: 9999;
      display: flex;
      align-items: center;
      .quick-select-btn {
        display: flex;
        align-items: center;
        margin-left: 10px;
        div {
          cursor: pointer;
          padding: 3px 25px;
          // border-radius: 5px;
          // 左边圆角

          background-color: #2d518b;
          color: #b4bdcc;

          &:first-child {
            border-radius: 20px 0 0 20px;
          }

          &:last-child {
            border-radius: 0 20px 20px 0;
          }

          &.active {
            background-color: #00d4ff;
            color: #fff;
          }
        }
      }
    }

    .bottom-dialog-chart {
      flex: 1;
      .chart-container {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
