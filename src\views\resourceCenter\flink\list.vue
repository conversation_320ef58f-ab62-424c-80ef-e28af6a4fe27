<template>
  <div class="table-box">
    <sl-page-header
      title="Flink"
      title-line="Flink 一个批处理和流处理结合的统一计算框架，提供数据分发以及并行化计算的流数据处理引擎。"
      :icon="{
        class: 'page_FLINK',
        color: '#0052D9',
        size: '40px',
      }"
    >
    </sl-page-header>
    <div class="resource-tab">
      <div class="btn op" style="margin-top: 8px" v-if="!shouldHideResourceOperations">
        <el-button @click="handleBatchRecycle" type="primary" v-permission="'BatchRecycle'">
          批量回收
        </el-button>
      </div>
      <div class="filter-form-con">
        <sl-form
          class="filter-form"
          :class="{ collapsed: collapsed }"
          ref="formRef"
          :options="formOptions"
          v-model="formModel"
        >
        </sl-form>
      </div>
      <div class="table-layout">
        <DataList
          ref="dataListRef"
          :query-params="queryParams"
          :hide-operations="shouldHideResourceOperations"
        ></DataList>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref } from 'vue'
import { Delete, Search, ArrowDown, ArrowUp, Upload } from '@element-plus/icons-vue'
import SlPageHeader from '@/components/SlPageHeader/index.vue'
import slForm from '@/components/form/SlForm.vue'
import DataList from './components/DataList.vue'
import ConditionFilter from '../conditionFilter.vue'
import useBusiSystemOptions from '../hooks/useBusiSystemOptions'
import { normalizeExportArgs } from '@/views/resourceCenter/utils'
import { useDownload } from '@/hooks/useDownload'
import { resourceExport } from '@/api/modules/resourecenter'
import { useRolePermission } from '../hooks/useRolePermission'
import { useGlobalDicStore } from '@/stores/modules/dic'

const { shouldHideResourceOperations } = useRolePermission()

const { busiSystemOptions } = useBusiSystemOptions()
const globalDic = useGlobalDicStore()
const { getDic } = globalDic

const formRef = ref<any>(null)
const queryParams = ref<any>({ type: 'flink' })

const formModel = reactive({})

function reset() {
  formRef.value!.resetFields()
  queryParams.value = { ...queryParams.value, ...formModel }
}

function search() {
  queryParams.value = { ...queryParams.value, ...formModel }
}

// 是否默认折叠搜索项
const collapsed = ref(true)

const formOptions = reactive([
  {
    style: 'padding: 0',
    groupItems: [
      {
        label: 'Flink名称',
        type: 'input',
        key: 'deviceName',
        span: 8,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        label: '业务系统',
        type: 'select',
        key: 'businessSysId',
        span: 8,
        options: busiSystemOptions,
        disabled: true,
        hidden: false,
        defaultSelect: true,
      },
      {
        span: 8,
        render() {
          return (
            <div style="display: flex;justify-content: flex-end;">
              <el-button type="primary" link>
                <ConditionFilter
                  formModel={formModel}
                  resourceList={formOptions[0].groupItems}
                ></ConditionFilter>
              </el-button>
              <el-button onClick={reset} icon={<Delete />}>
                重置
              </el-button>
              <el-button onClick={search} icon={<Search />} type="primary">
                搜索
              </el-button>
              <el-button
                onClick={() =>
                  useDownload(
                    ...normalizeExportArgs(resourceExport, 'flink', 'Flink.xlsx', {
                      ...formModel,
                    }),
                  )
                }
                icon={<Upload />}
                type="primary"
              >
                导出
              </el-button>
              <el-button
                type="primary"
                link
                class="search-isOpen"
                onClick={() => (collapsed.value = !collapsed.value)}
              >
                {collapsed.value ? '展开' : '折叠'}
                <el-icon class="el-icon--right">
                  {collapsed.value ? <ArrowDown /> : <ArrowUp />}
                </el-icon>
              </el-button>
            </div>
          )
        },
      },
      {
        label: '规格',
        type: 'input',
        key: 'spec',
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '申请时长',
        type: 'select',
        key: 'applyTime',
        options: getDic('time'),
        span: 8,
        disabled: false,
        hidden: true,
      },
      {
        label: '租户',
        type: 'input',
        key: 'tenantName',
        span: 8,
        disabled: false,
        hidden: true,
      },
    ],
  },
])

// 暴露给外部调用的批量回收功能
const dataListRef = ref<any>(null)

const handleBatchRecycle = () => {
  dataListRef.value?.handleBatchRecycle()
}
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sl-page-content {
  // 减去头部的标题高度 - 不减去会超出屏幕
  overflow-x: hidden;
  height: calc(100% - 90px);
}
.header-search-input {
  width: 350px;
}
.table-layout {
  margin: 2px 8px 8px 8px;
  flex: 1;
  overflow: hidden;
}
.btn.op {
  margin: 8px 8px 0 8px;
}
.filter-form-con {
  margin: 8px 8px 0 8px;
  background: #fff;
  border-radius: 8px;
}
.filter-form.collapsed {
  height: 50px;
}
.filter-form {
  height: 110px;
  overflow: hidden;
  transition: height 0.2s;
}
</style>
