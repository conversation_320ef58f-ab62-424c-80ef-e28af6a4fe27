<template>
  <Card class="slicestatus-card">
    <Title title="算力切片状态" />
    <div class="slicestatus-container" v-show="isShow">
      <SlicestatusTop ref="SlicestatusTopRef" />
      <SlicestatusBotton ref="SlicestatusBottonRef" />
    </div>
  </Card>
</template>

<script setup lang="ts">
import Card from './Card.vue'
import Title from './Title.vue'
import SlicestatusTop, { type GpuDataItem } from './SlicestatusTop.vue'
import SlicestatusBotton from './SlicestatusBotton.vue'
import { ref, watchEffect, nextTick } from 'vue'
import { totalPhysicaltApi, totalPhysicalSliceApi } from '@/api/modules/zsMap'

const props = defineProps<{
  areaCode?: string
}>()

const SlicestatusBottonRef = ref<InstanceType<typeof SlicestatusBotton>>()
const SlicestatusTopRef = ref<InstanceType<typeof SlicestatusTop>>()

const colors = ref<string[]>([
  '#4D9BF9',
  '#E91E63',
  '#FF9800',
  '#4CAF50',
  '#9C27B0',
  '#2196F3',
  '#FF5722',
])

const isShow = ref(false)

const initData = async (areaCode?: string) => {
  const { entity }: any = await totalPhysicaltApi({ areaCode })
  isShow.value = entity.length > 0
  const totalPhysicalt: GpuDataItem[] = entity.map((item: any, index: number) => {
    return {
      label: item.modelName,
      value: item.totalCount,
      color: colors.value[index % colors.value.length],
    }
  })

  //  获取 切片数据 组装一下
  const { entity: sliceEntity }: any = await totalPhysicalSliceApi({ areaCode })
  const sliceData = entity.map((item: any) => {
    const sliceItem = sliceEntity?.find((sliceItem: any) => sliceItem.modelName === item.modelName)
    if (sliceItem) {
      let totalCountMax = sliceItem.totalCount ?? 0
      if (sliceItem.modelName === 'T4') {
        totalCountMax = sliceItem.totalCount * 4
      }
      return {
        ...sliceItem,
        totalCountMax,
      }
    }
    return {
      ...item,
      modelName: item.modelName,
      totalCount: 0,
      totalCountMax: 0,
      usedCount: 0,
      remainingCount: 0,
      allocatedCount: 0,
    }
  })

  nextTick(() => {
    if (SlicestatusTopRef.value) {
      SlicestatusTopRef.value.initData(totalPhysicalt)
    }

    if (SlicestatusBottonRef.value) {
      SlicestatusBottonRef.value.initData(sliceData)
    }
  })
}
watchEffect(() => {
  initData(props.areaCode)
})
</script>

<style lang="scss" scoped>
.slicestatus-container {
  padding: 10px;
}
.slicestatus-card {
  height: 100%;
}
</style>
