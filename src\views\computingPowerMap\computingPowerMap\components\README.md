# 动态地图组件使用说明

## 概述

`DashboardCityMap.vue` 组件支持根据传入的参数动态展示不同城市的地图。默认展示浙江省地图，可以通过传入城市参数切换到对应的地市地图。

## 使用方法

### 1. 基本用法（显示浙江省地图）

```vue
<template>
  <DashboardCityMap />
</template>

<script setup>
import DashboardCityMap from './components/DashboardCityMap.vue'
</script>
```

### 2. 显示指定城市地图

```vue
<template>
  <!-- 显示杭州市地图 -->
  <DashboardCityMap city="hangzhou" />

  <!-- 显示温州市地图 -->
  <DashboardCityMap city="wenzhou" />

  <!-- 显示宁波市地图 -->
  <DashboardCityMap city="ningbo" />
</template>

<script setup>
import DashboardCityMap from './components/DashboardCityMap.vue'
</script>
```

### 3. 动态切换城市

```vue
<template>
  <div>
    <select v-model="selectedCity" @change="onCityChange">
      <option value="zhejiang">浙江省</option>
      <option value="hangzhou">杭州市</option>
      <option value="ningbo">宁波市</option>
      <option value="wenzhou">温州市</option>
      <option value="jiaxing">嘉兴市</option>
      <option value="huzhou">湖州市</option>
      <option value="shaoxing">绍兴市</option>
      <option value="jinhua">金华市</option>
      <option value="quzhou">衢州市</option>
      <option value="zhoushan">舟山市</option>
      <option value="taizhou">台州市</option>
      <option value="lishui">丽水市</option>
    </select>

    <DashboardCityMap :city="selectedCity" :key="selectedCity" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import DashboardCityMap from './components/DashboardCityMap.vue'

const selectedCity = ref('zhejiang')

const onCityChange = () => {
  console.log('切换到城市:', selectedCity.value)
}
</script>
```

## 支持的城市参数

| 参数值     | 城市名称 | 地图文件路径                   |
| ---------- | -------- | ------------------------------ |
| `zhejiang` | 浙江省   | `../json/zhejiang.json`        |
| `hangzhou` | 杭州市   | `../json/cities/hangzhou.json` |
| `ningbo`   | 宁波市   | `../json/cities/ningbo.json`   |
| `wenzhou`  | 温州市   | `../json/cities/wenzhou.json`  |
| `jiaxing`  | 嘉兴市   | `../json/cities/jiaxing.json`  |
| `huzhou`   | 湖州市   | `../json/cities/huzhou.json`   |
| `shaoxing` | 绍兴市   | `../json/cities/shaoxing.json` |
| `jinhua`   | 金华市   | `../json/cities/jinhua.json`   |
| `quzhou`   | 衢州市   | `../json/cities/quzhou.json`   |
| `zhoushan` | 舟山市   | `../json/cities/zhoushan.json` |
| `taizhou`  | 台州市   | `../json/cities/taizhou.json`  |
| `lishui`   | 丽水市   | `../json/cities/lishui.json`   |

## Props

### city

- **类型**: `string`
- **默认值**: `'zhejiang'`
- **描述**: 指定要显示的城市地图
- **可选值**: 见上表的参数值列

## 特性

1. **动态加载**: 根据传入的城市参数动态加载对应的地图数据文件
2. **错误处理**: 当城市地图文件加载失败时，自动回退到浙江省地图
3. **响应式**: 组件会根据城市参数的变化重新渲染地图
4. **类型安全**: 使用TypeScript提供完整的类型支持

## 注意事项

1. 确保相应的地图JSON文件存在于 `src/views/computingPowerMap/computingPowerMap/json/cities/` 目录中
2. 组件使用动态导入，初次加载可能稍慢
3. 建议使用 `:key` 属性确保组件在城市切换时正确重新渲染

## 地图文件格式

地图文件应为标准的GeoJSON格式，包含以下结构：

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": {
        "name": "区县名称",
        "adcode": 330102,
        "center": [120.1234, 30.1234],
        "centroid": [120.1234, 30.1234],
        "childrenNum": 0,
        "level": "district",
        "parent": { "adcode": 330100 },
        "subFeatureIndex": 0,
        "acroutes": [100000, 330000, 330100]
      },
      "geometry": {
        "type": "Polygon",
        "coordinates": [[[120.1, 30.1], [120.2, 30.1], ...]]
      }
    }
  ]
}
```
