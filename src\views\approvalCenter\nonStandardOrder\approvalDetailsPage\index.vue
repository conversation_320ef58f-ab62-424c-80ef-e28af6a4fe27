<template>
  <div class="table-box approvalDetailsPage">
    <sl-page-header
      :title="titleMap[opStatus]"
      :icon="{
        class: 'page_SPOderApproval',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回非标工单',
        function: handleBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="shopping-carts-scroll-view" class="scroll-view">
      <!--步骤条 -->
      <div class="steps-con">
        <BranchStep
          v-if="orderDesc.activityTask"
          :node-tree="orderDesc.activityTask!.root"
          :active-id="orderDesc.activityTask.currentTask"
          :gray-mode="orderDesc.orderStatus == 'CLOSE'"
        ></BranchStep>
      </div>
      <!-- Tabs 组件 -->
      <sl-base-tabs :show-count="false" :tabs="tabs" v-model="activeTab" v-if="currentTask">
      </sl-base-tabs>

      <div
        v-show="['home', 'profile'].includes(activeTab)"
        class="sl-page-content"
        v-if="currentTask"
      >
        <BasicInformation
          ref="basicInformationRef"
          v-show="activeTab == 'home'"
          :order-desc="orderDesc"
          :btn-auth="btnAuth"
        />

        <!-- 资源信息  -->
        <ResourceInformation
          ref="resourceInformationRef"
          v-show="activeTab == 'profile'"
          :order-desc="orderDesc"
          :btn-auth="btnAuth"
          v-model:disabled-btn="disabledBtn"
          @refresh="inintData"
        />
      </div>
      <div v-if="activeTab == 'settings'" class="sl-page-content table-main auditTable">
        <!-- 审核日志 -->
        <AuditTable :order-desc="orderDesc" />
      </div>
    </el-scrollbar>

    <!-- 按钮组件 -->
    <div
      v-if="pageStatus"
      v-show="['home', 'profile'].includes(activeTab)"
      class="sl-page-content page-footer"
    >
      <div class="sl-card">
        <RenderBtn />
      </div>
    </div>
    <!-- 审核组件 -->

    <SlAuditDialog
      v-show="visible"
      ref="SlAuditDialogRef"
      v-model:visible="visible"
      back-path="/nonStandardOrder"
      :audit-api="nonStandardOrderAuditApi"
    />
  </div>
</template>

<script setup lang="tsx">
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import BranchStep from '@/views/approvalCenter/components/BranchStep.vue'
import { computed, provide, ref, nextTick } from 'vue'
import {
  getNonStandardOrderDetailApi,
  nonStandardOrderAuditApi,
} from '@/api/modules/approvalCenter'
import { useRoute, useRouter } from 'vue-router'
import BasicInformation from './components/BasicInformation.vue'
import ResourceInformation from './components/ResourceInformation/index.vue'
import AuditTable from './components/AuditTable.vue'
import type { AuditDialogInstance } from '@/components/SlAuditDialog/interface'
import type { NonStandardOrderBtnsType, NonStandardOrderWorkorderType } from '../interface/type'
import useTreeWorkOrder from '@/hooks/useTreeWorkOrder'

let orderDesc = ref<NonStandardOrderWorkorderType>({
  activityTask: undefined,
  productApplyFile: [],
  resourceApplyFile: [],
})

const titleMap = {
  '1': '非标工单审批',
  '2': '非标工单查看',
  '3': '非标工单编辑',
  '4': '非标工单创建',
} as const

type IOpStatus = keyof typeof titleMap

const route = useRoute()
const router = useRouter()
const workOrderId = route.query?.workOrderId
const opStatus = route.query?.opStatus as IOpStatus
provide('workOrderId', workOrderId)
/**
 *  @readonly获取页面状态用来做表单权限控制
 *  */

const pageStatus = ref(opStatus === '1') //1:编辑 0:查看
provide('pageStatus', pageStatus)

// const productTotalCost = ref(0) // 资源信息的总价

let flag = ref(true) //判断是否第一次初始化参数
const inintData = async () => {
  if (!workOrderId) return
  // 获取工单详情
  const res = await getNonStandardOrderDetailApi({ workOrderId: String(workOrderId) })
  if (!res.entity) return
  res.entity.productTotalCost = res.entity.productTotalCost || 0
  res.entity.productApplyFile = res.entity.productApplyFile ?? []
  res.entity.resourceApplyFile = res.entity.resourceApplyFile ?? []
  // 给予默认值
  orderDesc.value = {
    ...res.entity,
  }
  // productTotalCost.value = res.entity.productTotalCost ?? 0
  getWorkOrderNode(orderDesc.value.activityTask!)
  nextTick(() => {
    resourceInformationRef.value?.initData(orderDesc.value, flag.value)
    // basicInformationRef.value?.initData(orderDesc.value)
    flag.value = false
  })
}
inintData()

// 当前节点
const { allTasks, currentTask, historyTasks, getWorkOrderNode } = useTreeWorkOrder()
provide('allTasks', allTasks)
provide('currentTask', currentTask)
provide('historyTasks', historyTasks)

const handleBack = () => {
  router.push('/nonStandardOrder')
}

const tabs = [
  { name: 'home', label: '基础信息' },
  { name: 'profile', label: '资源信息' },
  { name: 'settings', label: '审核日志' },
]
const activeTab = ref('profile') // 默认激活的 tab

// 按钮权限控制
const btnAuth = computed<NonStandardOrderBtnsType>(() => {
  return {
    // 响应方案经理审核
    response_scheme_manager: currentTask.value === 'response_scheme_manager',
    // 信息存档 - h超出  , l低于
    information_archive:
      currentTask.value === 'information_archive_h' ||
      currentTask.value === 'information_archive_l', // 信息存档 - 低于
    // 离线开通 - h超出  , l低于
    offline_open: currentTask.value === 'offline_open_h' || currentTask.value === 'offline_open_l', // 离线开通 - 低于
    // 开通网络 - h超出  , l低于
    network_provisioning:
      currentTask.value === 'network_provisioning_h' ||
      currentTask.value === 'network_provisioning_l', // 开通网络 - 低于
    // 资源开通 - h超出  , l低于
    resource_creation:
      currentTask.value === 'resource_creation_h' || currentTask.value === 'resource_creation_l',

    end: [
      'information_archive_h:autodit end',
      'resource_creation_h:autodit end',
      'information_archive_l:autodit end',
      'resource_creation_l:autodit end',
    ].includes(currentTask.value), //  结束节点
  }
})
provide('btnAuth', btnAuth)
//  表单组件
const resourceInformationRef = ref<InstanceType<typeof ResourceInformation> | null>(null)
const basicInformationRef = ref<InstanceType<typeof BasicInformation> | null>(null)

// ----------------------审核提交-----------------------

async function getAuditNode() {
  //后续别的表单可以根据这个逻辑去获取 添加if语句

  return []
}
// 审核组件
const SlAuditDialogRef = ref<AuditDialogInstance>()
const visible = ref(false)
//校验表单

// 获取提交的参数
async function changeParams(status: string) {
  let params: any = {
    orderId: route.query.workOrderId,
    currentNodeCode: currentTask.value,
    activiteStatus: status === 'pass' ? 1 : 0,
  }
  let fields: any[] = []

  if (status === 'reject') {
    params['nodeCode'] = allTasks.value[0].id
  }
  if (status === 'pass') fields = await getAuditNode()
  params = {
    ...params,
    ...(await resourceInformationRef.value?.submitForm()),
    ...(await basicInformationRef.value?.submitForm()),
  }

  return {
    params,
    fields,
  }
}
/**
 * 审核提交
 * @param status 审核状态
 * @param falg 是否需要弹窗 默认打开
 *  */
const subAudit = async (status: string, falg: boolean = true) => {
  // 1. 校验表单 - 架构审核节点
  if (
    status === 'pass' &&
    (!(await basicInformationRef.value?.validateForm()) ||
      !(await resourceInformationRef.value?.validateForm()))
  ) {
    return '请完善表单信息'
  }
  // 2.获取数据
  const { params, fields } = await changeParams(status)

  // 3.打开弹窗
  visible.value = true
  nextTick(() => {
    SlAuditDialogRef.value?.openDialog(
      {
        params,
        fields,
      },
      falg,
    )
  })
}

// ----------------------提交按钮-----------------------

// 网络开通->vpc
const openNetwork = () => {
  const path = '/vpcForm'
  openVpcOrrNetwork(path)
}
const openVpcOrrNetwork = (path: string) => {
  // form.value
  const query = {
    orderId: route.query.workOrderId,
    opStatus: route.query.opStatus,
    sourceType: 'FB',
  }

  router.push({
    path,
    query,
  })
}

const disabledBtn = ref(false)

// 渲染按钮组件
const RenderBtn = () => {
  const obj = {
    information_archive_l: '归档完成',
    information_archive_h: '归档完成',
    offline_open_l: '开通完成',
    offline_open_h: '开通完成',
    network_provisioning_l: '开通完成',
    network_provisioning_h: '开通完成',
    resource_creation_l: '开通完成',
    resource_creation_h: '开通完成',
  }
  // 1 .查看表单

  return (
    <>
      {!obj[currentTask.value as keyof typeof obj] && (
        <el-button onclick={() => subAudit('reject')}>审批驳回</el-button>
      )}
      {btnAuth.value?.network_provisioning && (
        <el-button type="primary" onclick={openNetwork}>
          开通网络
        </el-button>
      )}
      <el-button disabled={disabledBtn.value} type="primary" onclick={() => subAudit('pass')}>
        {obj[currentTask.value as keyof typeof obj] || '审批通过'}
      </el-button>
    </>
  )
}
</script>

<style lang="scss" scoped>
// 页面通用 标题下面的通用布局

.steps-con {
  background: #fff;
  padding: 20px;
  height: 250px;
  // 横向滚动 滚动条细一些
  overflow-x: auto; // 滚动条细一些
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.2);
  }
  &::-webkit-scrollbar-track {
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
  }
}
.auditTable {
  min-height: 350px;
}
.page-footer {
  text-align: right;
  padding-top: 2px;
  padding-bottom: 0;
}
</style>
