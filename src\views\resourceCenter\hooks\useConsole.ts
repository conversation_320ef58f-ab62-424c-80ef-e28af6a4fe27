import { ElMessage } from 'element-plus'
import { getEcsVncUrl, getVmwareVncUrl } from '@/api/modules/resourecenter'

/**
 * 控制台功能hooks
 */
export function useConsole() {
  // 支持控制台的domain列表
  const consoleDomainCodeList = [
    'plf_prov_nwc_zj_nfvo',
    'plf_prov_moc_zj_h3c',
    'plf_prov_moc_zj_huawei',
    'plf_prov_moc_zj_vmware',
    'plf_prov_moc_zj_inspur',
    'plf_prov_nwc_zj_plf',
    'plf_prov_it_zj_plf',
  ]

  /**
   * 判断是否支持控制台功能
   * @param row 资源行数据
   * @returns 是否支持控制台
   */
  function supportsConsole(row: any): boolean {
    return consoleDomainCodeList.includes(row.domainCode)
  }

  /**
   * 转换H3C的VNC URL格式（WebSocket格式）
   * @param originalUrl 原始URL
   * @returns 转换后的URL
   */
  function convertH3cVncUrl(originalUrl: string): string {
    try {
      const url = new URL(originalUrl)
      const pathname = url.pathname
      const search = url.search

      // 从当前location获取配置
      const currentLocation = window.location
      const protocol = currentLocation.protocol.replace(':', '')
      const domain = currentLocation.hostname
      const port = currentLocation.port || (protocol === 'https' ? '443' : '80')

      // 提取路径部分，如：api/vnc-router/vnc/ecs
      const pathPart = pathname.startsWith('/') ? pathname.substring(1) : pathname

      // 提取查询参数部分，如：instanceId=ecs-qt5tlhtx5moh&auth=xxx
      const queryPart = search.startsWith('?') ? search.substring(1) : search

      // 对查询参数进行URL编码
      const encodedQueryPart = encodeURIComponent(queryPart)

      // 构建path参数：路径 + %3F + 编码后的查询参数
      const pathParam = `${pathPart}%3F${encodedQueryPart}`

      // 构建新的URL
      const newUrl = `${protocol}://${domain}:${port}/novnc/vnc_lite.html?host=${domain}&port=${port}&encrypt=0&path=${pathParam}`

      return newUrl
    } catch (error) {
      console.error('H3C URL转换失败:', error)
      // 如果转换失败，返回原始URL
      return originalUrl
    }
  }

  /**
   * 转换华为的VNC URL格式
   * @param originalUrl 原始URL
   * @returns 转换后的URL
   */
  function convertHuaweiVncUrl(originalUrl: string): string {
    try {
      const url = new URL(originalUrl)

      // 从当前location获取配置
      const currentLocation = window.location
      const protocol = currentLocation.protocol.replace(':', '')
      const domain = currentLocation.hostname
      const port = currentLocation.port || (protocol === 'https' ? '443' : '80')

      // 提取token参数
      const token = url.searchParams.get('token') || ''

      // 提取路径部分，并将vnc_auto.html替换为websockify
      // 例如：/ecs/zjnb-rhbyy-hw-dc-01/vnc_auto.html -> ecs/zjnb-rhbyy-hw-dc-01/websockify
      let pathPart = url.pathname.startsWith('/') ? url.pathname.substring(1) : url.pathname
      pathPart = pathPart
        .replace('/vnc_auto.html', '/websockify')
        .replace('vnc_auto.html', 'websockify')

      // 构建path参数：路径 + %3F + token参数
      const pathParam = `${pathPart}%3Ftoken%3D${token}`

      // 构建新的URL
      const newUrl = `${protocol}://${domain}:${port}/novnc/vnc_lite.html?host=${domain}&port=${port}&encrypt=0&path=${pathParam}`

      return newUrl
    } catch (error) {
      console.error('华为 URL转换失败:', error)
      // 如果转换失败，返回原始URL
      return originalUrl
    }
  }

  /**
   * 打开VMware控制台
   * @param row 资源行数据
   * @param entity 控制台URL信息
   * @param domainCode 域编码
   */
  function openVmwareConsole(row: any, entity: any, domainCode: string) {
    try {
      // 解析entity获取sid和ticket
      const { sid, ticket, hostIp, port, wsUrl } = {
        sid: entity.sid,
        ticket: entity.ticket,
        hostIp: entity.hostIp,
        port: entity.ticketPort,
        wsUrl: entity.wsUrl,
      }

      // 只存储sid和ticket到sessionStorage，供VMware控制台页面使用
      sessionStorage.setItem('deviceId', row.deviceId)
      sessionStorage.setItem('sid', sid)
      sessionStorage.setItem('ticket', ticket)
      sessionStorage.setItem('hostIp', hostIp)
      sessionStorage.setItem('port', port)
      sessionStorage.setItem('wsUrl', wsUrl)

      const type = domainCode === 'plf_prov_nwc_zj_plf' ? '1' : '2'

      // 构建VMware控制台页面URL
      const consoleUrl = `/vmwareConsole?type=${type}`
      let consoleWindow: Window | null = null
      if (type === '1') {
        consoleWindow = window.open(consoleUrl, '_blank')
      } else {
        // 在新窗口中打开VMware控制台
        consoleWindow = window.open(
          consoleUrl,
          '_blank',
          'width=1200,height=800,scrollbars=yes,resizable=yes',
        )
      }

      if (!consoleWindow) {
        ElMessage.error('无法打开控制台窗口，请检查浏览器弹窗设置')
      } else {
        ElMessage.success('正在打开VMware控制台...')
      }
    } catch (error) {
      console.error('打开VMware控制台失败:', error)
      ElMessage.error('打开VMware控制台失败')
    }
  }

  /**
   * 转换浪潮的VNC URL格式
   * @param row 资源行数据
   * @returns 转换后的URL
   */
  function convertInspurVncUrl(row: any): string {
    // 从当前location获取配置
    const currentLocation = window.location
    const protocol = currentLocation.protocol.replace(':', '')
    const domain = currentLocation.hostname
    const port = currentLocation.port || (protocol === 'https' ? '443' : '80')

    const path = `cmp/api/vim/compute-servers/${row.instanceUuid}/exec`

    // 构建新的URL
    const newUrl = `${protocol}://${domain}:${port}/novnc/vnc_lite.html?host=${domain}&port=${port}&encrypt=0&path=${path}`

    return newUrl
  }

  /**
   * 打开控制台的主函数
   * @param row 资源行数据
   */
  async function handleOpenConsole(row: any) {
    try {
      const domainCode = row.domainCode

      if (domainCode === 'plf_prov_moc_zj_inspur') {
        // 浪潮平台直接使用转换后的URL
        const convertedUrl = convertInspurVncUrl(row)
        window.open(convertedUrl, '_blank')
      } else if (
        ['plf_prov_moc_zj_vmware', 'plf_prov_nwc_zj_plf', 'plf_prov_it_zj_plf'].includes(domainCode)
      ) {
        const { entity } = await getVmwareVncUrl({ id: row.deviceId })
        openVmwareConsole(row, entity, domainCode)
      } else {
        // 其他平台需要先获取VNC URL
        const { entity } = await getEcsVncUrl({ ecsId: row.deviceId })
        if (!entity) {
          ElMessage.error('获取控制台地址失败')
          return
        }

        switch (domainCode) {
          case 'plf_prov_nwc_zj_nfvo':
            window.open(entity, '_blank')
            break
          case 'plf_prov_moc_zj_h3c':
            window.open(convertH3cVncUrl(entity), '_blank')
            break
          case 'plf_prov_moc_zj_huawei':
            window.open(convertHuaweiVncUrl(entity), '_blank')
            break
          default:
            ElMessage.error('不支持的控制台类型')
        }
      }
    } catch (error) {
      console.error('打开控制台失败:', error)
      ElMessage.error('打开控制台失败')
    }
  }

  return {
    consoleDomainCodeList,
    supportsConsole,
    handleOpenConsole,
    convertH3cVncUrl,
    convertHuaweiVncUrl,
    openVmwareConsole,
    convertInspurVncUrl,
  }
}
