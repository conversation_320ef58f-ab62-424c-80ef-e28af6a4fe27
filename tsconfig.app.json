{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "components.d.ts", "auto-imports.d.ts", "shims.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": false, "noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["element-plus/global", "vite/client", "pinia-plugin-persistedstate"]}}