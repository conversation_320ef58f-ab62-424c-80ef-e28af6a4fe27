<template>
  <Container>
    <div class="header">
      <Breadcrumb />
    </div>
    <div class="content">
      <div class="content-top">
        <div class="content-top-item left">
          <!-- 集团客户总数 -->
          <div class="top total-customers">
            <div class="label">集团客户总数</div>
            <div class="value">{{ stats.totalCustom }}</div>
          </div>
          <!-- 统计卡片 -->
          <div class="bottom stats-cards">
            <div class="stat-card">
              <div class="card-content">
                <div class="label">订购单数</div>
                <div class="main-number">{{ stats.totalOrder }}</div>
                <div class="sub-info">
                  <span class="sub-label">本月新订单</span>
                  <span class="sub-value">{{ stats.newOrder }}</span>
                </div>
              </div>
            </div>
            <div class="stat-card">
              <div class="card-content">
                <div class="label">产品订购数</div>
                <div class="main-number">{{ stats.totalProduct }}</div>
                <div class="sub-info">
                  <span class="sub-label">本月订购产品</span>
                  <span class="sub-value">{{ stats.newProduct }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="item right">
          <!-- 集团客户概览 -->
          <div class="customer-overview">
            <div class="overview-header" style="margin-bottom: 12px">
              <span class="title">集团客户概览</span>
              <span v-if="customs.length > 0" @click="handleShowCustomersDialog" class="more">
                更多
              </span>
            </div>
            <div class="customer-list">
              <div v-if="customs.length > 0" class="customer-items">
                <div
                  @click="handleShowDetail(customer.customId)"
                  class="customer-item"
                  v-for="(customer, index) in customs"
                  :key="index"
                >
                  <div class="customer-info">
                    <div class="customer-name">
                      <el-tooltip :content="customer.customName" placement="top">
                        <span class="name">{{ customer.customName }}</span>
                      </el-tooltip>
                      <el-icon class="arrow-right"><ArrowRight /></el-icon>
                    </div>
                    <div class="customer-details">
                      <div class="detail-item">
                        <span class="detail-label">企业联系人：</span>
                        <span class="detail-value">{{ customer.contactName }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label">联系电话：</span>
                        <span class="detail-value">{{ customer.contactMobile }}</span>
                      </div>
                    </div>
                    <el-divider style="margin: 0px 0 4px 0" />
                    <div class="customer-stats">
                      <div class="stat-item">
                        <span class="stat-label">总订单数</span>
                        <span class="stat-value">{{ customer.totalOrder }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">本月订单数</span>
                        <span class="stat-value">{{ customer.monthTotalOrder }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">产品总数</span>
                        <span class="stat-value">{{ customer.totalProduct }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="empty-state">
                <div class="empty-content">
                  <div class="empty-text">暂无数据</div>
                  <div class="empty-desc">暂时没有客户数据</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-middle">
        <!-- vCPU利用率排行 -->
        <div class="card-item vcpu-ranking">
          <div class="card-header">
            <h3>vCPU利用率排行</h3>
          </div>
          <div class="ranking-grid">
            <!-- 圆环行 -->
            <div class="row progress-circles-row">
              <div class="col" v-for="(item, index) in vcpuRankingData" :key="`circle-${index}`">
                <div class="progress-circle">
                  <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle
                      cx="30"
                      cy="30"
                      r="20"
                      fill="none"
                      stroke="rgba(0,0,0,0.08)"
                      stroke-width="10"
                    />
                    <circle
                      cx="30"
                      cy="30"
                      r="20"
                      fill="none"
                      stroke="#3662EC"
                      stroke-width="10"
                      stroke-linecap="butt"
                      :stroke-dasharray="circumference"
                      :stroke-dashoffset="circumference - (item.percentage / 100) * circumference"
                      transform="rotate(90 30 30)"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- 百分比行 -->
            <div class="row percentages-row">
              <div
                class="col"
                v-for="(item, index) in vcpuRankingData"
                :key="`percentage-${index}`"
              >
                <div class="percentage-text">{{ item.percentage }}%</div>
              </div>
            </div>

            <!-- 徽章行 -->
            <div class="row badges-row">
              <div class="col" v-for="(item, index) in vcpuRankingData" :key="`badge-${index}`">
                <div class="rank-badge">
                  <img :src="item.rankIcon" :alt="`排名${index + 1}`" />
                </div>
              </div>
            </div>

            <!-- 编号行 -->
            <div class="row numbers-row">
              <div class="col" v-for="(item, index) in vcpuRankingData" :key="`number-${index}`">
                <div class="rank-number">{{ item.rankNumber }}</div>
              </div>
            </div>

            <!-- 水平进度条行 -->
            <div class="row progress-bar-row">
              <div class="progress-container">
                <div class="progress-bar">
                  <div class="progress-fill"></div>
                  <div
                    class="progress-node"
                    v-for="(item, index) in vcpuRankingData"
                    :key="`node-${index}`"
                    :style="{ left: `${index * 25}%` }"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 公司名称行 -->
            <div class="row companies-row">
              <div class="col" v-for="(item, index) in vcpuRankingData" :key="`company-${index}`">
                <el-tooltip :content="item.companyName" placement="top" :show-after="500">
                  <div class="company-name">{{ item.companyName }}</div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>

        <!-- 客户订购数排行 -->
        <div v-if="customerRankingLabels.length > 0" class="card-item customer-ranking">
          <div class="card-header">
            <h3>客户订购数排行</h3>
          </div>
          <div class="chart-container">
            <div class="chart-area">
              <!-- 折线图 -->
              <div class="line-chart-container">
                <img src="/images/accountView/line.png" alt="折线图" class="line-chart-image" />
              </div>

              <!-- 数据标注 -->
              <div class="data-labels">
                <!-- 顶部标注 -->
                <div class="label top-label">
                  <div class="label-content">
                    <div class="red-badge">
                      <img
                        :src="customerRankingLabels[0].redRankIcon"
                        :alt="`排名${customerRankingLabels[0].rank}`"
                      />
                    </div>
                    <div class="number">{{ customerRankingLabels[0].number }}</div>
                    <el-tooltip :content="customerRankingLabels[0].company" placement="top">
                      <div class="company">{{ customerRankingLabels[0].company }}</div>
                    </el-tooltip>
                    <div class="label-line top-line"></div>
                    <div class="progress-node"></div>
                  </div>
                </div>

                <!-- 左侧标注 -->
                <div class="label left-label">
                  <div class="label-content">
                    <div class="red-badge">
                      <img
                        :src="customerRankingLabels[1].redRankIcon"
                        :alt="`排名${customerRankingLabels[1].rank}`"
                      />
                    </div>
                    <div class="number">{{ customerRankingLabels[1].number }}</div>
                    <el-tooltip :content="customerRankingLabels[1].company" placement="top">
                      <div class="company">{{ customerRankingLabels[1].company }}</div>
                    </el-tooltip>
                    <div class="label-line left-line"></div>
                    <div class="progress-node"></div>
                  </div>
                </div>

                <!-- 右侧标注 -->
                <div class="label right-label">
                  <div class="label-content">
                    <div class="red-badge">
                      <img
                        :src="customerRankingLabels[2].redRankIcon"
                        :alt="`排名${customerRankingLabels[2].rank}`"
                      />
                    </div>
                    <div class="number">{{ customerRankingLabels[2].number }}</div>
                    <el-tooltip :content="customerRankingLabels[2].company" placement="top">
                      <div class="company">{{ customerRankingLabels[2].company }}</div>
                    </el-tooltip>
                    <div class="label-line right-line"></div>
                    <div class="progress-node"></div>
                  </div>
                </div>

                <!-- 底部左标注 -->
                <div class="label bottom-left-label">
                  <div class="label-content">
                    <div class="progress-node"></div>
                    <div class="red-badge">
                      <img
                        :src="customerRankingLabels[3].redRankIcon"
                        :alt="`排名${customerRankingLabels[3].rank}`"
                      />
                    </div>
                    <el-tooltip :content="customerRankingLabels[3].company" placement="top">
                      <div class="company">{{ customerRankingLabels[3].company }}</div>
                    </el-tooltip>
                    <div class="number">{{ customerRankingLabels[3].number }}</div>
                  </div>
                </div>

                <!-- 底部右标注 -->
                <div class="label bottom-right-label">
                  <div class="label-content">
                    <div class="progress-node"></div>
                    <div class="red-badge">
                      <img
                        :src="customerRankingLabels[4].redRankIcon"
                        :alt="`排名${customerRankingLabels[4].rank}`"
                      />
                    </div>
                    <el-tooltip :content="customerRankingLabels[4].company" placement="top">
                      <div class="company">{{ customerRankingLabels[4].company }}</div>
                    </el-tooltip>
                    <div class="number">{{ customerRankingLabels[4].number }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 内存利用率排行 -->
        <div class="card-item memory-ranking">
          <div class="card-header">
            <h3>内存利用率排行</h3>
          </div>
          <div class="memory-content">
            <!-- 左侧气泡区域 -->
            <div class="bubbles-area">
              <div
                class="bubble"
                v-for="(item, index) in memoryRankingData.slice(0, 5)"
                :key="`bubble-${index}`"
                :class="item.bubbleSize"
                :style="{
                  backgroundImage: `url(/images/accountView/ball${index + 1}.png)`,
                }"
              >
                <span class="bubble-text">{{ item.percentage + '%' }}</span>
              </div>
            </div>

            <!-- 右侧公司列表 -->
            <div class="companies-area">
              <div
                class="company-item"
                v-for="(item, index) in memoryRankingData"
                :key="`company-${index}`"
              >
                <div class="rank-icon" :class="`top${index + 1}`">
                  <img :src="item.rankIcon" :alt="`排名${index + 1}`" />
                </div>
                <el-tooltip :content="item.companyName" placement="top">
                  <div class="company-name">{{ item.companyName }}</div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="content-bottom">
        <div class="card-item resource-ranking">
          <div class="card-header">
            <h3>资源利用率排行</h3>
            <el-radio-group v-model="resourceType">
              <el-radio-button label="vCPU" />
              <el-radio-button label="内存" />
            </el-radio-group>
          </div>
          <div class="resource-ranking-list">
            <div
              class="ranking-card"
              v-for="(item, index) in resourceRankingData"
              :key="`resource-${index}`"
              :class="`top${index + 1}`"
            >
              <div class="card-header-section">
                <div class="rank-info">
                  <span class="rank-label">TOP {{ index + 1 }}</span>
                  <span class="percentage">{{ item.percentage }}%</span>
                </div>
              </div>
              <el-empty
                v-if="item.hostName === '虚位以待'"
                :image-size="50"
                description="虚位以待"
              />
              <div v-else class="card-content-section">
                <div class="resource-info">
                  <div class="info-row">
                    <span class="label">主机名称</span>
                    <el-tooltip :content="item.hostName" placement="top" :show-after="500">
                      <span class="value">{{ item.hostName }}</span>
                    </el-tooltip>
                  </div>
                  <div class="info-row">
                    <span class="label">资源类型</span>
                    <el-tooltip :content="item.resourceType" placement="top" :show-after="500">
                      <span class="value">{{ item.resourceType }}</span>
                    </el-tooltip>
                  </div>
                  <div class="info-row">
                    <span class="label">资源id</span>
                    <el-tooltip :content="item.resourceId" placement="top" :show-after="500">
                      <span class="value">{{ item.resourceId }}</span>
                    </el-tooltip>
                  </div>
                  <div class="info-row">
                    <span class="label">所属客户</span>
                    <el-tooltip :content="item.customer" placement="top" :show-after="500">
                      <span class="value">{{ item.customer }}</span>
                    </el-tooltip>
                  </div>
                  <div class="info-row">
                    <span class="label">所属云</span>
                    <el-tooltip :content="item.cloud" placement="top" :show-after="500">
                      <span class="value">{{ item.cloud }}</span>
                    </el-tooltip>
                  </div>
                  <div class="info-row">
                    <span class="label">所属资源池</span>
                    <el-tooltip :content="item.resourcePool" placement="top" :show-after="500">
                      <span class="value">{{ item.resourcePool }}</span>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <CustomersDialog v-if="customersDialogVisible" v-model="customersDialogVisible" />
  </Container>
</template>

<script setup lang="ts">
import Breadcrumb from './components/Breadcrumb.vue'
import Container from './components/Container.vue'
import { ArrowRight } from '@element-plus/icons-vue'
import { computed, ref, watch } from 'vue'
import {
  getCustomCountApi,
  getCustomsApi,
  getMemoryRankingApi,
  getTotalProductRankingApi,
  getVcpuRankingApi,
  getResourceRankingApi,
} from '@/api/modules/computingPowerMap'
import CustomersDialog from './components/customersDialog.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const customersDialogVisible = ref(false)
const handleShowCustomersDialog = () => {
  customersDialogVisible.value = true
}
const handleShowDetail = (customId: string) => {
  router.push({
    path: '/accountDetail',
    query: {
      customId: customId,
    },
  })
}

// 客户数据接口
interface CustomerRecord {
  customId: string
  customNo: string
  customName: string
  billId: string
  tenantId: string
  contactName: string
  email: string
  contactMobile: string
  createdTime: string
  totalOrder: number
  monthTotalOrder: number
  totalProduct: number
  monthTotalProduct: number
}

// vCPU/内存利用率排行接口
interface CpuMemoryRankingRecord {
  customName: string
  cpuUsePercent: number
  memUsePercent: number
}

// 客户订购数排行接口
interface CustomerOrderRankingRecord {
  customId: string
  customName: string
  tenantId: number
  tenantName: string
  contactName: string
  contactMobile: string
  totalOrder: number
  monthTotalOrder: number
  totalProduct: number
}

// 客户经理数据统计
const stats = ref({
  // 集团客户总数
  totalCustom: '0',
  //订购单数
  totalOrder: '0',
  // 本月新订单
  newOrder: '0',
  // 产品订购数
  totalProduct: '0',
  // 本月订购产品
  newProduct: '0',
})

// 集团客户概览,前二个
interface CustomerDisplay {
  customId: string
  customName: string
  contactName: string
  contactMobile: string
  totalOrder: string
  monthTotalOrder: string
  totalProduct: string
}

const customs = ref<CustomerDisplay[]>([])

// vCPU利用率排行数据显示格式
interface VcpuRankingDisplay {
  percentage: number
  rankIcon: string
  rankNumber: string
  companyName: string
}

// 内存利用率排行数据显示格式
interface MemoryRankingDisplay {
  percentage: number
  companyName: string
  rankIcon: string
  bubbleSize: string
}

// 客户订购数排行标注数据显示格式
interface CustomerRankingLabelDisplay {
  number: string
  company: string
  redRankIcon: string
  rank: number
}

// vCPU利用率排行数据
const vcpuRankingData = ref<VcpuRankingDisplay[]>([])

// 内存利用率排行数据
const memoryRankingData = ref<MemoryRankingDisplay[]>([])

// 客户订购数排行标注数据
const customerRankingLabels = ref<CustomerRankingLabelDisplay[]>([])

async function getVcpuRanking() {
  const { entity } = await getVcpuRankingApi({})
  // 确保始终有5条数据
  const dataList: VcpuRankingDisplay[] = []

  for (let i = 0; i < 5; i++) {
    if (entity && Array.isArray(entity) && entity[i]) {
      // 有真实数据
      const item = entity[i] as CpuMemoryRankingRecord
      dataList.push({
        percentage: Number((item.cpuUsePercent || 0).toFixed(2)),
        rankIcon: `/images/accountView/rank${i < 3 ? i + 1 : '4_5'}.png`,
        rankNumber: String(i + 1).padStart(2, '0'),
        companyName: item.customName || '虚位以待',
      })
    } else {
      // 没有数据，使用默认值
      dataList.push({
        percentage: 0,
        rankIcon: `/images/accountView/rank${i < 3 ? i + 1 : '4_5'}.png`,
        rankNumber: String(i + 1).padStart(2, '0'),
        companyName: '虚位以待',
      })
    }
  }

  vcpuRankingData.value = dataList
}

async function getMemoryRanking() {
  const { entity } = await getMemoryRankingApi({})
  // 确保始终有5条数据
  const dataList: MemoryRankingDisplay[] = []

  for (let i = 0; i < 5; i++) {
    if (entity && Array.isArray(entity) && entity[i]) {
      // 有真实数据
      const item = entity[i] as CpuMemoryRankingRecord
      dataList.push({
        percentage: Number((item.memUsePercent || 0).toFixed(2)),
        companyName: item.customName || '虚位以待',
        rankIcon: `/images/accountView/rank${i < 3 ? i + 1 : '4_5'}.png`,
        bubbleSize: `top${i + 1}`,
      })
    } else {
      // 没有数据，使用默认值
      dataList.push({
        percentage: 0,
        companyName: '虚位以待',
        rankIcon: `/images/accountView/rank${i < 3 ? i + 1 : '4_5'}.png`,
        bubbleSize: `top${i + 1}`,
      })
    }
  }

  memoryRankingData.value = dataList
}

async function getResourceRanking() {
  try {
    // 根据当前选择的资源类型传递不同的orderType参数
    const orderType = resourceType.value === 'vCPU' ? 'vCPU' : 'MEM'
    const { entity } = await getResourceRankingApi({ orderType })
    // 处理返回的数据，确保始终有5条数据
    const dataList: ResourceRankingDisplay[] = []

    for (let i = 0; i < 5; i++) {
      if (entity && Array.isArray(entity) && entity[i]) {
        // 有真实数据
        const item = entity[i] as ResourceRankingRecord
        dataList.push({
          percentage: item.topPercent?.toFixed(2) || '0.00', // 如果API没有返回百分比，使用随机值或默认值
          hostName: item.deviceName || '虚位以待',
          resourceType:
            {
              ecs: '云主机',
              evs: '云硬盘',
              eip: '弹性IP',
              mysql: 'MySQL云数据库',
              bms: '裸金属',
              gms: 'gpu裸金属',
              postgreSql: 'PostgreSql',
              redis: 'redis',
              other: '其他产品',
              gcs: 'GPU云主机',
              obs: '对象存储',
              bucket: '对象存储（桶）',
              slb: '负载均衡',
              nat: 'NAT网关',
              vpc: 'VPC',
              network: '网络',
              netcard: '多平面网络',
              cq: '容器配额',
              backup: '云灾备',
              cloudport: '云端口',
              vpn: 'vpn',
              nas: 'nas',
              pm: '物理机',
              flink: 'flink',
              kafka: 'kafka',
              es: 'ElasticSearch',
              rdsMysql: 'MySQL云数据库',
              unknown: '--',
            }[item.deviceType] || item.deviceType,
          resourceId: item.deviceId || '暂无',
          customer: item.customName || '虚位以待',
          cloud: item.domainName || '平台云',
          resourcePool: item.resourcePoolName || '暂无',
        })
      } else {
        // 没有数据，使用默认值
        dataList.push({
          percentage: '0.00',
          hostName: '虚位以待',
          resourceType: '--',
          resourceId: '暂无',
          customer: '虚位以待',
          cloud: '--',
          resourcePool: '暂无',
        })
      }
    }

    resourceRankingData.value = dataList
  } catch (error) {
    console.error('获取资源利用率排行数据失败:', error)
    // 出错时显示默认数据
    resourceRankingData.value = Array.from({ length: 5 }, () => ({
      percentage: '0.00',
      hostName: '虚位以待',
      resourceType: '云主机',
      resourceId: '暂无',
      customer: '虚位以待',
      cloud: '平台云',
      resourcePool: '暂无',
    }))
  }
}

async function getTotalProductRanking() {
  const { entity } = await getTotalProductRankingApi({})
  // 确保始终有5条数据
  const dataList: CustomerRankingLabelDisplay[] = []

  for (let i = 0; i < 5; i++) {
    if (entity && Array.isArray(entity) && entity[i]) {
      // 有真实数据
      const item = entity[i] as CustomerOrderRankingRecord
      dataList.push({
        number: item.totalProduct?.toLocaleString() || '0',
        company: item.customName || '虚位以待',
        redRankIcon: `/images/accountView/red_rank${i + 1}.png`,
        rank: i + 1,
      })
    } else {
      // 没有数据，使用默认值
      dataList.push({
        number: '0',
        company: '虚位以待',
        redRankIcon: `/images/accountView/red_rank${i + 1}.png`,
        rank: i + 1,
      })
    }
  }

  customerRankingLabels.value = dataList
}

async function getStats() {
  const { entity } = await getCustomCountApi({})
  if (entity) {
    stats.value = {
      totalCustom: entity.totalCustom?.toLocaleString() || '0',
      totalOrder: entity.totalOrder?.toLocaleString() || '0',
      newOrder: entity.monthTotalOrder?.toLocaleString() || '0',
      totalProduct: entity.totalProduct?.toLocaleString() || '0',
      newProduct: entity.monthTotalProduct?.toLocaleString() || '0',
    }
  }
}

async function getCustoms() {
  const { entity } = await getCustomsApi({})
  if (entity.length > 0) {
    // 只取前两个客户用于概览展示
    customs.value = entity.slice(0, 2).map((item: CustomerRecord) => ({
      customId: item.customId,
      customName: item.customName || '暂无',
      contactName: item.contactName || '暂无',
      contactMobile: item.contactMobile || '暂无',
      totalOrder: item.totalOrder?.toLocaleString() || '0',
      monthTotalOrder: item.monthTotalOrder?.toLocaleString() || '0',
      totalProduct: item.totalProduct?.toLocaleString() || '0',
    }))
  }
}
// 页面初始化
getStats()
getCustoms()
getVcpuRanking()
getMemoryRanking()
getTotalProductRanking()

// 资源利用率排行数据接口定义
interface ResourceRankingRecord {
  deviceName: string
  deviceType: string
  deviceId: string
  customName: string
  domainName: string
  resourcePoolName: string
  topPercent?: number // 利用率百分比（前端计算或后端返回）
}

// 资源利用率排行数据显示格式
interface ResourceRankingDisplay {
  percentage: string
  hostName: string
  resourceType: string
  resourceId: string
  customer: string
  cloud: string
  resourcePool: string
}

// 资源利用率排行数据
const resourceType = ref('vCPU')
const resourceRankingData = ref<ResourceRankingDisplay[]>([])

// 监听资源类型变化，重新获取数据
watch(
  resourceType,
  () => {
    getResourceRanking()
  },
  { immediate: true },
)

// 计算环形进度条的周长
const circumference = computed(() => 2 * Math.PI * 20)
</script>

<style scoped lang="scss">
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .header-right {
    width: 300px;
    display: flex;
    align-items: center;
    .el-select {
      margin-left: 10px;
      &:first-child {
        flex: 1;
      }
      &:last-child {
        flex: 2;
      }
    }
  }
}

.content {
  margin-top: 20px;

  .content-middle {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    .card-item {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 20px 20px 0 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      .card-header {
        margin-bottom: 20px;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }
      }
    }

    // vCPU利用率排行样式
    .vcpu-ranking {
      .ranking-grid {
        .row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .col {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        .progress-circles-row {
          margin-bottom: 8px;

          .progress-circle {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .percentages-row {
          margin-bottom: 8px;

          .percentage-text {
            font-size: 12px;
            font-weight: 600;
            color: #3662ec;
            text-align: center;
          }
        }

        .badges-row {
          margin-bottom: 8px;

          .rank-badge {
            display: flex;
            justify-content: center;

            img {
              width: 24px;
            }
          }
        }

        .numbers-row {
          margin-bottom: 12px;

          .rank-number {
            font-size: 12px;
            color: #3662ec;
            font-weight: 600;
            text-align: center;
          }
        }

        .progress-bar-row {
          margin-bottom: 16px;

          .progress-container {
            width: 100%;
            display: flex;
            justify-content: center;
            padding: 0 32px;

            .progress-bar {
              position: relative;
              width: 100%;
              height: 2px;
              background: linear-gradient(to right, #3662ec 0%, #ccc 100%);
              border-radius: 1px;

              .progress-fill {
                height: 100%;
                background: #3662ec;
                background: linear-gradient(to right, #3662ec 50%, #ccc 100%);
                border-radius: 1px;
                width: 100%;
              }

              .progress-node {
                position: absolute;
                top: 50%;
                width: 2px;
                height: 2px;
                border-radius: 50%;
                border: 2px solid #3662ec;
                background: #fff;
                transform: translate(-50%, -50%);
              }
            }
          }
        }

        .companies-row {
          .company-name {
            font-size: 10px;
            color: #333;
            line-height: 1.4;
            padding: 0 5px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            overflow: hidden;
            word-break: break-all;
            height: calc(1.4em * 2);
            cursor: pointer;
          }
        }
      }
    }

    // 客户订购数排行样式
    .customer-ranking {
      .chart-container {
        .chart-area {
          position: relative;
          height: 240px;

          .line-chart-container {
            width: 100%;
            height: 200px;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .line-chart-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            margin-top: 81px;
          }

          .data-labels {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;

            .label {
              position: absolute;
              color: rgba(0, 0, 0, 0.6);

              .label-content {
                background: transparent;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 1px;

                .red-badge {
                  img {
                    width: 16px;
                  }
                }

                .number {
                  font-size: 12px;
                  font-weight: 600;
                  color: #3d3d3d;
                  text-align: center;
                }

                .company {
                  font-size: 10px;
                  color: #666;
                  text-align: center;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 150px;
                }
              }

              .label-line {
                border-left: 1px dashed #3662ec;

                &.top-line {
                  height: 70px;
                }

                &.left-line {
                  height: 13px;
                }

                &.right-line {
                  height: 8px;
                }

                &.bottom-left-line {
                  height: 30px;
                }

                &.bottom-right-line {
                  height: 30px;
                }
              }
              .progress-node {
                width: 5px;
                height: 5px;
                border-radius: 50%;
                border: 1px solid #3662ec;
                background: #fff;
              }
            }

            .top-label {
              top: 8px;
              left: 50%;
              transform: translateX(-50%);
            }

            .left-label {
              top: 70px;
              left: -10px;
            }

            .right-label {
              top: 70px;
              right: -22px;
            }

            .bottom-left-label {
              bottom: 40px;
              left: 24px;
            }

            .bottom-right-label {
              bottom: 40px;
              right: -5px;
            }
          }
        }
      }
    }

    // 内存利用率排行样式
    .memory-ranking {
      .memory-content {
        display: flex;
        gap: 30px;

        .bubbles-area {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: center;
          gap: 20px;
          padding: 20px;
          position: relative;

          .bubble {
            border-radius: 50%;
            color: #3d3d3d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            position: relative;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            animation: bubble-float 3s ease-in-out infinite;

            .bubble-text {
              position: relative;
              z-index: 10;
              font-weight: 600;
              color: #3d3d3d;
            }

            &.top1 {
              width: 100px;
              height: 100px;
              animation-delay: 0s;
              position: absolute;
              top: 48px;
              left: -6px;

              .bubble-text {
                font-size: 16px;
              }
            }

            &.top2 {
              width: 90px;
              height: 90px;
              animation-delay: -1s;
              position: absolute;
              top: -10px;
              left: 85px;

              .bubble-text {
                font-size: 14px;
              }
            }

            &.top3 {
              width: 60px;
              height: 60px;
              animation-delay: -2s;
              position: absolute;
              top: 86px;
              left: 100px;

              .bubble-text {
                font-size: 12px;
              }
            }
            &.top4 {
              width: 50px;
              height: 50px;
              animation-delay: -2s;
              position: absolute;
              top: 143px;
              left: 70px;

              .bubble-text {
                font-size: 10px;
              }
            }
            &.top5 {
              width: 35px;
              height: 35px;
              animation-delay: -2s;
              position: absolute;
              top: 164px;
              left: 30px;

              .bubble-text {
                font-size: 10px;
              }
            }
          }

          @keyframes bubble-float {
            0%,
            100% {
              transform: translateY(0px);
            }
            50% {
              transform: translateY(-5px);
            }
          }
        }

        .companies-area {
          flex: 1;
          display: flex;
          flex-direction: column;

          .company-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 6px 0;

            .rank-icon {
              img {
                width: 16px;
              }
              padding: 5px 7px 3px 7px;
              border-radius: 6px;
              &.top1 {
                background: rgba(205, 225, 253, 1);
              }
              &.top2 {
                background: rgba(205, 225, 253, 0.7);
              }
              &.top3 {
                background: rgba(205, 225, 253, 0.4);
              }
              &.top4 {
                background: rgba(205, 225, 253, 0.3);
              }
              &.top5 {
                background: rgba(205, 225, 253, 0.1);
              }
            }

            .company-name {
              font-size: 10px;
              color: #333;
              line-height: 1.4;
              flex: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              display: inline-block;
              text-align: center;
              line-height: 1;
              max-width: 112px;
              text-align: left;
            }
          }
        }
      }
    }
  }
  .content-bottom {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    padding-bottom: 20px;
    .card-item {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .resource-ranking {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }

        .el-radio-group {
          .el-radio-button {
            --el-border-radius-base: 4px;
          }
          .el-radio-button__inner {
            padding: 8px 16px;
            font-size: 12px;
            border-color: #e4e7ed;
            color: #606266;
          }
          .el-radio-button:first-child .el-radio-button__inner {
            border-left-color: #e4e7ed;
          }
          .el-radio-button__original-radio:checked + .el-radio-button__inner {
            background-color: #3662ec;
            border-color: #3662ec;
            color: #fff;
          }
        }
      }

      .resource-ranking-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .ranking-card {
          width: calc(20% - 12px);
          background: white;
          border-radius: 8px;
          border: 1px solid #f0f0f0;
          overflow: hidden;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          .card-header-section {
            padding: 12px 16px;
            background: linear-gradient(135deg, #3662ec 0%, #4f80ff 100%);

            .rank-info {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .rank-label {
                font-size: 14px;
                font-weight: 600;
                color: #fff;
              }

              .percentage {
                font-size: 16px;
                font-weight: 700;
                color: #fff;
              }
            }
          }

          .card-content-section {
            padding: 16px;

            .resource-info {
              .info-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-size: 12px;

                &:last-child {
                  margin-bottom: 0;
                }

                .label {
                  color: #999;
                  font-weight: 400;
                  white-space: nowrap;
                }

                .value {
                  color: #1d2129;
                  font-weight: 500;
                  text-align: right;
                  flex: 1;
                  margin-left: 24px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  cursor: pointer;
                }
              }
            }
          }

          // 不同排名的颜色差异
          &.top1 .card-header-section {
            background: linear-gradient(135deg, #3662ec 0%, #4f80ff 100%);
          }

          &.top2 .card-header-section {
            background: linear-gradient(135deg, #4f80ff 0%, #6b9eff 100%);
          }

          &.top3 .card-header-section {
            background: linear-gradient(135deg, #6b9eff 0%, #87bbff 100%);
          }

          &.top4 .card-header-section {
            background: linear-gradient(135deg, #87bbff 0%, #a3d9ff 100%);
          }

          &.top5 .card-header-section {
            background: linear-gradient(135deg, #a3d9ff 0%, #bfe6ff 100%);
          }
        }
      }
    }
  }

  .content-top {
    display: flex;
    gap: 20px;

    .content-top-item.left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-weight: bold;
    }

    .left {
      flex: 1;

      .total-customers {
        background: linear-gradient(135deg, #80bff8 0%, #4984f5 100%);
        border-radius: 6px;
        padding: 10px 16px;
        color: white;
        display: flex;
        align-items: end;
        align-content: space-between;
        justify-content: space-between;

        .label {
          font-size: 16px;
          opacity: 0.9;
          margin-bottom: 8px;
        }

        .value {
          font-size: 28px;
          font-weight: bold;
        }
      }

      .stats-cards {
        display: flex;
        gap: 16px;

        .stat-card {
          flex: 1;
          background: linear-gradient(135deg, #80bff8 0%, #4984f5 100%);
          border-radius: 6px;
          padding: 20px;
          color: white;

          .card-content {
            .main-number {
              font-size: 28px;
              font-weight: bold;
              margin-bottom: 18px;
            }

            .label {
              font-size: 14px;
              opacity: 0.9;
            }

            .sub-info {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              align-items: center;

              .sub-label {
                opacity: 0.8;
              }

              .sub-value {
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    .right {
      flex: 1.2;

      .customer-overview {
        background: white;
        border-radius: 6px;
        padding: 12px 15px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        .overview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }

          .more {
            color: #487dea;
            font-size: 14px;
            cursor: pointer;
            text-decoration: underline;
            position: relative;
            &::after {
              content: '';
              width: 4px;
              height: 4px;
              background: #fa5151;
              border-radius: 50%;
              position: absolute;
            }
            &:hover {
              text-decoration: underline;
            }
          }
        }

        .customer-list {
          .customer-items {
            display: flex;
            gap: 12px;
            cursor: pointer;
          }

          .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 90px;
            padding: 20px;

            .empty-content {
              text-align: center;
              color: #999;

              .empty-text {
                font-size: 16px;
                margin-bottom: 8px;
                color: #666;
              }

              .empty-desc {
                font-size: 12px;
                color: #999;
              }
            }
          }

          .customer-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            box-shadow: 0 0px 6px rgba(0, 0, 0, 0.2);
            flex: 1;

            &:last-child {
              border-bottom: none;
            }

            .customer-info {
              flex: 1;

              .customer-name {
                .name {
                  width: 80px;
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  display: inline-block;
                  line-height: 1;
                }
                position: relative;
                &::before {
                  content: '';
                  display: inline-block;
                  width: 3px;
                  height: 16px;
                  background: #4facfe;
                  position: absolute;
                  left: -12px;
                  top: 50%;
                  transform: translateY(-50%);
                }
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;

                span:first-child {
                  font-size: 16px;
                  color: #3d3d3d;
                }

                .arrow-right {
                  color: #6c6cea;
                  font-size: 12px;
                  justify-items: flex-end;
                }
              }

              .customer-details {
                display: flex;
                gap: 4px;
                flex-direction: column;

                .detail-item {
                  font-size: 12px;
                  color: rgba(0, 0, 0, 0.6);
                  margin-bottom: 4px;

                  .detail-label {
                    color: #999;
                  }

                  .detail-value {
                    color: rgba(0, 0, 0, 0.6);
                  }
                }
              }

              .customer-stats {
                display: flex;
                gap: 16px;
                justify-content: space-between;

                .stat-item {
                  font-size: 12px;
                  color: #666;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  .stat-label {
                    color: #666;
                  }
                  .stat-value {
                    color: #666;
                    font-weight: bold;
                    margin-top: 4px;
                  }
                }
              }
            }

            .arrow {
              color: #ccc;
              font-size: 18px;
              cursor: pointer;

              &:hover {
                color: #4facfe;
              }
            }
          }
        }
      }
    }
  }
}
</style>
