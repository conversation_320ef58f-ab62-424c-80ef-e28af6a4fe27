<template>
  <div class="table-box">
    <sl-page-header
      title="负载均衡策略配置"
      :icon="{
        class: 'page-fuzaijunheng',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>

    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <!-- Tabs组件 -->
      <sl-base-tabs :show-count="false" :tabs="tabs" v-model="activeTab"></sl-base-tabs>

      <div class="sl-page-content">
        <!-- 详情信息 -->
        <div v-show="activeTab === 'basic'" class="tab-content">
          <SlbBasicInfo ref="slbBasicInfoRef" :slb-id="slbId"></SlbBasicInfo>
        </div>

        <!-- 监听列表 -->
        <div v-show="activeTab === 'listener'" class="tab-content">
          <ListenerList ref="listenerListRef" :slb-id="slbId"></ListenerList>
        </div>

        <!-- 虚拟服务器列表 -->
        <div v-show="activeTab === 'vserver'" class="tab-content">
          <VServerGroupList ref="vServerGroupListRef" :slb-id="slbId"></VServerGroupList>
        </div>

        <!-- 主备服务器列表 -->
        <div v-show="activeTab === 'pbserver'" v-if="isShowPbServerTab" class="tab-content">
          <PbServerGroupList ref="pbServerGroupListRef" :slb-id="slbId"></PbServerGroupList>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import SlBaseTabs from '@/components/base/SlBaseTabs.vue'
import { useRouter, useRoute } from 'vue-router'
import ListenerList from '@/views/resourceCenter/slb/components/ListenerList.vue'
import VServerGroupList from '@/views/resourceCenter/slb/components/VServerGroupList.vue'
import PbServerGroupList from '@/views/resourceCenter/slb/components/PbServerGroupList.vue'
import SlbBasicInfo from '@/views/resourceCenter/slb/components/SlbBasicInfo.vue'
import { useRolePermission } from '../hooks/useRolePermission'

const { isNetworkOperator } = useRolePermission()

const router = useRouter()
const route = useRoute()

// 获取资源详情ID和是否显示主备服务器组标签
const slbId = ref<string>((route.query.id as string) || '')
const isShowPbServerTab = ref<boolean>(route.query.showPbServerTab === 'true')

// 定义标签页 - 当用户是网络运维角色时，只显示详情信息标签页
// 否则显示全部标签页，包括监听列表、虚拟服务器组等
const tabs = ref([{ name: 'basic', label: '详情信息' }])

// 只有当用户不是网络运维角色时，才添加其他标签页
if (!isNetworkOperator.value) {
  tabs.value.push({ name: 'listener', label: '监听列表' })
  tabs.value.push({ name: 'vserver', label: '虚拟服务器组列表' })

  // 根据条件添加主备服务器组标签
  if (isShowPbServerTab.value) {
    tabs.value.push({ name: 'pbserver', label: '主备服务器组列表' })
  }
}

// 定义激活的标签页
const activeTab = ref('basic')

// 组件引用
const slbBasicInfoRef = ref()
const listenerListRef = ref()
const vServerGroupListRef = ref()
const pbServerGroupListRef = ref()

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/slbList',
  })
}
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-page-content {
  padding: 0 16px 16px;
}

.tab-content {
  margin-top: 16px;
}
</style>
