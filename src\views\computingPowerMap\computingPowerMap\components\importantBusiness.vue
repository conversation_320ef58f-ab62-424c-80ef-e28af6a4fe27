<template>
  <section class="power-panel4" :class="{ active: props.isActive }">
    <div class="card-content">
      <Title title="重要业务TOP3" />
      <div class="importantBusiness">
        <div class="importantBusinessRow">
          <div class="item1"></div>
          <div class="item2">业务名称</div>
          <div class="item2">虚拟机数</div>
          <div class="item2">CPU利用率</div>
          <div class="item2">内存利用率</div>
        </div>
        <template v-if="dataList.length > 0">
          <div
            @click="showImportantBusinessDialog(item)"
            :key="'importantBusinessRow' + index"
            class="importantBusinessRow"
            v-for="(item, index) in dataList"
          >
            <div class="item1" :class="'item1-' + (index + 1)">
              <b>{{ index + 1 }}</b>
            </div>
            <div class="item2">{{ item.bsNameType }}</div>
            <div class="item2">{{ item.vmNum }}</div>
            <div class="item2 item2-2">
              <div>{{ parseFloat((item.cpuRate * 100).toFixed(2)) + '%' }}</div>
              <div style="width: 80%; margin-top: 10px">
                <el-progress
                  color="linear-gradient(to right, rgba(61, 110, 239, 1), rgba(61, 207, 255, 1))"
                  :show-text="false"
                  :percentage="parseFloat((item.cpuRate * 100).toFixed(2))"
                />
              </div>
            </div>
            <div class="item2 item2-2">
              <div>{{ parseFloat((item.memoryRate * 100).toFixed(2)) + '%' }}</div>
              <div style="width: 80%; margin-top: 10px">
                <el-progress
                  color="linear-gradient(to right, rgba(195, 31, 120, 1), rgba(255, 191, 63, 1))"
                  :show-text="false"
                  :percentage="parseFloat((item.memoryRate * 100).toFixed(2))"
                />
              </div>
            </div>
          </div>
        </template>
        <div v-else style="text-align: center">暂无数据</div>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { onMounted, onBeforeMount, watch, ref } from 'vue'
import Title from './Title.vue'

import { getComPowerMapStatsCriticalBusiness } from '@/api/modules/comPowerCenter'

const emit = defineEmits(['showImportantDialog'])

const props = defineProps({
  requestParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  isActive: {
    type: Boolean,
    default: false,
  },
})

// 监听参数变化自动重新请求（可选）
watch(
  () => props.requestParams,
  () => {
    getDataInfo()
  },
  { deep: true },
)

// 原始数值堆叠柱状图数据
onMounted(() => {
  getDataInfo()
})

// 定义类型/接口
interface OptionItem {
  orderNum: any
  bsNameType: string
  vmNum: number
  cpuRate: number
  memoryRate: number
}
const dataList = ref<OptionItem[]>([])
const getDataInfo = () => {
  getComPowerMapStatsCriticalBusiness({
    cloudName: props.requestParams.cloudName,
    platformTypeName: props.requestParams.platformTypeName,
    cityCode: props.requestParams.cityCode,
  }).then((res: any) => {
    if (res.code === 200) {
      dataList.value = res.entity.filter((item: any) => item.vmNum != 0)
    }
  })
}
const showImportantBusinessDialog = (item: any) => {
  emit('showImportantDialog', item)
}
onBeforeMount(() => {
  // barChart?.dispose()
})
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.power-panel4 {
  position: relative;
  height: 248px;
  border-radius: 16px;
  padding: 2px;
  background: url('/images/computingPower/comPowerRight2Bg.png') no-repeat center center;
  background-size: 100% 100%;
  cursor: pointer;
}
.power-panel4.active {
  background: url('/images/computingPower/comPowerRight2BgActive.png') no-repeat center center;
  background-size: 100% 100%;
}
.card-content {
  padding: 0 20px;
  height: 100%;
}

.bg34B3E0Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #34b3e0;
  margin-right: 5px;
}

.bg9BD8B4Grid {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: #9bd8b4;
  margin-right: 5px;
}

//重保业务部分
.importantBusiness {
  .importantBusinessRow {
    display: flex;
    margin-bottom: 10px;
    & > div {
      //display: inline-block;
      height: 40px;
      //vertical-align: middle;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    div.item1 {
      width: 16%;
      b {
        display: inline-block;
        width: 23px;
        height: 23px;
        background: #3161b4;
        border-radius: 2px;
        color: #080c13;
        font-size: 15px;
        text-align: center;
        font-weight: normal;
        line-height: 23px;
      }
    }
    div.item1.item1-1 {
      b {
        background: #e06c5d;
      }
    }
    div.item1.item1-2 {
      b {
        background: #3da2f8;
      }
    }
    div.item1.item1-3 {
      b {
        background: #37b9d6;
      }
    }
    div.item2 {
      width: 21%;
    }
    div.item2.item2-2 {
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    div:nth-child(2) {
      justify-content: left;
    }
  }
}
</style>

<style>
.importantBusinessRow .el-progress-bar__outer {
  background-color: #b9ccee !important;
}
</style>
