<template>
  <div>
    <div class="sl-card mb8">
      <sl-block-title>工单信息</sl-block-title>
      <sl-form
        v-if="orderDesc"
        :label-width="140"
        ref="slFormRef"
        v-model="orderDescRef"
        :options="options"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="orderDescRef" :fields="item" :dic-collection="dicCollection" />
        </template>
      </sl-form>
    </div>

    <div class="sl-card mb8">
      <sl-block-title>工单附件信息</sl-block-title>
      <sl-form
        :show-block-title="false"
        :label-width="140"
        ref="slFormRef"
        v-model="orderDescRef"
        :options="options2"
      >
        <template #upload="{ item }">
          <!-- 上传文件 -->
          <SlUpload
            style="width: 100%"
            v-model:file-list="orderDescRef[item.key]"
            :file-type="item.fileType"
            v-bind="item.props?.upload"
          />
        </template>
      </sl-form>
    </div>

    <div class="sl-card mb8">
      <sl-block-title>工单费用信息</sl-block-title>
      <sl-form
        :show-block-title="false"
        :label-width="140"
        ref="slFormRef"
        v-model="orderDescRef"
        :options="options3"
      >
        <template #enumText="{ item }">
          <EnumText :form-model="orderDescRef" :fields="item" :dic-collection="dicCollection" />
        </template>
      </sl-form>
    </div>

    <div class="sl-card mb8">
      <sl-block-title>补充信息</sl-block-title>
      <sl-form
        :show-block-title="false"
        :label-width="140"
        ref="slFormRef"
        v-model="orderDescRef"
        :options="options4"
      >
        <template #upload="{ item }">
          <!-- 上传文件 -->
          <SlUpload
            style="width: 100%"
            v-model:file-list="orderDescRef[item.key]"
            :file-type="item.fileType"
            v-bind="item.props?.upload"
            :disabled="!(pageStatus && btnAuth?.response_scheme_manager)"
          />
        </template>
        <template #enumText="{ item }">
          <EnumText :form-model="orderDescRef" :fields="item" :dic-collection="dicCollection" />
        </template>
      </sl-form>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { maskPhoneNumber } from '@/utils'
import EnumText from '@/views/approvalCenter/components/EnumText.vue'
import { computed, inject, ref } from 'vue'
import type { NonStandardOrderBtnsType } from '../../interface/type'
import { useVModel } from '@vueuse/core'

const props = withDefaults(
  defineProps<{
    orderDesc: FormDataType
  }>(),
  {
    orderDesc: () => ({
      productApplyFile: [],
    }),
  },
)
const emit = defineEmits(['update:orderDesc'])

const orderDescRef = useVModel(props, 'orderDesc', emit)

const dicCollection = ref({})

const pageStatus = inject('pageStatus', ref(true))

const btnAuth = inject('btnAuth', ref<NonStandardOrderBtnsType>())

const options = [
  {
    groupName: '工单信息',
    groupItems: [
      {
        label: '客户名称',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'customerName',
        span: 8,
      },
      {
        label: '客户联系人',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'contactPerson',
        span: 8,
      },
      {
        label: '客户经理',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'customerManager',
        span: 8,
      },
      {
        label: '客户编码',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'customerCode',
        span: 8,
      },
      {
        label: '客户联系方式',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'contactPhone',
        span: 8,
      },
      {
        label: '客户经理联系方式',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'managerPhone',
        span: 8,
      },
      {
        label: '工单标题',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'orderTitle',
        span: 8,
      },
      {
        label: '分公司领导',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'businessDepartLeaderName',
        span: 8,
      },
      {
        label: '资源申请说明',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'orderDesc',
        span: 16,
      },
    ],
  },
]

const options2 = [
  {
    groupName: '工单附件信息',
    groupItems: [
      {
        label: '资源上云说明书',
        type: 'slot',
        slotName: 'upload',
        key: 'resourceApplyFile',
        span: 24,
        props: {
          upload: {
            drag: false,
            fileType: 'RESOURCE_EXPLAIN',
            disabled: true,
            style: {
              'max-width': '500px',
            },
          },
        },
      },
    ],
  },
]

const options3 = [
  {
    groupName: '工单费用信息',
    groupItems: [
      {
        label: '合同费用',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'contractCost',
        beforeText: '￥',
        afterText: '元',
        span: 8,
      },
    ],
  },
]

const options4 = ref([
  {
    groupName: '补充信息',
    groupItems: [
      {
        label: '产品申请方案',
        type: 'slot',
        slotName: 'upload',
        key: 'productApplyFile',
        span: 24,
        props: {
          upload: {
            drag: false,
            fileType: 'RESOURCE_EXPLAIN',
            style: {
              'max-width': '500px',
            },
          },
        },
      },
      {
        label: '产品合计成本价格',
        type: 'slot',
        slotName: 'enumText',
        keyName: 'productTotalCost',
        beforeText: '￥',
        afterText: '元',
        span: 8,
      },
      {
        label: '备注',
        type: computed(() =>
          !(pageStatus.value && btnAuth.value?.response_scheme_manager) ? 'slot' : 'input',
        ),
        key: 'remark',
        keyName: 'remark',
        slotName: 'enumText',
        props: {
          type: 'textarea',
          rows: 4,
          maxlength: 500,
          showWordLimit: true,
        },
        span: 18,
      },
    ],
  },
])

const submitForm = async () => {
  if (btnAuth.value?.response_scheme_manager) {
    return {
      remark: orderDescRef.value.remark,
      productTotalCost: orderDescRef.value.productTotalCost,
      productApplyFile: orderDescRef.value.productApplyFile ?? [],
    }
  }
  return {}
}

const validateForm = async () => {
  return true
}

defineExpose({
  submitForm,
  validateForm,
})
</script>

<style></style>
