<template>
  <div id="ContainerQuotaDetail" class="table-box">
    <sl-page-header
      title="容器配额详情"
      :icon="{
        class: 'page_rongqipeie',
        color: '#0052D9',
        size: '30px',
      }"
      :show-back="true"
      :back="{
        title: '返回列表',
        function: handleGoBack,
      }"
    >
    </sl-page-header>
    <el-scrollbar wrap-class="slb-detail-scroll-view" class="scroll-view">
      <div class="sl-card">
        <sl-form
          :show-block-title="false"
          :label-width="150"
          ref="slFormRef"
          v-model="detailData"
          :options="formOptions"
        ></sl-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, reactive } from 'vue'

import SlPageHeader from '@/components/SlPageHeader/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { getContainerQuotaDetail } from '@/api/modules/resourecenter'

const router = useRouter()
const route = useRoute()

// 获取资源详情ID
const resourceId = ref<string>((route.query.id as string) || '')

const detailData = reactive<any>({
  cqName: '',
  businessSystemName: '',
  catalogueDomainName: '',
  domainName: '',
  regionName: '',
  azName: '',
  vCpus: '',
  ram: '',
  gpuRatio: '',
  gpuVirtualMemory: '',
  gpuCore: '',
  applyUserName: '',
  applyTime: '',
  a4Account: '',
  a4Phone: '',
  createTime: '',
})

const formOptions = reactive<any[]>([
  {
    groupItems: [
      {
        label: '配额名称',
        type: 'text',
        key: 'cqName',
        span: 8,
      },
      {
        label: 'vCPU(核)',
        type: 'text',
        key: 'vCpus',
        span: 8,
      },
      {
        label: '内存(GB)',
        type: 'text',
        key: 'ram',
        span: 8,
      },
      {
        label: 'GPU算力',
        type: 'text',
        key: 'gpuRatio',
        span: 8,
      },
      {
        label: 'GPU显存(GB)',
        type: 'text',
        key: 'gpuVirtualMemory',
        span: 8,
      },
      {
        label: 'GPU卡数量(个)',
        type: 'text',
        key: 'gpuCore',
        span: 8,
      },
      {
        label: '云类型',
        type: 'text',
        key: 'catalogueDomainName',
        span: 8,
      },
      {
        label: '云平台',
        type: 'text',
        key: 'domainName',
        span: 8,
      },
      {
        label: '资源池',
        type: 'text',
        key: 'regionName',
        span: 8,
      },
      {
        label: '申请人',
        type: 'text',
        key: 'applyUserName',
        span: 8,
      },
      {
        label: '申请时长',
        type: 'text',
        key: 'applyTime',
        span: 8,
      },
      {
        label: '4A账号',
        type: 'text',
        key: 'a4Account',
        span: 8,
      },
      {
        label: '4A账号绑定的手机号',
        type: 'text',
        key: 'a4Phone',
        span: 8,
      },
      {
        label: '业务系统',
        type: 'text',
        key: 'businessSystemName',
        span: 8,
      },
      {
        label: '创建时间',
        type: 'text',
        key: 'createTime',
        span: 8,
      },
    ],
  },
])

const fetchResourceDetail = async () => {
  const res = await getContainerQuotaDetail({
    id: resourceId.value,
  })
  if (res && res.entity) {
    for (const key in detailData) {
      detailData[key] = res.entity[key]
    }
  }
}

// 返回上一页
const handleGoBack = () => {
  router.push({
    path: '/containerQuotas',
  })
}

// 初始化
onMounted(async () => {
  if (resourceId.value) {
    await fetchResourceDetail()
  }
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-x: hidden;
}

.sl-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin: 8px;
}
</style>
