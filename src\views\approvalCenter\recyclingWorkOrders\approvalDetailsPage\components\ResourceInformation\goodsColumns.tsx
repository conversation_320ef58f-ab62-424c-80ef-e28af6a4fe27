import type { ColumnProps } from '@/components/SlProTable/interface'
import type { RecyclingColumnsType } from '../../../interface/type'
import { useGlobalDicStore } from '@/stores/modules/dic'
import { h, ref, defineComponent } from 'vue'
import SlDialog from '@/components/SlDialog/index.vue'
const globalDic = useGlobalDicStore()
const { getDic } = globalDic
// 云服务器
const ecsColumns: RecyclingColumnsType['ecs'] = [
  { prop: 'vmName', label: '主机名称', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  { prop: 'osVersion', label: '系统版本', width: 120 },
  {
    prop: 'spec',
    label: '实例规格',
    minWidth: 180,
  },
  {
    prop: 'syncRecovery ',
    label: '是否组合回收',
    width: 120,
    render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
  },
  {
    prop: 'sysDisk',
    label: '系统盘',
    minWidth: '150px',
  },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row.evsModelList?.length
            ? (scope.row.evsModelList?.map((item: any) => item.dataDisk).join(',') ?? '--')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'ip', label: 'IP', minWidth: 220 },
  { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
  {
    label: '带宽',
    prop: 'eipModel.bandwidth',
    minWidth: 100,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 200 },
]
// 云硬盘
const evsColumns: RecyclingColumnsType['evs'] = [
  {
    label: '数据盘',
    prop: 'dataDisk',
    minWidth: 100,
  },
  {
    label: '是否挂载云主机',
    prop: 'mountDataDisk',
    minWidth: 130,
    render(scope) {
      return <div>{scope.row.vmName ? '是' : '否'}</div>
    },
  },
  { prop: 'vmName', label: '云主机', minWidth: 200 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// 云硬盘
const shareEvsColumns: RecyclingColumnsType['shareEvs'] = [
  {
    label: '数据盘',
    prop: 'dataDisk',
    minWidth: 100,
  },
  {
    label: '是否挂载云主机',
    prop: 'mountDataDisk',
    minWidth: 130,
    render(scope) {
      return <div>{scope.row.vmName ? '是' : '否'}</div>
    },
  },
  { prop: 'vmName', label: '云主机', minWidth: 200 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// 对象存储
const obsColumns: RecyclingColumnsType['obs'] = [
  { prop: 'obsName', label: '对象存储名称', minWidth: 200 },
  { prop: 'storeType', label: '存储类型', width: 100 },
  { prop: 'capacity', label: '容量', width: 100 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// 负载均衡
const slbColumns: RecyclingColumnsType['slb'] = [
  { prop: 'slbName', label: '负载均衡名称', minWidth: 200 },
  { prop: 'spec', label: '实例规格', minWidth: 200 },
  {
    prop: 'syncRecovery ',
    label: '是否组合回收',
    width: 120,
    render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
  },
  { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
  {
    prop: 'eipModel.bandwidth',
    label: '带宽大小',
    width: 100,
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]
// NAT网关
const natColumns: RecyclingColumnsType['nat'] = [
  { prop: 'natName', label: '网关名称', minWidth: 200 },
  { prop: 'spec', label: '实例规格', minWidth: 200 },
  {
    prop: 'syncRecovery ',
    label: '是否组合回收',
    width: 120,
    render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
  },
  { prop: 'vpcName', label: 'VPC', minWidth: 150 },
  {
    prop: 'subnetName',
    label: '子网',
    minWidth: 220,
    render(scope) {
      return (
        <div>
          {scope.row.planeNetworkModel?.subnets?.length
            ? (scope.row.planeNetworkModel?.subnets
                ?.map((item: any) => item.subnetName)
                .join(',') ?? '--')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
  { prop: 'eipModel.bandwidth', label: '带宽大小', width: 100 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// NAS
const nasColumns: RecyclingColumnsType['nas'] = [
  { prop: 'name', label: 'NAS名称', minWidth: 200 },
  { prop: 'path', label: '存储路径', minWidth: 150 },
  {
    label: '储存大小',
    prop: 'storageSize',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.storageSize ? scope.row.storageSize + 'GB' : '--'}</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// VPN
const vpnColumns: RecyclingColumnsType['vpn'] = [
  { prop: 'name', label: 'VPN名称', minWidth: 200 },
  { prop: 'maxConnection', label: '最大客户端数', minWidth: 120 },
  {
    label: '带宽',
    prop: 'bandwidth',
    minWidth: 120,
    render(scope) {
      return <div>{scope.row.bandwidth ? scope.row.bandwidth + 'M' : '--'} </div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// eip弹性ip
const eipColumns: RecyclingColumnsType['eip'] = [
  { label: '弹性公网名称', prop: 'eipName', minWidth: 180 },
  { label: '弹性公网地址', prop: 'eip', minWidth: 150 },
  {
    label: '带宽大小',
    prop: 'bandwidth',
    minWidth: 100,
  },
  { prop: 'relatedDeviceType', label: '绑定设备类型', minWidth: 150 },
  { prop: 'relatedDeviceName', label: '绑定设备名称', minWidth: 150 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

export const networkColumns: ColumnProps[] = [
  { prop: 'networkName', label: '网络名称', minWidth: 200 },
  { prop: 'catalogueDomainName', label: '云类型', minWidth: 100 },
  { prop: 'networkType', label: '网络类型', minWidth: 100 },
  { prop: 'domainName', label: '云平台', minWidth: 150 },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'orderCode', label: '工单编号', minWidth: 200 },
  { prop: 'billId', label: '计费号', width: 150 },
]

export const vpcColumns: ColumnProps[] = [
  { prop: 'vpcName', label: 'VPC名称', minWidth: 200 },
  { prop: 'tenantName', label: '租户', minWidth: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'orderCode', label: '工单编号', minWidth: 150 },
  { prop: 'cidr', label: '网段', minWidth: 150 },
  // { prop: 'subnetNum', label: '子网个数', minWidth: 100 },
  { prop: 'billId', label: '计费号', width: 150 },
  { prop: 'userName', label: '申请人', minWidth: 100 },
  // { prop: 'createdTime', label: '创建时间', minWidth: 150 },
]

// MySQL数据库
const mysqlColumns: RecyclingColumnsType['mysql'] = [
  { prop: 'vmName', label: 'MySQL云数据库', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  { prop: 'osVersion', label: '系统版本', width: 120 },
  { prop: 'spec', label: '实例规格', minWidth: 180 },
  {
    label: '系列',
    prop: 'deployType',
    minWidth: 100,
    enum: [
      { label: '单机版本', value: 'ALONE' },
      { label: '主备版本', value: 'COLONY' },
    ],
  },
  {
    prop: 'syncRecovery ',
    label: '是否组合回收',
    width: 120,
    render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
  },
  { prop: 'sysDisk', label: '系统盘', minWidth: '150px' },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row.evsModelList?.length
            ? (scope.row.evsModelList?.map((item: any) => item.dataDisk).join(',') ?? '--')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'ip', label: 'IP', minWidth: 220 },
  { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
  { label: '带宽', prop: 'eipModel.bandwidth', minWidth: 100 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 200 },
]

// PostgreSQL云数据库
const postgreSqlColumns: RecyclingColumnsType['postgreSql'] = [
  { prop: 'vmName', label: 'PostgreSQL云数据库', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  { prop: 'osVersion', label: '系统版本', width: 120 },
  { prop: 'spec', label: '实例规格', minWidth: 180 },
  {
    label: '系列',
    prop: 'deployType',
    minWidth: 100,
    enum: [
      { label: '单机版本', value: 'ALONE' },
      { label: '主备版本', value: 'COLONY' },
    ],
  },
  {
    prop: 'syncRecovery ',
    label: '是否组合回收',
    width: 120,
    render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
  },
  { prop: 'sysDisk', label: '系统盘', minWidth: '150px' },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row.evsModelList?.length
            ? (scope.row.evsModelList?.map((item: any) => item.dataDisk).join(',') ?? '--')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'ip', label: 'IP', minWidth: 220 },
  { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
  { label: '带宽', prop: 'eipModel.bandwidth', minWidth: 100 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 200 },
]

// Redis缓存
const redisColumns: RecyclingColumnsType['redis'] = [
  { prop: 'vmName', label: '通用Redis', minWidth: 250 },
  { prop: 'resourceId', label: '资源ID', width: 180 },
  { prop: 'osVersion', label: '系统版本', width: 120 },
  { prop: 'spec', label: '实例规格', minWidth: 180 },
  {
    prop: 'syncRecovery ',
    label: '是否组合回收',
    width: 120,
    render: ({ row }: any) => (row.syncRecovery === false ? '否' : '是'),
  },
  { prop: 'sysDisk', label: '系统盘', minWidth: '150px' },
  {
    prop: 'evsInfo',
    label: '数据盘',
    minWidth: '150px',
    render(scope) {
      return (
        <div>
          {scope.row.evsModelList?.length
            ? (scope.row.evsModelList?.map((item: any) => item.dataDisk).join(',') ?? '--')
            : '--'}
        </div>
      )
    },
  },
  { prop: 'ip', label: 'IP', minWidth: 220 },
  { prop: 'eipModel.eip', label: '弹性公网IP', minWidth: 220 },
  { label: '带宽', prop: 'eipModel.bandwidth', minWidth: 100 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 200 },
]

// 裸金属
const pmColumns: RecyclingColumnsType['pm'] = [
  { label: '裸金属名称', prop: 'pmName', minWidth: 150 },
  {
    label: 'CPU',
    prop: 'vcpus',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.vcpus ? scope.row.vcpus + '核' : '--'}</div>
    },
  },
  { label: '内存', prop: 'ram', minWidth: 100 },
  { label: '硬盘', prop: 'diskSize', minWidth: 100 },
  { label: '显卡型号', prop: 'gpuCardType', minWidth: 120 },
  { label: '显卡类型', prop: 'gpuType', minWidth: 120 },
  {
    label: '显卡数量',
    prop: 'gpuNum',
    minWidth: 100,
    render(scope) {
      return <div>{scope.row.gpuNum ? scope.row.gpuNum + '张' : '--'}</div>
    },
  },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

export const backupColumns: ColumnProps[] = [
  { prop: 'jobName', label: '云灾备名称', minWidth: 200 },
  {
    label: '备份类型',
    prop: 'backupType',
    minWidth: 100,
    enum: [
      { label: '云主机', value: 'ECS' },
      {
        label: '云硬盘',
        value: 'EVS',
      },
    ],
  },
  {
    label: '备份频率',
    prop: 'frequency',
    minWidth: 100,
    enum: [
      { label: '每天', value: 'days' },
      { label: '每周', value: 'weeks' },
    ],
  },
  {
    label: '星期',
    prop: 'daysOfWeek',
    minWidth: 100,
  },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
]

// Kafka
const kafkaColumns: RecyclingColumnsType['kafka'] = [
  { label: 'Kafka名称', prop: 'name', minWidth: 150 },
  { label: '数据流量', prop: 'dataFlow', minWidth: 120 },
  { label: '副本', prop: 'replication', minWidth: 100 },
  { label: '保留时间', prop: 'retainTime', minWidth: 120 },
  { label: '数据存储总量', prop: 'dataStorageTotal', minWidth: 120 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// Flink
const flinkColumns: RecyclingColumnsType['flink'] = [
  { label: 'Flink名称', prop: 'flinkName', minWidth: 150 },
  { label: 'vCPU', prop: 'vCpus', minWidth: 100 },
  { label: '内存', prop: 'ram', minWidth: 100 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// ElasticSearch
const esColumns: RecyclingColumnsType['es'] = [
  { label: '索引模板名称', prop: 'name', minWidth: 150 },
  {
    label: '索引模板',
    prop: 'indexTemplate',
    minWidth: 150,
    render(scope) {
      return h(
        defineComponent({
          setup() {
            const showDialog = ref(false)
            const handleClick = (e: MouseEvent) => {
              e.stopPropagation()
              showDialog.value = true
            }
            return () => [
              h(
                'span',
                {
                  style: 'color: #409EFF; cursor: pointer;',
                  onClick: handleClick,
                },
                '查看',
              ),
              showDialog.value &&
                h(
                  SlDialog,
                  {
                    modelValue: showDialog.value,
                    'onUpdate:modelValue': (val: boolean) => (showDialog.value = val),
                    title: '索引模板',
                    width: '600px',
                    showCancel: false,
                    showConfirm: false,
                    closeOnClickModal: true,
                    appendToBody: true,
                  },
                  {
                    default: () =>
                      h(
                        'pre',
                        {
                          style:
                            'max-height: 400px; overflow: auto; background: #f5f5f5; padding: 16px; border-radius: 4px;',
                        },
                        JSON.stringify(scope.row.indexTemplate, null, 2),
                      ),
                  },
                ),
            ]
          },
        }),
      )
    },
  },
  { label: '日均增量数据', prop: 'averageDailyIncrementData', minWidth: 120 },
  { label: '数据保留时间', prop: 'retainTime', minWidth: 120 },
  { label: '索引副本', prop: 'numberOfReplicas', minWidth: 100 },
  { label: '磁盘大小', prop: 'diskSize', minWidth: 100 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

// 国产Redis
const bldRedisColumns: RecyclingColumnsType['bldRedis'] = [
  { label: '实例名称', prop: 'name', minWidth: 150 },
  { label: '实例ip', prop: 'ip', minWidth: 150 },
  { label: 'CPU架构', prop: 'cpuArchitecture', minWidth: 120 },
  // { label: '业务系统名称', prop: 'businessSysName', minWidth: 150 },
  // { label: '用户名', prop: 'username', minWidth: 120 },
  { label: '申请时长', prop: 'applyTime', minWidth: 100, enum: getDic('time') },
  { prop: 'tenantName', label: '租户', minWidth: 200 },
  { prop: 'businessSystemName', label: '业务系统', minWidth: 150 },
  { prop: 'domainName', label: '所属云', width: 150 },
  { prop: 'regionName', label: '资源池', minWidth: 200 },
  { prop: 'expireTime', label: '到期时间', width: 180 },
]

const recyclingAllColumns: RecyclingColumnsType = {
  ecs: ecsColumns,
  gcs: ecsColumns,
  evs: evsColumns,
  shareEvs: shareEvsColumns,
  obs: obsColumns,
  slb: slbColumns,
  nat: natColumns,
  nas: nasColumns,
  vpn: vpnColumns,
  network: networkColumns,
  vpc: vpcColumns,
  eip: eipColumns,
  mysql: mysqlColumns,
  postgreSql: postgreSqlColumns,
  redis: redisColumns,
  backup: backupColumns,
  pm: pmColumns,
  kafka: kafkaColumns,
  flink: flinkColumns,
  es: esColumns,
  bldRedis: bldRedisColumns,
}

export default recyclingAllColumns

export const recyclingValueEnum = [
  { code: 'ecs', desc: '云主机', goodsList: 'ecsListExt' },
  { code: 'gcs', desc: 'GPU云主机', goodsList: 'gcsListExt' },
  { code: 'evs', desc: '云硬盘', goodsList: 'evsListExt' },
  { code: 'shareEvs', desc: '共享数据盘', goodsList: 'shareEvsListExt' },
  { code: 'obs', desc: '对象存储', goodsList: 'obsListExt' },
  { code: 'slb', desc: '负载均衡', goodsList: 'slbListExt' },
  { code: 'nat', desc: 'NAT网关', goodsList: 'natListExt' },
  { code: 'nas', desc: 'NAS', goodsList: 'nasListExt' },
  { code: 'vpn', desc: 'VPN', goodsList: 'vpnListExt' },
  { code: 'eip', desc: '弹性公网', goodsList: 'eipListExt' },
  { code: 'vpc', desc: 'VPC', goodsList: 'vpcListExt' },
  { code: 'network', desc: '网络', goodsList: 'networkListExt' },
  { code: 'mysql', desc: 'MySQL云数据库', goodsList: 'mysqlListExt' },
  { code: 'postgreSql', desc: 'PostgreSQL云数据库', goodsList: 'postgreSqlListExt' },
  { code: 'redis', desc: '通用Redis', goodsList: 'redisListExt' },
  { code: 'backup', desc: '云灾备', goodsList: 'backupListExt' },
  { code: 'pm', desc: '裸金属', goodsList: 'pmListExt' },
  { code: 'kafka', desc: 'Kafka', goodsList: 'kafkaListExt' },
  { code: 'flink', desc: 'Flink', goodsList: 'flinkListExt' },
  { code: 'es', desc: 'ElasticSearch', goodsList: 'esListExt' },
  { code: 'bldRedis', desc: '国产Redis', goodsList: 'bldRedisListExt' },
]

export const indexColumn: ColumnProps = { type: 'index', label: '序号', width: 55 }

export const messageColumn: ColumnProps = { prop: 'message', label: '失败原因', minWidth: 300 }

export const recoveryStatusList = [
  { label: '-', value: '0' },
  { label: '待回收', value: '1' },
  { label: '回收中', value: '2' },
  { label: '回收完成', value: '3' },
  { label: '回收失败', value: '4' },
  { label: '未回收', value: null },
]

export const decommissionStatusList = [
  { label: '退维成功', value: 'success' },
  { label: '退维中', value: 'running' },
  { label: '待退维', value: 'wait' },
  { label: '退维失败', value: 'fail' },
]
