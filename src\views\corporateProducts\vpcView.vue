<template>
  <div class="vpc-view">
    <div>
      <sl-page-header
        :title="`VPC详情 `"
        :show-back="true"
        :back="{
          title: '返回列表',
          function: () => router.go(-1),
        }"
      />
    </div>
    <sl-form
      v-if="formData.networks?.length"
      class="corporate-products​"
      label-position="left"
      :options="formOptions"
      :model-value="formData"
      :label-width="120"
      ref="formRef"
    >
      <template #subnet-slot="{ form, item }">
        <div>
          <SubnetView
            v-if="form.networks?.length"
            :subnets="form.networks?.[0]?.subnetDTOList ?? []"
          />
          <br />
          <Subnet v-if="canAddSubnet" :form="form" :item="item" :can-add-subnet="canAddSubnet" />
        </div>
      </template>
    </sl-form>

    <!-- 右下角价格面板 -->
    <div class="price-panel">
      <div class="con">
        <div class="price-section"></div>
        <div class="action-section">
          <el-button class="cancel-btn" @click="handleCancelAddSubnet">取消</el-button>

          <el-button
            v-if="canAddSubnet"
            type="primary"
            class="order-btn"
            @click="handleAddSubnetSuccess"
          >
            提交申请
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { addVpcSubnetBatch, getVpcDetail } from '@/api/modules/resourecenter'
import { ElMessage } from 'element-plus'
import { isValidAlphanumericWithUnderscore } from '../resourceCenter/utils'
import Subnet from './components/Subnet.vue'
import SubnetView from './components/SubnetView.vue'
import SlForm from '@/components/form/SlForm.vue'
import { uuid } from '@/utils'

const formData = ref<any>({
  vpcName: '',
  tenantName: '',
  ipRange: '',
  networks: [],
  subnet: [],
})

const formOptions = ref([
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '区域',
        type: 'text',
        getter: (form: any) =>
          form.azName ? `${form.domainName} , ${form.regionName} , ${form.azName}` : '',
        span: 24,
      },
      {
        label: '名称',
        type: 'text',
        key: 'vpcName',
        span: 13,
        getter: (form: any) => form.networks?.[0]?.vpcName ?? '',
      },
      {
        label: '租户',
        type: 'text',
        span: 13,
        getter: (form: any) => form.tenantName ?? '',
      },
      {
        label: '网段',
        type: 'text',
        key: 'ipRange',
        span: 24,
        getter: (form: any) => form.networks?.[0]?.cidr ?? '',
      },
    ],
  },
  {
    style: 'margin:10px;padding:15px 10px;',
    gutter: 40,
    groupItems: [
      {
        label: '子网',
        type: 'slot',
        slotName: 'subnet-slot',
        key: 'subnet',
        span: 16,
        maxRows: 10,
        addButtonText: '添加子网',
        emptyText: '暂无子网配置',
        showEmptyState: true,
        required: true,
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!formData.value.subnet.length) {
                callback(new Error('请输入子网'))
              }
              if (formData.value.subnet.some((item: any) => !item.name)) {
                callback(new Error('请输入子网名称'))
              }
              if (
                formData.value.subnet.some(
                  (item: any) => !isValidAlphanumericWithUnderscore(item.name),
                )
              ) {
                callback(new Error('子网名称不符合规范'))
              }
              if (
                formData.value.subnet.some(
                  (item: any) => !item.ip1 || !item.ip2 || !item.ip3 || !item.ip4,
                )
              ) {
                callback(new Error('请输入子网IP'))
              }
              if (formData.value.subnet.some((item: any) => !item.cidr)) {
                callback(new Error('请输入子网掩码'))
              }

              callback()
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
    ],
  },
])

const route = useRoute()
const router = useRouter()

const vpcId = route.query.vpcId as string
const addSubnet = route.query.addSubnet as string

// 是否可以添加子网
const canAddSubnet = ref(false)

// 获取VPC详情
const getVpcDetailInfo = async () => {
  try {
    const { entity } = await getVpcDetail({ vpcId })
    formData.value = JSON.parse(entity.detail)
    formData.value.subnet = []
    // 根据状态判断是否可以添加子网
    canAddSubnet.value = addSubnet == '1'
  } catch (error: any) {
    console.log(error)
  }
}

const getParams = () => {
  let subnetDTOList = formData.value.subnet.map((item: any) => ({
    subnetName: item.name,
    startIp: `${item.ip1}.${item.ip2}.${item.ip3}.${item.ip4}`,
    netmask: item.cidr,
    // 生成uuid
    uuid: uuid(16),
  }))
  let form = JSON.parse(JSON.stringify(formData.value))
  form.networks[0].subnetDTOList.push(...subnetDTOList)
  delete form.subnet
  const detail = JSON.stringify({
    ...form,
  })
  return {
    vpcId,
    detail,
    subnetDTOList,
  }
}

const formRef = ref<InstanceType<typeof SlForm>>()

// 添加子网成功回调
const handleAddSubnetSuccess = async () => {
  // 1.校验
  const valid = await formRef.value?.validate()
  if (!valid) return
  await addVpcSubnetBatch(getParams())
  ElMessage.success('添加子网成功')
  handleCancelAddSubnet()
}

// 取消添加子网
const handleCancelAddSubnet = () => {
  router.go(-1)
}

onMounted(() => {
  if (!vpcId) {
    ElMessage.error('缺少VPC ID参数')
    router.go(-1)
    return
  }

  getVpcDetailInfo()
})
</script>

<style scoped>
/* 通用的form label加粗样式 */
.corporate-products​ :deep(.el-form-item__label) {
  font-weight: bold;
}
.corporate-products​ :deep(.table-main) {
  border: 1px solid #e4e7ed;
  box-shadow: none;
}

/* 网段配置样式 */
.subnet-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.subnet-input-section {
  display: flex;
  align-items: center;
}

.subnet-recommendations {
  display: flex;
}

.recommendation-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 12px;
}

.recommendation-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-left: 12px;
}

.recommendation-item {
  display: flex;
  justify-content: space-between;
  min-width: 140px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.subnet-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.action-text {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
  flex: 0.9;
}

.eip-config :deep(.el-checkbox) {
  font-size: 14px;
}

.eip-config :deep(.el-checkbox__label) {
  font-weight: normal;
  color: #606266;
}

/* 展开按钮容器样式 */
.expand-container {
  display: flex;
  align-items: center;
  position: relative;
}
/* 图标样式 */
.expand-icon {
  transition: transform 0.3s ease;
  font-size: 14px;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

/* 价格面板样式 */
.price-panel {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0 10px;

  .con {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.price-section {
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.price-info {
  display: block;
}

.discount-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.discount-price {
  font-size: 24px;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 8px;
}

.discount-badge {
  font-size: 12px;
  color: #ff6b35;
  background: #fff2e8;
  padding: 2px 6px;
  border-radius: 4px;
}

.original-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.original-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.original-price {
  font-size: 12px;
  color: #909399;
  text-decoration: line-through;
  margin-right: 8px;
}

.detail-link {
  font-size: 12px;
  padding: 0;
  height: auto;
}

.warning-text {
  font-size: 11px;
  color: #f56c6c;
  line-height: 1.4;
}

.action-section {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  align-self: end;
}

.cancel-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #606266;
  border-color: #dcdfe6;
}

.cart-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}

.order-btn {
  flex: 1;
  height: 36px;
  font-size: 14px;
}
</style>
