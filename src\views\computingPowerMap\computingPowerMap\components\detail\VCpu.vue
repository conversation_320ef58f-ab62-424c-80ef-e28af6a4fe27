<template>
  <div class="vcpu-container flx-justify-between" @click="handleClick">
    <div class="vcpu-chart">
      <svg width="150" height="140" viewBox="0 0 120 120">
        <circle cx="60" cy="60" r="50" fill="none" stroke="#96b5eb" stroke-width="8" />
        <circle
          cx="60"
          cy="60"
          r="50"
          fill="none"
          stroke="url(#storageGradient)"
          stroke-width="8"
          stroke-linecap="round"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="strokeDashoffset"
          transform="rotate(-90 60 60)"
        />
        <defs>
          <linearGradient id="storageGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color: #00d4ff; stop-opacity: 1" />
            <stop offset="100%" style="stop-color: #0066ff; stop-opacity: 1" />
          </linearGradient>
        </defs>
      </svg>
      <div class="progress-text">
        <div class="percentage">{{ vcpuData.percentage.toFixed(2) }}%</div>
        <div>分配率</div>
      </div>
    </div>

    <div class="vcpu-details">
      <div class="vcpu-header">
        <div class="vcpu-total">
          <span class="total-label">vCpu总数</span>
          <span class="total-value">
            <span class="valueNumber ml5 mr5">
              {{ vcpuData.total }}
            </span>
            核
          </span>
        </div>
      </div>
      <div class="detail-item">
        <div class="detail-icon blue"></div>
        <div class="detail-info">
          <div class="detail-label">vCPU已开通数</div>
          <div class="detail-value">
            <span class="valueNumber">{{ vcpuData.used }}</span>
            核
          </div>
        </div>
      </div>
      <div class="detail-item">
        <div class="detail-icon light-blue"></div>
        <div class="detail-info">
          <div class="detail-label">vCPU剩余量</div>
          <div class="detail-value">
            <span class="valueNumber">{{ vcpuData.remaining }}</span>
            核
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义 props
const props = defineProps<{
  vcpuData?: {
    total: number
    used: number
    remaining: number
    percentage: number
  }
}>()

// 定义事件
const emit = defineEmits<{
  click: []
}>()

// vCPU数据，使用 props 或默认值
const vcpuData = computed(
  () =>
    props.vcpuData || {
      total: 0,
      used: 0,
      remaining: 0,
      percentage: 0,
    },
)

// 圆形进度条计算
const radius = 50
const circumference = 2 * Math.PI * radius

const strokeDashoffset = computed(() => {
  const progress = vcpuData.value.percentage / 100
  return circumference * (1 - progress)
})

// 点击事件处理
const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
.vcpu-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.valueNumber {
  font-size: 26px;
  font-weight: bold;
  color: #004fb1;
}

.vcpu-chart {
  width: 150px;

  position: relative;
  .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: 14px;
    .percentage {
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.vcpu-details {
  flex: 1;
}

.vcpu-header {
  border-bottom: 10px solid transparent;
  border-image: url('/images/computingPower/comPowerSplitIconRevolve.png') 10;
  text-align: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.detail-icon {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.detail-icon.blue {
  background-color: #4a90e2;
}

.detail-icon.light-blue {
  background-color: #87ceeb;
}

.detail-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

:deep(.el-progress-bar__outer) {
  background-color: #96b5eb;
}
</style>
