<template>
  <div class="memory-container" @click="handleClick">
    <div class="memory-header flx-justify-between">
      <div class="total-label">
        <img
          class="memory-icon"
          src="/images/computingPower/ram-fill.png"
          alt=""
          width="22"
          height="14"
        />
        内存总量
      </div>
      <div class="total-value valueNumber">{{ memoryData.total }}{{ memoryData.memoryunits }}</div>
    </div>

    <div class="memory-content">
      <div class="progress-bar mb20">
        <!-- 背景灰色 -->
        <el-progress
          :percentage="memoryData.percentage"
          :color="'linear-gradient(to right, rgba(61, 110, 239, 1), rgba(61, 207, 255, 1))'"
          :stroke-width="16"
          :show-text="true"
          :text-inside="true"
          :format="getLabel"
        />
      </div>

      <div class="memory-details flx-justify-between">
        <div class="detail-item">
          <div class="detail-icon blue"></div>
          <div class="detail-info">
            <div class="detail-value valueNumber">
              {{ memoryData.used }}{{ memoryData.memoryunits }}
            </div>
            <div class="detail-label">内存已开通</div>
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-icon light-blue"></div>
          <div class="detail-info">
            <div class="detail-value valueNumber">
              {{ memoryData.remaining }}{{ memoryData.memoryunits }}
            </div>
            <div class="detail-label">内存剩余量</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义 props
const props = defineProps<{
  memoryData?: {
    total: number
    used: number
    remaining: number
    percentage: number
    memoryunits: string
  }
}>()

// 定义事件
const emit = defineEmits<{
  click: []
}>()

// 内存数据，使用 props 或默认值
const memoryData = computed(
  () =>
    props.memoryData || {
      total: 0,
      used: 0,
      remaining: 0,
      percentage: 0,
      memoryunits: 'GB',
    },
)

let getLabel = (percent: number) => {
  let str = '分配率：' + percent.toFixed(2).replace(/\.?0+$/, '')
  return str + '%' || '0%'
}

// 点击事件处理
const handleClick = () => {
  emit('click')
}
</script>

<style scoped>
.memory-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  padding-top: 20px;
}

.memory-header {
  margin-bottom: 20px;
}

.valueNumber {
  font-size: 32px;
  font-weight: bold;
  color: #004fb1;
}

.detail-item {
  display: flex;
  gap: 8px;
}

.detail-icon {
  width: 12px;
  height: 12px;
  margin-top: 10px;
  border-radius: 2px;
  flex-shrink: 0;
}
.blue {
  background-color: #004fb1;
}

.light-blue {
  background-color: #87ceeb;
}

:deep(.el-progress-bar__outer) {
  background-color: #96b5eb;
}
</style>
