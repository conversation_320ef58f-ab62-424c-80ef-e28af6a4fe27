<template>
  <div
    class="no-card"
    :class="{ 'show-checkbox-all': showCheckboxAll() }"
    v-if="goodsType && goodsType !== 'unknown'"
  >
    <div v-if="pageStatus" class="flx-align-center" style="width: 50%; max-width: 900px">
      <!-- 架构审核 -->
      <template v-if="btnAuth.schema_administrator">
        <div class="title-input">
          <el-form-item label="批量选择资源池:">
            <el-select
              clearable
              filterable
              v-model="poolId"
              style="width: 100%"
              placeholder="请选择资源池"
            >
              <el-option
                v-for="itme in resourcePoolsDic"
                :key="itme.id"
                :label="itme.name"
                :value="itme.id"
              />
            </el-select>
          </el-form-item>
        </div>
        <el-button :disabled="!poolId" type="primary" @click="() => setPool()">确 定</el-button>
      </template>
    </div>
    <sl-pro-table
      :columns="tableColumns()"
      :data="goodsAllDetails"
      :pagination="false"
      :is-show-search="false"
      row-key="ids"
      ref="proTable"
    >
    </sl-pro-table>
    <!-- 弹窗 -->
    <el-dialog
      title="资源详情"
      v-model="dialogVisible"
      width="80%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <sl-pro-table
        :columns="tableColumns(false)"
        :data="goodsDetails"
        :pagination="false"
        :is-show-search="false"
      >
        <template #originName="{ row }">
          {{ row.goodsName }}
        </template>
      </sl-pro-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button :disabled="disCancel" @click="dialogVisible = false">
            {{ isCanOperation ? '取 消' : '关 闭' }}
            <el-tooltip
              v-if="disCancel"
              key="disCancel"
              class="box-item"
              effect="dark"
              content="当前有资源状态发生改变，请点击“提交”按钮合并工单。"
              placement="top-start"
            >
              <el-icon class="ml5">
                <el-icon><Warning /></el-icon>
              </el-icon>
            </el-tooltip>
          </el-button>
          <el-button v-if="isCanOperation" type="primary" @click="submit">提 交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { computed, inject, ref, toRefs, watch, type VNode } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { goodsTypeEnum, type goodsTypeCodeEnum } from '../../../interface/type'
import { useVModel } from '@vueuse/core'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import goodsAllColumns from './goodsColumns'
import { showTips } from '@/utils'
import useModelList, { type goodsDetailsType } from './useModelList'

// 列表状态  开通
type PropsType = {
  goodsList: FormDataType[]
  resourcePoolsDic: any[] //资源池字典
  goodsType: goodsTypeCodeEnum
  orderId: string
  azList: FormDataType
  orderDesc: FormDataType //工单详情信息
  domainCodeStatus: boolean //某些云平台集合的权限
}
const props = withDefaults(defineProps<PropsType>(), {})
const { resourcePoolsDic, goodsType } = toRefs(props)
const pageStatus = inject('pageStatus', ref(true))

const btnAuth = inject(
  'btnAuth',
  ref({
    resource_creation: false,
    schema_administrator: false,
    autodit_end: false,
  }),
)

// 是否可操作合并拆分 且 添加开通数据以及分配资源池
const isCanOperation = computed(() => {
  return pageStatus.value && (btnAuth.value.resource_creation || btnAuth.value.schema_administrator)
})

const emit = defineEmits(['update:goodsList'])
const goodsListRef = useVModel(props, 'goodsList', emit)

/**
 * 生成查重的keys
 */
const getKeys = () => {
  const keys: string[] = ['idHash', 'status', 'regionId']
  if (btnAuth.value.resource_creation) keys.push(...goodsTypeEnum[goodsType.value]?.params)
  return keys
}

const { insertAtIndexAndClear, mergeParent, uniqueByPropertiesWithCustomKey, createObjectsArray } =
  useModelList(getKeys)

const goodsAllDetails = computed({
  get() {
    if (!goodsListRef.value?.length) return []

    const newGoods = JSON.parse(JSON.stringify(goodsListRef.value))
    return uniqueByPropertiesWithCustomKey<goodsDetailsType>(newGoods, getKeys(), 'openNum')
  },
  set(val) {
    const arrall: any[] = []
    val.map((row: any) => {
      arrall.push(...createObjectsArray(row))
    })

    goodsListRef.value = arrall
  },
})

// -----------------------弹窗数据---------------------

const goodsDetails = ref<goodsDetailsType[]>([])
const goodIndex = ref<number>(-1)
const dialogVisible = ref(false)

// 是否禁用取消
const disCancel = ref<boolean>(false)

const close = () => {
  goodIndex.value = -1
  goodsDetails.value = []
  disCancel.value = false
}

const submit = () => {
  const newGoods = JSON.parse(JSON.stringify(goodsDetails.value))
  const items = uniqueByPropertiesWithCustomKey(newGoods, getKeys(), 'openNum').map((item) => {
    return { ...item }
  })
  // 合并父级
  const goods = JSON.parse(JSON.stringify(goodsAllDetails.value))

  insertAtIndexAndClear(goods, goodIndex.value, items)

  goodsAllDetails.value = mergeParent(goods)
  dialogVisible.value = false
}

// -----------------------获取字典---------------------

const undateGoodsAllDetails = () => {
  if (!dialogVisible.value) goodsAllDetails.value = mergeParent(goodsAllDetails.value)
}

// 实例规格 changeFn

// -----------------------表单列表---------------------

const editFn = (row: any, index: number) => {
  goodsDetails.value = createObjectsArray(row)
  goodIndex.value = index
  dialogVisible.value = true
}

// 禁用资源池
const disabledResource = (row: goodsDetailsType) => {
  if (!pageStatus.value || !btnAuth.value.schema_administrator) return true

  // 当为云硬盘的时候判断
  if (['evs', 'eip', 'shareEvs'].includes(goodsType.value) && row['vmName']) return true
  return false
}

// 操作按钮

const operationRender = (
  { row, $index }: { row: any; $index: number },
  falg: boolean = true,
): VNode => {
  return (
    <>
      {falg && (
        <el-button type="primary" onClick={() => editFn(row, $index)} link>
          详情
        </el-button>
      )}
    </>
  )
}

const operationColumns = (falg: boolean = true) => {
  const width = btnAuth.value.resource_creation ? '140px' : '80px'

  return {
    prop: 'operation',
    label: '操作',
    align: 'center',
    fixed: 'right',
    width,
    render: ({ row, $index }: { row: any; $index: number }) =>
      operationRender({ row, $index }, falg),
  }
}

const columns = ref<ColumnProps<goodsDetailsType>[]>([
  {
    prop: 'openNum',
    label: '数量',
    align: 'center',
    width: '100px',
  },
  {
    prop: 'regionId',
    label: '资源池',
    align: 'center',
    width: '220px',
    render: ({ row }: { row: goodsDetailsType }): VNode => {
      return (
        <>
          {disabledResource(row) ? (
            <span>{row['regionName'] ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              v-model={row.regionId}
              style="width: 100%"
              placeholder="请选择资源池"
              onChange={() => {
                row['regionName'] = ''
                row['regionCode'] = ''
                let obj = resourcePoolsDic.value.find((item: any) => item.id === row.regionId)
                if (obj) {
                  row['regionName'] = obj.name
                  row['regionCode'] = obj.code
                }
                undateGoodsAllDetails()
              }}
            >
              {resourcePoolsDic.value.map((option) => (
                <el-option key={option.id} label={option.name} value={option.id} />
              ))}
            </el-select>
          )}
        </>
      )
    },
  },
])

const tableColumns = computed(() => (falg: boolean = true) => {
  if (!goodsType.value || goodsType.value === 'unknown') return []

  let indexColumn: ColumnProps = { type: 'index', label: '序号', width: 55 }
  const newColumns = [indexColumn, ...goodsAllColumns[goodsType.value], ...columns.value]
  if (falg || btnAuth.value.resource_creation) newColumns.push(operationColumns(falg))
  if (
    falg &&
    pageStatus.value &&
    (btnAuth.value.resource_creation || btnAuth.value.schema_administrator)
  )
    newColumns.unshift(selectedColumns())
  return newColumns
})

//  -------------批量赋值操作---------------------
const proTable = ref<ProTableInstance>()
const poolId = ref('')
/**
 *
 * @param poolIdList
 * @param falg true: 资源池 false: 可用区
 */
const chageDetails = async (poolIdList: string[]) => {
  let obj = resourcePoolsDic.value.find((item: any) => item.id === poolId.value)

  const arr = goodsAllDetails.value.map((item: any) => {
    const orderGoodsIdString = item.ids?.join('|')
    if (poolIdList.includes(orderGoodsIdString!)) {
      item['regionId'] = obj.id
      item['regionName'] = obj.name
      item['regionCode'] = obj.code
    }
    return {
      ...item,
    }
  })

  return arr
}

const showCheckboxAll = () => {
  if (!btnAuth.value.resource_creation) return false
  if (!goodsAllDetails.value.length) return false

  const firstValue = goodsAllDetails.value[0]['regionId']
  return !goodsAllDetails.value.every((item: any) => item['regionId'] === firstValue)
}
const selectable = (row: any) => {
  // 当为云硬盘的时候判断
  if (['evs', 'eip', 'shareEvs'].includes(goodsType.value) && row['vmName']) return false

  return true
}

const selectedColumns = (): ColumnProps => {
  return {
    type: 'selection',
    align: 'center',
    fixed: 'left',
    width: '55px',
    selectable: selectable,
  }
}

watch(
  () => resourcePoolsDic.value,
  (newVal) => {
    if (!newVal.length) {
      poolId.value = ''
    }
  },
)

const setPool = async () => {
  if (!proTable.value?.isSelected) return showTips('请选择要操作的数据')
  await ElMessageBox.confirm('确定要批量赋值吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })

  const poolIdList = proTable.value?.selectedListIds.map((item: any) => item.join('|'))

  const arr = await chageDetails(poolIdList)

  goodsAllDetails.value = mergeParent(arr)
  proTable.value?.clearSelection()
}
</script>

<style lang="scss" scoped>
.title-input {
  flex: 1;
  padding-top: 20px;
  margin-right: 10px;
}

/* 隐藏全选复选框 */
.show-checkbox-all {
  :deep() {
    .el-table .el-table__header th .el-checkbox {
      display: none;
    }
  }
}
</style>
