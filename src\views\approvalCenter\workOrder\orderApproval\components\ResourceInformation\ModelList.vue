<template>
  <div
    class="no-card"
    :class="{ 'show-checkbox-all': showCheckboxAll() }"
    v-if="goodsType && goodsType !== 'unknown'"
  >
    <div v-if="pageStatus" class="flx-align-center" style="width: 50%; max-width: 900px">
      <!-- 架构审核 -->
      <template v-if="btnAuth.schema_administrator">
        <div class="title-input">
          <el-form-item label="批量选择资源池:">
            <el-select
              clearable
              filterable
              v-model="poolId"
              style="width: 100%"
              placeholder="请选择资源池"
            >
              <el-option
                v-for="itme in resourcePoolsDic"
                :key="itme.id"
                :label="itme.name"
                :value="itme.id"
              />
            </el-select>
          </el-form-item>
        </div>
        <el-button :disabled="!poolId" type="primary" @click="() => setPool()">确 定</el-button>
      </template>
      <!-- 资源开通 -->
      <template
        v-if="
          btnAuth.resource_creation &&
          !['cq', 'backup', 'nas', 'kafka', 'flink', 'es', 'pm', 'bldRedis'].includes(goodsType)
        "
      >
        <div class="title-input">
          <el-form-item label="批量选择可用区:">
            <el-select
              clearable
              filterable
              v-model="azId"
              style="width: 100%"
              placeholder="请选择可用区"
            >
              <el-option
                v-for="itme in azList[selePoolId]"
                :key="itme.id"
                :label="itme.name"
                :value="itme.id"
              />
              <el-option v-if="!selePoolId" disabled label="请先选择要操作的资源" :value="''" />
            </el-select>
          </el-form-item>
        </div>
        <el-button :disabled="!azId" type="primary" @click="() => setPool(false)">确 定</el-button>
      </template>
    </div>
    <sl-pro-table
      :columns="tableColumns()"
      :data="goodsAllDetails"
      :pagination="false"
      :is-show-search="false"
      row-key="ids"
      ref="proTable"
    >
    </sl-pro-table>
    <!-- 弹窗 -->
    <el-dialog
      title="资源详情"
      v-model="dialogVisible"
      width="80%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <sl-pro-table
        :columns="tableColumns(false)"
        :data="goodsDetails"
        :pagination="false"
        :is-show-search="false"
      >
        <template #originName="{ row }">
          {{ row.goodsName }}
        </template>
      </sl-pro-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button :disabled="disCancel" @click="dialogVisible = false">
            {{ isCanOperation ? '取 消' : '关 闭' }}
            <el-tooltip
              v-if="disCancel"
              key="disCancel"
              class="box-item"
              effect="dark"
              content="当前有资源状态发生改变，请点击“提交”按钮合并工单。"
              placement="top-start"
            >
              <el-icon class="ml5">
                <el-icon><Warning /></el-icon>
              </el-icon>
            </el-tooltip>
          </el-button>
          <el-button v-if="isCanOperation" type="primary" @click="submit">提 交</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 配置网络 -->
    <ConfigureNetwork @update-networks="updateNetworks" ref="configureNetworkRef" />
  </div>
</template>

<script setup lang="tsx">
import { computed, inject, ref, toRefs, type VNode } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { goodsTypeEnum, type goodsTypeCodeEnum } from '../../../interface/type'
import { useVModel } from '@vueuse/core'
import type { ColumnProps, ProTableInstance } from '@/components/SlProTable/interface'
import goodsAllColumns from './goodsColumns'
import { showTips, uniqueByKeys } from '@/utils'
import useModelList, { type goodsDetailsType } from './useModelList'
import {
  standardWorkOrderResOpenApi,
  getvpcTreeApi,
  getListFlavorApi,
  getNetworkTreeApi,
} from '@/api/modules/approvalCenter'
import ConfigureNetwork from './ConfigureNetwork.vue'
// 列表状态  开通
type PropsType = {
  goodsList: FormDataType[]
  resourcePoolsDic: any[] //资源池字典
  goodsType: goodsTypeCodeEnum
  orderId: string
  azList: FormDataType
  orderDesc: FormDataType //工单详情信息
  domainCodeStatus: boolean //某些云平台集合的权限
}
const props = withDefaults(defineProps<PropsType>(), {})
const { resourcePoolsDic, goodsType, azList } = toRefs(props)
const pageStatus = inject('pageStatus', ref(true))
const isInnovationPool = inject('isInnovationPool', ref(false))
const innovationWorkList = inject('innovationWorkList', ref<FormDataType>({})) //网络列表
const isOffline = inject('isOffline', ref(false))

const btnAuth = inject(
  'btnAuth',
  ref({
    resource_creation: false,
    schema_administrator: false,
    autodit_end: false,
  }),
)

// 是否可操作合并拆分 且 添加开通数据以及分配资源池
const isCanOperation = computed(() => {
  return pageStatus.value && (btnAuth.value.resource_creation || btnAuth.value.schema_administrator)
})

const emit = defineEmits(['update:goodsList'])
const goodsListRef = useVModel(props, 'goodsList', emit)

// 是否包含configureTheNetwork
const isConfigureNetwork = computed(() =>
  ['ecs', 'gcs', 'mysql', 'redis'].includes(goodsType.value),
)

const isMultiple = computed(
  () =>
    props.orderDesc?.domainCode === 'plf_prov_nwc_zj_nfvo' &&
    ['ecs', 'mysql', 'redis'].includes(goodsType.value),
)

// 状态字典
const statuses = [
  { value: 'wait_open', label: '待开通' },
  { value: 'opening', label: '开通中' },
  { value: 'open_success', label: '开通成功' },
  { value: 'open_fail', label: '开通失败' },
]

/**
 * 生成查重的keys
 */
const getKeys = () => {
  const keys: string[] = ['idHash', 'status', 'regionId']
  if (btnAuth.value.resource_creation) keys.push(...goodsTypeEnum[goodsType.value]?.params)
  return keys
}

const { insertAtIndexAndClear, mergeParent, uniqueByPropertiesWithCustomKey, createObjectsArray } =
  useModelList(getKeys)

const goodsAllDetails = computed({
  get() {
    if (!goodsListRef.value?.length) return []

    const newGoods = JSON.parse(JSON.stringify(goodsListRef.value))
    return uniqueByPropertiesWithCustomKey<goodsDetailsType>(newGoods, getKeys(), 'openNum')
  },
  set(val) {
    const arrall: any[] = []
    val.map((row: any) => {
      arrall.push(...createObjectsArray(row))
    })

    goodsListRef.value = arrall
  },
})

// -----------------------弹窗数据---------------------

const goodsDetails = ref<goodsDetailsType[]>([])
const goodIndex = ref<number>(-1)
const dialogVisible = ref(false)

// 是否禁用取消
const disCancel = ref<boolean>(false)

const close = () => {
  goodIndex.value = -1
  goodsDetails.value = []
  disCancel.value = false
}

const submit = () => {
  const newGoods = JSON.parse(JSON.stringify(goodsDetails.value))
  const items = uniqueByPropertiesWithCustomKey(newGoods, getKeys(), 'openNum').map((item) => {
    return { ...item }
  })
  // 合并父级
  const goods = JSON.parse(JSON.stringify(goodsAllDetails.value))

  insertAtIndexAndClear(goods, goodIndex.value, items)

  goodsAllDetails.value = mergeParent(goods)
  dialogVisible.value = false
}

// -----------------------获取字典---------------------

//获取云主机可用区调用字典
// 模板字典
const specDic = ref<FormDataType>({})
// 获取模板字典
const getSpecDic = async (ids: any, key: string) => {
  if (specDic.value[key]) return
  if (
    !['ecs', 'mysql', 'redis'].includes(goodsType.value) ||
    props.orderDesc?.domainCode !== 'plf_prov_nwc_zj_nfvo'
  )
    return
  const { entity } = await getListFlavorApi(ids)
  specDic.value = { ...specDic.value, [key]: entity }
}
const networkDic = ref<FormDataType>({})
// 公网字典
const changeAz = async (value: number, row: goodsDetailsType, falg: boolean = true) => {
  row['azName'] = ''
  row['azCode'] = ''
  row['azId'] = ''
  row['subnetIds'] = []
  row['subnetName'] = ''
  row['vpcName'] = ''
  row['vpcCode'] = ''
  row['vpcId'] = ''
  row['templateCode'] = ''
  row['privateNetworkDic'] = []
  !isInnovationPool.value && (row['planeNetworkModel'] = [])
  const obj = azList.value[row.regionId].find((item: any) => item.id === value)
  if (obj) {
    row['azName'] = obj.name
    row['azCode'] = obj.code
    row['azId'] = obj.id
    let params = {
      orderId: props.orderId,
      regionCode: row.regionCode,
      azCode: obj.code,
    }
    let ids = {
      regionId: row.regionId,
      azId: row.azId,
      name: row.flavorName,
    }
    // change 时间获取
    falg && (await getNetworkDic(params, `${row.regionId}-${row.azId}`))
    falg && (await getSpecDic(ids, `${row.azId}-${row.flavorName}`))
  }
  falg && undateGoodsAllDetails()
  return {
    ...row,
  }
}
//网络接口
const getNetworkDic = async (params: any, key: string) => {
  if (isInnovationPool.value) return
  if (networkDic.value[key]) return
  // 配置网络 清空
  if (isConfigureNetwork.value) return

  const { entity } = props.domainCodeStatus
    ? await getNetworkTreeApi(params)
    : await getvpcTreeApi(params)

  let arr = entity.map((item: any) => {
    return {
      ...item,
      name: item.vpcName ? item.vpcName : item.name,
      subnetOrderList: item.subnetOrderList ? item.subnetOrderList : item.vpcSubnetOrderList,
    }
  })
  networkDic.value[key] = arr
}

// 私网字典
const getPrivateNetworkDic = async (value: number, row: any) => {
  row['subnetIds'] = []
  row['subnetName'] = ''
  row['vpcName'] = ''
  row['vpcCode'] = ''
  if (!value) row['privateNetworkDic'] = []
  else {
    const obj = (
      isInnovationPool.value
        ? innovationWorkList.value[row.regionCode]
        : networkDic.value[`${row.regionId}-${row.azId}`]
    ).find((item: any) => item.id === value)
    row['vpcName'] = obj.name
    row['vpcCode'] = obj.code
    row['privateNetworkDic'] = obj?.subnetOrderList ?? []
  }
  undateGoodsAllDetails()
}

const undateGoodsAllDetails = () => {
  if (!dialogVisible.value) goodsAllDetails.value = mergeParent(goodsAllDetails.value)
}

// 实例规格 changeFn

// -----------------------表单列表---------------------

const editFn = (row: any, index: number) => {
  goodsDetails.value = createObjectsArray(row)
  goodIndex.value = index
  dialogVisible.value = true
}

/**
 * 当为开通中 和 开通成功时 禁用
 * @param status 状态
 */

const rowStatus = (status: string) => {
  return status && ['opening', 'open_success', 'open_fail'].includes(status)
}

/**
 * 当维开通中 和 开通成功时 禁用
 * @param status 状态
 */
const rowStatusBtn = (status: string) => {
  return status && ['opening', 'open_success'].includes(status)
}

// 禁用资源池
const disabledResource = (row: goodsDetailsType) => {
  if (!pageStatus.value || !btnAuth.value.schema_administrator) return true

  // 当为云硬盘的时候判断
  if (['evs', 'eip'].includes(goodsType.value) && row['vmName']) return true
  return false
}
// 禁用网络
const disabledNetwork = (row: goodsDetailsType) => {
  if (!pageStatus.value || !btnAuth.value.resource_creation) return true

  if (rowStatus(row.status)) return true

  // 当为云硬盘的时候判断
  if (['evs', 'eip'].includes(goodsType.value) && row['vmName']) return true

  return false
}

// 开通
// 网络开通函数
const networkOpen = async (row: any) => {
  // 1.组合数据 校验
  let obj: any = {
    standardWorkOrderId: props.orderId,
    openResType: goodsType.value,
  }
  // 详情内 id 与外层的id参数不同
  if (dialogVisible.value) obj['openResIds'] = [row.id]
  else obj['openResIds'] = row.ids

  let flag = true
  let goodsParams = goodsTypeEnum[goodsType.value]?.params ?? []

  // 2.1 当不为 网络云-创新资源池 的时候 不校验 模板字段
  if (props.orderDesc?.domainCode !== 'plf_prov_nwc_zj_nfvo') {
    goodsParams = goodsParams.filter((item) => item !== 'templateCode')
  }
  goodsParams.forEach((item) => {
    flag = flag && row[item]
  })
  if (!flag) {
    return showTips('请先选择要提交的数据！')
  }
  const setVlueKey = ['azCode', 'azId', 'azName']
  setVlueKey.map((item) => {
    if (row[item]) obj[item] = row[item]
  })
  row['templateCode'] && (obj['templateCode'] = row['templateCode'])
  // 配置网络的入参
  if (isConfigureNetwork.value) {
    if (!row.planeNetworkModel || !row.planeNetworkModel?.length)
      return showTips('请配置网络信息！')
    obj['planeNetworkModelList'] = row.planeNetworkModel.map((item: any) => {
      return {
        type: item.type,
        id: item.id,
        name: item.name,
        plane: item.plane,
        subnets: item.subnets,
        sort: item.sort,
      }
    })
  } else {
    // 普通的网络
    obj['planeNetworkModelList'] = [
      {
        type: row.vpcType,
        id: row.vpcId,
        name: row.vpcName,
        plane: row.plane,
        subnets: row.subnets,
        sort: 0,
      },
    ]
  }

  // 重新开通先做个假的
  if (row.status === 'open_fail' && goodsType.value !== 'cq') {
    row.status = 'opening'
    row.statusCn = '开通中'
    ElMessage.success('已提交开通')
    return
  }

  try {
    await standardWorkOrderResOpenApi(obj)
    row.status = 'opening'
    row.statusCn = '开通中'
    ElMessage.success('已提交开通')
  } catch (e) {
    console.error(e)
    disCancel.value = false
  }
}

// 操作按钮

const operationRender = (
  { row, $index }: { row: any; $index: number },
  falg: boolean = true,
): VNode => {
  return (
    <>
      {btnAuth.value.resource_creation && pageStatus.value && !isOffline.value && (
        <el-button
          type="primary"
          disabled={rowStatusBtn(row.status)}
          onClick={() => networkOpen(row)}
          link
        >
          {row.status === 'open_fail' ? '重新开通' : '开通'}
        </el-button>
      )}
      {falg && (
        <el-button type="primary" onClick={() => editFn(row, $index)} link>
          详情
        </el-button>
      )}
    </>
  )
}

const operationColumns = (falg: boolean = true) => {
  const width = btnAuth.value.resource_creation ? '140px' : '80px'

  return {
    prop: 'operation',
    label: '操作',
    align: 'center',
    fixed: 'right',
    width,
    render: ({ row, $index }: { row: any; $index: number }) =>
      operationRender({ row, $index }, falg),
  }
}

const columns = ref<ColumnProps<goodsDetailsType>[]>([
  {
    prop: 'openNum',
    label: '数量',
    align: 'center',
    width: '100px',
  },
  {
    prop: 'regionId',
    label: '资源池',
    align: 'center',
    width: '220px',
    render: ({ row }: { row: goodsDetailsType }): VNode => {
      return (
        <>
          {disabledResource(row) ? (
            <span>{row['regionName'] ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              v-model={row.regionId}
              style="width: 100%"
              placeholder="请选择资源池"
              onChange={() => {
                row['regionName'] = ''
                row['regionCode'] = ''
                let obj = resourcePoolsDic.value.find((item: any) => item.id === row.regionId)
                if (obj) {
                  row['regionName'] = obj.name
                  row['regionCode'] = obj.code
                }
                undateGoodsAllDetails()
              }}
            >
              {resourcePoolsDic.value.map((option) => (
                <el-option key={option.id} label={option.name} value={option.id} />
              ))}
            </el-select>
          )}
        </>
      )
    },
  },
  {
    prop: 'azId',
    label: '可用区',
    align: 'center',
    width: '220px',
    isShow:
      (btnAuth.value.resource_creation || btnAuth.value.autodit_end) &&
      !['cq', 'backup', 'nas', 'kafka', 'flink', 'es', 'pm', 'bldRedis'].includes(
        goodsType.value,
      ) &&
      goodsType.value !== 'gcs',
    render: ({ row }: { row: any }): VNode => {
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.azName ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              v-model={row.azId}
              style="width: 100%"
              placeholder="请选择可用区"
              onChange={(value: number) => {
                changeAz(value, row)
              }}
            >
              {azList.value[row.regionId]?.map((option: any) => (
                <el-option key={option.id} label={option.name} value={option.id} />
              ))}
              {!azList.value[row.regionId]?.length && (
                <el-option label="数据不存在请刷新页面" value="1" disabled={true} />
              )}
            </el-select>
          )}
        </>
      )
    },
  },
  {
    prop: 'vpcId',
    label: '网络',
    align: 'center',
    width: '250px',
    render: ({ row }: { row: any }): VNode => {
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.vpcName ?? '--'}</span>
          ) : (
            <el-select
              class="mr10"
              clearable
              filterable
              v-model={row.vpcId}
              style="width: 100%"
              placeholder="请选择网络"
              onChange={(value: number) => getPrivateNetworkDic(value, row)}
            >
              {(isInnovationPool.value
                ? innovationWorkList.value[row.regionCode]
                : networkDic.value[`${row.regionId}-${row.azId}`]
              )?.map((option: any) => (
                <el-option key={option.id} label={option.name} value={option.id} />
              ))}
            </el-select>
          )}
        </>
      )
    },
  },
  {
    prop: 'subnetId',
    label: '子网',
    align: 'center',
    width: '250px',
    render: ({ row }: { row: any }): VNode => {
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.subnetNames ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              multiple
              multiple-limit={isMultiple.value ? 0 : 1}
              v-model={row.subnetIds}
              style="width: 100%"
              placeholder="请选择子网络"
              onChange={(value: string[]) => {
                row.subnetNames = ''
                if (value.length > 0) {
                  row['subnets'] = []
                  row.subnetId = value.join(',')
                  row.subnetNames = row.privateNetworkDic
                    ?.filter((item: any) => value.includes(item.id))
                    .map((item: any) => {
                      row['subnets'].push({
                        subnetId: item.id,
                        subnetName: item.subnetName,
                        subnetIp: item.cidr,
                      })
                      return item.subnetName
                    })
                    .join(',')
                }

                undateGoodsAllDetails()
              }}
            >
              {row.privateNetworkDic?.map((option: any) => (
                <el-option key={option.id} label={option.subnetName} value={option.id} />
              ))}
              {!row.privateNetworkDic?.length && (
                <el-option label="请先选择网络,若不存在请删除重选" value="1" disabled={true} />
              )}
            </el-select>
          )}
        </>
      )
    },
  },
  {
    prop: 'templateCode',
    label: '模板',
    align: 'center',
    width: '250px',
    render: ({ row }: { row: any }): VNode => {
      return (
        <>
          {disabledNetwork(row) ? (
            <span>{row.templateCode ?? '--'}</span>
          ) : (
            <el-select
              clearable
              filterable
              v-model={row.templateCode}
              style="width: 100%"
              placeholder="请选择模板"
            >
              {specDic.value[`${row.azId}-${row.flavorName}`]?.map((option: any) => (
                <el-option key={option.id} label={option.flavorName} value={option.flavorName} />
              ))}
              {!specDic.value[`${row.azId}-${row.flavorName}`]?.length && (
                <el-option label="数据不存在" value="1" disabled={true} />
              )}
            </el-select>
          )}
        </>
      )
    },
  },
  {
    prop: 'numbers',
    label: '网络资源',
    align: 'center',
    width: '150px',
    isShow:
      (btnAuth.value.resource_creation || btnAuth.value.autodit_end) && isConfigureNetwork.value,
    render: ({ row, $index }: { row: any; $index: number }): VNode => {
      return (
        <>
          <el-button type="primary" onClick={() => openNetworkDialog(row, $index)} link>
            配置网络
          </el-button>
        </>
      )
    },
  },
  {
    prop: 'message',
    label: '失败原因',
    align: 'center',
    width: '300px',
    isShow: btnAuth.value.resource_creation,
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    width: '100px',
    fixed: 'right',
    isShow: btnAuth.value.resource_creation,

    render: ({ row }: { row: any }): VNode => {
      let obj = {
        wait_open: 'primary',
        opening: 'warning',
        open_success: 'success',
        open_fail: 'danger',
      }
      const type = obj[row.status as keyof typeof obj] ?? 'primary'
      return (
        <el-text type={type} underline={false}>
          {statuses.find((item) => item.value === row.status)?.label}
        </el-text>
      )
    },
  },
])

// 是否显示列
const ishideColumn = (prop: string) => {
  return (
    (btnAuth.value.resource_creation || btnAuth.value.autodit_end) &&
    goodsTypeEnum[goodsType.value]?.params?.some((item) => item === prop)
  )
}

const tableColumns = computed(() => (falg: boolean = true) => {
  if (!goodsType.value || goodsType.value === 'unknown') return []

  //  过滤 可用区 和 网络类型 ,实例规格
  const columnsProps = ['vpcId', 'subnetId', 'templateCode']

  const filterColumns = columns.value.filter((item) => {
    if (columnsProps.includes(item.prop!)) {
      if (item.prop === 'templateCode') {
        return ishideColumn(item.prop!) && props.orderDesc?.domainCode === 'plf_prov_nwc_zj_nfvo'
      }
      if (isConfigureNetwork.value) return false
      return ishideColumn(item.prop!)
    }
    if (item.prop === 'message') return !falg

    return true
  })
  let indexColumn: ColumnProps = { type: 'index', label: '序号', width: 55 }
  const newColumns = [indexColumn, ...goodsAllColumns[goodsType.value], ...filterColumns]
  if (falg || btnAuth.value.resource_creation) newColumns.push(operationColumns(falg))
  if (
    falg &&
    pageStatus.value &&
    (btnAuth.value.resource_creation || btnAuth.value.schema_administrator)
  )
    newColumns.unshift(selectedColumns())
  return newColumns
})

//  -------------批量赋值操作---------------------
const proTable = ref<ProTableInstance>()
const poolId = ref('') // 资源池

const azId = ref('') // 可用区
const selePoolId = computed(() => {
  if (!proTable.value?.selectedList.length) return ''
  return proTable.value?.selectedList[0].regionId
})

const selectable = (row: any) => {
  // 当为云硬盘的时候判断
  if (['evs', 'eip'].includes(goodsType.value) && row['vmName']) return false

  if (btnAuth.value.resource_creation) {
    if (rowStatus(row.status)) return false
    const pooold = proTable.value?.selectedList.map((item: any) => item.regionId) ?? []
    return !pooold.length || !row.regionId || pooold.includes(row.regionId)
  }

  return true
}

/**
 *
 * @param poolIdList
 * @param falg true: 资源池 false: 可用区
 */
const chageDetails = async (poolIdList: string[], falg: boolean) => {
  let obj = falg
    ? resourcePoolsDic.value.find((item: any) => item.id === poolId.value)
    : azList.value[selePoolId.value].find((item: any) => item.id === azId.value)
  let networkParams: any[] = []
  let specParams: any[] = []
  const arr = goodsAllDetails.value.map((item: any) => {
    const orderGoodsIdString = item.ids?.join('|')
    if (poolIdList.includes(orderGoodsIdString!)) {
      if (falg) {
        item['regionId'] = obj.id
        item['regionName'] = obj.name
        item['regionCode'] = obj.code
      } else {
        item['subnetIds'] = []
        item['subnetName'] = ''
        item['vpcName'] = ''
        item['vpcCode'] = ''
        item['vpcId'] = ''
        item['templateCode'] = ''
        !isInnovationPool.value && (item['planeNetworkModel'] = [])
        item['privateNetworkDic'] = []
        item['azName'] = obj.name
        item['azCode'] = obj.code
        item['azId'] = obj.id
        // 获取网络接口入参
        networkParams.push({
          orderId: props.orderId,
          regionCode: item.regionCode,
          azCode: item.azCode,
          key: `${item.regionId}-${item.azId}`,
        })
        // 获取模板接口入参
        specParams.push({
          regionId: item.regionId,
          azId: item.azId,
          name: item.flavorName,
          key: `${item.azId}-${item.flavorName}`,
        })
      }
    }
    return {
      ...item,
    }
  })
  // 获取网络字典集合
  networkParams = uniqueByKeys(networkParams, ['regionCode', 'azCode', 'key'])
  specParams = uniqueByKeys(specParams, ['regionId', 'azId', 'name', 'key'])

  await Promise.all(
    networkParams.map((item) => {
      let key = item.key
      delete item.key
      return getNetworkDic(item, key)
    }),
  )
  await Promise.all(
    specParams.map((item) => {
      let key = item.key
      delete item.key
      return getSpecDic(item, key)
    }),
  )
  return arr
}

const showCheckboxAll = () => {
  if (!btnAuth.value.resource_creation) return false
  if (!goodsAllDetails.value.length) return false

  const firstValue = goodsAllDetails.value[0]['regionId']
  return !goodsAllDetails.value.every((item: any) => item['regionId'] === firstValue)
}

const selectedColumns = (): ColumnProps => {
  return {
    type: 'selection',
    align: 'center',
    fixed: 'left',
    width: '55px',
    selectable: selectable,
  }
}

const setPool = async (falg = true) => {
  if (!proTable.value?.isSelected) return showTips('请选择要操作的数据')
  await ElMessageBox.confirm('确定要批量赋值吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })

  const poolIdList = proTable.value?.selectedListIds.map((item: any) => item.join('|'))

  const arr = await chageDetails(poolIdList, falg)

  goodsAllDetails.value = mergeParent(arr)
  proTable.value?.clearSelection()
  azId.value = ''
  poolId.value = ''
}
// -----------------end---------------------

// -----------------配置网络--------------
const configureNetworkRef = ref<InstanceType<typeof ConfigureNetwork>>()

const openNetworkDialog = async (row: any, index: number) => {
  if (!row.azId && !isInnovationPool.value) return showTips('请先选择可用区')

  let params = {
    orderId: props.orderId,
    regionCode: row.regionCode,
    azCode: row.azCode,
  }
  await configureNetworkRef.value?.openDialog({
    row,
    index,
    disabled: disabledNetwork(row),
    multiple:
      props.orderDesc?.domainCode === 'plf_prov_nwc_zj_nfvo' &&
      ['ecs', 'mysql', 'redis'].includes(goodsType.value),
    vpc: props.domainCodeStatus,
    params,
  })
}

const updateNetworks = (data: any, index: number) => {
  if (dialogVisible.value) {
    goodsDetails.value[index]['planeNetworkModel'] = JSON.parse(JSON.stringify(data))
  } else {
    goodsAllDetails.value[index]['planeNetworkModel'] = JSON.parse(JSON.stringify(data))
    undateGoodsAllDetails()
  }
}
// -----------------end---------------------
</script>

<style lang="scss" scoped>
.title-input {
  flex: 1;
  padding-top: 20px;
  margin-right: 10px;
}
/* 隐藏选择列中的全选复选框 */
/* .el-table__header th.is-leaf .el-checkbox {
  display: none;
} */
/* 隐藏全选复选框 */
.show-checkbox-all {
  :deep() {
    .el-table .el-table__header th .el-checkbox {
      display: none;
    }
  }
}
</style>
