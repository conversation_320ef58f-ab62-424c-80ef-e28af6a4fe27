<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="sl-form-container">
      <div class="sl-form">
        <el-form>
          <div class="group-con bg-fff sl-layout">
            <el-row class="sl-layout-inner">
              <el-col :span="24">
                <el-form-item
                  :label="fileLabel"
                  :rules="[{ required: true, message: '请上传文件', trigger: ['blur', 'change'] }]"
                  prop="_fileList"
                >
                  <div class="upload-box" style="width: 100%">
                    <el-upload
                      ref="uploadRef"
                      :limit="1"
                      :drag="true"
                      :accept="accept"
                      :auto-upload="false"
                      :on-change="handleFileChange"
                      :file-list="fileList"
                      :http-request="handleHttpRequest"
                    >
                      <div class="el-upload__text">点击上传 <em>/拖拽到此区域 </em></div>
                    </el-upload>
                    <div class="el-upload__tip">
                      <span style="color: red">
                        请上传{{ acceptTip }}格式文件，大小在 100M 以内
                      </span>
                      <el-button class="ml5" type="primary" @click="handleDownloadTemplate" link>
                        {{ templateBtnText }}
                      </el-button>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleUpload">提交</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 导入结果弹窗 -->
  <el-dialog
    v-model="resultVisible"
    title="导入结果"
    width="800px"
    :before-close="handleResultClose"
    :close-on-click-modal="false"
  >
    <div class="import-result">
      <div class="result-summary">
        <el-alert
          :title="resultTitle"
          :type="resultType"
          :description="resultDescription"
          show-icon
          :closable="false"
        />
      </div>

      <div v-if="errorDetails.length > 0" class="error-details">
        <h4>错误详情：</h4>
        <el-table :data="errorDetails" border style="width: 100%">
          <el-table-column prop="rowNumber" label="行号" width="80" />
          <el-table-column prop="fieldName" label="字段名" width="120" />
          <el-table-column prop="errorMessage" label="错误信息" />
          <el-table-column prop="errorValue" label="错误值" width="150" />
        </el-table>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleResultClose">关闭</el-button>
        <el-button v-if="hasSuccess" type="primary" @click="handleSuccess">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus'
import SlMessage from '@/components/base/SlMessage'
import { useDownload } from '@/hooks/useDownload'

/**
 * 导入API函数类型定义
 */
type ImportApiFunction = (formData: FormData) => Promise<{
  code: number
  message: string
  entity: {
    totalRows: number
    successCount: number
    failureCount: number
    errorDetails?: Array<{
      rowNumber: number
      fieldName: string
      errorMessage: string
      errorValue: string
    }>
  }
}>

/**
 * 下载模板API函数类型定义
 */
type DownloadTemplateApiFunction = (params: any) => Promise<any>

/**
 * 成功回调函数类型定义
 */
type SuccessCallback = () => void

/**
 * 组件属性定义
 */
interface ImportDialogProps {
  /** 弹窗显示状态 */
  modelValue: boolean
  /** 弹窗标题 */
  title?: string
  /** 文件标签文字 */
  fileLabel?: string
  /** 接受的文件类型 */
  accept?: string
  /** 文件类型提示文字 */
  acceptTip?: string
  /** 导入API函数 */
  importApi: ImportApiFunction
  /** 下载模板API函数 */
  downloadTemplateApi: DownloadTemplateApiFunction
  /** 模板下载按钮文字 */
  templateBtnText?: string
  /** 模板文件名 */
  templateFileName?: string
  /** 导入成功回调函数 */
  onSuccess?: SuccessCallback
}

/**
 * 导入结果类型定义
 */
interface ImportResult {
  totalRows: number
  successCount: number
  failureCount: number
  errorDetails?: Array<{
    rowNumber: number
    fieldName: string
    errorMessage: string
    errorValue: string
  }>
}

/**
 * 错误详情类型定义
 */
interface ErrorDetail {
  rowNumber: number
  fieldName: string
  errorMessage: string
  errorValue: string
}

const props = withDefaults(defineProps<ImportDialogProps>(), {
  title: '导入',
  fileLabel: '文件',
  accept: '.xlsx,.xls',
  acceptTip: 'xlsx',
  templateBtnText: '模板下载',
  templateFileName: '导入模板.xlsx',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// ==================== 响应式数据 ====================
/** 导入弹窗显示状态 */
const visible = ref(false)
/** 结果弹窗显示状态 */
const resultVisible = ref(false)
/** 上传组件实例 */
const uploadRef = ref<UploadInstance>()
/** 文件列表 */
const fileList = ref<UploadFile[]>([])

// ==================== 导入结果相关数据 ====================
/** 导入结果数据 */
const importResult = ref<ImportResult | null>(null)
/** 错误详情列表 */
const errorDetails = ref<ErrorDetail[]>([])

// ==================== 计算属性 ====================
/**
 * 结果标题
 * @returns 根据成功和失败数量返回对应的标题
 */
const resultTitle = computed(() => {
  if (!importResult.value) return ''
  const { successCount, failureCount } = importResult.value
  if (successCount > 0 && failureCount === 0) return '导入成功'
  if (successCount === 0 && failureCount > 0) return '导入失败'
  return '部分导入成功'
})

/**
 * 结果类型
 * @returns 根据成功和失败数量返回对应的类型
 */
const resultType = computed(() => {
  if (!importResult.value) return 'info'
  const { successCount, failureCount } = importResult.value
  if (successCount > 0 && failureCount === 0) return 'success'
  if (successCount === 0 && failureCount > 0) return 'error'
  return 'warning'
})

/**
 * 结果描述
 * @returns 显示成功和失败的数量统计
 */
const resultDescription = computed(() => {
  if (!importResult.value) return ''
  const { successCount, failureCount } = importResult.value
  return `成功导入 ${successCount} 条，失败 ${failureCount} 条`
})

/**
 * 是否有成功导入的数据
 * @returns 如果有成功导入的数据返回true
 */
const hasSuccess = computed(() => {
  return (importResult.value?.successCount ?? 0) > 0
})

// ==================== 监听器 ====================
/**
 * 监听modelValue变化，控制弹窗显示状态
 */
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (!val) {
      fileList.value = []
      resultVisible.value = false
    }
  },
)

/**
 * 监听visible变化，同步到父组件
 */
watch(visible, (val) => emit('update:modelValue', val))

// ==================== 事件处理函数 ====================
/**
 * 关闭导入弹窗
 */
const handleClose = () => {
  visible.value = false
  fileList.value = []
}

/**
 * 关闭结果弹窗
 */
const handleResultClose = () => {
  resultVisible.value = false
  importResult.value = null
  errorDetails.value = []
}

/**
 * 处理导入成功后的操作
 */
const handleSuccess = () => {
  handleResultClose()
  handleClose()
  props.onSuccess?.()
}

/**
 * 文件选择变化处理
 * @param file 当前选择的文件
 * @param files 所有文件列表
 */
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
}

/**
 * 手动上传触发
 */
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  uploadRef.value?.submit()
}

/**
 * 自定义上传逻辑
 * @param options 上传选项
 */
const handleHttpRequest = async (options: any) => {
  const { file } = options
  try {
    const formData = new FormData()
    formData.append('file', file)
    const res = await props.importApi(formData)

    if (res.code === 200) {
      // 处理导入结果
      importResult.value = res.entity
      errorDetails.value = res.entity?.errorDetails || []

      // 根据结果类型显示不同消息
      const { successCount, failureCount } = res.entity
      if (successCount > 0 && failureCount === 0) {
        // 完全成功
        SlMessage.success('导入成功')
        handleClose()
        props.onSuccess?.()
      } else if (successCount === 0 && failureCount > 0) {
        // 完全失败
        ElMessage.error('导入失败，请查看错误详情')
        resultVisible.value = true
      } else {
        // 部分成功
        ElMessage.warning('部分导入成功，请查看详情')
        resultVisible.value = true
      }
    }

    // 上传成功后从列表中移除文件
    fileList.value = fileList.value.filter((f) => f.uid !== file.uid)
  } catch (error) {
    ElMessage.error(`${file.name} 导入失败`)
    console.error('导入错误:', error)
  }
}

/**
 * 下载模板文件
 */
const handleDownloadTemplate = async () => {
  try {
    await useDownload(props.downloadTemplateApi, props.templateFileName, {})
  } catch (error) {
    ElMessage.error('模板下载失败')
    console.error('下载错误:', error)
  }
}
</script>

<style scoped lang="scss">
.upload-box {
  width: 100%;
}

.import-result {
  .result-summary {
    margin-bottom: 20px;
  }

  .error-details {
    h4 {
      margin: 16px 0 12px 0;
      color: #f56c6c;
    }
  }
}
</style>
